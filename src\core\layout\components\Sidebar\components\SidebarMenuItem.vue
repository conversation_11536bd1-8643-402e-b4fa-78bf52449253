<template>
  <!-- 如果菜单项没有隐藏则显示 -->
  <div v-if="item.visible === 1">
    <!-- 显示只有一个子路由或没有子路由的菜单项 -->
    <template
      v-if="
        hasOneShowingChild(item.children, item as RouteRecordRaw) &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        item.visible === 1
      "
    >
      <AppLink
        v-if="onlyOneChild.visible === 1 && onlyOneChild.path"
        :to="{
          path: resolvePath(onlyOneChild.path),
          query: onlyOneChild?.params,
        }"
      >
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{ 'submenu-title-noDropdown': !isNest }"
        >
          <SidebarMenuItemTitle
            :icon="onlyOneChild?.icon"
            :title="onlyOneChild?.menuName"
            :level="onlyOneChild?.level"
          />
        </el-menu-item>
      </AppLink>
    </template>

    <!-- 显示具有多个子路由的父菜单项 -->
    <el-sub-menu v-else :index="resolvePath(item.path)" teleported>
      <template #title>
        <SidebarMenuItemTitle
          v-if="item"
          :icon="item && item?.icon"
          :title="item.menuName"
        />
      </template>
      <SidebarMenuItem
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(item.path)"
      />
    </el-sub-menu>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: "SidebarMenuItem",
  inheritAttrs: false,
});

import path from "path-browserify";
import { isExternal } from "@/core/utils/index";
import { RouteRecordRaw } from "vue-router";

const props = defineProps({
  /**
   * 当前路由项对象
   */
  item: {
    type: Object,
    required: true,
  },

  /**
   * 父层级完整路由路径
   */
  basePath: {
    type: String,
    required: true,
  },
  /**
   * 是否为嵌套路由
   */
  isNest: {
    type: Boolean,
    default: false,
  },
});

const onlyOneChild = ref();

/**
 * 判断当前路由是否只有一个显示的子路由
 *
 * @param children 子路由数组
 * @param parent 父级路由对象
 * @returns 布尔值，表示是否只有一个显示的子路由
 */
function hasOneShowingChild(
  children: RouteRecordRaw[] = [],
  parent: RouteRecordRaw
) {
  // 如果有子菜单且子菜单数量大于0，直接返回false以显示子菜单
  if (Array.isArray(children) && children.length > 0) {
    return false;
  }

  // 如果没有子路由，显示父级路由
  onlyOneChild.value = { ...parent, path: parent.path || "", noShowingChildren: true };
  return true;
}

/**
 * 解析路由路径，将相对路径转换为绝对路径
 *
 * @param routePath 路由路径
 * @returns 绝对路径
 */
function resolvePath(routePath: string) {
  if (isExternal(routePath)) {
    return routePath;
  }
  if (isExternal(props.basePath)) {
    return props.basePath;
  }

  // 完整路径(/system/user) = 父级路径(/system) + 路由路径(user)
  const fullPath = path.resolve(props.basePath, routePath);
  return fullPath;
}
</script>

<style lang="scss">
.hideSidebar {
  .submenu-title-noDropdown {
    position: relative;
    padding: 0 !important;

    .el-tooltip {
      padding: 0 !important;

      .sub-el-icon {
        margin-left: 19px;
      }
    }

    & > span {
      display: inline-block;
      width: 0;
      height: 0;
      overflow: hidden;
      visibility: hidden;
    }
  }

  .el-sub-menu {
    overflow: hidden;

    & > .el-sub-menu__title {

      .sub-el-icon {
        margin-left: 19px;
      }

      .el-sub-menu__icon-arrow {
        display: none;
      }
    }
  }

  .el-menu--collapse {
    width: $sidebar-width-collapsed;

    .el-sub-menu {
      & > .el-sub-menu__title {
        & > span {
          display: inline-block;
          width: 0;
          height: 0;
          overflow: hidden;
          visibility: hidden;
        }
      }
    }
  }
}

.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-sub-menu__title {
    white-space: nowrap;
    padding: 0px 16px;
}

.el-menu-item{
    background: $menu-item-background !important;
    font-size: 13px !important;
    color: $menu-item-text !important;
    height: 34px !important;
    padding-left: 32px !important;
}

.el-sub-menu {
    border-bottom: 1px solid #F2F3F4 !important;
}

.el-sub-menu__title{
    height: 48px !important;
    i{
        transform: rotateZ(270deg) !important;
        width: 0 !important;
        height: 0 !important;
        border-left: 6px solid transparent !important;
        border-right: 6px solid transparent !important;
        border-top: 6px solid #90979E !important;
        border-radius: 8px !important
    }
}

.el-sub-menu.is-opened{
    .el-sub-menu__title{
        i{
            transform: rotateZ(360deg) !important;
        }
    }
}

.el-sub-menu.is-active{
    .el-sub-menu__title{
        color:$menu-hover-text !important;
        .svg-div{
            .svg-icon{
                fill:$menu-hover-text !important;
            }
        }
        i{
            border-top: 6px solid $menu-active-background !important;
        }
    }
}

.el-menu-item.is-active {
    color: $menu-active-text !important;
    background: $menu-active-background !important;
}
.el-menu-item.is-active::before {
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: #ffffff;
    border-radius: 50%;
}
.el-menu-item:hover:not(.is-active){
    color:$menu-hover-text !important;
    background: $menu-hover !important;
}
.el-sub-menu__title:hover{
    color:$menu-hover-text !important;
    background: $menu-hover !important;
    i{
        border-top: 6px solid $menu-active-background !important;
    }
    .svg-icon{
        fill:$menu-hover-text !important;
    }
}

.el-menu--popup{
    padding: 0px !important;
}
</style>
