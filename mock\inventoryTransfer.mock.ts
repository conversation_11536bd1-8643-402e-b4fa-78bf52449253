import { defineMockWms} from "./base";
import {InventoryTransferInfoDetailVO} from "@/modules/wms/api/inventoryTransfer";

export default defineMockWms([

  //获取当前仓库的库区下拉数据源
    {
        url: "storeArea/queryListByCurrentWarehouse",
        method: ["GET"],
        body: {
            code: 0,
            data:  [
                {
                    areaCode: '1',
                    areaName: '库区1',

                },
                {
                    areaCode: '2',
                    areaName: '库区2',

                },
            ],
            msg: "一切ok",
        },
    },

    //查询可选的原商品列表
    {
        url: "productStock/selectPage",
        method: ["POST"],
        body: {
            code: 0,
            data: {
                records: [
                    {
                        id: '1',
                        fullCategoryName: "商品分类",
                        productCode: "1000",
                        productName: "商品名称1",
                        productSpec: "商品规格1",
                        totalStockQty: 100,
                        availableStockQty: 90,
                        productUnitName:'斤',
                        sourceWarehouseAreaId: '1',
                        sourceWarehouseAreaName: 'warehouseAreaName1',

                    },
                    {
                        id: '2',
                        fullCategoryName: "商品分类2",
                        productCode: "2000",
                        productName: "商品名称2",
                        productSpec: "商品规格2",
                        totalStockQty: 100,
                        availableStockQty: 90,
                        productUnitName:'瓶',
                        sourceWarehouseAreaId: '2',
                        sourceWarehouseAreaName: 'warehouseAreaName2',
                    },
                    {
                        id: '3',
                        fullCategoryName: "商品分类3",
                        productCode: "3000",
                        productName: "商品名称3",
                        productSpec: "商品规格3",
                        totalStockQty: 50,
                        availableStockQty: 50,
                        productUnitName:'袋',
                        sourceWarehouseAreaId: '1',
                        sourceWarehouseAreaName: 'warehouseAreaName1',
                    },
                ],
                total: '2',
            },
            msg: "一切ok",
        },
    },

    // 库存转移分页列表
    {
        url: "inventoryTransferInfo/list",
        method: ["POST"],
        body: {
            code: 0,
            data: {
                records: [
                    {
                        id: '1',
                        transferOrderCode: '移库单号',
                        transferType: 1,
                        sourceWarehouseArea: '来源库区',
                        transferReasonName: '移库原因',
                        remark: "卑职呃呃",
                        createUserName:'ccxc',
                        createTime: 1740473528000,
                        disassemblyAssembleUser: '移库人',
                        disassemblyAssembleTime: 1740473528000,
                        transferStatus: 0,
                    },
                    {
                        id: '1',
                        transferOrderCode: '移库单号',
                        transferType: 1,
                        sourceWarehouseArea: '来源库区',
                        transferReasonName: '移库原因',
                        remark: "卑职呃呃",
                        createUserName:'ccxc',
                        createTime: 1740473528000,
                        disassemblyAssembleUser: '移库人',
                        disassemblyAssembleTime: 1740473528000,
                        transferStatus: 1,
                    },
                ],
                total: '2',
            },
            msg: "一切ok",
        },
    },

    // 库存转移详情
    {
        url: "inventoryTransferInfo/detail/1",
        method: ["GET"],
        body: {
            code: 0,
            data: {
                    id: '1',
                    transferOrderCode: '移库单号',
                    createTime: 1740473528000,
                    createUserName:'ccxc',
                    sourceWarehouseAreaCode: '1',
                    sourceWarehouseAreaName: '仓库库区名称',
                    transferReasonId: 1,
                    transferReasonName: '移库原因名称',
                    transferType: 1,
                    remark: "卑职呃呃",
                    inventoryTransferInfoDetailList:[
                        {
                            id: '1',
                            productCode: "1000",
                            productName: "productName",
                            productSpec:"商品规格",
                            targetWarehouseAreaCode:'1',
                            targetWarehouseAreaName:'1',
                            transferQty:10,
                            productUnit:'单位',
                            afterAvailableQty:5,
                            afterInventoryQty:200,
                            beforeAvailableQty:8,
                            beforeInventoryQty:100,
                            transferStatus: 1,
                        },
                    ],
                    transferStatus: 0,
                    warehouseCode: '1111',
                    disassemblyAssembleTime: 1740473528000,
            },
            msg: "一切ok",
        },
    },

    // 删除库存转移
    {
        url: "inventoryTransferInfo/del/1",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "删除成功",
        },
    },

    // 库存转移添加、编辑
    {
        url: "inventoryTransferInfo/save",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "删除成功",
        },
    },

]);
