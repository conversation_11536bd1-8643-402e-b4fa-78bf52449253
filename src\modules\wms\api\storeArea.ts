/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-22 13:42:59
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-03-19 09:22:49
 * @FilePath: \supply-manager-web\src\modules\wms\api\storeArea.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/core/utils/request";

const STOREAREA_BASE_URL = "/supply-wms/storeArea";

class StoreAreaAPI {
  /** 获取仓库分页数据 */
  static getStoreArea(queryParams?: PageQuery) {
    return request<any, PageResult<storeAreaInfo[]>>({
      url: `${STOREAREA_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }
  /** 添加库区 */
  static addStoreArea(data: storeAreaForm) {
    return request({
      url: `${STOREAREA_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }
  /** 编辑库区 */
  static editStoreArea(data: storeAreaForm) {
    return request({
      url: `${STOREAREA_BASE_URL}/update`,
      method: "post",
      data: data,
    });
  }
  /** 更改库区状态 */
  static updateStatus(data: { id?: string; status?: number }) {
    return request({
      url: `${STOREAREA_BASE_URL}/updateStatus`,
      method: "post",
      data: data,
    });
  }
  /** 删除库区 */
  static deleteStoreArea(data: { id?: string }) {
    return request({
      url: `${STOREAREA_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }
  /**
   * 获取国际国家列表-含港澳台
   *
   */
  static getAllCountry() {
    return request({
      url: `/supply-base/country/all`,
      method: "get",
    });
  }

  /**
   * 查询当前仓库是否禁用
   *
   */
  static queryIsDisable() {
    return request({
      url: `/supply-wms/warehouse/isDisable`,
      method: "get",
    });
  }
}

/** 角色分页对象 */
export interface storeAreaInfo {
  /** 库区编码 */
  areaCode?: string;
  /** 库区名称 */
  areaName?: string;
}
export interface storeAreaForm {
  id?: string;
  warehouseCode?: string;
  areaCode?: string;
  areaName?: string;
  contactPerson?: string;
  mobile?: string;
  notes?: string;
  status?: number;
  countryAreaCode?: string;
  contactLandline?: string;
  areaType?: number;
}
export interface storeAreaPageQuery extends PageQuery {
  /** 角色名称 */
  roleName?: string;
  /** 状态 */
  status?: number;
}

export default StoreAreaAPI;
