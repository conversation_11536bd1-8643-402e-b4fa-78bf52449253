import request from "@/core/utils/request";

const PURCHASE_BASE_URL = "/supply-wms/lossOrder";

class ReportLossOrderAPI {

  /** 获取报损单分页数据 */
  static getReportLossOrderPage(queryParams?: ReportLossOrderPageQuery) {
    return request<any, PageResult<ReportLossOrderPageVO[]>>({
      url: `${PURCHASE_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

  /**删除报损单 */
  static deleteReportLossOrder(data: { id?:string }) {
      return request({
          url: `${PURCHASE_BASE_URL}/delete`,
          method: "post",
          data: data,
      });
  }

  /**暂存报损单 */
  static saveReportLossOrder(data: ReportLossOrderFrom) {
      return request({
          url: `${PURCHASE_BASE_URL}/save`,
          method: "post",
          data: data,
      });
  }

    /**提交报损单 */
    static submitReportLossOrder(data: ReportLossOrderFrom) {
        return request({
            url: `${PURCHASE_BASE_URL}/submit`,
            method: "post",
            data: data,
        });
    }

    /** 报损单详情 */
    static getReportLossOrderDetail(data:{id?:string}) {
        return request<any, ReportLossOrderFrom>({
            url: `${PURCHASE_BASE_URL}/detail`,
            method: "post",
            data: data,
        });
    }

    // 领单
    static receiveReportLossOrder(data:{id?:string}) {
        return request({
            url: `${PURCHASE_BASE_URL}/pickOrder`,
            method: "post",
            data: data,
        });
    }
    // 取消领单
    static cancelReceiveReportLossOrder(data:{id?:string}) {
        return request({
            url: `${PURCHASE_BASE_URL}/releaseOrder`,
            method: "post",
            data: data,
        });
    }

    // 获取完成的分拣单
    static getCompletedOrderList(data: {sortingStatus: number | null}){
        return request({
            url: `/supply-wms/sorting/handleSuccess`,
            method: "post",
            data: data,
        })
    }


     /** 报损单查询库区:查询库存>0的库区 */
  static queryHasStockListByCurrentWarehouse(data?: { status?: number }) {
    return request({
      url: `/supply-wms/storeArea/queryHasStockListByCurrentWarehouse`,
      method: "get",
      params: data,
    });
  }
}

export default ReportLossOrderAPI;

/** 报损单分页查询参数 */
export interface ReportLossOrderPageQuery extends PageQuery {
  /** 报损单号 */
  lossOrderCode?: string;
  /** 报损类型  (1->商品拆装)*/
  orderTypeList?: number[];
  /** 状态 (0->草稿，1->已完成)*/
  orderStatusList?:number[];
  /** 是否领单状态筛选 (0->未领用，1->已领用)*/
  receivingStatusList?: number[];
  /** 时间类型(1->创建时间，2->计划交货时间)*/
  dateType?: number;
  /** 时间范围 */
  dateRange?: string[];
}

/** 报损单分页对象 */
export interface ReportLossOrderPageVO {
  /** ID */
  id?: string;
  /** 报损单号 */
  lossOrderCode?: string;
  /** 报损类型  (1->商品拆装) */
  outboundType?: number;
  /** 源单据类型 （12:拆装单）*/
  sourceOrderType?: string;
  /** 拆装单号 （源单据编号）*/
  sourceOrderCode?: string;
  /** 报损人 */
  reporterName?: string;
  /** 报损时间 */
  reportTime?: string;
  /** 创建人 */
  createUserName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 备注 */
  remark?: string;
  /** 状态 (0->草稿，1->已完成)*/
  orderStatus?: number;
}

/** 报损单对象 */
export interface ReportLossOrderFrom{
    /** ID */
    id?: string;
    /** 报损单号 */
    lossOrderCode?: string;
    /** 报损类型  (1->商品拆装) */
    outboundType?: number;
    /** 源单据类型 （12:拆装单）*/
    sourceOrderType?: any;
    /** 拆装单号 （源单据编号）*/
    sourceOrderCode?: string;
    /** 报损人 */
    reporterName?: string;
    /** 报损时间 */
    reportTime?: string;
    /** 创建人 */
    createUserName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 备注 */
    remark?: string;
    /** 状态 (0->草稿，1->已完成)*/
    orderStatus?: number;
    /** 报损单明细 */
    reportLossOrderDetailList?: ProductVO[];
    // 报损类型
    orderType?: number;
    productList: []
}


export interface ProductVO{
    /** ID */
    id?: string;
    /** 商品编码 */
    productCode?: string;
    /** 商品名称 */
    productName?: string;
    /** 商品规格 */
    productSpec?: string;
    /** 出库库区编码 */
    warehouseAreaId?: string;
    // sourceWarehouseAreaId?: string;
    /** 出库库区名称 */
    warehouseAreaName?: string;
    // sourceWarehouseAreaName?: string;
    /** 出库数量 */
    disassemblyOutQty?: number;
    /** 报损数量 */
    lossQty?: number;
    /**	报损状态（0:不报损;1:报损）*/
    lossStatus?: number;
}
