import defaultProductImage from "/images/default_product_image.png"

/**
 * 图片过滤
 * @param data
 * @param Fields
 */

export function filterImg(data: any, Fields: string[]) {
  function traverse(obj: any) {
    if (obj !== null && typeof obj === 'object') {
      // 如果是数组，则遍历每个元素
      if (Array.isArray(obj)) {
        for (const item of obj) {
          traverse(item);
        }
      } else {
        // 普通对象，遍历属性
        for (const key in obj) {
          if (Fields.includes(key)) {
            // 匹配到的字段,如果有值，则返回该值，否则返回默认图片路径
            obj[key] = obj[key] || defaultProductImage;
          } else {
            traverse(obj[key]); // 递归处理子对象
          }
        }
      }
    }
  }

  traverse(data);
  return data;
}
