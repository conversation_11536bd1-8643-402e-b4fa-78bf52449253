<template>
    <div class="app-container">
        <div class="addInventoryTransfer"  v-loading="loading">
            <div >
                <div class="page-title">
                    <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                    <div>
                        <span v-if="type=='add'">{{ $t("inventoryTransfer.button.addInventoryTransfer") }}</span>
                        <span v-else> {{t('inventoryTransfer.label.transferOrderCode')}}：{{form.transferOrderCode}}</span>
                    </div>
                </div>
            </div>
            <div class="grad-row" style="position: absolute; top: 15px;right: 30px"  v-if="type=='edit'">
                <span class="el-form-item__label">{{ $t("inventoryTransfer.label.createUserName") }}：<span  class="el-form-item__content">{{form.createUserName}}</span></span>
                <span class="el-form-item__label"> {{t('inventoryTransfer.label.createTime')}}： <span class="el-form-item__content">{{parseDateTime(form.createTime, "dateTime")}}</span></span>
            </div>
            <div class="page-content">
                <el-form
                        :model="form"
                        :rules="rules"
                        ref="fromRef"
                        label-width="108px"
                        label-position="right"
                >
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("inventoryTransfer.label.basicInformation") }}
                        </div>
                    </div>
                    <div v-if="type=='add'">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.outWarehouseArea')" prop="sourceWarehouseAreaCode">
                                    <el-select
                                      v-model="form.sourceWarehouseAreaCode"
                                      :placeholder="$t('common.placeholder.selectTips')"
                                      clearable
                                      filterable
                                      @change="changeSourceWarehouseAreaCode"
                                      class="!w-[256px]"
                                    >
                                        <el-option v-for="item in outWarehouseAreaList" :key="item.areaCode" :label="item.warehouseArea" :value="item.areaCode"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferType')" prop="transferType">
                                    <el-select
                                      v-model="form.transferType"
                                      :placeholder="$t('common.placeholder.selectTips')"
                                      clearable
                                      filterable
                                      disabled
                                      class="!w-[256px]"
                                    >
                                        <el-option v-for="item in inventoryTransferTypeList" :key="item.inventoryTransferTypeId" :label="item.inventoryTransferTypeName" :value="item.inventoryTransferTypeId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferReasonName')" prop="transferReasonId">
                                    <el-select
                                      v-model="form.transferReasonId"
                                      :placeholder="$t('common.placeholder.selectTips')"
                                      clearable
                                      filterable
                                      class="!w-[256px]"
                                    >
                                        <el-option v-for="item in inventoryTransferReasonList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item :label="$t('inventoryTransfer.label.remark')" prop="remark">
                                    <el-input
                                      :rows="4"
                                      type="textarea"
                                      show-word-limit
                                      v-model="form.remark"
                                      :placeholder="$t('common.placeholder.inputTips')"
                                      maxlength="200"
                                      clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div v-if="type=='detail'">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.createUserName')">
                                    {{form.createUserName}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.createTime')">
                                    {{parseDateTime(form.createTime, "dateTime")}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferUserName')">
                                    {{form.transferUserName}}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferReasonName')">
                                    <span>{{form.transferReasonName}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.outWarehouseArea')">
                                    <span>{{form.sourceWarehouseAreaName}} | {{form.sourceWarehouseAreaCode}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferType')">
                                    <span>{{form.transferType}}</span>
                                </el-form-item>
                            </el-col>


                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferTime')">
                                    <span  v-if="form.transferTime" style="word-break:break-all;"> {{parseDateTime(form.transferTime, "dateTime")}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.receiptStatus')">
                                    <span  v-if="!isEmpty(form.receiptStatus)" style="word-break:break-all;">{{filterReceiveStatus(form.receiptStatus)}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.receiveName')">
                                    <span  v-if="form.transferUserName" style="word-break:break-all;">{{form.transferUserName}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.receiveTime')">
                                    <span  v-if="form.transferTime" style="word-break:break-all;"> {{parseDateTime(form.transferTime, "dateTime")}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferStatus')">
                                    <span  v-if="!isEmpty(form.transferStatus)" style="word-break:break-all;">{{filterStatus(form.transferStatus)}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.remark')">
                                    <span  v-if="form.remark" style="word-break:break-all;">{{form.remark}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div v-if="type=='edit'">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.createUserName')">
                                    {{form.createUserName}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.createTime')">
                                    {{parseDateTime(form.createTime, "dateTime")}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferStatus')">
                                    <span  v-if="!isEmpty(form.transferStatus)" style="word-break:break-all;">{{filterStatus(form.transferStatus)}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.outWarehouseArea')" prop="sourceWarehouseAreaCode">
                                    <el-select
                                            v-model="form.sourceWarehouseAreaCode"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            filterable
                                            @change="changeSourceWarehouseAreaCode"
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in outWarehouseAreaList" :key="item.areaCode" :label="item.warehouseArea" :value="item.areaCode"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferType')" prop="transferType">
                                    <el-select
                                            v-model="form.transferType"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            filterable
                                            disabled
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in inventoryTransferTypeList" :key="item.inventoryTransferTypeId" :label="item.inventoryTransferTypeName" :value="item.inventoryTransferTypeId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.transferReasonName')" prop="transferReasonId">
                                    <el-select
                                            v-model="form.transferReasonId"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            filterable
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in inventoryTransferReasonList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>

                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.receiptStatus')">
                                    <span  v-if="!isEmpty(form.receiptStatus)" style="word-break:break-all;">{{filterReceiveStatus(form.receiptStatus)}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.receiveName')">
                                    <span  v-if="form.transferUserName" style="word-break:break-all;">{{form.transferUserName}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('inventoryTransfer.label.receiveTime')">
                                    <span  v-if="form.transferTime" style="word-break:break-all;"> {{parseDateTime(form.transferTime, "dateTime")}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item :label="$t('inventoryTransfer.label.remark')" prop="remark">
                                    <el-input
                                            :rows="4"
                                            type="textarea"
                                            show-word-limit
                                            v-model="form.remark"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            maxlength="200"
                                            clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line"></div>
                    <div class="title-lable" style="position: relative">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("inventoryTransfer.label.inventoryTransferInformation") }}
                        </div>
                        <div class="button-add cursor-pointer" @click="addProduct()" v-if="type!=='detail'">
                            {{$t('inventoryTransfer.button.addInventoryTransferProduct')}}
                        </div>
                    </div>
                    <div>
                        <el-table
                                :data="form.inventoryTransferInfoDetailList"
                                highlight-current-row
                                stripe
                        >
                            <el-table-column type="index" :label="$t('common.sort')" width="60" />
                            <el-table-column :label="$t('inventoryTransfer.label.productInformation')" show-overflow-tooltip min-width="220px">
                                <template #default="scope">
                                    <div class="product-div">

                                        <div class="product">
                                            <div class="product-name">{{scope.row.productName}}</div>
                                            <div>
                                                <span class="product-key">{{$t('inventoryTransfer.label.productCode')}}：</span>
                                                <span class="product-value">{{scope.row.productCode}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('inventoryTransfer.label.productSpec')" prop="productSpec" show-overflow-tooltip min-width="100px"/>
                            <el-table-column :label="$t('inventoryTransfer.label.productUnit')" prop="productUnitName" show-overflow-tooltip min-width="100px"/>

                            <el-table-column :label="$t('inventoryTransfer.label.totalStockQty')" prop="totalStockQty" show-overflow-tooltip min-width="100px"/>
                            <el-table-column :label="`${$t('inventoryTransfer.label.totalStockWeight')}(kg)`" prop="beforeInventoryWeight" show-overflow-tooltip min-width="100px"/>
                            <el-table-column :label="$t('inventoryTransfer.label.availableStockQty')" prop="availableStockQty" show-overflow-tooltip min-width="100px"/>
                            <el-table-column :label="`${$t('inventoryTransfer.label.availableStockWeight')}(kg)`" prop="beforeAvailableWeight" show-overflow-tooltip min-width="120px"/>
                            <el-table-column :label="'*'+$t('inventoryTransfer.label.targetWarehouseArea')" show-overflow-tooltip min-width="200px">
                                <template #default="scope">
                                        <el-form-item class="mt15px" label-width="0px" :prop="'inventoryTransferInfoDetailList.'+scope.$index+'.targetWarehouseAreaCode'" :rules="dialog.visible?'':[{required:true,message:t('inventoryTransfer.rules.targetWarehouseAreaCode'),trigger:['blur','change']}]">
                                            <el-input
                                                    v-if="type=='detail'"
                                                    v-model="scope.row.targetWarehouseAreaCode"
                                                    disabled
                                            >
                                            </el-input>
                                            <el-select
                                                    v-else
                                                    v-model="scope.row.targetWarehouseAreaCode"
                                                    :placeholder="$t('common.placeholder.selectTips')"
                                                    filterable
                                                    clearable
                                                    :disabled="type=='detail'"
                                            >
                                                <el-option v-for="item in outWarehouseAreaEnableList" :key="item.areaCode" :label="item.warehouseArea" :value="item.areaCode"></el-option>
                                            </el-select>

                                        </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column :label="'*'+$t('inventoryTransfer.label.transferQty')" show-overflow-tooltip  min-width="200px">
                                <template #default="scope">
<!--                                    <el-form-item class="mt15px" label-width="0px" :prop="'inventoryTransferInfoDetailList.'+scope.$index+ '.transferQty'" :rules="dialog.visible?'':[{required:true,message:t('inventoryTransfer.rules.transferQty'),trigger:['blur','change']}, { required: true, validator: validateTransferQty, trigger: ['blur','change'] },{pattern: /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^\d\.\d{1,3}$)/, message:t('inventoryTransfer.rules.transferQtyFormat'), trigger: ['blur','change']}]">-->
                                    <el-form-item class="mt15px" label-width="0px" :prop="'inventoryTransferInfoDetailList.'+scope.$index+ '.transferQty'" :rules="dialog.visible?'':[{required:true,message:t('inventoryTransfer.rules.transferQty'),trigger:['blur','change']}, { required: true, validator: validateTransferQty, trigger: ['blur','change'] },{pattern: /^[1-9]\d{0,3}$/, message:t('inventoryTransfer.rules.transferQtyFormat'), trigger: ['blur','change']}]">
                                        <el-input
                                                v-model="scope.row.transferQty"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                clearable
                                                :disabled="type=='detail'"
                                        >
                                            <template #append>{{scope.row.productUnitName}}</template>
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column :label="'*'+$t('inventoryTransfer.label.transferWeight')+'(kg)'" show-overflow-tooltip  min-width="200px">
                                <template #default="scope">
                                    <el-form-item class="mt15px" label-width="0px" :prop="'inventoryTransferInfoDetailList.'+scope.$index+ '.transferWeight'"
                                                  :rules="dialog.visible?'':[{required:true,message:t('inventoryTransfer.rules.transferWeight'),trigger:['blur','change']}, { required: true, validator: validateTransferWeight, trigger: ['blur','change'] },{pattern: /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/, message:t('inventoryTransfer.rules.TransferWeightFormat'), trigger: ['blur','change']}]">
                                        <el-input
                                          v-model="scope.row.transferWeight"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          clearable
                                          :disabled="type=='detail'"
                                        >
                                            <template #append>kg</template>
                                        </el-input>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('inventoryTransfer.label.receiveDetail')" width="80" v-if="type==='detail'">
                                <template #default="scope">
                                    <el-button
                                      type="primary"
                                      link
                                      @click="handleView(scope.row)"
                                    >
                                        {{$t('common.select')}}
                                    </el-button>
                                </template>
                            </el-table-column>
                            <el-table-column fixed="right" :label="$t('common.handle')" width="80" v-if="type!=='detail'">
                                <template #default="scope">
                                    <el-button
                                            type="danger"
                                            link
                                            @click="handleDelete(scope.$index)"
                                    >
                                        {{$t('common.delete')}}
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-form>
            </div>
            <div class="page-footer"  v-if="type!=='detail'">
                <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
<!--                <el-button type="primary" plain @click="handleSubmit(0)" :loading="submitLoading">{{ $t("inventoryTransfer.button.saveDraft") }}</el-button>-->
                <el-button type="primary" @click="handleSubmit(2)" :loading="submitLoading">{{ $t("inventoryTransfer.button.confirmTransfer") }}</el-button>
            </div>
            <AddProduct
                    ref="addProductRef"
                    v-model:visible="dialog.visible"
                    :title="dialog.title"
                    :totalStockQtyShow="true"
                    :availableStockQtyShow="true"
                    :outWarehouseAreaFromShow="true"
                    :hasTotalStockQty="true"
                    @onSubmit="onSubmit"
            />
        </div>
        <DetailList  ref="detailListRef"
                     v-model:dialog-visible="detailDialog.visible"
                     :title="detailDialog.title" />

    </div>
</template>

<script setup lang="ts">


    defineOptions({
        name: "AddInventoryTransfer",
        inheritAttrs: false,
    });

    import AddProduct from "../../../components/addProduct.vue";
    import DetailList from "./components/detailList.vue";


    import { parseDateTime,isEmpty } from "@/core/utils/index.js";
    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import InventoryTransferAPI, {InventoryTransferFrom} from "@/modules/wms/api/inventoryTransfer";
    import CommonAPI, { ProductAllPageQuery,ProductAllPageVO}  from "@/modules/wms/api/common";


    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const outWarehouseAreaList = ref([])
    const outWarehouseAreaEnableList = ref([])
    const inventoryTransferReasonList = ref([])
    const fromRef = ref()
    const submitLoading = ref(false)
    const queryFormRef = ref(ElForm);
    const loading = ref(false);
    const id = route.query.id;
    const type = route.query.type;
    const addProductRef = ref();
    const supplierList = ref([])
    const formUpdateRef = ref(null);
    const inventoryTransferTypeList = ref([
        {
            inventoryTransferTypeId: 1,
            inventoryTransferTypeName: t('inventoryTransfer.inventoryTransferTypeList.normalTransfer')
        },
    ])

    const statusList = ref([
        {
            statusId: 0,
            statusName: t("inventoryTransfer.statusList.draft"),
        },
        {
            statusId: 1,
            statusName: t("inventoryTransfer.statusList.movement"),
        },
        {
            statusId: 2,
            statusName: t("inventoryTransfer.statusList.finish"),
        },
    ]);
    const receiveOption = ref([
        {
            value: 0,
            label: t("inventoryTransfer.whetherOption.no"),
        },
        {
            value: 1,
            label: t("inventoryTransfer.whetherOption.yes"),
        },
    ]);
    const dialog = reactive({
        title: "",
        visible: false,
    });
    const detailDialog = reactive({
        title: "",
        visible: false,
    });
    const detailListRef=ref()
    const orderTypeList = ref([
        {
            key: 2,
            value: t('purchaseOrder.orderTypeList.marketDirectSupply')
        },
        {
            key: 1,
            value:t('purchaseOrder.orderTypeList.suppliersDirectSupply')
        }
    ])
    const form = reactive<InventoryTransferFrom>({
        transferType:1,
        inventoryTransferInfoDetailList:[]
    });
    const queryParams = reactive<ProductAllPageQuery>({
        page: 1,
        limit: 20,
    });
    const productTotal = ref(0);
    const productAllList = ref<ProductAllPageVO[]>();
    const rules = reactive({
        sourceWarehouseAreaCode: [{ required: true, message: t("inventoryTransfer.rules.sourceWarehouseAreaCode"), trigger: ["blur","change"] }],
        transferType: [{ required: true, message: t("inventoryTransfer.rules.transferType"), trigger: ["blur","change"] }],
        transferReasonId: [{ required: true, message: t("inventoryTransfer.rules.transferReasonId"), trigger: ["blur","change"] }],
    });

    function validateTransferQty(rule, value, callback) {
        let index = rule.field?.split('.')[1]
        let availableStockQty = form.inventoryTransferInfoDetailList[index].availableStockQty?form.inventoryTransferInfoDetailList[index].availableStockQty:0
        if(value && availableStockQty){
            if (value > parseFloat(availableStockQty)) {
                callback(new Error(t('inventoryTransfer.message.transferQtyTips')));
            } else {
                callback();
            }
        }
    }
    function validateTransferWeight(rule, value, callback) {
        let index = Number(rule.field?.split('.')[1])


        let beforeAvailableWeight = form.inventoryTransferInfoDetailList[index].beforeAvailableWeight?form.inventoryTransferInfoDetailList[index].beforeAvailableWeight:0

        if (value && value > parseFloat(beforeAvailableWeight)) {
             callback(new Error(t('inventoryTransfer.message.transferWeightTips')));
           } else {
              callback();
        }

    }

    /** 查询出库库区列表(库存大于0的库区) */
    function getOutWarehouseAreaNumberList() {
        return new Promise((resolve, reject) => {
            CommonAPI.getOutWarehouseAreaNumberList()
                .then((data) => {
                    outWarehouseAreaList.value = data;
                    if(outWarehouseAreaList.value && outWarehouseAreaList.value.length>0){
                        outWarehouseAreaList.value.map((item)=>{
                            item.warehouseArea =item.areaName + ' | ' + item.areaCode
                            return item
                        });

                    }
                    resolve();
                })
                .catch((error) => {
                    loading.value = false;
                    reject(error);
                })
        });
    }

    /** 查询出库库区列表(启用) */
  function getOutWarehouseAreaEnableList(params) {
        return new Promise((resolve, reject) => {
           CommonAPI.getOutWarehouseAreaList(params)
                .then((data) => {
                    outWarehouseAreaEnableList.value = data;
                    if(outWarehouseAreaEnableList.value && outWarehouseAreaEnableList.value.length>0){
                        outWarehouseAreaEnableList.value.map((item)=>{
                            item.warehouseArea = item.areaName+ '|' +item.areaCode
                            return item
                        });
                    }
                    resolve();
                })
               .catch((error) => {
                   loading.value = false;
                   reject(error);
               })
        });
    }

    /** 查询移库原因列表 */
    function getInventoryTransferReasonList() {
        return new Promise((resolve, reject) => {
            let params = {
                dictKey:'wms_inventory_transfer_reason'
            }
            InventoryTransferAPI.getInventoryTransferReasonList(params)
                .then((data) => {
                    inventoryTransferReasonList.value = data;
                    resolve();
                })
                .catch((error) => {
                    loading.value = false;
                    reject(error);
                })
            })
    }

    function changeSourceWarehouseAreaCode(){
        form.inventoryTransferInfoDetailList=[]
    }

    function onSubmit(data) {
        if(data){
            data.collection.forEach(item => {
                delete item.id;
            });
            data.collection=data.collection.map(item=>{
                return{
                    ...item,
                    beforeAvailableWeight:item.availableStockWeight,
                    beforeInventoryWeight:item.totalStockWeight,
                }
})
            form.inventoryTransferInfoDetailList=data.collection.concat(form.inventoryTransferInfoDetailList);
            console.log("===inventoryTransferInfoDetailList==="+form.inventoryTransferInfoDetailList);
        }
    }

    /** 假删除*/
    function handleDelete(index?: number) {
        let planPurchaseCount =form.inventoryTransferInfoDetailList[index].planPurchaseCount?parseFloat(form.inventoryTransferInfoDetailList[index].planPurchaseCount):0
        let planPurchaseAmount=form.inventoryTransferInfoDetailList[index].planPurchaseAmount?parseFloat(form.inventoryTransferInfoDetailList[index].planPurchaseAmount):0
        form.totalPurchaseCount= (parseFloat(form.totalPurchaseCount) - planPurchaseCount).toFixed(2)
        form.totalPurchaseAmount= (parseFloat(form.totalPurchaseAmount) - planPurchaseAmount).toFixed(2)
        form.inventoryTransferInfoDetailList.splice(index, 1);
        ElMessage.success(t('inventoryTransfer.message.deleteSucess'));
    }

    async function handleClose() {
        await tagsViewStore.delView(route);
        router.go(-1);
    };

    /** 添加/编辑库存转移 */
    function handleSubmit(val){

        if(form.inventoryTransferInfoDetailList && form.inventoryTransferInfoDetailList.length==0){
            return  ElMessage.error(t('inventoryTransfer.message.addOrEditInventoryTransferTips'));
        }
        fromRef.value.validate((valid) => {
            if (!valid) return;
            submitLoading.value=true
            let sourceWarehouseArea =  outWarehouseAreaList.value.filter(item =>form.sourceWarehouseAreaCode==item.areaCode)
            let transferReason =  inventoryTransferReasonList.value.filter(item =>form.transferReasonId==item.dictValue)
            let inventoryTransferInfoDetailList = []
            let inventoryTransferInfoDetailListReapet = []
            if(form.inventoryTransferInfoDetailList && form.inventoryTransferInfoDetailList.length>0){
                form.inventoryTransferInfoDetailList.forEach((item)=>{
                    let targetWarehouseArea = outWarehouseAreaEnableList.value.filter(out =>item.targetWarehouseAreaCode==out.areaCode)
                    let obj= {
                        id:item.id?item.id:'',
                        productCode:item.productCode,
                        targetWarehouseAreaCode:item.targetWarehouseAreaCode,
                        targetWarehouseAreaName:targetWarehouseArea[0].areaName,
                        transferQty:item.transferQty,
                        transferWeight:item.transferWeight,
                        // availableStockQty:item.availableStockQty,
                    }
                    let obj1 = {...obj}
                    inventoryTransferInfoDetailList.push(obj)
                    obj1.availableStockQty=item.availableStockQty
                    inventoryTransferInfoDetailListReapet.push(obj1)
                })
                /** 判重 */
                let mapArr = inventoryTransferInfoDetailListReapet.map((item: any, index: any) => {
                    item.key = JSON.stringify({ productCode: item.productCode, targetWarehouseAreaCode: item.targetWarehouseAreaCode })
                    return  item.key;
                });
                let setArr = new Set(mapArr);
                if(setArr.size < mapArr.length){
                    submitLoading.value = false;
                    return  ElMessage.error(t('inventoryTransfer.message.inventoryTransferInfoDetailListReapetTips'));
                }

                const productMap = new Map();
                inventoryTransferInfoDetailListReapet.forEach(product => {
                    if (!productMap.has(product.productCode)) {
                        productMap.set(product.productCode, { productCode: product.productCode, transferQtyTotal: 0 });
                    }
                    const currentProduct = productMap.get(product.productCode);
                    currentProduct.transferQtyTotal += parseFloat(product.transferQty);
                    currentProduct.availableStockQty = parseFloat(product.availableStockQty);
                    productMap.set(product.productCode, currentProduct);
                });
                const groupedProducts = Array.from(productMap.values()); // 将Map转换为数组以便于操作或显示
                let flag =  groupedProducts.some(item =>item.transferQtyTotal > item.availableStockQty)
                if(flag){
                    submitLoading.value = false;
                    return  ElMessage.error(t('inventoryTransfer.message.transferQtyTotalTips'))
                }
            }
            let params = {
                transferStatus:val,
                sourceWarehouseAreaCode:form.sourceWarehouseAreaCode,
                sourceWarehouseAreaName:sourceWarehouseArea[0].areaName,
                transferType:form.transferType,
                transferReasonId:form.transferReasonId,
                transferReasonName:transferReason[0].dictName,
                remark:form.remark,
                inventoryTransferInfoDetailDTOList:inventoryTransferInfoDetailList
            }
            if(type!=='add'){
               params.id=form.id
            }


            if(val==1){
                ElMessageBox.confirm(t('inventoryTransfer.message.confirmTransferTips'), t('common.tipTitle'), {
                    confirmButtonText: t('common.confirm'),
                    cancelButtonText: t('common.cancel'),
                    type: "warning",
                }).then(
                    () => {
                        InventoryTransferAPI.addAndEditInventoryTransfer(params)
                            .then((data) => {
                                ElMessage.success(val==0?t('inventoryTransfer.message.saveDraftSucess'):t('inventoryTransfer.message.confirmTransferSucess'));
                                handleClose()
                            })
                            .finally(() => {
                                submitLoading.value = false;
                            });
                    },
                    () => {
                        submitLoading.value = false;
                        ElMessage.info(t('inventoryTransfer.message.confirmTransferConcel'));
                    }
                );
            }else{
                InventoryTransferAPI.addAndEditInventoryTransfer(params)
                    .then((data) => {
                        ElMessage.success(val==0?t('inventoryTransfer.message.saveDraftSucess'):t('inventoryTransfer.message.confirmTransferSucess'));
                        handleClose()
                    })
                    .finally(() => {
                        submitLoading.value = false;
                    });
            }
        })
    }

    /** 查询库存转移详情 */
    function queryInventoryTransferDetail(){
        loading.value = true;
        let params = {
            id:id
        }
        InventoryTransferAPI.queryInventoryTransferDetail(params)
            .then((data) => {
                Object.assign(form,data)
                let inventoryTransferInfoDetailDTOList = []
                if(data.inventoryTransferInfoDetailList && data.inventoryTransferInfoDetailList.length>0){
                    data.inventoryTransferInfoDetailList.forEach((item)=>{
                        console.log("item====")
                        console.log(item)
                        let obj= {
                            id:item.id,
                            productCode:item.productCode,
                            productName:item.productName,
                            productSpec:item.productSpec,
                            productUnitName:item.productUnit,
                         /*   targetWarehouseAreaCode:targetWarehouseArea && targetWarehouseArea.length>0?item.targetWarehouseAreaCode:'',
                            targetWarehouseAreaName:targetWarehouseArea && targetWarehouseArea.length>0?targetWarehouseArea[0].areaName:'',*/
                            transferQty:Number(item.transferQty),
                            beforeInventoryWeight:item.beforeInventoryWeight,
                            beforeAvailableWeight:item.beforeAvailableWeight,
                            transferWeight:item.transferWeight,
                            totalStockQty:type=='edit'?item.inventoryQty:Number(item.beforeInventoryQty),
                            availableStockQty:type=='edit'?item.availableQty:Number(item.beforeAvailableQty)
                        }
                        if(type=='detail'){
                            obj.targetWarehouseAreaCode=item.targetWarehouseAreaName + ' | ' + item.targetWarehouseAreaCode
                            obj.targetWarehouseAreaName=item.targetWarehouseAreaName
                        }else{
                            let targetWarehouseArea = outWarehouseAreaEnableList.value.filter(out =>item.targetWarehouseAreaCode==out.areaCode)
                            obj.targetWarehouseAreaCode=targetWarehouseArea && targetWarehouseArea.length>0?item.targetWarehouseAreaCode:''
                            obj.targetWarehouseAreaName=targetWarehouseArea && targetWarehouseArea.length>0?targetWarehouseArea[0].areaName:''
                        }
                        inventoryTransferInfoDetailDTOList.push(obj)
                        form.inventoryTransferInfoDetailList=inventoryTransferInfoDetailDTOList
                    })
                }
                if(type=='detail'){
                    const inventoryTransferType = inventoryTransferTypeList.value.filter(out =>form.transferType==out.inventoryTransferTypeId)
                    form.transferType=inventoryTransferType[0].inventoryTransferTypeName
                }
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 添加商品 */
   function addProduct() {
       if(form.sourceWarehouseAreaCode==undefined){
           fromRef.value.validate((valid) => {
           if (!valid) return;
               addProductOpen()
           })
       }else{
           addProductOpen()
       }
    }


    /** 添加商品 */
    function addProductOpen() {
        dialog.title = t('purchaseOrder.title.addProduct');
        let params = {
            warehouseAreaCode: form.sourceWarehouseAreaCode,
        }
        addProductRef.value.setFormData({queryParams: params});
        addProductRef.value.getOutWarehouseAreaList();
        addProductRef.value.queryManagerCategoryList();
        dialog.visible = true;
        getOutWarehouseAreaEnableList({status:1});
    }

    /**
     * 查看明细
     * @param item
     */
    function handleView(item: any) {
        detailDialog.visible = true;
        detailDialog.title = `${t('inventoryTransfer.label.ysnCodeTitle')} - ${item.productCode} | ${item.productName}`;
        let params = {
            detailId: item.id,
        };
        detailListRef.value.handleQuery(params);

    }

    /**
     * 移库状态
     * @param val
     */
    function filterStatus(val:any) {
        if (!isEmpty(val)) {
            return statusList.value.find((item:any) => item.statusId === val)?.statusName || '';
        }
    }
    /**
     * 领单状态
     * @param val
     */
    function filterReceiveStatus(val:any) {
        if (!isEmpty(val)) {
            return receiveOption.value.find((item:any)  => item.value === val)?.label || '';
        }
    }

   onMounted(async () => {
       if(type!=='add'){
           loading.value = true;
       }
        if(type!=='detail'){
            await getOutWarehouseAreaNumberList();
            await getInventoryTransferReasonList();
            await getOutWarehouseAreaEnableList({status:1});
        }
        if(type!=='add'){
           queryInventoryTransferDetail();
        }
    });
</script>
<style scoped lang="scss">
    .addInventoryTransfer {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .button-add{
                position: absolute;
                right: 10px;
                top: 30px;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                color: var(--el-color-primary)
            }
            .table-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 50px;
                background: #F4F6FA;
                box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                padding: 15px 12px;
            }
        }
    }
</style>
