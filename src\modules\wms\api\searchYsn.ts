import request from "@/core/utils/request";
const BASE_URL = '/supply-wms/productStockYsn'
class SearchYsnApi {
  /** 分页查询 */
  static queryPage(queryParams ?: queryPageDto){
    return request<any, PageResult<queryPageResponse[]>>({
      url: `${BASE_URL}/queryPageList`,
      method: 'post',
      data: queryParams,
    })
  }
  /** YSN状态下拉 */
  static getYsnStatusEnumList(){
    return request({
      url: `${BASE_URL}/getYsnStatusEnumList`,
      method: 'get'
    })
  }
}
export default SearchYsnApi

/**
 * 分页查询请求实体
 */
export interface queryPageDto extends PageQuery{
  /** 商品 */
  productSearch ?: string;
  /** YSN状态 */
  ysnStatusList ?: number[];
  /** 库区 */
  warehouseAreaCodeList?: string[];
  /** YSN码 */
  ysnCode ?: string;
}
/**
 * 分页查询响应实体
 */
export interface queryPageResponse {
  /** YSN码 */
  /** 商品编码 */
  /** 商品名称 */
  /** 实际重量 */
  /** 原始重量 */
  /** YSN状态 */
  /** 所在库区 */
}
