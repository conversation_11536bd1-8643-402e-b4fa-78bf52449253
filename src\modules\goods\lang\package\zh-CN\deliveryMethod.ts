export default {
  deliveryMethod: {
    // 页面标题
    title: "配送方式管理",

    // 左侧分类管理
    category: {
      title: "配送方式分类",
      add: "新增",
      edit: "编辑",
      delete: "删除",
      deleteConfirm: "确定要删除选中的分类吗？",
      deleteSuccess: "删除成功",
      addCategory: "新增分类",
      editCategory: "编辑分类",
      categoryName: "分类名称",
      categoryNamePlaceholder: "请输入分类名称",
      categoryNameRequired: "请输入分类名称",
      sort: "排序",
      sortPlaceholder: "请输入排序数值",
      allCategories: "全部",
      saveSuccess: "保存成功",
      operation: "操作"
    },

    // 右侧配送方式管理
    method: {
      title: "配送方式",
      search: "搜索",
      reset: "重置",
      add: "新增",
      enable: "启用",
      disable: "禁用",
      batchEnable: "批量启用",
      batchDisable: "批量禁用",
      enableSuccess: "启用成功",
      disableSuccess: "禁用成功",
      batchEnableSuccess: "批量启用成功",
      batchDisableSuccess: "批量禁用成功",
      edit: "编辑",
      delete: "删除",
      deleteConfirm: "确定要删除这个配送方式吗？",
      deleteSuccess: "删除成功",

      // 表格列
      methodName: "配送方式",
      category: "分类",
      status: "状态",
      updateTime: "更新时间",
      operation: "操作",

      // 状态
      enabled: "启用",
      disabled: "禁用",

      // 表单
      addMethod: "新增配送方式",
      editMethod: "编辑配送方式",
      methodNamePlaceholder: "请输入配送方式名称",
      methodNameRequired: "请输入配送方式名称",
      categoryRequired: "请选择分类",
      categoryPlaceholder: "请选择分类",
      statusRequired: "请选择状态",

      // 搜索表单
      searchMethodName: "配送方式",
      searchMethodNamePlaceholder: "请输入配送方式名称",
      searchCategory: "分类",
      searchCategoryPlaceholder: "请选择分类",
      searchStatus: "状态",
      searchStatusPlaceholder: "请选择状态",

      saveSuccess: "保存成功",
      selectTip: "请选择要操作的数据",
      
    },

    // 通用
    common: {
      confirm: "确定",
      cancel: "取消",
      save: "保存",
      close: "关闭",
      loading: "加载中...",
      noData: "暂无数据",
      total: "共 {total} 条",
      page: "页",
      goto: "前往",
      itemsPerPage: "条/页"
    },
    message: {
      disableBatchConfirmDeliveryMethod: "确定要禁用选中的配送方式吗"
    }
  }

}; 