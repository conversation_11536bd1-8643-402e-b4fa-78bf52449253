export default {
  warehouseAreaType: {
    // 页面标题
    title: {
      list: "库区存储类型",
      add: "新增库区存储类型",
      edit: "编辑库区存储类型"
    },
    
    // 表单标签
    label: {
      typeCode: "类型编码",
      typeName: "类型名称", 
      sort: "排序",
      enableStatus: "状态",
      updateTime: "更新时间",
      operation: "操作"
    },
    
    // 占位符
    placeholder: {
      typeNamePlaceholder: "请输入类型名称",
      typeCodePlaceholder: "请输入类型编码",
      sortPlaceholder: "请输入排序数值"
    },
    
    // 按钮文本
    button: {
      search: "搜索",
      reset: "重置",
      add: "新增",
      edit: "编辑",
      delete: "删除",
      save: "保存",
      cancel: "取消",
      enable: "启用",
      disable: "禁用"
    },
    
    // 状态文本
    status: {
      enabled: "启用",
      disabled: "禁用"
    },
    
    // 表单验证规则
    rules: {
      typeCode: "请输入类型编码",
      typeName: "请输入类型名称", 
      sort: "请输入排序",
      sortNumber: "排序必须为数字"
    },
    
    // 消息提示
    message: {
      loadFailed: "数据加载失败",
      operationFailed: "操作失败",
      addSuccess: "新增成功",
      editSuccess: "编辑成功",
      deleteSuccess: "删除成功",
      deleteConfirm: "确定要删除这个库区存储类型吗？",
      enableSuccess: "启用成功",
      disableSuccess: "禁用成功",
      enableBatchConfirm: "确定要启用选中的库区存储类型吗？",
      disableBatchConfirm: "确定要禁用选中的库区存储类型吗？",
      selectTip: "请选择要操作的数据"
    }
  }
}; 