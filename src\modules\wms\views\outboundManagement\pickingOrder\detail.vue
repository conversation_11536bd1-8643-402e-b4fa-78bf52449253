<script setup lang="ts">
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import ProductMgAPI from "@/modules/wms/api/pickingOrder";
import { parseTime } from "@/core/utils/index";
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
import { useI18n } from "vue-i18n"; // 导入国际化
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { useForm } from "./composables";
const {statusClass} = useForm();
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const isEdit = ref(route.query.type === "edit");
const formRef = ref<FormInstance>();
import {PickingListVO, PickingListProductVO, PickingListProductDetailVO} from "@/modules/wms/types/pickingOrder";

export interface MapObject {
  key?: { [key: string]: any };
  [property: string]: any;
}

// YSN数据类型定义
interface YsnItem {
  /** YSN码 */
  ysnCode?: string;
  /** 重量(kg) */
  weight?: number;
  /** 扫描时间 */
  scanTime?: string;
  [property: string]: any;
}

// YSN分页查询参数
interface YsnPageQuery extends PageQuery {
  /** 拣货单商品拣货明细表id */
  pickingListProductDetailId?: number;
}

const pickingListCode = ref(route.query.pickingListCode);

const handleSave = () => {
  if (isEdit.value) {
    const dataCopy = JSON.parse(JSON.stringify(formData.value));
    dataCopy.imagesUrls = JSON.stringify(dataCopy.imagesUrls);
    dataCopy.mainImageUrl = JSON.stringify(dataCopy.mainImageUrl);
    ProductMgAPI.updateProduct(dataCopy).then(() => {
      ElMessage.success(t('WMSPickingOrder.message.productEditOk'));
      handleClose();
    });
  } else {
    const dataCopy = JSON.parse(JSON.stringify(formData.value));
    dataCopy.imagesUrls = JSON.stringify(dataCopy.imagesUrls);
    dataCopy.mainImageUrl = JSON.stringify(dataCopy.mainImageUrl);
    ProductMgAPI.saveProduct(dataCopy).then(() => {
      ElMessage.success(t('WMSPickingOrder.message.productCreateOk'));
      handleClose();
    });
  }
};

const tagsViewStore = useTagsViewStore();
const handleClose = async () => {
  await tagsViewStore.delView(route);
  router.push("/wms/pickingOrder");
};

const standardOptions = [
  { value: 1, label: "是" },
  { value: 0, label: "否" },
];

const detail = ref<PickingListVO>({});
const productList = ref<PickingListProductVO[]>([]);
const loading = ref(false);

// YSN相关状态
const ysnDialog = reactive({
  visible: false,
  productCode: '',
  productName: '',
});
const ysnList = ref<YsnItem[]>([]);
const ysnLoading = ref(false);
const ysnTotal = ref(0);
const ysnQueryParams = reactive<YsnPageQuery>({
  page: 1,
  limit: 10,
  pickingListProductDetailId: 0,
});

const fetchDetail = (data: Record<string, any> = {}) => {
  loading.value = true;
  ProductMgAPI.getProductDetailById(data)
    .then((res) => {
      detail.value = res;
      productList.value = res.pickingListProductVOS || [];
    })
    .catch((err) => {
      ElMessage.error(t('WMSPickingOrder.message.getDetailFail'));
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleViewYSN = (row: PickingListProductVO) => {
  // 获取第一个拣货明细的ID作为查询参数
  const firstDetail = row.pickingListProductDetailVOList?.[0];
  if (!firstDetail?.id) {
    ElMessage.warning('未找到拣货明细信息');
    return;
  }

  ysnDialog.visible = true;
  ysnDialog.productCode = row.productCode || '';
  ysnDialog.productName = row.productName || '';
  
  ysnQueryParams.pickingListProductDetailId = firstDetail.id;
  ysnQueryParams.page = 1;
  
  queryYsnList();
};

// 查询YSN列表
const queryYsnList = () => {
  if (!ysnQueryParams.pickingListProductDetailId) {
    ElMessage.error('缺少拣货明细ID');
    return;
  }
  
  ysnLoading.value = true;
  const queryParams = {
    ...ysnQueryParams,
    pickingListProductDetailId: ysnQueryParams.pickingListProductDetailId
  };
  
  ProductMgAPI.queryPageByPickingListProductDetailId(queryParams)
    .then((res: any) => {
      ysnList.value = res.records || res.list || [];
      ysnTotal.value = parseInt(res.total) || 0;
    })
    .catch((error) => {
      console.error('查询YSN列表失败', error);
      ElMessage.error('查询YSN列表失败');
    })
    .finally(() => {
      ysnLoading.value = false;
    });
};

// 处理YSN分页变化
const handleYsnPageChange = (val: number) => {
  ysnQueryParams.page = val;
  queryYsnList();
};

const handleYsnSizeChange = (val: number) => {
  ysnQueryParams.limit = val;
  ysnQueryParams.page = 1;
  queryYsnList();
};

onMounted(() => {
  fetchDetail({ id: route.query.id });
});
</script>

<template>
  <div class="product-add" v-loading="loading">
    <div class="card-header mb-24px">
      <img
        @click="handleClose"
        src="@/core/assets/images/arrow-left.png"
        alt=""
        class="back-btn"
      />
      <span @click="handleClose" class="code">
       {{$t('WMSPickingOrder.label.pickingListCode')}}{{ pickingListCode }}
      </span>
    </div>
    <div class="card-title mb-24px">{{$t('WMSPickingOrder.label.orderInfo')}}</div>
    <div class="card-content">
      <div class="order-info-grid">
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.pickingListCode')}}</div>
          <div class="info-value">{{ detail.pickingListCode || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.pickingType')}}</div>
          <div class="info-value">{{ detail.pickingType === 1 ? "按单拣货" : "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.createUserName')}}</div>
          <div class="info-value">{{ detail.createUserName || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.createTime')}}</div>
          <div class="info-value">
            {{ parseTime(detail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') || "--" }}
          </div>
        </div>

        <div class="info-item">
          <div class="info-label">计划发货时间</div>
          <div class="info-value">
            {{ parseTime(detail.planDeliveryTime, '{y}-{m}-{d} {h}:{i}:{s}') || "--" }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.plannedReceivedTime')}}</div>
          <div class="info-value">
            {{ parseTime(detail.plannedReceivedTime, '{y}-{m}-{d} {h}:{i}:{s}') || "--" }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.deliveryNoticeCode')}}</div>
          <div class="info-value">{{ detail.deliveryNoticeCode || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.pickOrderCode')}}</div>
          <div class="info-value">{{ detail.billOfLadingCode || "--" }}</div>
        </div>
        <template v-if="detail.status !== 4">
          <div class="info-item">
            <div class="info-label">{{$t('WMSPickingOrder.label.pickPerson')}}</div>
            <div class="info-value">{{ detail.actualPicker || "--" }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">{{$t('WMSPickingOrder.label.pickTime')}}</div>
            <div class="info-value">
              {{ parseTime(detail.actualPickingTime, '{y}-{m}-{d} {h}:{i}:{s}') || "--" }}
            </div>
          </div>
        </template>
         <template v-if="detail.status === 4">
           <div class="info-item">
             <div class="info-label">{{$t('WMSPickingOrder.label.cancelPerson')}}</div>
             <div class="info-value">
               {{ (detail.updateUserName) || "--" }}
             </div>
           </div>
           <div class="info-item">
             <div class="info-label">{{$t('WMSPickingOrder.label.cancelTime')}}</div>
             <div class="info-value">
               {{ parseTime(detail.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') || "--" }}
             </div>
           </div>
           <div class="info-item">
             <div class="info-label">{{$t('WMSPickingOrder.label.status')}}</div>
             <div class="info-value">
               {{ (detail.statusStr) || "--" }}
             </div>
           </div>
           <div class="info-item">
             <div class="info-label">{{$t('WMSPickingOrder.label.cancelReason')}}</div>
             <div class="info-value">
               {{ (detail.cancelReason) || "--" }}
             </div>
           </div>

           
         </template>
         <div class="info-item">
          <div class="info-label">是否领单</div>
          <div class="info-value">{{ detail.receivingStatus === 1 ? "是" : "否" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">领单人</div>
          <div class="info-value">{{ detail.receivingUserName || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">领单时间</div>
          <div class="info-value">{{ parseTime(detail.receivingTime, '{y}-{m}-{d} {h}:{i}:{s}') || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">状态</div>

          <!-- 状态:1 初始 2 拣货失败 3 拣货完成 4 已撤销 -->
          <span class="custom-status status" :class="statusClass(detail.status)">{{ detail.statusStr}}</span>
        
        </div>
      </div>
    </div>
    <div class="card-title mb-24px">{{$t('WMSPickingOrder.label.productDetail')}}</div>
    <el-table
      :data="productList"
      border
      style="width: 100%"
      max-height="600"
      v-loading="loading"
    >
      <el-table-column type="index" :label="$t('WMSPickingOrder.label.productInfo')" width="60" align="center" fixed="left"/>
      <el-table-column :label="$t('WMSPickingOrder.label.productInfo')" fixed="left">
        <template #default="scope">
          <div class="product-info">
            <div class="product-name">{{ scope.row.productName }}</div>
            <div class="product-code">
              <span class="label">{{$t('WMSPickingOrder.label.productCode')}}:</span>
              <span class="value">{{ scope.row.productCode }}</span>
            </div>
            <div class="product-spec">
              <span class="label">{{$t('WMSPickingOrder.label.productSpecs')}}:</span>
              <sapn class="value">{{ scope.row.productSpecs }}</sapn>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- 计划拣货总量 -->
      <el-table-column
        prop="totalPlanedPick"
        :label="$t('WMSPickingOrder.label.totalPlanedPick')"
        width="120"
        align="center"
      />

       <!-- 计划拣货总重量 -->
       <el-table-column
        prop="totalPlanedPickWeight"
        :label="'计划拣货总重量(kg)'"
        width="150"
        align="center"
      />
      <el-table-column
        prop="totalActualPick"
        :label="$t('WMSPickingOrder.label.actualPickCount')"
        width="120"
        align="center"
      >
        <template #default="scope">
          <span
          >
            {{ scope.row.totalActualPick || '--' }}{{ scope.row.productUnitName }}
          </span>
          </template>
      </el-table-column>
         <!-- 实际拣货总重量 -->
         <el-table-column
        prop="totalActualPickWeight"
        :label="'实际拣货总重量(kg)'"
        width="150"
        align="center"
      />
      <!-- 拣货库区 -->
      <el-table-column :label="$t('WMSPickingOrder.label.pickArea')" align="center">
        <template #default="scope">
          <div
            v-for="(location, index) in scope.row.pickingListProductDetailVOList"
            :key="index"
            class="location-item"
          >
            <div class="location-code">{{ location.warehouseAreaName  || '--'}}</div>|
            <div class="location-quantity">
              <span>{{ location.warehouseAreaCode  || '--'}}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column  :label="'计划拣货数量'" align="center">
        <template #default="scope">
          <div
            v-for="(location, index) in scope.row.pickingListProductDetailVOList"
            :key="index"
            class="location-item"
          >
            <div class="location-quantity">{{ location.warehouseAreaPlanedPick  || '--'}}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column  :label="'计划拣货重量(kg)'" align="center">
        <template #default="scope">
          <div
            v-for="(location, index) in scope.row.pickingListProductDetailVOList"
            :key="index"
            class="location-item"
          >
            <div class="location-quantity">{{ location.warehouseAreaPlanedPickWeight  || '--'}}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="'本次拣货量'" align="center">
        <template #default="scope">
          <div
            v-for="(location, index) in scope.row.pickingListProductDetailVOList"
            :key="index"
            class="location-item"
          >
            <div class="location-quantity">
              {{ location.actualPickThisTime || '--'}}
            </div>
          </div>
        </template>
      </el-table-column>


      <el-table-column :label="'本次拣货重量'" align="center">
        <template #default="scope">
          <div
            v-for="(location, index) in scope.row.pickingListProductDetailVOList"
            :key="index"
            class="location-item"
          >
            <div class="location-quantity">
              {{ location.warehouseAreaActualPickWeight || '--'}}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="'拣货商品YSN'" align="center">
        <template #default="scope">
          <div
            v-for="(location, index) in scope.row.pickingListProductDetailVOList"
            :key="index"
            class="location-item"
          >
            <el-button type="text" v-hasPerm="['wms:pickingorder:ysn']" @click="handleViewYSN(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="status" :label="$t('WMSPickingOrder.label.status')" width="100" align="center">
        <template #default="scope">
          <span class="custom-status status" :class="statusClass(scope.row.status)">{{ scope.row.statusStr}}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="card-footer">
      <el-button @click="handleClose">{{$t('WMSPickingOrder.label.cancel')}}</el-button>
    </div>

    <!-- YSN列表弹窗 -->
    <el-dialog
      v-model="ysnDialog.visible"
      :title="`商品YSN码-${ysnDialog.productCode} | ${ysnDialog.productName}`"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-table 
        :data="ysnList" 
        v-loading="ysnLoading"
        border
        stripe
        max-height="400"
      >
        <template #empty>
          <el-empty v-if="!ysnList.length && !ysnLoading" description="暂无YSN数据" />
        </template>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="ysnCode" label="YSN码" min-width="240" show-overflow-tooltip />
        <el-table-column prop="weight" label="重量(kg)" width="120" align="right">
          <template #default="{ row }">
            {{ row.weight || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="scanTime" label="扫描时间" width="160" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.scanTime ? parseTime(row.scanTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
          </template>
        </el-table-column>
      </el-table>
      
      <div style="margin-top: 16px; text-align: center;" v-if="ysnTotal > 0">
        <el-pagination
          v-model:current-page="ysnQueryParams.page"
          v-model:page-size="ysnQueryParams.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="ysnTotal"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @current-change="handleYsnPageChange"
          @size-change="handleYsnSizeChange"
        />
      </div>
      
      <template #footer>
        <el-button @click="ysnDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.product-add {
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;

  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;

    .code {
      font-family: "PingFangSC", "PingFang SC";
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .status {
      font-family: "PingFangSC", "PingFang SC";
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }

  .card-content {
    margin-bottom: 24px;

    .order-info-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      padding: 0 20px;
      border-radius: 4px;

      .info-item {
        display: flex;
        .info-label {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #90979e;
          line-height: 32px;
          text-align: center;
          font-style: normal;
          margin-bottom: 8px;
          margin-right: 8px;
        }

        .info-value {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #151719;
          line-height: 32px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }

  .upload-container {
    display: flex;
    flex-direction: column;
  }

  .tip {
    font-family: "PingFangSC", "PingFang SC";
    font-weight: 400;
    font-size: 14px;
    color: #90979e;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }

  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
    text-align: right;
    border-top: 1px solid #ebeef5;
  }

  .conversion-relation {
    display: flex;
    align-items: center;
    gap: 8px;

    .equal-sign {
      margin: 0 4px;
    }
  }

  .dimensions-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .dimension-input {
      width: 80px;
    }

    .volume-input {
      width: 100px;
    }
  }
}

.product-info {
  padding: 8px 0;
  .product-name {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #151719;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    margin-bottom: 4px;
  }
  .product-code,
  .product-spec {
    margin-bottom: 4px;
    .label{
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #90979E;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }
    .value{
      color: #52585F;
    }
  }
}

.location-item {
  display: flex;
  justify-content: center;
  margin-bottom: 4px;
  padding: 4px 0;
  border-bottom: 1px dashed #f0f0f0;
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
  .location-code {
    color: #151719;
    font-size: 13px;
  }
  .location-quantity {
    color: #151719;
    font-size: 13px;
    font-weight: 500;
  }
}
.card-footer{
  padding: 16px 30px;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-top: 1px solid #ebeef5;
  text-align: right;
}
</style>
<!-- End of file -->
