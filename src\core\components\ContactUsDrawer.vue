<template>
  <el-drawer
    v-model="drawerVisible"
    :title="$t('contactForm.title')"
    size="500px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="contact-drawer"
    destroy-on-close
    @opened="handleDrawerOpened"
  >
    <div class="drawer-content">
      <ContactForm
        ref="contactFormRef"
        :submitting="submitting"
        :product-type="productType"
        @submit="handleSubmit"
        @cancel="handleClose"
      />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import ContactForm from './ContactForm.vue'
import ContactAPI from '@/core/api/contact'
import type { ContactFormData, ContactSubmissionRequest } from '@/core/api/contact'
import { ElMessage } from 'element-plus'
const { t } = useI18n();

defineOptions({
  name: 'ContactUsDrawer'
})

// Props
interface Props {
  visible: boolean
  productType?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  productType: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'submit-success': []
}>()

// Refs
const contactFormRef = ref<InstanceType<typeof ContactForm>>()
const submitting = ref(false)

// Computed
const drawerVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value)
  }
})

// Methods
const handleClose = () => {
  if (submitting.value) {
    return // Prevent closing while submitting
  }
  
  drawerVisible.value = false
  // Reset form when closing
  nextTick(() => {
    contactFormRef.value?.resetForm()
  })
}

const handleDrawerOpened = () => {
  // Focus first input field when drawer opens for accessibility
  nextTick(() => {
    const firstInput = document.querySelector('.contact-drawer .el-input__inner') as HTMLInputElement
    if (firstInput) {
      firstInput.focus()
    }
  })
}

const handleSubmit = async (formData: ContactFormData) => {
  try {
    submitting.value = true
    
    // Prepare submission data
    const submissionData: ContactSubmissionRequest = {
      ...formData,
      appCode: props.productType === 'customerSteward' ? '1' : props.productType === 'wms' ? '2' : '3',
      enterpriseName: props.productType === 'customerSteward' ? '客户管家ERP' : props.productType === 'wms' ? '伽蓝WMS' : '圆通集运平台',
    }
    
    // Submit to API
    const response = await ContactAPI.submitContactForm(submissionData)
    
    // Show success message
    ElMessage.success(t('contactForm.messages.submitSuccess'))
    
    // Emit success event
    emit('submit-success')
    
    // Close drawer after short delay
    setTimeout(() => {
      handleClose()
    }, 1500)
    
  } catch (error) {
    console.error('Contact form submission failed:', error)
    ElMessage.error(t('contactForm.messages.submitError'))
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss">
.contact-drawer {
  .el-drawer__header {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 0;
    
    .el-drawer__title {
      font-weight: 500;
      font-size: 18px;
    }
  }

  .el-drawer__body {
    padding: 0;
  }

  .drawer-content {
    // padding: 20px;
    height: 100%;
    overflow-y: auto;
  }

  // Responsive design
  @media (max-width: 768px) {
    .el-drawer {
      width: 90% !important;
    }
    
    .drawer-content {
      padding: 15px;
    }
  }

  @media (max-width: 480px) {
    .el-drawer {
      width: 100% !important;
    }
    
    .drawer-content {
      padding: 10px;
    }
  }
}
</style>