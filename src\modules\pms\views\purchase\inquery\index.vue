<template>
  <div class="app-container purchaseInquiry">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('purchaseInquiry.label.orderNo')">
              <el-input
                v-model="queryParams.inquiryCode"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('purchaseInquiry.label.status')">
              <el-select
                v-model="queryParams.inquiryStatus"
                clearable
                :placeholder="$t('common.placeholder.selectTips')"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('purchaseInquiry.label.deliveryTime')">
              <el-date-picker
                v-model="queryParams.deliveryTime"
                type="daterange"
                :range-separator="$t('purchaseInquiry.label.to')"
                :start-placeholder="$t('purchaseInquiry.label.startTime')"
                :end-placeholder="$t('purchaseInquiry.label.endTime')"
                value-format="YYYY-MM-DD"
                @change="handleDeliveryTimeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('purchaseInquiry.label.submitTime')">
              <el-date-picker
                v-model="queryParams.submitTime"
                type="daterange"
                :range-separator="$t('purchaseInquiry.label.to')"
                :start-placeholder="$t('purchaseInquiry.label.startTime')"
                :end-placeholder="$t('purchaseInquiry.label.endTime')"
                value-format="YYYY-MM-DD"
                @change="handleSubmitTimeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('purchaseInquiry.label.supplier')">
              <el-select
                v-model="queryParams.supplierId"
                clearable
                filterable
                :placeholder="$t('common.placeholder.selectTips')"
              >
                <el-option
                  v-for="item in supplierList"
                  :key="item.supplierId"
                  :label="item.supplierName"
                  :value="item.supplierId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <section class="width-100% text-right">
                <el-button
                  type="primary"
                  @click="handleQuery"
                  v-hasPerm="['pms:inquery:list']"
                >
                  {{ $t("common.search") }}
                </el-button>
                <el-button
                  @click="handleResetQuery"
                  v-hasPerm="['pms:inquery:reset']"
                >
                  {{ $t("common.reset") }}
                </el-button>
              </section>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          type="primary"
          @click="gotoCreateInquery"
          v-hasPerm="['pms:inquery:add']"
        >
          新建询价单
        </el-button>
      </template>
      <el-table v-loading="loading" :data="tableData" style="width: 100%">
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          :label="$t('purchaseInquiry.table.orderNo')"
          prop="inquiryCode"
          min-width="150"
          fixed="left"
        />
        <el-table-column
          :label="$t('purchaseInquiry.table.deliveryTime')"
          prop="planDeliveryCycle"
          min-width="180"
        >
          <template #default="{ row }">
            {{ parseTimeHandle(row.startDeliveryDate) }}-{{
              parseTimeHandle(row.endDeliveryDate)
            }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseInquiry.table.warehouseName')"
          prop="warehouseName"
          min-width="100"
        />
        <el-table-column
          :label="$t('purchaseInquiry.table.supplier')"
          prop="suppliers"
          min-width="180"
          show-overflow-tooltip
          :class-name="'text-nowrap'"
        />
        <el-table-column
          :label="$t('purchaseInquiry.table.status')"
          prop="inquiryStatus"
          min-width="100"
        >
          <!--          <template #default="{ row }">
            <el-tag :type="getStatusType(row.inquiryStatus)">
              {{ $t(`purchaseInquiry.status.${row.inquiryStatus}`) }}
            </el-tag>
          </template>-->
          <template #default="{ row }">
            <div
              class="contract status"
              :class="inqueryStatusClass(row.inquiryStatus)"
            >
              {{ row.inquiryStatusStr }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseInquiry.table.amount')"
          prop="productCount"
          min-width="80"
        />
        <el-table-column
          :label="$t('purchaseInquiry.table.inquirySchedule')"
          prop="inquirySchedule"
          min-width="100"
        />
        <el-table-column
          :label="$t('purchaseInquiry.table.createTime')"
          prop="submitTime"
          min-width="150"
        >
          <template #default="{ row }">
            <span>{{ parseTimeHandle(row.submitTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseInquiry.table.completeTime')"
          prop="completeTime"
          min-width="120"
        >
          <template #default="{ row }">
            <span>
              {{ row.completeTime ? parseTimeHandle(row.completeTime, '{y}-{m}-{d} {h}:{i}:{s}') : "--" }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('purchaseInquiry.table.operation')"
          fixed="right"
          width="250"
        >
          <template #default="{ row }">
            <el-button
              v-if="getOperationButtons(row).view.show"
              link
              type="primary"
              @click="handleView(row)"
              v-hasPerm="['pms:inquery:detail']"
            >
              {{ $t("purchaseInquiry.button.view") }}
            </el-button>
            <!--            询价单已关闭只能查看和复制-->
            <el-button
              v-if="getOperationButtons(row).quote.show"
              link
              type="primary"
              @click="handleQuote(row)"
              v-hasPerm="['pms:inquery:quotation']"
            >
              {{ $t("purchaseInquiry.button.report") }}
            </el-button>
            <el-button
              v-if="getOperationButtons(row).confirm.show"
              link
              type="primary"
              @click="handlePrice(row)"
              v-hasPerm="['pms:inquery:price']"
            >
              {{ $t("purchaseInquiry.button.price") }}
            </el-button>
            <el-button
              v-if="getOperationButtons(row).copy.show"
              link
              type="primary"
              @click="handleCopy(row)"
              v-hasPerm="['pms:inquery:copy']"
            >
              {{ $t("purchaseInquiry.button.copy") }}
            </el-button>
            <el-button
              v-if="getOperationButtons(row).close.show"
              link
              type="primary"
              @click="handleClose(row)"
              v-hasPerm="['pms_inquery_close']"
            >
              {{ $t("purchaseInquiry.button.close") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes , prev, pager, next, jumper"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from "element-plus";
import { ElMessageBox, ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import InqueryAPI from "@pms/api/inquery";
import SupplierAPI from "@pms/api/supplier";
const { t } = useI18n();
import { parseTime } from "@/core/utils";
const loading = ref(false);
const total = ref(0);
const tableData = ref([]);
const queryFormRef = ref<FormInstance>();
const multipleSelection = ref([]);
const router = useRouter();
const queryParams = ref({
  inquiryStatus: "", // 询价状态
  inquiryCode: "", // 询价单号
  supplierId: undefined,
  deliveryTime: [],
  submitTime: [],
  pageNum: 1,
  pageSize: 20,
  startDeliveryDate: undefined, // 计划交货日期开始时间
  endDeliveryDate: undefined, // 计划交货日期结束时间
  startSubmitDate: undefined, // 询价单提交时间开始时间
  endSubmitDate: undefined, // 询价单提交时间结束时间
});

const statusOptions = ref([
  { label: "询价中", value: 0 },
  { label: "已确定", value: 1 },
  { label: "已完成", value: 2 },
  { label: "已关闭", value: 3 },
]);

const supplierList = ref([]);
const getSupplierList = async () => {
  const data = await SupplierAPI.getSupplierListAll();
  supplierList.value = data;
};
const gotoCreateInquery = () => {
  router.push({
    path: "/pms/purchase/createInquery",
  });
};

const parseTimeHandle = (time: string, pattern="{y}-{m}-{d}") => {
  return parseTime(new Date(time), pattern);
};

// 控制操作按钮显示的方法
const getOperationButtons = (row: any) => {
  const status = row.inquiryStatus;

  // 基础按钮配置
  const buttons = {
    view: {
      show: true, // 查看按钮始终显示
      text: "查看",
      handler: () => handleView(row),
    },
    quote: {
      show: status === 0, // 询价中状态可报价
      text: "报价",
      handler: () => handleQuote(row),
    },
    confirm: {
      show: status === 1, // 已确定状态可定价
      text: "定价",
      handler: () => handleConfirm(row),
    },
    copy: {
      show: true, // 除了已关闭状态都可复制
      text: "复制",
      handler: () => handleCopy(row),
    },
    close: {
      show: [0, 1].includes(status), // 询价中和已确定状态可关闭
      text: "关闭",
      handler: () => handleClose(row),
    },
  };

  return buttons;
};

const quotationBtnShow = (row) => {
  // 报价按钮显示
};
const inqueryStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    0: "unwokring", // 询价中
    1: "executing", // 已确定
    2: "dateexpired", // 已完成
    3: "cancelled", // 已关闭
  };
  return statusMap[status];
};

const handleSubmitTimeChange = (val: [string, string] | null) => {
  if (val) {
    queryParams.value.startSubmitDate = new Date(
      val[0] + " 00:00:00"
    ).getTime();
    queryParams.value.endSubmitDate = new Date(val[1] + " 23:59:59").getTime();
  } else {
    queryParams.value.startSubmitDate = "";
    queryParams.value.endSubmitDate = "";
  }
};

const handleDeliveryTimeChange = (val: [string, string] | null) => {
  if (val) {
    queryParams.value.startDeliveryDate = new Date(
      val[0] + " 00:00:00"
    ).getTime();
    queryParams.value.endDeliveryDate = new Date(
      val[1] + " 23:59:59"
    ).getTime();
  } else {
    queryParams.value.startDeliveryTime = "";
    queryParams.value.endDeliveryTime = "";
  }
};

const handleQuery = () => {
  getList();
};

const handleResetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value = {
    inquiryStatus: "", // 询价状态
    inquiryCode: "", // 询价单号
    supplierId: undefined,
    deliveryTime: [],
    submitTime: [],
    pageNum: 1,
    pageSize: 20,
    startDeliveryDate: undefined, // 计划交货日期开始时间
    endDeliveryDate: undefined, // 计划交货日期结束时间
    startSubmitDate: undefined, // 询价单提交时间开始时间
    endSubmitDate: undefined, // 询价单提交时间结束时间
  };
  getList();
};

const getList = async () => {
  loading.value = true;
  try {
    const formData = JSON.parse(JSON.stringify(queryParams.value));
    delete formData.deliveryTime;
    delete formData.submitTime;
    const res = await InqueryAPI.getInqueryList(formData);
    tableData.value = res.records;
    total.value = parseInt(res.total) || 0;
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error(error);
  }
};

const handleView = (row: any) => {
  router.push({
    path: "/pms/purchase/inqueyDetail",
    query: { inquiryId: row.inquiryId, inquiryCode: row.inquiryCode },
  });
};

const handleQuote = (row: any) => {
  router.push({
    path: "/pms/purchase/inqueyReport",
    query: { inquiryId: row.inquiryId },
  });
};

const handlePrice = (row: any) => {
  router.push({
    path: "/pms/purchase/inqueyPricing",
    query: { inquiryId: row.inquiryId },
  });
};

const handleCopy = (row: any) => {
  /*ElMessageBox.confirm(
    t("purchaseInquiry.message.confirmCopy"),
    t("common.warning"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {

    })
    .catch(() => {
    });*/
  router.push({
    path: "/pms/purchase/copyInquery",
    query: { inquiryId: row.inquiryId, inquiryCode: row.inquiryCode, action: "copy" },
  });
};

const handleClose = (row: any) => {
  ElMessageBox.confirm(
    t("purchaseInquiry.message.confirmClose"),
    t("common.warning"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      InqueryAPI.closeInquiry(row.inquiryId)
        .then(() => {
          getList();
          ElMessage({
            type: "success",
            message: t("purchaseInquiry.message.closeSuccess"),
          });
        })
        .catch(() => {
          ElMessage({
            type: "error",
            message: t("purchaseInquiry.message.closeFailed"),
          });
        });
      // TODO: Add actual close logic here
    })
    .catch(() => {
      // User canceled operation
    });
};

onMounted(() => {
  getList();
  getSupplierList();
});
</script>

<style lang="scss" scoped>
:deep(.el-button--primary.el-button--default.is-link) {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #762ADB ;
  line-height: 20px;
  text-align: center;
  font-style: normal;
}
.purchaseInquiry {
  .search-container {
    margin-bottom: 12px;
  }
  .pagination-container {
    padding: 16px 0 0;
  }
}
</style>
