<template>
  <div class="app-container">
    <div class="addQualityInspectionOrder" v-loading="loading">
      <div>
        <div class="page-title">
          <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon>
              <Back />
            </el-icon></div>
          <div class="purchase-title">
            <span v-if="type == 'add'">{{ $t("qualityInspectionOrder.button.addQualityInspectionOrder") }}</span>
            <span v-else> {{ t('qualityInspectionOrder.label.inspectionCode') }}：{{ form.inspectionCode }}</span>
          </div>
          <!--<div class="purchase" v-if="type == 'detail'">
            <div class="purchase">
              <div class="purchase-status purchase-status-color1" v-if="form.inspectionResultType == 0">
                {{ t('qualityInspectionOrder.inspectionResultTypeList.NoExceptions') }}</div>
              <div class="purchase-status purchase-status-color1" v-if="form.inspectionResultType == 1">
                {{ t('qualityInspectionOrder.inspectionResultTypeList.abnormal') }}</div>
            </div>
          </div>-->
        </div>
      </div>
      <div class="grad-row" style="position: absolute; top: 15px;right: 30px" v-if="type == 'edit'">
        <span class="el-form-item__label">{{ $t("qualityInspectionOrder.label.createUserName") }}：<span
            class="el-form-item__content">{{ form.createUserName }}</span></span>
        <span class="el-form-item__label"> {{ t('qualityInspectionOrder.label.createTime') }}： <span
            class="el-form-item__content">{{ parseDateTime(form.createTime, "dateTime") }}</span></span>
      </div>
      <div v-if="type == 'detail'" class="inspectionResultType-style">
        <img v-if="form.inspectionResultType == 0" src="@/core/assets/images/no_exceptions.png" alt="light" />
        <img v-if="form.inspectionResultType == 1" src="@/core/assets/images/abnormal.png" alt="light" />
      </div>
      <div class="page-content">
        <el-form :model="form" :rules="rules" ref="fromRef" label-width="112px" label-position="right">
          <div class="title-lable">
            <div class="title-content">
              {{ $t("qualityInspectionOrder.label.basicInformation") }}
            </div>
          </div>
          <div v-if="type == 'detail'">
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.createUserName')">
                  {{ form.createUserName }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.createTime')">
                  {{ parseDateTime(form.createTime, "dateTime") }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.inspector')">
                  {{ form.inspector }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.inspectionTime')">
                  {{ parseDateTime(form.inspectionTime, "dateTime") }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.inspectionType')">
                  <span
                    v-if="form.inspectionType == 0">{{ $t('qualityInspectionOrder.inspectionTypeList.collectionTransportationQualityInspection') }}</span>
                  <span
                    v-if="form.inspectionType == 1">{{ $t('qualityInspectionOrder.inspectionTypeList.inWarehouseQualityInspection') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.receivingOrderCode')"
                  v-if="form.inspectionType == 0">
                  <span>{{ form.receivingOrderCode }}</span>
                </el-form-item>
                <el-form-item :label="$t('qualityInspectionOrder.label.warehouseArea')" v-else>
                  <span>{{ form.warehouseAreaName }} | {{ form.warehouseAreaCode }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.status')">
                  <span v-if="form.status == 1">{{ $t('qualityInspectionOrder.statusList.draft') }}</span>
                  <span v-if="form.status == 2">{{ $t('qualityInspectionOrder.statusList.QualityInspectionFinish') }}</span>
                  <span v-if="form.status == 3">{{ $t('qualityInspectionOrder.statusList.qualityInspectioning') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.receivingStatus')">
                  <span v-if="form.receivingStatus == 0">{{ $t('qualityInspectionOrder.claimTypeList.no') }}</span>
                  <span v-if="form.receivingStatus == 1">{{ $t('qualityInspectionOrder.claimTypeList.yes') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.receivingUserName')">
                  {{ form.receivingUserName || '-' }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.receivingTime')">
                  {{ parseDateTime(form.receivingTime, "dateTime") || '-' }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.qualityInspectionProductCount')">
                  {{ form.productCount || '-' }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.qualityInspectionProductWeight')">
                  {{ form.productWeight || '-' }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('reportLossOrder.label.remark')">
                  <span v-if="form.remark" style="word-break:break-all;">{{ form.remark }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div v-if="type !== 'detail'">
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('qualityInspectionOrder.label.inspectionType')" prop="inspectionType">
                  <el-select v-model="form.inspectionType" :placeholder="$t('common.placeholder.selectTips')" clearable
                    @change="changeQualityInspectionOrder(1)" class="!w-[256px]" :disabled="type == 'edit'">
                    <el-option v-for="item in inspectionTypeList" :key="item.inspectionTypeId"
                      :label="item.inspectionTypeName" :value="item.inspectionTypeId"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="form.inspectionType == 0">
                <el-form-item :label="$t('qualityInspectionOrder.label.receivingOrderCode')" prop="receivingOrderCode">
                  <el-select v-model="form.receivingOrderCode" :placeholder="$t('common.placeholder.selectTips')"
                    clearable filterable :disabled="type == 'edit'" @change="changeQualityInspectionOrder(null)"
                    class="!w-[256px]">
                    <el-option v-for="item in receivingOrderList" :key="item.receivingOrderCode"
                      :label="item.receivingOrderCode" :value="item.receivingOrderCode"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="form.inspectionType == 1">
                <el-form-item :label="$t('qualityInspectionOrder.label.warehouseArea')" prop="warehouseAreaCode">
                  <el-select v-model="form.warehouseAreaCode" :placeholder="$t('common.placeholder.selectTips')"
                    clearable filterable :disabled="type == 'edit'" @change="changeQualityInspectionOrder(null)"
                    class="!w-[256px]">
                    <el-option v-for="item in outWarehouseAreaList" :key="item.areaCode" :label="item.warehouseArea"
                      :value="item.areaCode"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="(form.inspectionType == 0 || form.inspectionType == 1)">
                <!-- 是否有异常 -->
                <el-form-item :label="$t('qualityInspectionOrder.label.isAbnormal')" prop="inspectionResultType">
                  <el-select v-model="form.inspectionResultType" :placeholder="$t('common.placeholder.selectTips')"
                    clearable @change="changeQualityInspectionOrder(null)" class="!w-[256px]">
                    <el-option v-for="item in inspectionResultTypeList" :key="item.statusId" :label="item.statusName"
                      :value="item.statusId"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item :label="$t('reportLossOrder.label.remark')" prop="remark">
                  <el-input :rows="4" type="textarea" show-word-limit v-model="form.remark"
                    :placeholder="$t('common.placeholder.inputTips')" maxlength="200" clearable />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="line"></div>

          <!-- 质检信息 -->
          <div class="title-lable" style="justify-content:space-between;" v-if="show">
            <div class="title-content">
              {{ $t("qualityInspectionOrder.label.qualityInspectionInformation") }}
            </div>
            <!-- 添加商品按钮 -->
            <div class="button-add cursor-pointer" @click="addProduct()"
              v-if="type !== 'detail' && form.inspectionType == 1">
              {{ $t('qualityInspectionOrder.button.addProduct') }}
            </div>
          </div>
          <div v-if="show">
            <el-table :data="form.productList" highlight-current-row stripe>
              <el-table-column type="index" :label="$t('common.sort')" width="60" />
              <el-table-column :label="$t('qualityInspectionOrder.label.productInformation')" show-overflow-tooltip
                min-width="180px">
                <template #default="scope">
                  <div class="product-div">
                    <div class="product">
                      <div class="product-name">{{ scope.row.productName }}</div>
                      <div>
                        <span class="product-key">{{ $t('qualityInspectionOrder.label.productCode') }}：</span>
                        <span class="product-value">{{ scope.row.productCode }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('qualityInspectionOrder.label.productSpec')" prop="productSpec"
                show-overflow-tooltip min-width="100px" />
                <!-- 单位 productUnitName-->
                <el-table-column :label="$t('qualityInspectionOrder.label.productUnitName')" prop="productUnitName"
                show-overflow-tooltip min-width="100px" />
              <el-table-column v-if="form.inspectionType == 0"
                :label="$t('qualityInspectionOrder.label.qualityInspectionQty')" prop="productActualQty"
                show-overflow-tooltip align="right" min-width="100px">
                <template #default="scope">
                  <span :style="{ 'color': type == 'detail' ? 'var(--el-color-primary)' : '' }" class="cursor-pointer"
                    @click="type == 'detail' ? openYsnList(scope.row, 'all') : ''">{{ scope.row.productActualQty }}</span>
                </template>
              </el-table-column>
              <el-table-column v-else :label="$t('qualityInspectionOrder.label.qualityInspectionQty')"
                prop="totalStockQty" show-overflow-tooltip align="right" min-width="100px">
                <template #default="scope">
                  <span :style="{ 'color': type == 'detail' ? 'var(--el-color-primary)' : '' }" class="cursor-pointer"
                    @click="type == 'detail' ? openYsnList(scope.row, 'all') : ''">{{ scope.row.totalStockQty }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="form.inspectionType == 0"
                :label="$t('qualityInspectionOrder.label.qualityInspectionQtyWei')" prop="productActualWeight"
                show-overflow-tooltip align="right" min-width="120px" />
              <el-table-column v-if="form.inspectionType == 1"
                :label="$t('qualityInspectionOrder.label.qualityInspectionQtyWei')" prop="totalStockWeight"
                show-overflow-tooltip align="right" min-width="120px" />
              <el-table-column :label="$t('qualityInspectionOrder.label.isAbnormal')" show-overflow-tooltip
                min-width="100px">
                <template #default="scope">
                  <div v-if="type == 'detail'" class="purchase">
                    <span class="purchase-status purchase-status-color1"
                      v-if="scope.row.inspectionStatus == 1">{{ t('qualityInspectionOrder.inspectionResultTypeList.abnormal') }}</span>
                    <span class="purchase-status purchase-status-color3"
                      v-if="scope.row.inspectionStatus == 0">{{ t('qualityInspectionOrder.inspectionResultTypeList.NoExceptions') }}</span>
                  </div>
                  <el-switch v-else :active-text="$t('qualityInspectionOrder.label.activeBtn')"
                    :inactive-text="$t('qualityInspectionOrder.label.inactiveBtn')" inline-prompt
                    v-model="scope.row.inspectionStatus" :active-value="1" :disabled="form.inspectionResultType == 0"
                    :inactive-value="0">
                  </el-switch>
                </template>
              </el-table-column>
              <el-table-column v-if="form.productList && form.productList.some((perm) => perm.inspectionStatus == 1)"
                :label="$t('qualityInspectionOrder.label.abnormalQty')" show-overflow-tooltip min-width="200px">
                <template #default="scope">
                  <!--                                    <el-form-item v-if="scope.row.inspectionStatus==1" class="mt15px" label-width="0px" :prop="'productList.'+scope.$index+'.abnormalQty'" :rules="scope.row.inspectionStatus==1 && !dialog.visible?[{required:true,message:t('qualityInspectionOrder.rules.abnormalQty'),trigger:['blur','change']}, { required: true, validator: validatorLossQty, trigger: ['blur','change']},{pattern: /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^\d\.\d{1,3}$)/, message:t('qualityInspectionOrder.rules.abnormalQtyFormat'), trigger: ['blur','change']}]:''">-->
                  <!--                                    <el-form-item v-if="scope.row.inspectionStatus==1" class="mt15px" label-width="0px" :prop="'productList.'+scope.$index+'.abnormalQty'" :rules="scope.row.inspectionStatus==1 && !dialog.visible?[{required:true,message:t('qualityInspectionOrder.rules.abnormalQty'),trigger:['blur','change']}, { required: true, validator: validatorLossQty, trigger: ['blur','change']},{pattern: /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/, message:t('qualityInspectionOrder.rules.abnormalQtyFormat'), trigger: ['blur','change']}]:''">-->
                  <el-form-item v-if="scope.row.inspectionStatus == 1" class="mt15px" label-width="0px"
                    :prop="'productList.' + scope.$index + '.abnormalQty'"
                    :rules="scope.row.inspectionStatus == 1 && !dialog.visible ? [{ required: true, message: t('qualityInspectionOrder.rules.abnormalQty'), trigger: ['blur', 'change'] }, { required: true, validator: validatorLossQty, trigger: ['blur', 'change'] }, { pattern: /^[1-9]\d*$/, message: t('qualityInspectionOrder.rules.abnormalQtyFormat'), trigger: ['blur', 'change'] }] : ''">
                    <el-input v-model="scope.row.abnormalQty" :placeholder="$t('common.placeholder.inputTips')"
                      clearable :disabled="type == 'detail' || form.inspectionResultType == 0" maxlength="8">
                      <template #append>{{ scope.row.productUnitName }}</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column v-if="form.productList && form.productList.some((perm) => perm.inspectionStatus == 1)"
                :label="$t('qualityInspectionOrder.label.abnormalWei')" show-overflow-tooltip min-width="200px">
                <template #default="scope">
                  <el-form-item v-if="scope.row.inspectionStatus == 1" class="mt15px" label-width="0px"
                    :prop="'productList.' + scope.$index + '.abnormalWeight'"
                    :rules="scope.row.inspectionStatus == 1 && !dialog.visible ? [{ required: true, message: t('qualityInspectionOrder.rules.abnormalWeight'), trigger: ['blur', 'change'] }, { required: true, validator: validatorLossQty, trigger: ['blur', 'change'] }, { pattern: /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/, message: t('qualityInspectionOrder.rules.abnormalWeightFormat'), trigger: ['blur', 'change'] }] : ''">
                    <el-input v-model="scope.row.abnormalWeight" :placeholder="$t('common.placeholder.inputTips')"
                      clearable :disabled="type == 'detail' || form.inspectionResultType == 0">
                      <template #append>{{ $t('qualityInspectionOrder.label.weightUnitName') }}</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column v-if="form.productList && form.productList.some((perm) => perm.inspectionStatus == 1)"
                :label="$t('qualityInspectionOrder.label.abnormalCause')" show-overflow-tooltip min-width="200px">
                <template #default="scope">
                  <el-form-item v-if="scope.row.inspectionStatus == 1" class="mt15px" label-width="0px"
                    :prop="'productList.' + scope.$index + '.abnormalCode'"
                    :rules="scope.row.inspectionStatus == 1 && !dialog.visible ? [{ required: true, message: t('qualityInspectionOrder.rules.abnormalCause'), trigger: ['blur', 'change'] }] : ''">
                    <el-input v-if="type == 'detail'" v-model="scope.row.abnormalCode" disabled>
                    </el-input>
                    <el-cascader v-else v-model="scope.row.abnormalCode" :options="abnormalList" :props="propsCategory"
                      @change="handleChange(scope.$index)" ref="cascaderRef" filterable
                      :placeholder="$t('common.placeholder.selectTips')"
                      :disabled="type == 'detail' || form.inspectionResultType == 0" clearable />
                  </el-form-item>
                </template>
              </el-table-column>
              <!--                            <el-table-column v-if="type=='detail' && form.productList && form.productList.some((perm) => perm.inspectionStatus==1)" :label="$t('qualityInspectionOrder.label.abnormalYsn')" show-overflow-tooltip min-width="200px">-->
              <el-table-column v-if="type == 'detail' && form.inspectionType == 1 && form.inspectionResultType == 1"
                :label="$t('qualityInspectionOrder.label.abnormalYsn')" show-overflow-tooltip min-width="200px">
                <template #default="scope">
                  <el-button v-if="scope.row.inspectionStatus == 1" type="primary" link
                    @click="openYsnList(scope.row, 'abnormal')">{{ $t('common.select') }}</el-button>
                </template>
              </el-table-column>
              <el-table-column :label="$t('common.handle')" min-width="100px">
                <template #default="scope">
                  <div v-if="type == 'add' ? scope.row.imagesNum : scope.row.imagesUrls">
                    <el-badge :value="type == 'add' ? scope.row.imagesNum : JSON.parse(scope.row.imagesUrls).length"
                      :offset="[10, 8]" type="primary">
                      <el-button v-if="scope.row.inspectionStatus == 1" type="primary" link
                        @click="handleUpload(scope.row, scope.$index)">
                        <span v-if="type == 'add'"> {{ $t("qualityInspectionOrder.button.upload") }}</span>
                        <span v-if="type !== 'add'"> {{ $t("qualityInspectionOrder.button.attachment") }}</span>
                      </el-button>
                    </el-badge>
                  </div>
                  <div v-else>
                    <el-button v-if="scope.row.inspectionStatus == 1" type="primary" link
                      @click="handleUpload(scope.row, scope.$index)">
                      <span v-if="type == 'add'"> {{ $t("qualityInspectionOrder.button.upload") }}</span>
                      <span v-if="type !== 'add'"> {{ $t("qualityInspectionOrder.button.attachment") }}</span>
                    </el-button>
                  </div>
                  <!-- <span v-if="type=='add'"> {{$t("qualityInspectionOrder.button.upload")}}</span>
                                       <span v-if="type!=='add'"> {{$t("qualityInspectionOrder.button.attachment")}}</span>-->
                  <el-button v-if="type !== 'detail' && form.inspectionType == 1" type="danger" link
                    @click="handleDelete(scope.$index)">{{ $t('common.delete') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

        </el-form>
      </div>
      <div class="page-footer" v-if="type !== 'detail'">
        <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
        <!--  <el-button type="primary" plain @click="handleSubmit(1)" :loading="submitLoading">{{ $t("qualityInspectionOrder.button.saveDraft") }}</el-button>-->
        <el-button type="primary" @click="handleSubmit(2)" :loading="submitLoading">{{
          $t("qualityInspectionOrder.button.finishQualityInspection") }}</el-button>
      </div>
      <AddProduct ref="addProductRef" v-model:visible="dialog.visible" :title="dialog.title"
        :outWarehouseAreaShow="true" :availableStockQtyShow="true" :outWarehouseAreaFromShow="true"
        :hasTotalStockQty="true" @onSubmit="onSubmit" />

      <!--上传-->
      <UploadDialog v-model:visible="uploadDialog.visible" ref="uploadDialogRef" :ifDrag="ifDrag"
        :showUploadBtn="type == 'detail' ? false : true" @on-submit="onSubmitUpload" />

      <YsnList ref="ysnListRef" v-model:visible="ysnDialog.visible" :title="ysnDialog.title" />

    </div>
  </div>
</template>

<script setup lang="ts">


import { CascaderProps } from "element-plus";

defineOptions({
  name: "AddQualityInspectionOrder",
  inheritAttrs: false,
});

import { parseDateTime } from "@/core/utils/index.js";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import QualityInspectionOrderAPI, { QualityInspectionOrderFrom } from "@/modules/wms/api/qualityInspectionOrder";
import shippingReceiptAPI from "@/modules/wms/api/shippingReceipt";
import ReportLossOrderAPI, { ReportLossOrderFrom } from "@/modules/wms/api/reportLossOrder";
import CommonAPI, { ProductAllPageQuery, ProductAllPageVO } from "@/modules/wms/api/common";
import AddProduct from "../../../components/addProduct.vue";
import YsnList from "./components/ysnListDialog.vue";
import UploadDialog from "./components/uploadDialog.vue";

const uploadDialog = reactive({
  visible: false,
});
const ifDrag = ref(true);
const tableIndex = ref();
const uploadDialogRef = ref();
const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore()
const { t } = useI18n();
const outWarehouseAreaList = ref([])
const receivingOrderList = ref([])
const abnormalList = ref([])
const cascaderRef = ref();
const propsCategory: CascaderProps = {
  checkStrictly: false,
  value: 'dictValue',
  label: 'dictName',
  children: 'subList',
}
const fromRef = ref()
const submitLoading = ref(false)
const queryFormRef = ref(ElForm);
const ysnListRef = ref();
const addProductRef = ref();
const productTotal = ref(0);
const productAllList = ref<ProductAllPageVO[]>();
const dialog = reactive({
  title: "",
  visible: false,
});
const ysnDialog = reactive({
  title: "",
  visible: false,
});
const loading = ref(false);
const id = route.query.id;
const type = route.query.type;
const supplierList = ref([])
const formUpdateRef = ref(null);
const show = ref(false)
const inspectionTypeList = ref([
  {
    inspectionTypeId: 0,
    inspectionTypeName: t('qualityInspectionOrder.inspectionTypeList.collectionTransportationQualityInspection')
  },
  {
    inspectionTypeId: 1,
    inspectionTypeName: t('qualityInspectionOrder.inspectionTypeList.inWarehouseQualityInspection')
  },
])
const inspectionResultTypeList = ref([
  {
    statusId: 1,
    statusName: t('qualityInspectionOrder.inspectionResultTypeList.abnormal')
  },
  {
    statusId: 0,
    statusName: t('qualityInspectionOrder.inspectionResultTypeList.NoExceptions')
  },
])
const claimTypeList = ref([
  {
    key: 0,
    value: t('qualityInspectionOrder.claimTypeList.no')
  },
  {
    key: 1,
    value: t('qualityInspectionOrder.claimTypeList.yes')
  },
])

const form = reactive<ReportLossOrderFrom>({
  orderType: 1,
  productList: []
});
const queryParams = reactive<ProductAllPageQuery>({
  page: 1,
  limit: 20,
});
const rules = reactive({
  inspectionType: [{ required: true, message: t("qualityInspectionOrder.rules.inspectionType"), trigger: ["blur", "change"] }],
  receivingOrderCode: [{ required: true, message: t("qualityInspectionOrder.rules.receivingOrderCode"), trigger: ["blur", "change"] }],
  warehouseAreaCode: [{ required: true, message: t("qualityInspectionOrder.rules.warehouseAreaCode"), trigger: ["blur", "change"] }],
  inspectionResultType: [{ required: true, message: t("qualityInspectionOrder.rules.inspectionResultType"), trigger: ["blur", "change"] }],
});

/** 查询出库库区列表(库存大于的0的所有状态的库区) */
function getOutWarehouseAreaNumberList() {
  return new Promise((resolve, reject) => {
    CommonAPI.getOutWarehouseAreaNumberList()
      .then((data) => {
        outWarehouseAreaList.value = data;
        if (outWarehouseAreaList.value && outWarehouseAreaList.value.length > 0) {
          outWarehouseAreaList.value.map((item) => {
            item.warehouseArea = item.areaName + '|' + item.areaCode
            return item
          });
        }
        resolve();
      })
      .catch((error) => {
        loading.value = false;
        reject(error);
      })
  });
}

/** 查询收运单列表 */
function getReceivingOrderList() {
  shippingReceiptAPI.getReceivingOrderList()
    .then((data) => {
      receivingOrderList.value = data;
    })
}

function changeQualityInspectionOrder(val) {
  if (val == 1) {
    form.receivingOrderCode = undefined
    form.warehouseAreaCode = undefined
  }
  if (form.inspectionType == 0 && form.receivingOrderCode !== undefined && form.inspectionResultType !== undefined) {
    show.value = true
    queryInspectionProductListByOrderCode()
  } else if (form.inspectionType == 1 && form.warehouseAreaCode !== undefined && form.inspectionResultType !== undefined) {
    if (form.inspectionResultType == 1) {
      show.value = true
      form.productList = []
    } else {
      show.value = false
      form.productList = []
    }
  } else {
    show.value = false
    form.productList = []
  }
}

function handleChange(index) {
  if (cascaderRef.value.getCheckedNodes()) {
    let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues
    if (abnormalList.value && abnormalList.value.length > 0) {
      abnormalList.value.forEach(item => {
        let abnormalCause = []
        if (valueArr[0] == item.dictValue) {
          abnormalCause.push(item.dictName)
          if (item.subList && item.subList.length > 0) {
            item.subList.forEach(ch => {
              if (valueArr[1] == ch.dictValue) {
                abnormalCause.push(ch.dictName)
                console.log('====wwwww1====')
              }
            })
          }
          form.productList[index].abnormalCause = abnormalCause
        }
      })
    } else {
      console.log('====wwwww2===')
    }
  }
}

/** 查询异常原因列表 */
function getAbnormalList() {
  return new Promise((resolve, reject) => {
    let params = {
      dictKey: "wms_inspection_abnormal_reason",
    }
    CommonAPI.findTreeListByDictKey(params)
      .then((data) => {
        abnormalList.value = data;
        resolve();
      })
      .catch((error) => {
        loading.value = false;
        reject(error);
      })
  });
}

async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
};

// 上传
function handleUpload(row: any, index: any) {
  tableIndex.value = index;
  uploadDialog.visible = true;
  uploadDialogRef.value.setEditType("add");
  if (row.imagesUrls) {
    uploadDialogRef.value.setFormData(JSON.parse(row.imagesUrls));
  }
  if (type == 'detail') {
    ifDrag.value = false
  } else {
    ifDrag.value = true
  }
}
//上传提交
function onSubmitUpload(data: any) {
  form.productList[tableIndex.value].imagesUrls = JSON.stringify(data);
  form.productList[tableIndex.value].imagesNum = data.length;
}

/** 暂存/提交 */
function handleSubmit(val) {
  fromRef.value.validate((valid) => {
    if (!valid) return;
    if (form.productList && form.productList.length == 0 && (form.inspectionType == 0 || (form.inspectionType == 1 && form.inspectionResultType == 1))) {
      return ElMessage.error(t('qualityInspectionOrder.message.qualityInspectionOrderTips'));
    }
    if (form.productList && form.productList.length > 0 && form.inspectionResultType == 1) {
      const flag = form.productList.some(item => item.inspectionStatus == 1)
      if (!flag) {
        return ElMessage.error(t('qualityInspectionOrder.message.qualityInspectionLessTips'));
      }
    }
    submitLoading.value = true
    let productList = []
    if (form.productList && form.productList.length > 0) {
      form.productList.forEach((item) => {
        let obj = {
          id: item.id,
          productCode: item.productCode,
          productName: item.productName,
          productSpecs: item.productSpec,
          productUnitId: item.productUnitId,
          productUnitName: item.productUnitName,
          inspectionStatus: item.inspectionStatus,
        }
        if (form.inspectionType == 0) {
          obj.productActualQty = item.productActualQty
          obj.productActualWeight = item.productActualWeight
        } else {
          obj.totalStockQty = item.totalStockQty
          obj.totalStockWeight = item.totalStockWeight
        }
        if (item.inspectionStatus == 1) {
          obj.abnormalQty = item.abnormalQty
          obj.abnormalWeight = item.abnormalWeight
          obj.abnormalCode = item.abnormalCode && item.abnormalCode.length > 0 ? item.abnormalCode.join(',') : ''
          obj.abnormalCause = item.abnormalCause && item.abnormalCause.length > 0 ? item.abnormalCause.join(',') : ''
          obj.imagesUrls = item.imagesUrls
        }
        productList.push(obj)
      })
    }
    let params = {
      inspectionCode: form.inspectionCode,
      inspectionType: form.inspectionType,
      inspectionResultType: form.inspectionResultType,
      remark: form.remark,
      productList: productList,
      status: val,
    }
    if (type !== 'add') {
      params.id = form.id
    }
    if (form.inspectionType == 0) {
      params.receivingOrderCode = form.receivingOrderCode
    } else {
      params.warehouseAreaCode = form.warehouseAreaCode
      const targetWarehouseArea = outWarehouseAreaList.value.filter(out => form.warehouseAreaCode == out.areaCode)
      params.warehouseAreaName = targetWarehouseArea[0].areaName
      params.warehouseAreaType = targetWarehouseArea[0].areaType
    }
    if (type == 'add') {
      if (val == 2) {
        ElMessageBox.confirm(t('qualityInspectionOrder.message.finishQualityInspectionTips'), t('common.tipTitle'), {
          confirmButtonText: t('common.confirm'),
          cancelButtonText: t('common.cancel'),
          type: "warning",
        }).then(
          () => {
            addSubmit(params, val)
          },
          () => {
            submitLoading.value = false;
            ElMessage.info(t('qualityInspectionOrder.message.finishQualityInspectionConcel'));
          }
        );
      } else {
        addSubmit(params, val)
      }
    } else {
      if (val == 2) {
        ElMessageBox.confirm(t('qualityInspectionOrder.message.finishQualityInspectionTips'), t('common.tipTitle'), {
          confirmButtonText: t('common.confirm'),
          cancelButtonText: t('common.cancel'),
          type: "warning",
        }).then(
          () => {
            editSubmit(params, val)
          },
          () => {
            submitLoading.value = false;
            ElMessage.info(t('qualityInspectionOrder.message.finishQualityInspectionConcel'));
          }
        );
      } else {
        editSubmit(params, val)
      }
    }
  })
}

function addSubmit(params, val) {
  QualityInspectionOrderAPI.addQualityInspectionOrder(params)
    .then((data) => {
      ElMessage.success(val == 1 ? t('qualityInspectionOrder.message.saveDraftSucess') : t('qualityInspectionOrder.message.finishQualityInspectionSucess'));
      handleClose()
    })
    .finally(() => {
      submitLoading.value = false;
    });
}

function editSubmit(params, val) {
  QualityInspectionOrderAPI.editQualityInspectionOrder(params)
    .then((data) => {
      ElMessage.success(val == 1 ? t('qualityInspectionOrder.message.saveDraftSucess') : t('qualityInspectionOrder.message.finishQualityInspectionSucess'));
      handleClose()
    })
    .finally(() => {
      submitLoading.value = false;
    });
}

const status = route.query.status;
/** 查询质检单详情 */
function queryQualityInspectionOrderDetail() {
  loading.value = true;
  let params = {
    id: id
  }
  QualityInspectionOrderAPI.queryQualityInspectionOrderDetail(params)
    .then((data) => {
      Object.assign(form, data)
      if (type !== 'detail') {
        let warehouseArea = outWarehouseAreaList.value.filter(out => form.warehouseAreaCode == out.areaCode)
        form.warehouseAreaCode = warehouseArea && warehouseArea.length > 0 ? form.warehouseAreaCode : ''
        form.warehouseAreaName = warehouseArea && warehouseArea.length > 0 ? warehouseArea[0].areaName : ''
      }
      if (form.productList && form.productList.length > 0) {
        form.productList.forEach(item => {
          item.productSpec = item.productSpecs
          if (type == 'add') {
            item.inspectionStatus = 0
          }
          if (item.inspectionStatus == 1) {
            if (type == 'detail') {
              item.abnormalCode = item.abnormalCause
              item.abnormalCause = item.abnormalCause
            } else {
              item.abnormalCode = item.abnormalCode && item.abnormalCode.length > 0 ? item.abnormalCode.split(',') : ''
              item.abnormalCause = item.abnormalCause && item.abnormalCause.length > 0 ? item.abnormalCause.split(',') : ''
            }

          }
        })
      }
      /* if(form.inspectionType==1 && form.inspectionResultType==0){
           show.value=false
       }else{
           show.value=true
       }*/

      show.value = true
      // 质检单，草稿状态的库内巡检单质检，进入质检页面默认无异常，质检信息商品列表和添加商品按钮不用展示
      if(form.inspectionType==1 && type !== 'detail' && status == 1){
        show.value = form.inspectionResultType != 0;
        return;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 查询ysn列表 */
function openYsnList(row, pageType) {
  ysnDialog.title = t('qualityInspectionOrder.title.productYsn') + row.productCode + '|' + row.productName;
  let queryParams = {
    inspectionProductId: row.id,
    typeList: form.inspectionType == 0 ? 1 : 2,
    pageType: pageType,
    status: form.status,
  }
  if (form.inspectionType == 0) {
    queryParams.receivingOrderCode = form.receivingOrderCode
  }
  ysnListRef.value.setFormData({ queryParams: queryParams });
  ysnDialog.visible = true;
}

/** 添加商品 */
function addProduct() {
  dialog.title = t('qualityInspectionOrder.title.addProduct');
  addProductRef.value.getOutWarehouseAreaList();
  addProductRef.value.queryManagerCategoryList();
  let queryParams = {
    warehouseAreaCode: form.warehouseAreaCode,
    typeList: 3,
  }
  addProductRef.value.setFormData({ queryParams: queryParams });
  dialog.visible = true;
}

function onSubmit(data) {
  if (data) {
    let arr = data.collection.concat(form.productList);
    let uniqueArr = [...new Map(arr.map(item => [item.productCode, item])).values()];
    form.productList = uniqueArr;
    console.log("===inventoryTransferInfoDetailList===" + form.productList);
  }
}

/** 假删除*/
function handleDelete(index?: number) {
  form.productList.splice(index, 1);
  ElMessage.success(t('qualityInspectionOrder.message.deleteSucess'));
}


/**根据收运单查询质检商品列表(质检单创建页面) */
function queryInspectionProductListByOrderCode() {
  let params = {
    receivingOrderCode: form.receivingOrderCode,
    hasProductActualQty: true,
  }
  shippingReceiptAPI.queryInspectionProductListByOrderCode(params)
    .then((data) => {
      let arr = []
      if (data && data.length > 0) {
        data.forEach(item => {
          let obj = {
            id: item.id,
            productCode: item.productCode,
            productName: item.productName,
            productSpec: item.productSpecs,
            productUnitId: item.productUnitId,
            productUnitName: item.productUnitName,
            productActualQty: item.productActualQty,//收运数量
            productActualWeight: item.receivedWeight,//收运重量
          }
          arr.push(obj)
        })
      }
      nextTick(()=>{
        form.productList = arr
      })
    })
}

onMounted(async () => {
  if (type !== 'add') {
    loading.value = true;
  }
  if (type !== 'detail') {
    await getAbnormalList();
    await getOutWarehouseAreaNumberList();
    getReceivingOrderList();
  }
  if (type !== 'add') {
    queryQualityInspectionOrderDetail();
  }
});
</script>
<style scoped lang="scss">
.addQualityInspectionOrder {
  position: relative;
  background: #FFFFFF;
  border-radius: 4px;

  .inspectionResultType-style {
    position: absolute;
    right: 10%;
    top: 20px;

    img {
      width: 120px;
      height: 105px;
      background: #FFFFFF;
      border-radius: 4px;
    }
  }

  .page-content {
    .button-add {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary)
    }

    .table-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 50px;
      background: #F4F6FA;
      box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585F;
      font-style: normal;
      padding: 15px 12px;
    }

    .el-switch.is-checked .el-switch__core .el-switch__inner {
      width: 60px;
    }
  }
}
</style>
<style lang="scss">
.addQualityInspectionOrder {
  .page-content {
    .el-switch .el-switch__inner {
      width: 60px !important;
    }
  }
}
</style>
