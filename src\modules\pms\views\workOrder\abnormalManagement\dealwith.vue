<template>
    <div class="app-container">
        <div class="dealwith" v-loading="loading">
            <div class="page-title">
                <div class="purchase-title">
                    <div @click="handleClose" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                    <div>{{t('abnormalManagement.label.workOrderCode')}}：{{form.workOrderCode}}</div>
                </div>
                <div class="purchase">
                    <div class="purchase-status purchase-status-color1" v-if="form.workOrderStatus==1">{{t('abnormalManagement.workOrderStatusList.toBeTreated')}}</div>
                    <div class="purchase-status purchase-status-color3" v-if="form.workOrderStatus==3">{{t('abnormalManagement.workOrderStatusList.complete')}}</div>
                    <div class="purchase-status purchase-status-color0" v-if="form.orderPurchaseStatus==4">{{t('abnormalManagement.workOrderStatusList.cancel')}}</div>
                </div>
            </div>
            <div class="page-content">
                <el-form :model="form"  :rules="rules" ref="fromRef" label-width="110px" label-position="right">
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("abnormalManagement.label.basicInformation") }}
                        </div>
                    </div>
                    <div class="grad-row">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('abnormalManagement.label.workOrderCode')">
                                    {{form.workOrderCode}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('abnormalManagement.label.purchaseCode')">
                                    {{form.purchaseCode }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('abnormalManagement.label.receiptCode')">
                                    {{form.receiptCode }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8" v-if="form.orderType==1">
                                <el-form-item :label="$t('abnormalManagement.label.supplierId')">
                                    {{form.supplierName}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('abnormalManagement.label.purchaserUserId')">
                                    {{form.purchaseUser}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('abnormalManagement.label.createUserName')">
                                    <span class="encryptBox">
                                        <span v-if="form.createUserName && form.createUserName.length <= 1">{{form.createUserName}}</span>
                                        <span v-else>
                                          {{form.createUserName}}
                                          <el-icon
                                                  v-hasPermEye="['pms:workOrder:abnormalManagement:eye']"
                                                  v-if="form.createUserName"
                                                  @click="form.createUserNameShow ? desensitization('createUserName'):''"
                                                  class="encryptBox-icon"
                                                  color="#762ADB "
                                                  size="16"
                                          >
                                            <component :is="form.createUserNameShow ? 'View' : ''" />
                                          </el-icon>
                                        </span>
                                    </span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" v-if="form.orderType==2">
                                <el-form-item :label="$t('abnormalManagement.label.createUserPhone')">
                                    <span class="encryptBox">
                                        <span v-if="form.createUserPhone && form.createUserPhone.length <= 4">{{form.createUserPhone}}</span>
                                        <span v-else>
                                          {{form.createUserPhone}}
                                          <el-icon
                                                  v-hasPermEye="['pms:workOrder:abnormalManagement:eye']"
                                                  v-if="form.createUserPhone"
                                                  @click="desensitization('createUserPhone')"
                                                  class="encryptBox-icon"
                                                  color="#762ADB "
                                                  size="16"
                                          >
                                            <component :is="form.mobilePhoneShow ? 'View' : ''" />
                                          </el-icon>
                                        </span>
                                    </span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8" v-if="form.orderType==1">
                                <el-form-item :label="$t('abnormalManagement.label.createUserPhone')">
                                    <span class="encryptBox">
                                        <span v-if="form.createUserPhone && form.createUserPhone.length <= 4">{{form.createUserPhone}}</span>
                                        <span v-else>
                                          {{form.createUserPhone}}
                                          <el-icon
                                                  v-hasPermEye="['pms:workOrder:abnormalManagement:eye']"
                                                  v-if="form.createUserPhone"
                                                  @click="desensitization('createUserPhone')"
                                                  class="encryptBox-icon"
                                                  color="#762ADB "
                                                  size="16"
                                          >
                                            <component :is="form.mobilePhoneShow ? 'View' : ''" />
                                          </el-icon>
                                        </span>
                                    </span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('abnormalManagement.label.createTime')">
                                    <span  v-if="form.createTime">{{ parseDateTime(form.createTime, "dateTime") }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('abnormalManagement.label.workOrderType')">
                                    <span  v-if="form.workOrderType==1">{{t('abnormalManagement.workOrderTypeList.purchaseReturn')}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line"></div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("abnormalManagement.label.productInformation") }}
                        </div>
                    </div>
                    <div v-if="form.workOrderProductDetailList && form.workOrderProductDetailList.length>0">
                        <el-table
                                :data="form.workOrderProductDetailList"
                                highlight-current-row
                                stripe
                        >
                            <el-table-column type="index" :label="$t('common.sort')" width="60"  align="center"/>
                            <el-table-column :label="$t('abnormalManagement.label.product')" min-width="150" show-overflow-tooltip>
                                <template #default="scope">
                                    <div class="product-div">
                                        <div class="picture">
                                            <img :src="scope.row.productImage" alt="">
                                        </div>
                                        <div class="product">
                                            <div>
                                                <span class="product-key">{{$t('abnormalManagement.label.productCode')}}：</span>
                                                <span class="product-value">{{scope.row.productCode}}</span>
                                            </div>
                                            <div class="product-name">{{scope.row.productName}}</div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('abnormalManagement.label.productUnit')" prop="productUnit" show-overflow-tooltip/>
                            <el-table-column :label="$t('abnormalManagement.label.workOrderQuantity')" prop="workOrderQuantity" show-overflow-tooltip align="right"/>
                            <el-table-column :label="$t('abnormalManagement.label.workOrderAmount')" prop="workOrderAmount" show-overflow-tooltip align="right">
                                <template #default="scope">
                                    <span v-if="scope.row.workOrderAmount">
                                          <span v-if="form.currencyCode == 'CNY'">￥</span>
                                          <span v-else>$</span>
                                        {{scope.row.workOrderAmount}}
                                    </span>
                                    <span v-else>-</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('abnormalManagement.label.workOrderRemark')"  prop="workOrderRemark" show-overflow-tooltip/>
                            <el-table-column :label="$t('abnormalManagement.label.attachmentInfo')" show-overflow-tooltip>
                                <template #default="scope">
                                    <div class="product-div">
                                        <div class="picture">
                                            <img :src="scope.row.attachmentInfo" alt="">
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column :label="'*'+$t('abnormalManagement.label.handleType')" show-overflow-tooltip>
                                <template #default="scope">
                                    <el-form-item label-width="0px" :prop="'workOrderProductDetailList.'+scope.$index +'.handleType'" :rules="[{required:true,message:t('abnormalManagement.rules.handleType'),trigger:'change'}]">
                                        <el-select
                                                v-if="type =='dealwith'"
                                                v-model="scope.row.handleType"
                                                :placeholder="$t('common.placeholder.selectTips')"
                                                clearable
                                                filterable
                                                @change="changeHandleType(scope.row,scope.$index)"
                                                class="!w-[150px]"
                                        >
                                            <el-option v-for="item in dealingWithProcessingList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                                        </el-select>
                                        <div v-if="type =='select'">
                                            <span  v-if="scope.row.handleType==1">{{t('abnormalManagement.dealingWithProcessingList.purchaseReturn')}}</span>
                                            <span  v-if="scope.row.handleType==2">{{t('abnormalManagement.dealingWithProcessingList.continuSell')}}</span>
                                            <span  v-if="scope.row.handleType==3">{{t('abnormalManagement.dealingWithProcessingList.damageIntoWarehouse')}}</span>
                                        </div>
                                    </el-form-item>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="table-title">
                            <span>
                                {{ $t("abnormalManagement.label.workOrderAmountTotal") }}
                                <span class="color">
                                     <span v-if="form.currencyCode == 'CNY'">￥</span>
                                     <span v-else>$</span>
                                     {{form.workOrderAmount}}
                                </span>
                            </span>
                            <span class="ml24px">
                                {{ $t("abnormalManagement.label.returnAmount") }}
                                <span class="color">
                                     <span v-if="form.currencyCode == 'CNY'">￥</span>
                                     <span v-else>$</span>
                                    {{form.returnAmount}}
                                </span>
                            </span>
                        </div>
                        <div>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item
                                            :label="$t('abnormalManagement.label.handleOpinion')"
                                            prop="handleOpinion"
                                    >
                                        <el-input
                                                :rows="4"
                                                type="textarea"
                                                show-word-limit
                                                maxlength="200"
                                                v-model="form.handleOpinion"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                clearable
                                                :disabled="type =='select'"
                                        />
<!--                                        <el-input-->
<!--                                                v-if="type =='dealwith'"-->
<!--                                                :rows="4"-->
<!--                                                type="textarea"-->
<!--                                                show-word-limit-->
<!--                                                maxlength="200"-->
<!--                                                v-model="form.handleOpinion"-->
<!--                                                :placeholder="$t('common.placeholder.inputTips')"-->
<!--                                                clearable-->
<!--                                        />-->
<!--                                        <span v-if="type =='select'">{{form.handleOpinion}}</span>-->
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                </el-form>
            </div>
            <div class="page-footer">
                <el-button v-if="type =='select'" @click="handleClose">{{ $t("abnormalManagement.button.return") }}</el-button>
                <el-button v-if="type =='dealwith'" @click="handleClose">{{ $t("common.cancel") }}</el-button>
                <el-button v-if="type =='dealwith'" type="primary" :loading="submitLoading" @click="doDealWith">{{ $t("common.confirm") }}</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

    import UserAPI from "@/core/api/accountManagement";

    defineOptions({
        name: "Dealwith",
        inheritAttrs: false,
    });

    import {useRoute,useRouter} from "vue-router";
    import {parseDateTime} from "@/core/utils/index.js";
    import AbnormalManagementAPI, { AbnormalManagementDetailFrom,DealWithFrom} from "@/modules/pms/api/abnormalManagement";
    import {useTagsViewStore} from "@/core/store";


    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const fromRef = ref()
    const submitLoading = ref(false)
    const queryFormRef = ref(ElForm);
    const roleFormRef = ref(ElForm);
    const loading = ref(false);
    const id = route.query.id;
    const type = route.query.type;
    const form = reactive<AbnormalManagementDetailFrom>({
        product:[],
        mobilePhoneShow:true,
        createUserNameShow:true,
    });
    const dealingWithProcessingList = ref([
        {
            key: 1,
            value: t('abnormalManagement.dealingWithProcessingList.purchaseReturn')
        },
        {
            key: 2,
            value:t('abnormalManagement.dealingWithProcessingList.continuSell')
        },
        {
            key: 3,
            value:t('abnormalManagement.dealingWithProcessingList.damageIntoWarehouse')
        }
    ])

    const rules = reactive({
        handleType: [
            { required: true, message: t("abnormalManagement.rules.handleType"), trigger: ["blur","change"] },
        ],
        handleOpinion: [
            {min:0,max:200, message: t("abnormalManagement.rules.handleOpinion"), trigger: "blur" },
        ],
    });

    async function handleClose(){
        await tagsViewStore.delView(route);
        router.go(-1);
    }

    function changeHandleType(row,index) {
        if(form.workOrderProductDetailList[index].workOrderAmount!==undefined){
            let returnAmount= '0'
            form.workOrderProductDetailList.forEach(item=>{
                if(item.handleType==1){
                    let workOrderAmount1=item.workOrderAmount?parseFloat(item.workOrderAmount):0
                    returnAmount= (parseFloat(returnAmount) + workOrderAmount1).toFixed(2)
                }
            })
            form.returnAmount=returnAmount
        }
    }

    /** 查询采购单详情 */
    function queryAbnormalManagementDetail(){
        loading.value = true;
        let params = {
            id:id
        }
        AbnormalManagementAPI.queryAbnormalManagementDetail(params)
            .then((data) => {
                Object.assign(form,data)
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /**处理保存*/
    function doDealWith() {
        fromRef.value.validate((valid) => {
            if (!valid) return;
            let data = {
                id: id,
                handleOpinion: form.handleOpinion,
                productHandleList: undefined
            }
            if(form.workOrderProductDetailList && form.workOrderProductDetailList.length>0){
                data.productHandleList = form.workOrderProductDetailList.map(item => {
                    return Object.assign({},{'handleType':item.handleType, "productId": item.productId})
                })
            }
            submitLoading.value=true
            AbnormalManagementAPI.doDealWith(data).then(res => {
                ElMessage.success(t('abnormalManagement.message.dealwithSucess'))
                handleClose()
            })
            .finally(() => {
                submitLoading.value = false;
            });
        })
    }

    function desensitization(name) {
        let params ={ id: form.id,name:name }
        AbnormalManagementAPI.desensitization(params)
            .then((data: any) => {
                if(name=='createUserPhone'){
                    form.createUserPhone = data;
                    form.mobilePhoneShow = false;
                }else  if(name=='createUserName'){
                    form.createUserName = data;
                    form.createUserNameShow = false;
                }
            })
            .finally(() => {});
    }


    onMounted(() => {
        queryAbnormalManagementDetail();
    });
</script>
<style scoped lang="scss">
    .dealwith {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .table-title{
                margin-top: 12px;
                margin-bottom: 12px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                .color{
                    color: #C00C1D ;
                }
            }
        }
        .encryptBox {
            word-wrap: break-word;
            word-break: break-all;
        }

        .encryptBox-icon {
            margin-left: 4px;
            cursor: pointer;
            vertical-align: text-top;
        }
    }
</style>
<style lang="scss">
    .dealwith {}
</style>

