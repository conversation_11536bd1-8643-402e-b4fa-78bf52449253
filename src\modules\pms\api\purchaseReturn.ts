import request from "@/core/utils/request";
const PURCHASE_BASE_URL = "/supply-pms/purchaseReturnOrder"
class PurchaseReturnApi {
  /** 分页查询 */
  static queryPageList(queryParams?: queryPageListReq){
    return request<any, PageResult<queryPageListResp[]>>({
      url: `${PURCHASE_BASE_URL}/page`,
      method: 'post',
      data: queryParams
    })
  }
  /** 手动同步 */
  static sync(data ?: any){
    return request({
      url: `${PURCHASE_BASE_URL}/sync`,
      method:'post',
      data:data
    })
  }
  /** 删除 */
  static delete(data : {returnOrderCode ?: string}){
    return request({
      url: `${PURCHASE_BASE_URL}/delete/${data.returnOrderCode}`,
      method: 'get'
    })
  }
  /** 保存 */
  static save(data : any){
    return request({
      url: `${PURCHASE_BASE_URL}/save`,
      method: 'post',
      data:  data
    })
  }
  /** 审核 */
  static approve(data : any){
    return request({
      url: `${PURCHASE_BASE_URL}/approve`,
      method: 'post',
      data: data
    })
  }
  /** 详情 */
  static detail(data : {returnOrderCode ?: string}){
    return request({
      url: `${PURCHASE_BASE_URL}/detail/${data.returnOrderCode}`,
      method: 'get'
    })
  }
  /**
   * 获取国际国家列表-含港澳台
   *
   */
  static getAllCountry() {
    return request({
      url: `/supply-base/country/all`,
      method: "get",
    });
  }
  /** 根据采购单号查询采购单详情 */
  static queryDetailByOrderCode(data : {orderCode ?: string}){
    return request({
      url: `/supply-pms/purchaseOrder/detail/${data.orderCode}`,
      method: 'get'
    })
  }
  /** 获取退货方式下拉  /deliveryMethods/queryPageList */
  static queryReturnMethodList(data?: any){
    return request({
      url: `/supply-biz-common/deliveryMethods/queryPageList`,
      method: "post",
      data: data
    })
  }
}
export default PurchaseReturnApi

/** 分页请求参数 */
export interface queryPageListReq extends PageQuery{
  /** 关联采购单号 */
  orderCode?: string;
  /** 退货单号 */
  returnOrderCode?: string;
  /** 采购类型 */
  orderType?: number;
  /** 供应商 */
  supplierId?: number;
  /** 采购员 */
  purchaseUserId?: number;
  /** 同步状态 */
  syncStatus?: number;
}
/** 分页响应参数 */
export interface queryPageListResp {
  /** 关联采购单号 */
  orderCode?: string;
  /** 退货单号 */
  returnOrderCode?: string;
  /** 采购类型 */
  orderType?: number;
  /** 采购员 */
  purchaseUserName?: string;
  /** 供应商 */
  supplierName?: string;
  /** 退货总金额 */
  totalReturnAmount?: string;
  /** 退货发起时间 */
  createTime?: number;
  /** 审核时间 */
  approveTime?: number;
  /** 制单人 */
  createUserName?: string;
  /** 审核人 */
  approveUserName?: string;
  /** 审核状态 */
  approveStatus?: number;
  /** 同步状态 */
  syncStatus?: number;
}
/** 详情 */
export interface detailForm {
  /** 关联采购单号 */
  orderCode?: string;
  /** 退货单号 */
  returnOrderCode?: string;
  /** 退货方式 */
  deliveryType?: number;
  deliveryName?: string;
  /** 计划送达时间 */
  planDeliveryTime?: string;
  /** 采购类型 */
  orderType?: number;
  /** 采购员 */
  purchaseUserName?: string;
  /** 供应商 */
  supplierName?: string;
  /** 退货总金额 */
  totalReturnAmount?: string;
  /** 退货发起时间 */
  createTime?: number;
  /** 审核时间 */
  approveTime?: number;
  /** 制单人 */
  createUserName?: string;
  /** 审核人 */
  approveUserId?: string;
  approveUserName?: string;
  /** 审核状态 */
  approveStatus?: number;
  /** 审核意见 */
  approveRemark?: string;
  /** 附件 */
  attachmentFiles?: string;
  /** 备注 */
  remark?: string;
  /** 退货总数量 */
  totalReturnCount?: number;
  /** 预计退货金额 */
  returnAmount?: number;
  /** 退货仓库 */
  warehouseCode?: string;
  warehouseName?: string;
  /** 退货地址 */
  returnAddress?: string;
  returnCountryId?: string;
  returnCountryName?: string;
  returnProvinceId?: string;
  returnProvinceName?: string;
  returnCityId?: string;
  returnCityName?: string;
  returnDistrictId?: string;
  returnDistrictName?: string;
}
