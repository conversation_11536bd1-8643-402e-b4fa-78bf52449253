<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" size="850px" @close="close" class="add-product">
        <div>
            <el-table
                    v-loading="loading"
                    :data="ysnList"
                    highlight-current-row
                    stripe
            >
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <el-table-column v-if="pageType=='all' && typeList==1" :label="$t('qualityInspectionOrder.label.boxCode')" prop="boxCode" show-overflow-tooltip min-width="200px"/>
                <el-table-column v-else :label="$t('qualityInspectionOrder.label.ysnCode')" prop="ysnCode" show-overflow-tooltip min-width="200px"/>
                <el-table-column v-if="pageType=='all' && typeList==1" :label="$t('qualityInspectionOrder.label.quantity')" prop="quantity" align="right"  show-overflow-tooltip />
                <el-table-column :label="$t('qualityInspectionOrder.label.productWeight')" prop="weight" align="right" show-overflow-tooltip/>
                <el-table-column :label="$t('qualityInspectionOrder.label.scanTime')" show-overflow-tooltip>
                    <template #default="scope">
                        {{ parseDateTime(scope.row.scanTime, "dateTime") }}
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                    v-if="ysnTotal > 0"
                    v-model:total="ysnTotal"
                    v-model:page="ysnFrom.page"
                    v-model:limit="ysnFrom.limit"
                    @pagination="queryList"
            />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
          </span>
        </template>
    </el-drawer>
</template>
<script setup lang="ts">
    import { parseDateTime } from "@/core/utils/index.js";
    import QualityInspectionOrderAPI, {QualityInspectionYsnPageQuery,QualityInspectionYsnPageVO} from "@/modules/wms/api/qualityInspectionOrder";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();
    const loading = ref(false);
    const ysnTotal = ref(0);
    const typeList = ref();
    const pageType = ref();
    const status = ref();
    const ysnList = ref<QualityInspectionYsnPageVO[]>()
    let ysnFrom = reactive<QualityInspectionYsnPageQuery>({
        page: 1,
        limit: 20,
    });

    function close() {
        emit("update:visible", false);
        ysnFrom.page = 1;
        ysnFrom.limit = 20;
    }

    function getQualityInspectionAbnormalYsnPage(){
        loading.value = true;
        let data = {
            ...ysnFrom,
        }
        QualityInspectionOrderAPI.getQualityInspectionAbnormalYsnPage(data)
            .then((data) => {
                ysnList.value = data.records;
                ysnTotal.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    function getQualityInspectionYsnPage(){
        loading.value = true;
        let data = {
            ...ysnFrom,
        }
        QualityInspectionOrderAPI.getQualityInspectionYsnPage(data)
            .then((data) => {
                ysnList.value = data.records;
                ysnTotal.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    function getQualityInspectionBoxPage(){
        loading.value = true;
        let data = {
            ...ysnFrom,
        }
        QualityInspectionOrderAPI.getQualityInspectionBoxPage(data)
            .then((data) => {
                ysnList.value = data.records;
                ysnTotal.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    function setFormData(data) {
        status.value = data.queryParams.status;
        typeList.value = data.queryParams.typeList;
        pageType.value = data.queryParams.pageType;
        ysnFrom.inspectionProductId = data.queryParams.inspectionProductId;
        ysnFrom.receivingOrderCode = data.queryParams.receivingOrderCode;
        queryList()
    }

    function queryList(){
        if(status.value==2){
            if(pageType.value=='all' && typeList.value==1){
                getQualityInspectionBoxPage()
            }else if(pageType.value=='all' && typeList.value!=1){
                getQualityInspectionYsnPage()
            }else{
                getQualityInspectionAbnormalYsnPage()
            }
        }
    }

    defineExpose({
        setFormData
    });
</script>

<style scoped lang="scss">
</style>
<style lang="scss">
</style>
