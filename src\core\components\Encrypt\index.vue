<!--
 * @Author: <PERSON><PERSON><PERSON> cheng<PERSON>@yto.net.cn
 * @Date: 2025-03-17 17:34:49
 * @LastEditors: ch<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-10 14:13:40
 * @FilePath: \supply-manager-web\src\core\components\Encrypt\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <span class="encryptBox">
    <template v-if="!props.nameType">
      <slot name="pre"></slot>
      {{ mobilePhoneShow ? phone : encryptPhone(phone) }}
      <el-icon
        v-if="props.phone"
        @click="mobilePhoneShow = !mobilePhoneShow"
        class="encryptBox-icon"
        color="#762ADB "
        size="16"
      >
        <component v-if="!nameShow" :is="nameShow ? 'Hide' : 'View'" />
      </el-icon>
    </template>
    <template v-else>
      <slot name="pre"></slot>
      {{ shouldEncryptName ? (nameShow ? name : encryptName(name)) : name }}
      <!-- {{ nameShow ? name : encryptName(name) }} -->
      <el-icon
        v-if="shouldShowNameIcon"
        @click="nameShow = !nameShow"
        class="encryptBox-icon"
        color="#762ADB "
        size="16"
      >
        <component v-if="!nameShow" :is="nameShow ? 'Hide' : 'View'" />
      </el-icon>
    </template>
  </span>
</template>
<script setup>
import { encryptPhone, encryptName } from "../../utils/index";
let props = defineProps({
  phone: {
    type: [String, Number],
  },
  name: {
    type: [String, Number],
  },
  nameType: {
    type: Boolean,
    default: false,
  },
});

let mobilePhoneShow = ref(false);
let nameShow = ref(false);
// 计算属性：是否需要加密姓名（长度>1且非空）
const shouldEncryptName = computed(() => {
  return props.name && String(props.name).length > 1;
});

// 计算属性：是否显示姓名切换图标
const shouldShowNameIcon = computed(() => {
  return props.name && shouldEncryptName.value;
});
</script>
<style lang="scss" scoped>
.encryptBox {
  // display: inline-flex;
  // justify-content: space-between;
  // align-items: center;
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  // align-self: flex-start;
  vertical-align: text-top;
}
</style>
