import request from "@/core/utils/request";

const CONTACT_BASE_URL = "/supply-base";

class ContactAPI {
  /** 提交联系表单 */
  static submitContactForm(data: ContactSubmissionRequest) {
    return request<any, ContactSubmissionResponse>({
      url: `${CONTACT_BASE_URL}/baseConsultMessage/add`,
      method: "post",
      data,
    });
  }
}

export default ContactAPI;

/** 联系表单数据 */
export interface ContactFormData {
  "enterpriseName": string, // 企业名称
  "appCode": string, // 应用编码 1客户管家ERP 2 珈蓝WMS 3 圆通集运平台
  "contact": string, // 联系人
  "contactAreaCode": string, // 联系人区号
  "contactNumber": string, // 联系电话
  "email": string, // 邮箱
  "intentionDescription": string // 意向描述
}

/** 联系表单提交请求 */
export interface ContactSubmissionRequest extends ContactFormData {
  /** 提交时间戳 */
  submittedAt: number;
  /** 来源页面 */
  source: string;
}

/** 联系表单提交响应 */
export interface ContactSubmissionResponse {
  /** 提交成功标识 */
  success: boolean;
  /** 联系记录ID */
  contactId: string;
  /** 响应消息 */
  message: string;
}