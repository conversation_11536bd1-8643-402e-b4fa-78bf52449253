<template>
  <div class="app-container">
    <div class="exception-management">
      <div class="search-container">
        <el-form :inline="true" :model="searchForm" ref="queryFormRef" label-width="96px">
          <el-form-item label="损益单号" prop="profitLossCode">
            <el-input v-model="searchForm.profitLossCode" placeholder="请输入损益单号" clearable class="!w-[256px]" />
          </el-form-item>
          <el-form-item label="商品" prop="productSearch">
            <el-input v-model="searchForm.productSearch" placeholder="请输入商品名称/编码" clearable class="!w-[256px]" />
          </el-form-item>
          <el-form-item label="YSN码" prop="ysnCode">
            <el-input v-model="searchForm.ysnCode" placeholder="请输入YSN码" clearable class="!w-[256px]" />
          </el-form-item>
          <el-form-item>
            <el-button v-hasPerm="['wms:abnormal_ysn_list']" type="primary" @click="handleSearch" :loading="loading">查询</el-button>
            <el-button v-hasPerm="['wms:abnormal_ysn_list']" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-card shadow="never" class="table-container">
        <el-table :data="tableData" border v-loading="loading" highlight-current-row stripe>
          <template #empty>
            <el-empty v-if="!tableData.length && !loading" description="暂无数据" />
          </template>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="ysnCode" label="YSN码" min-width="120" show-overflow-tooltip />
          <el-table-column prop="productCode" label="商品编码" min-width="120" show-overflow-tooltip />
          <el-table-column prop="productName" label="商品名称" min-width="180" show-overflow-tooltip />
          <el-table-column prop="weight" label="实际重量" min-width="100" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.weight || '-' }}
            </template>
          </el-table-column>
<!--          <el-table-column prop="originalWeight" label="原始重量" min-width="100" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.originalWeight || '-' }}
            </template>
          </el-table-column>-->
          <el-table-column prop="profitLossCode" label="损益单号" min-width="140" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.profitLossCode || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="ysnStatusDesc" label="YSN状态" min-width="100" fixed="right">
            <template #default="scope">
              <div class="purchase">
                <div v-if="scope.row.ysnStatus === 0" class="purchase-status purchase-status-color0">
                  {{ scope.row.ysnStatusDesc }}
                </div>
                <div v-else-if="scope.row.ysnStatus === 1" class="purchase-status purchase-status-color1">
                  {{ scope.row.ysnStatusDesc }}
                </div>
                <div v-else-if="scope.row.ysnStatus === 2" class="purchase-status purchase-status-color2">
                  {{ scope.row.ysnStatusDesc }}
                </div>
                <div v-else-if="scope.row.ysnStatus === 3" class="purchase-status purchase-status-color3">
                  {{ scope.row.ysnStatusDesc }}
                </div>
                <div v-else class="purchase-status">
                  {{ scope.row.ysnStatusDesc || '-' }}
                </div>
              </div>
            </template>
          </el-table-column>
<!--          <el-table-column prop="warehouseAreaName" label="库区名称" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.warehouseAreaName || '-' }}
            </template>
          </el-table-column>-->
        </el-table>

        <pagination v-model:total="total" v-model:page="searchForm.page"
          v-model:limit="searchForm.limit" @pagination="handleQuery" />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import YsnManagementApi, { AbnormalYsnItem, QueryAbnormalYsnParams } from '@/modules/wms/api/abnormalYSN';
import { ElMessage } from 'element-plus';
import Pagination from '@/core/components/Pagination/index.vue';

defineOptions({
  name: "AbnormalYsnManagement",
  inheritAttrs: false,
});

// 搜索表单
const searchForm = reactive<QueryAbnormalYsnParams>({
  profitLossCode: '',
  productSearch: '',
  ysnCode: '',
  page: 1,
  limit: 20,
});

const tableData = ref<AbnormalYsnItem[]>([]);
const total = ref(0);
const loading = ref(false);
const queryFormRef = ref();

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = { ...searchForm };
    const res = await YsnManagementApi.queryAbnormalYsnPageList(params);
    tableData.value = res.records || [];
    total.value = parseInt(res.total) || 0;

  } catch (error) {
    console.error('查询异常YSN列表失败', error);
    ElMessage.error('查询失败');
  } finally {
    loading.value = false;
  }
};

const handleQuery = (pagination?: any) => {
  if (pagination) {
    searchForm.page = pagination.page;
    searchForm.limit = pagination.limit;
  }
  fetchData();
};

const handleSearch = () => {
  searchForm.page = 1;
  fetchData();
};

const handleReset = () => {
  queryFormRef.value?.resetFields();
  searchForm.profitLossCode = '';
  searchForm.productSearch = '';
  searchForm.ysnCode = '';
  searchForm.page = 1;
  fetchData();
};

const handlePageChange = (page: number) => {
  searchForm.page = page;
  fetchData();
};

const handleSizeChange = (size: number) => {
  searchForm.limit = size;
  searchForm.page = 1;
  fetchData();
};

onMounted(fetchData);
</script>

<style lang="scss" scoped>
.exception-management {
  .search-container {
    margin-bottom: 16px;
  }

  .table-container {
    margin-top: 8px;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .purchase {
    display: flex;
    justify-content: center;

    .purchase-status {
      display: inline-block;
      padding: 0 10px;
      height: 24px;
      line-height: 24px;
      border-radius: 12px;
      font-size: 12px;
      color: #fff;
      text-align: center;
    }

    .purchase-status-color0 {
      background-color: #909399;
    }

    .purchase-status-color1 {
      background-color: #409EFF;
    }

    .purchase-status-color2 {
      background-color: #67C23A;
    }

    .purchase-status-color3 {
      background-color: #E6A23C;
    }

    .purchase-status-color4 {
      background-color: #F56C6C;
    }
  }
}
</style>
