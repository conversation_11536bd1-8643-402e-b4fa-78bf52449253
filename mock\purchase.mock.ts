import { defineMockPms } from "./base";
import {supplierList} from "@/modules/pms/api/purchase";

export default defineMockPms([

  {
        url: "product/productCategoryList",
        method: ["GET"],
        body: {
            code: 0,
            data: [
                {
                    productCategoryCode:'111',
                    productCategoryName:'shuiguo1',
                    children:[
                        {
                            productCategoryCode:'1111',
                            productCategoryName:'shuiguo001',
                            children:[
                                {
                                    productCategoryCode:'11111',
                                    productCategoryName:'shuiguo0013',
                                },
                                {
                                    productCategoryCode:'11112',
                                    productCategoryName:'shuiguo0014',
                                }
                            ]
                        },
                        {
                            productCategoryCode:'1112',
                            productCategoryName:'shuiguo002',
                            children:[
                                {
                                    productCategoryCode:'11121',
                                    productCategoryName:'shuiguo0025',
                                },
                                {
                                    productCategoryCode:'11122',
                                    productCategoryName:'shuiguo0026',
                                },
                            ],
                        },
                    ],
                },
                {
                    productCategoryCode:'222',
                    productCategoryName:'shuiguo2',
                    children:[
                        {
                            productCategoryCode:'2221',
                            productCategoryName:'shuiguo22',
                            children:[
                                {
                                    productCategoryCode:'22221',
                                    productCategoryName:'shuiguo222',
                                },
                            ],
                        },
                    ],
                },
            ],
            msg: "一切ok",
        },
    },

  {
    url: "product/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
          {
            id: '1',
            mainImageUrl: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
            productCode: "1000",
            productName: "采购商品1",
            productSpecName: 'we',
            conversionRelSecondUnitName: "瓶",
            fullCategoryName: "fen",
            isStandard: 1,
            supplier:  [
                {
                    supplierCode: '1',
                    supplierName: "供应商1",
                },
                {
                    supplierCode: '2',
                    supplierName: "供应商2",
                },
                {
                    supplierCode: '3',
                    supplierName: "供应商3",
                },
            ],
            createTime: "2021-03-25 12:39:54",
            status: 1,
          },
          {
              id: '2',
              mainImageUrl: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
              productCode: "2000",
              productName: "采购商品2",
              productSpecName: 'we2',
              conversionRelSecondUnitName: "斤",
              fullCategoryName: "dsfd",
              isStandard: 0,
              supplier: [],
              createTime: "2021-03-25 12:39:54",
              status: 0,
          },
        ],
        total: '2',
      },
      msg: "一切ok",
    },
  },

  // 批量下架商品
  {
      url: "product/product/batchOffShelf",
      method: ["POST"],
      body:{
          code: 0,
          data: null,
          msg: "下架商品成功",
      },
  },

    // 批量上架商品
    {
        url: "product/product/batchOnShelf",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "上架商品成功",
        },
    },

    // 单个上架/下架商品
    {
        url: "product/product/updateStatus",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "上架/下架商品成功",
        },
    },

  // 删除采购商品
  {
      url: "product/product/delete",
      method: ["POST"],
      body:{
          code: 0,
          data: null,
          msg: "删除采购商品成功",
      },
  },

  //  根据商品id查询采购商品详情
  {
      url: "product/product/detail",
      method: ["POST"],
      body: {
          code: 0,
          data:{
              id: '2',
              firstCategoryId: '4',
              secondCategoryId: '5',
              thirdCategoryId: '7',
              productName: '商品名称称',
              isStandard: 0,
              productUnitId: 1,
              conversionRelFirstNum: 11,
              conversionRelSecondNum: 22,
              conversionRelSecondUnitId: 2,
              length: 2,
              width: 2,
              height: 2,
              volume: 2,
              weight: 2,
              productBrandId: 2,
              shelfLife: 12,
              shelfLifeUnit: 1,
              lossRatio: 15,
              storageCondition: '存储条件',
              remark: 'remark',
              imageUrlList: [],
              status:1,
              supplierList:[
                  {
                      id: '11',
                      supplierCode: 'gfgfg',
                      supplierName: '供应商名称',
                      supplierWarehouseName: '22',
                      isDefault:0,
                  },
                  {
                      id: '22',
                      supplierCode: 'gfg22fg',
                      supplierName: '供应商名称22',
                      supplierWarehouseName: '22',
                      isDefault:1,
                  }
              ]
          },
          msg: "一切ok",
      },
  },

    //  添加采购商品
    {
        url: "product/product/save",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

    //  编辑采购商品
    {
        url: "product/product/update",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

    // 设置供应商
    {
        url: "product/product/batchUpdateProductSupplier",
        method: ["POST"],
        body: {
            code: 0,
            data: null,
            msg: "设置供应商成功",
        },
    },

    // 设置默认供应商
    {
        url: "product/product/batchUpdateProductDefaultSupplier",
        method: ["POST"],
        body: {
            code: 0,
            data: null,
            msg: "设置默认供应商成功",
        },
    },


  // 新增采购商品
  {
    url: "product/add",
    method: ["POST"],
    body: {
        code: 0,
        data: null,
        msg: "新增采购商品成功",
    },
  },

]);
