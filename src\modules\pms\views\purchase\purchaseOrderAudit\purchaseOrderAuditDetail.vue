<template>
    <div class="app-container">
        <div class="purchaseOrderDetail" v-loading="loading">
            <div class="page-title">
               <div class="purchase-title">
                  <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                  <div> {{t('purchaseOrder.label.orderCode')}}：{{form.orderCode}}</div>
               </div>
                <div class="purchase">
                    <div class="purchase-status purchase-status-color1" v-if="form.approveStatus==1">{{t('purchaseOrder.approveStatusOption[1]')}}</div>
                    <div class="purchase-status purchase-status-color3" v-if="form.approveStatus==2">{{t('purchaseOrder.approveStatusOption[2]')}}</div>
                    <div class="purchase-status purchase-status-color5" v-if="form.approveStatus==3">{{t('purchaseOrder.approveStatusOption[3]')}}</div>
                </div>
                <div class="item">{{$t('purchaseOrder.label.approveUserName')}}：{{form.approveUserName || '-'}}</div>
                <template v-if="form.approveStatus!==1">

                <div class="item">{{$t('purchaseOrder.label.approveTime')}}：{{form.approveTime?parseDateTime(form.approveTime, "dateTime"): '-'}}</div>
                <div class="item">{{$t('purchaseOrder.label.approveRemark')}}：{{form.approveRemark || '-'}}</div>
                </template>
            </div>
            <div class="page-content">
                <el-form
                        :model="form"
                        label-width="96px"
                        label-position="right"
                >
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("purchaseOrder.label.basicInformation") }}
                        </div>
                    </div>
                    <div class="grad-row">

                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.purchaseTheme')" prop="purchaseTheme" >
                                    {{form.purchaseTheme || '-'}}

                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.orderType')">
                                    <span v-if="form.orderType==1">{{t('purchaseOrder.orderTypeList.suppliersDirectSupply')}}</span>
                                    <span v-if="form.orderType==2">{{t('purchaseOrder.orderTypeList.marketDirectSupply')}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.purchaseUserName')">
                                    {{form.purchaseUserName}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.planDeliveryDate')">
                                    {{parseDateTime(form.planDeliveryDate, "date")}}
                                </el-form-item>
                            </el-col>

                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.storehouse')">
                                    {{form.warehouseName }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.orderSource')">
                                    <span v-if="form.orderSource==1">{{t('purchaseOrder.orderSourceList.manuallyAddPurchaseOrder')}}</span>
                                    <span v-if="form.orderSource==2">{{t('purchaseOrder.orderSourceList.purchaseTask')}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.createTime')">
                                    {{parseDateTime(form.createTime, "dateTime")}}
                                </el-form-item>
                            </el-col>

                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.createUserName')">
                                    {{form.createUserName }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" v-if="form.orderType==1">
                                <el-form-item :label="$t('purchaseOrder.label.supplierName')">
                                    {{form.supplierName }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("purchaseOrder.label.productListInformation") }}
                        </div>
                    </div>
                    <div>
                        <el-table
                                ref="tableSumRef1"
                                :data="form.purchaseOrderDetailVOList"
                                :summary-method="getSum"
                                :span-method="arraySpanMethod"
                                show-summary
                                class="purchaseOrderDetail-table"
                        >
                            <el-table-column type="index" :label="$t('common.sort')" width="60"  align="center"/>
<!--                            <el-table-column :label="$t('purchaseOrder.label.productImg')" min-width="150" show-overflow-tooltip>-->
<!--                                <template #default="scope">-->
<!--                                    <div class="product-div">-->
<!--                                        <div class="picture">-->
<!--                                            <img :src="scope.row.imagesUrls" alt="">-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </template>-->
<!--                            </el-table-column>-->
                            <el-table-column :label="$t('purchaseOrder.label.productName')" prop="productName" show-overflow-tooltip/>
                            <el-table-column :label="$t('purchaseOrder.label.unitName')" prop="unitName" show-overflow-tooltip/>
                            <el-table-column :label="$t('purchaseOrder.label.onHandStock')"  prop="inventoryCount" show-overflow-tooltip align="right">
                                <template #default="scope">
                                    <span>{{scope.row.inventoryCount || ' -'}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                              :label="$t('purchaseOrder.label.planPurchaseCount')"
                              prop="planPurchaseCount"
                              show-overflow-tooltip
                              align="right"
                            >
                                <template #default="scope">
                                  <span>
                                    {{ scope.row.planPurchaseCount || '-'}}
                                  </span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('purchaseOrder.label.purchasePrice')"  prop="planPurchasePrice" show-overflow-tooltip align="right">
                                <template #default="scope">

                                         <span v-if="form.orderType==2 || (form.orderType==1 && scope.row.currencyCode == 'CNY')">￥</span>
                                          <span v-else-if="!scope.row.currencyCode">￥</span>
                                         <span v-else>$</span>
                                        {{scope.row.planPurchasePrice || '-'}}

                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('purchaseOrder.label.planPurchaseAmount')" prop="planPurchaseAmount" show-overflow-tooltip align="right">
                                <template #default="scope">

                                        <span v-if="form.orderType==2 || (form.orderType==1 && scope.row.currencyCode == 'CNY')">￥</span>
                                            <span v-else-if="!scope.row.currencyCode">￥</span>
                                        <span v-else>$</span>
                                        {{scope.row.planPurchaseAmount || '-'}}

                                </template>
                            </el-table-column>
<!--                            <el-table-column :label="$t('purchaseOrder.label.receivedCount')" v-if="form.orderPurchaseStatus!==1 && form.orderPurchaseStatus!==0 && form.orderPurchaseStatus!==4" prop="receivedCount" show-overflow-tooltip align="right"></el-table-column>-->
<!--                            <el-table-column :label="$t('purchaseOrder.label.receivedAmount')" v-if="form.orderPurchaseStatus!==1 && form.orderPurchaseStatus!==0 && form.orderPurchaseStatus!==4" prop="receivedAmount" show-overflow-tooltip align="right">-->
<!--                                <template #default="scope">-->
<!--                                    <span v-if="form.orderType==2 || (form.orderType==1 && scope.row.currencyCode == 'CNY')">￥</span>-->
<!--                                    <span v-else-if="!scope.row.currencyCode">￥</span>-->
<!--                                    <span v-else>$</span>-->
<!--                                    {{scope.row.receivedAmount}}-->
<!--                                </template>-->
<!--                            </el-table-column>-->
                            <el-table-column :label="$t('purchaseOrder.label.remark')" prop="remark" show-overflow-tooltip>
                                <template #default="scope">
                                    <span v-if="(form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4)">{{scope.row.remark}}</span>
                                    <span v-else>-</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="total-amount">
                        <el-row class="flex-center-start">
                            <el-col :span="6">
                                <el-form-item :label="$t('purchaseOrder.label.totalPurchaseAmount')" prop="totalPurchaseAmount" >
                                    <span v-if="form.currencyCode == 'CNY'">￥</span>
                                    {{form.totalPurchaseAmount}}
                                </el-form-item>

                            </el-col>
                            <el-col :span="10">
                                <el-form-item :label="$t('purchaseOrder.label.discountType')" prop="discountType" >
                                    {{$t(`purchaseOrder.label.discountTypeOption[${form.discountType || 1}]`)}}

                                    <template v-if="form.discountType===2 || form.discountType===3">
                                        <span v-if="form.currencyCode == 'CNY'">￥</span> {{form.discountValue}}
                                    </template>
                                    <template v-if="form.discountType===3">%</template>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.totalDiscountedAmount')" prop="totalDiscountedAmount" >
                                    <span v-if="form.currencyCode == 'CNY'">￥</span> {{form.totalDiscountedAmount || '-'}}


                                </el-form-item>

                            </el-col>
                        </el-row>
                    </div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("purchaseOrder.label.otherInformation") }}
                        </div>
                    </div>
                    <div>
                        <el-row>
                            <el-col :span="24" class="file-div">
                                <el-form-item :label="$t('purchaseOrder.label.orderAttachmentFiles')">
                                    <upload-multiple
                                            :isDelete="false"
                                            :showUploadBtn="false"
                                            :isShowTip="false"
                                            :listType="`text`"
                                            :tips="''"
                                            :fileSize="10"
                                            :fileType="['rar','zip','docx','xls','xlsx','pdf','jpg','png','jpeg']"
                                            :isPrivate="`public-read`"
                                            :modelValue="form.orderAttachmentFiles"
                                            @update:model-value="onChangeMultiple"
                                            ref="detailPicsRef"
                                            :limit="10"
                                            :formRef="formUpdateRef"
                                            class="modify-multipleUpload"
                                            name="detailPic">
                                        <template #default="{ file }">
                                            点击上传
                                        </template>
                                    </upload-multiple>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item :label="$t('purchaseOrder.label.remark')">
                                    <span v-if="form.remark" style="word-break:break-all;">{{form.remark}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("purchaseOrder.label.approveRemark") }}
                        </div>
                    </div>
                    <div>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label-width="0" prop="approveRemark">
                                    <el-input
                                      v-if="pageType==='audit'"
                                      :rows="4"
                                      type="textarea"
                                      show-word-limit
                                      v-model="form.approveRemark"
                                      :placeholder="$t('purchaseOrder.rules.approveRemark')"
                                      maxlength="200"
                                      clearable
                                    />
                                    <template v-else>
                                        <span >{{ form.approveRemark ||'-' }}</span>
                                    </template>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>



                </el-form>
            </div>
            <div class="page-footer">
                <el-button  @click="handleClose">{{ $t("common.reback") }}</el-button>
                <el-button  type="danger"  v-if="pageType==='audit'"  :loading="submitLoading"  @click="handleAudit(false)">  {{ $t("purchaseOrder.button.reject") }}</el-button>

                <el-button  type="primary" v-if="pageType==='audit'" :loading="submitLoading" @click="handleAudit(true)">{{ $t("purchaseOrder.button.pass") }}</el-button>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">


    defineOptions({
        name: "purchaseOrderAuditDetail",
        inheritAttrs: false,
    });

    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import PurchaseOrderAPI, {
         PurchaseOrderFrom
    } from "@/modules/pms/api/purchaseOrder";
    import { parseDateTime } from "@/core/utils/index.js";



    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const fromRef = ref()
    const submitLoading = ref(false)
    const queryFormRef = ref(ElForm);
    const roleFormRef = ref(ElForm);
    const loading = ref(false);
    const id = route.query.id;
    const orderCode = route.query.orderCode;
    const pageType=route.query.pageType;
    const tableSumRef1 = ref();
    const form = reactive<PurchaseOrderFrom>({
        purchaseOrderDetailVOList:[]
    });


    async function handleClose(){
        await tagsViewStore.delView(route);
        router.go(-1);
    }


    /** 查询采购单详情 */
    function queryPurchaseOrderDetail(){
        loading.value = true;
        let params = {
            id:id,
            orderCode:orderCode,
        }
        PurchaseOrderAPI.queryPurchaseOrderDetail(params)
            .then((data) => {
                Object.assign(form,data)
            })
            .finally(() => {
                loading.value = false;
            });
    }

    function arraySpanMethod() {
        setTimeout(() => {
            if (tableSumRef1.value) {
                let current = tableSumRef1.value.$el
                    .querySelector(".el-table__footer-wrapper")
                    .querySelector(".el-table__footer");
                let cell = current.rows[0].cells;
                cell[0].colSpan = "9";
            }
        }, 50)
    }

    /** 合计 */
    function getSum(param) {
        const { columns, data } = param;
        const sums = [];
        let currenty = ''
        if(form.orderType==2 || (form.orderType==1 && form.purchaseOrderDetailVOList[0].currencyCode == 'CNY')){
            currenty = '￥'
        }else if (!form.purchaseOrderDetailVOList[0]?.currencyCode) {
            currenty = "￥";
        }
        else{
            currenty = '$'
        }
        columns.forEach((column, index) => {
           /* if (index === 0) {
                sums[index] = '合计:';
                return;
            }*/
            if(form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3){
                sums[0] ="合计:\u3000已收货量 " + form.totalReceivedCount +  "\u3000已收货金额  "+ currenty + form.totalReceivedAmount;
            }else{
                if(form.orderPurchaseStatus==1 || form.orderPurchaseStatus==4){
                    sums[0] ="合计:\u3000计划采购量 " +form.totalPurchaseCount +  "\u3000计划采购金额  " + currenty +form.totalPurchaseAmount;
                }else{
                    sums[0] ="合计:\u3000计划采购量 " +'-' +  "\u3000计划采购金额  "  + currenty +'-';
                }
            }
        });
        return sums;
    }

 function handleAudit(status:boolean){
     if(status===false && !form.approveRemark){
         return ElMessage.error(t("purchaseOrder.rules.approveRemark"));
     }

     submitLoading.value = true;
        let params = {
            id:id,
            isPass:status,
            approveRemark:form.approveRemark
        }

     PurchaseOrderAPI.approvePurchaseOrder(params)
        .then((data) => {
            handleClose()
        })
        .finally(() => {
            submitLoading.value = false;
        });

}



    onMounted(() => {
        queryPurchaseOrderDetail();
    });
</script>
<style scoped lang="scss">
    .purchaseOrderDetail {
        background: #FFFFFF;
        border-radius: 4px;
    }
</style>
<style lang="scss">
    .purchaseOrderDetail {
        .file-div {
            .el-form-item__content {
                display: block;
                margin-top: -38px;
            }
            .el-upload-list__item .is-success:hover{
                .el-upload-list__item-status-label {
                    width: 0px !important;
                    height: 0px !important;
                    display: none !important;
                }
                .el-icon {
                    width: 0px !important;
                    height: 0px !important;
                    display: none !important;
                }
                .el-icon--close-tip{
                    width: 0px !important;
                    height: 0px !important;
                    display: none !important;
                }
            }
            .el-upload-list__item-status-label {
                width: 0px !important;
                height: 0px !important;
                display: none !important;
            }
            .el-icon {
                width: 0px !important;
                height: 0px !important;
                display: none !important;
            }
            .el-icon--close-tip{
                width: 0px !important;
                height: 0px !important;
                display: none !important;
            }

        }
        .total-amount{
            margin-top: 20px;
        }
        .page-title{
            .item{
                margin-left: 30px;
            }
        }
        .el-form-item--default{
          margin-bottom: 0px !important;
        }
    }
    .purchaseOrderDetail-table .el-table__footer{
        .cell{
            display: flex;
            justify-content: flex-end;
        }

    }
</style>
