export default {
  purchaseReceivables: {
    pending: "待收款",
    completed: "已结清",
    supplyNo: "供应商",
    purchaseNo: "采购单号",
    refundNo: "退款工单",
    supplyCompanyName: "供应商名称",
    purchaseBuyer: "采购员",
    receivableAmount: "应收金额",
    receivableActuralAmount: "实收金额",
    billGenerationTime: "生成账单时间",
    billCompletedTime: "结清时间",
    operation: "操作",
    search: "搜索",
    reset: "重置",
    details: "明细",
    receipt: "收款",
    resultConfirmation: "结清归档",
    print: {
      billNumber: "账单编号",
      purchaseOrderNo: "采购单号",
      purchaser: "采购员",
      status: "单据状态",
      supplier: "供应商",
      remarks: "备注",
      sequence: "序号",
      workOrderNo: "工单号",
      receivableAmount: "应收金额",
      applicant: "申请人",
      receiptType: "收款类型",
      receiptMethod: "收款方式",
      receiptAmount: "收款金额",
      receiver: "收款人",
      receiptTime: "收款时间",
      voucherUploadTime: "上传凭证时间"
    },
    pleaseEnter: "请输入",
    pleaseSelect: "请选择",
    startDate: "开始日期",
    endDate: "结束日期",
    to: "至",
    supplier: "供应商",
    billNo: "账单编号",
    back: "返回",
    refresh: "刷新",
    submit: "提交",
    cancel: "取消",
    confirm: "确定",
    title: "采购退款账单",
    title2: "采购退款账单",
    title3: "采购退款账单(已结清)",
    message: {
      confirmSettlement: "确认要结清归档该账单吗?",
      tip: "提示",
      settlementSuccess: "结清归档成功",
      operationCancelled: "已取消操作",
      submitSuccess: "提交成功",
      paymentSuccess: "收款成功",
      paymentFailed: "收款失败",
      uploadFailed: "上传凭证失败",
      onlyOneFile: "只能上传一个文件",
      getDetailFailed: "获取账单详情失败",
    },
    paymentMethod: {
      transfer: "转账",
      cash: "现金",
      alipay: "线下支付宝",
      wechat: "线下微信",
    },
    settlementType: {
      payment: "付款",
      offsetSettlement: "冲账结算",
    },
    currency: {
      CNY: "人民币",
      USD: "美元",
    },
    detail: {
      billNumber: "账单编号",
      refundOrder: "退款工单",
      creator: "采购员",
      purchaseOrderNo: "采购单号",
      settlementTime: "结清时间",
      basicInfo: "基本信息",
      settlement: "结算",
      settlementType: "结算类型",
      payablesBillNo: "应付账单编号",
      paymentMethod: "收款方式",
      paymentAmount: "收款金额",
      currency: "结算币种",
      exchangeRate: "汇率",
      voucherUploadTime: "凭证上传时间",
      paymentTime: "收款时间",
      paymentVoucher: "付款凭证",
      remarks: "备注",
      operationRecords: "单据操作记录",
      operationTime: "操作时间",
      operationType: "操作类型",
      operator: "操作人",
      settled: "已结清",
      generationTime: "生成账单时间",
      supplier: "供应商",
      cancel: "取消",
    },
    collection: {
      title: "收款",
      paymentMethod: "收款方式",
      paymentAmount: "收款金额",
      currency: "结算币种",
      exchangeRate: "汇率",
      paymentTime: "收款时间",
      paymentVoucher: "收款凭证",
      remarks: "备注",
      uploadVoucher: "上传收款凭证",
      voucherTip: "支持jpg、jpeg、png、pdf格式，单个文件不超过10M",
      submitSuccess: "提交成功",
      refundDocuments: "退款单据",
      sequence: "序号",
      applicationDate: "申请日期",
      total: "合计 应收金额"
    },
    validation: {
      methodRequired: "请选择收款方式",
      amountRequired: "请输入收款金额",
      currencyRequired: "请选择结算币种",
      timeRequired: "请选择收款时间",
      voucherRequired: "请上传收款凭证",
      validNumber: "请输入有效的数字",
      rateGreaterThanZero: "汇率必须大于0",
      rateDecimalLimit: "汇率最多四位小数",
    },
    error: {
      fetchFailed: "获取数据失败",
      submitFailed: "提交失败",
      fetchListFailed: "获取应收账单列表失败"
    },
    button: {
      receipt: "收款",
      confirm: "确认",
      cancel: "取消",
      print: "打印",
    },
  },
};
