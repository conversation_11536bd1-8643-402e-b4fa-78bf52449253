<template>
  <div class="component-upload-image" v-loading="fileLoading">
    <!-- 图片上传 -->
    <section v-if="actionType === 'upload'">
      <el-upload
        v-loading.fullscreen.lock="fullscreenLoading"
        multiple
        :disabled="disabledVal"
        :drag="ifDrag && showUploadBtn"
        :list-type="listType"
        :on-success="handleUploadSuccess"
        :before-upload="handleBeforeUpload"
        :http-request="uploadFile"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        ref="imageUpload"
        :before-remove="handleDelete"
        :file-list="fileList"
        :on-preview="handlePictureCardPreview"
        :class="{ hide: fileList.length >= limit }"
      >
        <div v-if="ifDrag">
          <el-icon class="el-icon--upload" v-show="showUploadBtn">
            <upload-filled />
          </el-icon>
          <div class="el-upload__text" v-show="showUploadBtn">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
        </div>
        <!-- 文件icon -->
        <template #file="{ file, index }" v-if="listType === 'icon-card'">
          <div class="item_list">
            <div
              class="el-upload-list__item-delete"
              @click="handleDelete(file)"
              v-show="showUploadBtn"
            >
              <el-icon><CloseBold /></el-icon>
            </div>
            <div class="left_item">
              <img
                src="@/core/assets/images/pdf.png"
                class="img_style"
                alt=""
                v-if="file?.name?.split('.')[1] == 'pdf'"
              />
              <img
                src="@/core/assets/images/rar.png"
                class="img_style"
                alt=""
                v-else-if="
                  file?.name?.split('.')[1] == 'rar' ||
                  file?.name?.split('.')[1] == 'zip'
                "
              />
              <img
                src="@/core/assets/images/png.png"
                class="img_style"
                alt=""
                v-else
              />
            </div>
            <div class="right_item">
              <div class="name_style">
                {{ file.name }}
              </div>
              <div
                @click="downloadFileByUrl(file)"
                class="preview_style"
                v-if="
                  file?.name?.split('.')[1] == 'rar' ||
                  file?.name?.split('.')[1] == 'zip'
                "
              >
                下载
              </div>
              <div
                v-else
                @click="handlePictureCardPreview(file)"
                class="preview_style"
              >
                预览
              </div>
            </div>
          </div>
        </template>
        <!-- 图片 -->
        <template #file="{ file, index }" v-if="listType === 'picture-card'">
          <!--图片上传后的展示-->
          <!--                <slot :file="file" :index="index">-->
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
          <label
            v-if="file.status === 'success'"
            class="el-upload-list__item-status-label"
          >
            <el-icon class="el-icon-upload-success el-icon-check" color="#fff">
              <Check />
            </el-icon>
          </label>
          <span class="el-upload-list__item-actions">
            <span
              class="el-upload-list__item-preview"
              @click="handlePictureCardPreview(file)"
            >
              <el-icon><zoom-in /></el-icon>
            </span>
            <span
              class="el-upload-list__item-delete"
              v-if="isDelete"
              @click="handleDelete(file)"
            >
              <el-icon>
                <Delete />
              </el-icon>
            </span>
          </span>
          <!--                </slot>-->
        </template>
        <el-icon
          v-if="listType === 'picture-card'"
          class="avatar-uploader-icon"
        >
          <plus />
        </el-icon>
        <el-button
          v-if="listType === 'text' && !ifDrag"
          v-show="showUploadBtn"
          type="primary"
        >
          上传文件
        </el-button>
      </el-upload>
      <!-- 上传提示 -->
      <div class="el-upload__tip" v-if="showTip">
        <template v-if="customTips">
          {{ customTips }}
        </template>
        <template v-else>
          <template v-if="tips">
            {{ tips }}
          </template>
          <template v-if="fileType">
            只能上传{{ fileType.join("/") }}文件，
          </template>
          <template v-if="fileSize">大小不超过{{ fileSize }}MB，</template>
          <template v-if="limit">
            最多上传{{ limit }}
            <span v-if="listType === 'text'">份</span>
            <span v-else-if="listType === 'icon-card'">个</span>
            <span v-else>张</span>
          </template>
        </template>
      </div>
    </section>
    <!-- 文件预览模式 -->
    <section v-else-if="actionType === 'preview'" class="preview-file-wrapper">
      <!--      <el-link
        v-for="file in fileList"
        :key="file.name"
        @click="handleFilePreviewOrDownload(file)"
      >
        {{ file.name }}
      </el-link>-->
      <el-link
        target="_blank"
        v-for="file in fileList"
        :key="file.name"
        :href="file.url"
        class="preview-file-link"
      >
        {{ previewText || file.name }}
      </el-link>
    </section>
    <!--  图片预览展示  -->
    <el-image-viewer
      v-if="dialogVisible && listType === 'picture-card'"
      :url-list="[dialogImageUrl]"
      @close="closeImg"
    />
    <el-dialog
      v-else
      v-model="dialogVisible"
      title="预览"
      width="800px"
      append-to-body
    >
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
import Loading from "@/core/assets/images/loading.gif";
const props = defineProps({
  actionType: {
    type: String,
    default: "upload", // upload:上传，preview:预览
  },
  modelValue: [String, Object, Array],
  // 是否展示删除图标
  isDelete: {
    type: Boolean,
    default: true,
  },
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  listType: {
    //文件列表的类型
    type: String,
    default: "picture-card",
  },
  tips: {
    type: String,
    default: "",
  },
  customTips: {
    type: String,
    default: "",
  },
  isPrivate: {
    type: String,
    default: "default", // default:私密， public-read:公开
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  disabledVal: {
    type: Boolean,
    default: false,
  },
  formRef: {
    type: Object,
    default: () => {},
  },
  name: {
    type: String,
    default: "",
  },
  showUploadBtn: {
    type: Boolean,
    default: true,
  },
  ifDrag: {
    type: Boolean,
    default: false,
  },
  previewText: { // 预览文本
    type: String,
    default: "",
  },
});

const fullscreenLoading = ref(false);
const fileLoading = ref(false);
const emit = defineEmits(["files", "update:modelValue","file"]);
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const fileList = ref([]);
// 添加上传队列管理
const uploadQueue = ref(new Map()); // 存储正在上传的文件
const loadingInstance = ref(null); // 统一管理Loading实例
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

watch(
  () => props.modelValue,
  async (val) => {
    if (val && val.length > 0) {
      console.log("watch props.modelValue----", typeof val, val);
      // 首先将值转为数组
      fileLoading.value = true;
      const list = Array.isArray(val) ? val : JSON.parse(props.modelValue);
      // 处理数组项
      const processedList = [];
      // 根据list长度填充loading图片，避免图片加载闪烁
      fileList.value = Array.from({ length: list.length }, (_, index) => ({
        url: Loading,
        name: "加载中...",
        loading: true,
        id: `loading-${index}` // 添加唯一标识
      }));
      for (const item of list) {
        try {
          const fileInfo = item;
          const previewRes = await previewSingle(
            fileInfo.bucket,
            fileInfo.fileName,
            fileInfo.originalFileName
          );
          processedList.push({
            name: fileInfo.originalFileName,
            url: previewRes?.url || fileInfo.fileName, // 优先使用预览链接
            // rawInfo: item // 保存原始信息
            rawInfo: JSON.stringify(item), // 保存原始信息
          });
          
        } catch (e) {
          // 如果不是JSON字符串，则按普通URL处理
          processedList.push({
            name: item,
            url: item,
            // rawInfo: item
            rawInfo: JSON.stringify(item),
          });
        } 
        finally {
          fileLoading.value = false;
        }
      }
      fileList.value = processedList;
      console.log("fileList.value", fileList.value);
    } else {
      fileList.value = [];
    }
  },
  { deep: true, immediate: true }
);

async function uploadFile(options) {
  const fileId = `${options.file.name}_${options.file.size}_${Date.now()}`;
  
  // 添加到上传队列
  uploadQueue.value.set(fileId, {
    file: options.file,
    status: 'uploading'
  });
  
  // 如果是第一个文件开始上传，显示Loading
  if (uploadQueue.value.size === 1) {
    loadingInstance.value = ElLoading.service({
      lock: true,
      text: "正在上传文件，请稍候...",
      background: "rgba(0, 0, 0, 0.7)",
    });
  }

  try {
    const res = await commonUpload(options.file, "image", props.isPrivate);
    if (res.res.status === 200) {
      // 原子操作：增加计数器
      number.value++;
      const fileInfo = JSON.parse(res.url);
      uploadList.value.push({
        name: fileInfo.originalFileName,
        url: fileInfo.fileName,
        rawInfo: res.url, // 保存完整的原始信息
        file: options.file, // 保存原始file
      });
      
      // 更新队列状态
      uploadQueue.value.set(fileId, {
        file: options.file,
        status: 'success'
      });

    } else {
      ElMessage.error(res.msg);
      number.value--;
      // 更新队列状态为失败
      uploadQueue.value.set(fileId, {
        file: options.file,
        status: 'error'
      });
    }
  } catch (error) {
    ElMessage.error("上传失败");
    // 更新队列状态为失败
    uploadQueue.value.set(fileId, {
      file: options.file,
      status: 'error'
    });
  } finally {
    // 从队列中移除
    uploadQueue.value.delete(fileId);
    
    // 检查是否所有文件都已处理完成
    if (uploadQueue.value.size === 0) {
      // 关闭Loading
      if (loadingInstance.value) {
        loadingInstance.value.close();
        loadingInstance.value = null;
      }
      // 处理上传完成逻辑
      uploadedSuccessfully();
    }
  }
}

// 修改文件预览处理
function handlePictureCardPreview(file) {
  if (props.isPrivate === "default") {
    try {
      const fileInfo =
        typeof file.rawInfo === "string"
          ? JSON.parse(file.rawInfo)
          : file.rawInfo;

      // 获取文件扩展名
      const fileExt = fileInfo.originalFileName.split(".").pop().toLowerCase();

      // 调用预览接口获取预览地址
      previewSingle(
        fileInfo.bucket,
        fileInfo.fileName,
        fileInfo.originalFileName
      )
        .then((res) => {
          if (!!res) {
            // 图片格式
            if (["png", "jpg", "jpeg", "gif"].includes(fileExt)) {
              dialogImageUrl.value = res.url;
              dialogVisible.value = true;
            }
            // PDF格式
            else if (fileExt === "pdf") {
              window.open(res.url, "_blank");
            }
            // 其他格式直接下载
            else {
              const link = document.createElement("a");
              link.href = res.url;
              link.download = fileInfo.originalFileName;
              link.target = "_blank";
              console.log("link.download", link.download);
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }
          } else {
            window.open(file.url);
            // ElMessage.error("文件预览失败");
          }
        })
        .catch(() => {
          // ElMessage.error("文件预览失败");
        });
    } catch (e) {
      // ElMessage.error("文件预览失败");
    }
  } else {
    // 公共文件，直接使用 url
    try {
      // 从原始信息中获取文件信息
      const fileInfo =
        typeof file.rawInfo === "string"
          ? JSON.parse(file.rawInfo)
          : file.rawInfo;
      console.log("fileInfo-------", fileInfo);
      const fileExt = fileInfo.originalFileName.split(".").pop().toLowerCase();

      if (["png", "jpg", "jpeg", "gif"].includes(fileExt)) {
        dialogImageUrl.value = file.url;
        dialogVisible.value = true;
      } else if (fileExt === "pdf") {
        window.open(file.url, "_blank");
      } else {
        const link = document.createElement("a");
        link.href = file.url;
        link.download = fileInfo.originalFileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (e) {
      // 如果无法解析原始信息，尝试直接使用文件URL
      window.open(file.url, "_blank");
    }
  }
}
// 修改数据转换方法
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (list[i].rawInfo) {
      strs += list[i].rawInfo + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}
// 上传前loading加载
function handleBeforeUpload(file) {
  let isValidType = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name
        .slice(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
    }
    isValidType = props.fileType.some((type) => {
      const lowerType = type.toLowerCase();
      return fileExtension === lowerType;
    });
  }

  if (!isValidType) {
    ElMessage.error(
      `文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`
    );
    return false;
  }

  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  return true;
}
// 文件个数超出
function handleExceed() {
  ElMessage.error(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  // if (res.code === 200) {
  //   uploadList.value.push({ name: res.fileName, url: res.fileName });
  //   uploadedSuccessfully();
  // } else {
  //   number.value--;
  //   proxy.$modal.closeLoading();
  //   proxy.$modal.msgError(res.msg);
  //   proxy.$refs.imageUpload.handleRemove(file);
  //   uploadedSuccessfully();
  // }
}

// 删除图片
function handleDelete(file) {
  if (props.disabledVal) return;
  const findex = fileList.value.map((f) => f.name).indexOf(file.name);
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1);
    emit("update:modelValue", rawInfoToArr(fileList.value));
    // emit("update:modelValue", listToString(fileList.value));
    
    // 在文件删除后进行表单验证
    nextTick(() => {
      props.formRef?.validateField(props.name);
    });
    return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  // 只有当所有文件都处理完成且有成功上传的文件时才执行
  if (uploadList.value.length > 0) {
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value);
    console.log("===fileList===" + fileList.value);
    
    // 重置状态
    uploadList.value = [];
    number.value = 0;

    // 发送更新事件
    emit("update:modelValue", rawInfoToArr(fileList.value));
    emit("files", fileList.value);
    let arrFile = [];
    fileList.value.forEach((item) => {
        if (item.file) {
          arrFile.push(item.file);
        }
    });
    emit("file", arrFile);
    
    // 在文件列表更新完成后进行表单验证
    nextTick(() => {
      props.formRef?.validateField(props.name);
    });
  }
}

function rawInfoToArr(rawInfo) {
  let arr = [];
  rawInfo.forEach((item) => {
    arr.push(JSON.parse(item.rawInfo));
  });
  return arr;
}

// 上传失败
function handleUploadError() {
  ElMessage.error("上传图片失败");
  // 检查是否还有正在上传的文件，如果没有则关闭Loading
  if (uploadQueue.value.size === 0 && loadingInstance.value) {
    loadingInstance.value.close();
    loadingInstance.value = null;
  }
}
/**
 * 下载文件
 */
function downloadFile(file) {
  window.open(file.url);
}

// 关闭
function closeImg() {
  dialogVisible.value = false;
}

function downloadFileByUrl(file) {
  console.log(file);
  const downLink = (href) => {
    const link = document.createElement("a");
    link.target = "_blank";
    link.href = href;
    link.download = file.name;
    link.click();
    window.URL.revokeObjectURL(href);
  };
  try {
    const res = new XMLHttpRequest();
    res.open("GET", file.url, true);
    res.responseType = "blob";
    res.onload = function () {
      const newUrl = window.URL.createObjectURL(res.response);
      downLink(newUrl);
    };
    res.send();
  } catch (e) {
    downLink(file.url);
  }
}

function handleFilePreviewOrDownload(file) {
  const fileInfo =
    typeof file.rawInfo === "string" ? JSON.parse(file.rawInfo) : file.rawInfo;
  handleFileByType(file.url, file.name, fileInfo.originalFileName);
  /* try {
    const fileInfo = typeof file.rawInfo === "string"
      ? JSON.parse(file.rawInfo)
      : file.rawInfo;

    // Get file extension to determine how to handle the file
    const fileExt = fileInfo.originalFileName.split(".").pop().toLowerCase();

    // For private files, use the previewSingle function
    if (props.isPrivate === "default") {
      previewSingle(
        fileInfo.bucket,
        fileInfo.fileName,
        fileInfo.originalFileName
      )
        .then((res) => {
          if (res) {
            handleFileByType(res.url, fileExt, fileInfo.originalFileName);
          } else {
            ElMessage.error("文件预览失败");
          }
        })
        .catch(() => {
          ElMessage.error("获取文件预览链接失败");
        });
    } else {
      // For public files, use the direct URL
      handleFileByType(file.url, fileExt, fileInfo.originalFileName);
    }
  } catch (error) {
    console.error("文件预览或下载失败", error);
    ElMessage.error("文件预览或下载失败");
  } */
}

// Helper function to handle different file types
function handleFileByType(url, fileExt, fileName) {
  // Handle images - show in dialog
  if (["png", "jpg", "jpeg", "gif"].includes(fileExt)) {
    dialogImageUrl.value = url;
    dialogVisible.value = true;
  }
  // Handle PDFs - open in new tab
  else if (fileExt === "pdf") {
    window.open(url, "_blank");
  }
  // Handle other files - download
  else {
    window.open(url, "_blank");
    /* const link = document.createElement("a");
    link.href = url;
    link.download = fileName;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); */
  }
}

defineExpose({
  handlePictureCardPreview,
  handleDelete,
});
</script>

<style scoped lang="scss">
:deep(.hide .el-upload--picture-card) {
  display: none;
}

.el-upload__tip {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #90979e;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  display: inline-flex;
}
.preview-file-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.preview-file-link {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #762adb;
  line-height: 32px;
  text-align: left;
  font-style: normal;
}
.item_list {
  background: #f2f3f4;
  border-radius: 8px;
  display: flex;
  width: 30%;
  height: 68px;
  align-items: center;
  padding: 16px;
  position: relative;
  float: left;
  margin-right: 16px;
  margin-bottom: 5px !important;
  .el-upload-list__item-delete {
    top: 5px;
    display: block;
  }
}
.img_style {
  width: 26px;
  height: auto;
  margin-right: 8px;
  display: flex;
  align-items: center;
}
.preview_style {
  color: #762adb;
  text-align: end;
  cursor: pointer;
}
.right_item {
  overflow: auto;
  width: 100%;
  .name_style {
    display: -webkit-box; /* 将元素设置为弹性盒子 */
    -webkit-box-orient: vertical; /* 设置内容垂直排列 */
    -webkit-line-clamp: 1; /* 限制显示的行数 */
    overflow: hidden; /* 隐藏超出容器的内容 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
}
:deep(.el-upload-list) {
  overflow: auto !important;
}

:deep(.el-upload-list__item) {
  margin-bottom: 0px !important;
}
</style>
