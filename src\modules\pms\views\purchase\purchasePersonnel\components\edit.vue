<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" width="500px" @close="close">
        <el-form :model="purchasePersonnelForm" :rules="rules" ref="purchasePersonnelFormRef" label-position="top">
            <el-form-item :label="$t('purchasePersonnel.label.selecPurchaserName')" prop="userId">
               <el-select
                   v-model="purchasePersonnelForm.userId"
                   :placeholder="$t('common.placeholder.selectTips')"
                   filterable
                   clearable
               >
                    <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id"></el-option>
               </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" :loading="submitLoading" @click="submitForm">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-drawer>
</template>
<script setup>
    import PurchasePersonnelAPI from "@/modules/pms/api/purchasePersonnel";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();
    const userList = ref([]);
    const purchasePersonnelFormRef = ref();
    const initForm = () => ({
        userId: "",
    });
    const data = reactive({
        purchasePersonnelForm: initForm()
    });
    const { purchasePersonnelForm } = toRefs(data);
    const rules = reactive({
        userId: [
            {
                required: true, message: t("purchasePersonnel.rules.userId"), trigger: "blur",
            },
        ],
    });
    const  submitLoading= ref(false)

    function close() {
        emit("update:visible", false);
        reset();
    }

    function reset() {
        purchasePersonnelFormRef.value.clearValidate();
        purchasePersonnelFormRef.value.resetFields();
        purchasePersonnelForm.value = initForm();
    }

    function submitForm() {
        purchasePersonnelFormRef.value.validate((valid) => {
            if (!valid) return;
            submitLoading.value = true;
            let params = {
                userId:purchasePersonnelForm.value.userId,
            };
            PurchasePersonnelAPI.addPurchasePersonnel(params)
                .then((res) => {
                    ElMessage.success(t("purchasePersonnel.message.addSucess"));
                    close();
                    emit("onSubmit");
                })
                .finally(() => {
                    submitLoading.value = false;
                });
        });
    }


    function queryUserList() {
        PurchasePersonnelAPI.getUserList().then(data => {
            userList.value=data
        })
    }

    defineExpose({
        queryUserList
    });
</script>

<style scoped lang="scss">
    .demo-tabs{
        .el-tabs{
            width: 100%;
        }
    }

</style>
<style lang="scss">
</style>
