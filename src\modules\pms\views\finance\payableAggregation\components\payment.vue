<template>
  <el-drawer
    v-model="isVisible"
    :title="title"
    :close-on-click-modal="false"
    :size="500"
    @close="close"
    class="drawer-wrapper"
  >
    <div class="drawer-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="top"
      >
        <el-form-item
          :label="$t('payableAggregation.label.paymentMethod')"
          prop="paymentMethod"
        >
          <el-select
            v-model="formData.paymentMethod"
            :placeholder="$t('common.placeholder.selectTips')"
            clearable
          >
            <el-option
              v-for="(item, index) in paymentMethodOption"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('payableAggregation.label.paymentAmount')"
          prop="paymentAmount"
        >
          <el-input
            v-model="formData.paymentAmount"
            clearable
            :placeholder="$t('common.placeholder.inputTips')"
            :maxlength="12"
          />
        </el-form-item>
        <el-form-item
          :label="$t('payableAggregation.label.paymentTime')"
          prop="paymentTime"
        >
          <el-date-picker
            v-model="formData.paymentTime"
            type="datetime"
            value-format="x"
            :placeholder="$t('common.placeholder.selectTips')"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          :label="$t('payableAggregation.label.paymentVoucher')"
          prop="paymentVoucher"
        >
          <upload-multiple
            :tips="``"
            :fileSize="10"
            :modelValue="formData.paymentVoucher"
            @update:model-value="onChangeMultiple"
            ref="detailPicsRef"
            :limit="1"
            :formRef="formUpdateRef"
            class="modify-multipleUpload"
            name="detailPic"
          ></upload-multiple>
        </el-form-item>
        <el-form-item
          :label="$t('payableAggregation.label.remark')"
          prop="remark"
        >
          <el-input
            v-model="formData.remark"
            type="textarea"
            :placeholder="$t('common.placeholder.inputTips')"
            :rows="5"
            :maxlength="100"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="drawer-footer">
        <el-button @click="close">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ $t("common.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import API from "@/modules/pms/api/payableAggregation";

const { t } = useI18n();

const props = defineProps({
  drawerVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});
const initForm = () => ({
  payableAccountId: "",
  paymentMethod: "",
  paymentAmount: "",
  paymentTime: "",
  paymentVoucher: "",
  remark: "",
});

const state = reactive({
  loading: false,
  formRef: null,
  formData: initForm(),
  paymentMethodOption: [],
  formUpdateRef: null,
}) as any;

const { loading, formRef, formData, paymentMethodOption, formUpdateRef } =
  toRefs(state);

const rules = reactive({
  paymentMethod: [
    {
      required: true,
      message: `${t("common.placeholder.selectTips")}`,
      trigger: "change",
    },
  ],
  paymentAmount: [
    {
      required: true,
      message: `${t("common.placeholder.inputTips")}`,
      trigger: "blur",
    },
    {
      pattern: /^([1-9]\d{0,8}(\.\d{1,2})?|0\.(?!0+$)\d{1,2})$/,
      message: t("payableAggregation.rules.amount"),
      trigger: "blur",
    },
  ],
  paymentTime: [
    {
      required: true,
      message: `${t("common.placeholder.selectTips")}`,
      trigger: "change",
    },
  ],
  paymentVoucher: [
    {
      required: true,
      message: `${t("common.placeholder.selectTips")}`,
      trigger: "change",
    },
  ],
});

const emit = defineEmits(["update:drawerVisible", "onSubmit"]);

const isVisible = computed({
  get: () => {
    getPaymentMethodList();
    return props.drawerVisible;
  },
  set: (val: any) => {
    close();
  },
});

/**
 * 关闭
 */

function close() {
  emit("update:drawerVisible", false);
  reset();
}

/**
 * 重置
 */
function reset() {
  formData.value = initForm();
  formRef.value.resetFields();
  formRef.value.clearValidate();
}

/**
 * 设置数据
 * @param data
 */
function setData(data: any) {
  formData.value.payableAccountId = data.id;
}

/**
 * 提交
 */
function handleSubmit() {
  console.log(formData.value);
  formRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      let params = {
        ...formData.value,
      };
      params.paymentMethodName =
        paymentMethodOption.value?.find(
          (item: any) => item.value === formData.value.paymentMethod
        )?.label || "";
      params.paymentVoucher = JSON.stringify(params.paymentVoucher);
      console.log(params);
      API.savePayment(params)
        .then((data: any) => {
          ElMessage.success(t("payableAggregation.message.actionSuccess"));
          close();
          emit("onSubmit");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

/**
 * 处理上传
 * @param val
 */
function onChangeMultiple(val: any) {
  formData.value.paymentVoucher = val ? val : [];
  if (
    formData.value.paymentVoucher &&
    formData.value.paymentVoucher.length > 0
  ) {
    formRef.value.clearValidate("paymentVoucher"); //清除图片校验文字
  }
}

/**
 * 支付方式
 */
function getPaymentMethodList() {
  let params = {
    enableStatus: 1,
  };
  API.queryPaymentMethodList(params).then((data: any) => {
    paymentMethodOption.value = data?.map((item: any) => {
      return {
        label: item.methodName,
        value: item.id,
      };
    });
  });
}

defineExpose({
  setData,
});
</script>

<style lang="scss" scoped>
.drawer-content {
}
</style>
