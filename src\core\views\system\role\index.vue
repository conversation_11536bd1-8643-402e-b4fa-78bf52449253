<template>
  <div class="app-container role">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item prop="roleName" :label="$t('roleManagement.label.roleName')">
          <el-input
            v-model="queryParams.roleName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
          />
        </el-form-item>

        <el-form-item :label="$t('roleManagement.label.status')" prop="status">
            <el-select
                v-model="queryParams.status"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[100px]"
            >
                <el-option v-for="item in roleStatusList" :key="item.statusId" :label="item.statusName" :value="item.statusId"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item :label="$t('roleManagement.label.affiliatedDepartment')" prop="deptIds">
          <!--<el-cascader
            ref="deptRef"
            v-model="queryParams.deptIds"
            :options="deptList"
            :props="{ value: 'id', label: 'deptName',  multiple: true, checkStrictly: true }"
            collapse-tags
            clearable
            @change="setDeptIds"
          />-->
          <el-cascader
            v-model="queryParams.deptIds"
            :options="deptList"
            :props="deptProps"
            @change="setDeptIds"
            collapse-tags
            :show-all-levels="false"
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button  v-hasPerm="['pms:system:role:search']" type="primary" @click="handleQuery">
                {{$t('common.search')}}
          </el-button>
          <el-button v-hasPerm="['pms:system:role:reset']" @click="handleResetQuery">
                {{$t('common.reset')}}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
         <el-button v-hasPerm="['pms:system:role:add']" type="primary" @click="handleOpenDialog()">
                {{$t('roleManagement.button.addRole')}}
          </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="roleList"
        highlight-current-row
        stripe
      >
          <template #empty>
              <Empty/>
          </template>
        <el-table-column type="index" :label="$t('common.sort')" width="60"  align="center"/>
        <el-table-column :label="$t('roleManagement.label.roleName')" prop="roleName" show-overflow-tooltip/>
        <el-table-column :label="$t('roleManagement.label.userCount')" prop="userCount" show-overflow-tooltip align="right" />
        <el-table-column :label="$t('roleManagement.label.roleDesc')" prop="roleDesc" show-overflow-tooltip />
        <el-table-column :label="$t('roleManagement.label.affiliatedDepartment')" prop="deptName" show-overflow-tooltip />
        <el-table-column :label="$t('roleManagement.label.roleStatus')" align="center" show-overflow-tooltip>
          <template #default="scope">
            <el-switch :active-text="$t('common.activeBtn')"
                       :inactive-text="$t('common.inactiveBtn')"
                       v-hasPerm="['pms:system:role:updateStatus']"
                       inline-prompt
                       v-model="scope.row.status"
                       :active-value="1"
                       :inactive-value="0"
                       @change="changeRoleStatus(scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('common.handle')" width="220">
          <template #default="scope">
            <el-button
              v-hasPerm="['pms:system:role:update']"
              type="primary"
              link
              @click="handleOpenDialog(scope.row)"
            >
                      {{$t('common.edit')}}
            </el-button>
            <el-button
              v-hasPerm="['pms:system:role:delete']"
              type="danger"
              link
              @click="handleDelete(scope.row)"
            >
                      {{$t('common.delete')}}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 角色表单弹窗 -->
    <Edit
        ref="editRoleRef"
        v-model:visible="dialog.visible"
        :title="dialog.title"
        @onSubmit="handleEditQueryTable"
    />

  </div>
</template>

<script setup lang="ts">

import UserAPI from "@/core/api/accountManagement";

defineOptions({
  name: "Role",
  inheritAttrs: false,
});

import RoleAPI, { RolePageVO, RoleForm, RolePageQuery } from "@/core/api/role";
import {IObject} from "@/core/components/CURD/types";
import Edit from "./components/edit.vue";
import { useUserStore } from "@/core/store";
const userStore = useUserStore();

const { t } = useI18n();
const queryFormRef = ref(null);

const roleStatusList = ref([
    // {
    //     statusId: ' ',
    //     statusName: t('common.statusEmun.all')
    // },
    {
        statusId: 0,
        statusName: t('common.statusEmun.disable')
    },
    {
        statusId: 1,
        statusName:t('common.statusEmun.enable')
    }
])

const loading = ref(false);
const total = ref(0);

const queryParams = reactive<RolePageQuery>({
  page: 1,
  limit: 20,
});

const roleList = ref<RolePageVO[]>();

const editRoleRef = ref();
const deptList = ref([]);
const deptProps = ref(
  { value: 'id', label: 'deptName',  multiple: true, checkStrictly: true, emitPath: false }
)
const deptRef = ref()

const dialog = reactive({
  title: "",
  visible: false,
});

const findNode = (value, nodes) => {
  for (const node of nodes) {
    if (node.id === value) return node
    if (node.children) {
      const found = findNode(value, node.children)
      if (found) return found
    }
  }
}

const getDescendantValues = (node) => {
  return [
    node.id,
    ...(node.children?.flatMap(child => getDescendantValues(child)) || [])
  ]
}

const getAncestorValues = (value, nodes, path) => {
  for (const node of nodes) {
    debugger
    if (node.id === value) return path
    if (node.children) {
      const found = getAncestorValues(value, node.children, [...path, node.id])
      if (found.length) return found
    }
  }
  return []
}

function findParentIds(selectedIds, options = deptList.value, parentIds = []) {
  options.forEach(option => {
    if (selectedIds.includes(option.id)) {
      parentIds.push(...getAncestorIds(option.id))
    }
    if (option.children) {
      findParentIds(selectedIds, option.children, parentIds)
    }
  })
  return [...new Set(parentIds)]
}
// 获取某个节点的所有祖先ID
function getAncestorIds(id) {
  const findParent = (options, targetId, ancestors = []) => {
    for (const option of options) {
      if (option.id === targetId) return ancestors
      if (option.children) {
        const found = findParent(option.children, targetId, [...ancestors, option.id])
        if (found) return found
      }
    }
    return null
  }
  return findParent(deptList.value, id) || []
}
function getUnselectedNodes(selectedIds) {
  const checkedNodes = selectedIds || []
  const unselectedNodes = []

  // 递归遍历树结构
  function traverse(nodes) {
    nodes.forEach(node => {
      const isChecked = checkedNodes.some(
        (checkedNode: any) => checkedNode === node.id
      )

      if (!isChecked) {
        unselectedNodes.push(node.id)
      }

      if (node.children) {
        traverse(node.children)
      }
    })
  }
  traverse(deptList.value)
  return unselectedNodes || []
}
function setDeptIds(selectedIds) {
  if(Array.from(selectedIds).length > 0){
    console.log("selectedIds=====",selectedIds)
    const newSelected = []

    // 处理父节点选中时自动添加子节点
    selectedIds.forEach(val => {
      const node = findNode(val, deptList.value)
      if (node?.children) {
        getDescendantValues(node).forEach(v => !selectedIds.includes(v) ? newSelected.push(v) : '')
      }
    })
    // const arr = [...Array.from(selectedIds),...newSelected]
    const noChecked = getUnselectedNodes(Array.from(selectedIds))
    console.log("noChecked====",noChecked)

    const disabledParentIds = findParentIds(noChecked)
    console.log("newSelected=====",newSelected)
    console.log("disabledParentIds=====",disabledParentIds)

    queryParams.deptIds = [...selectedIds.filter(id => !disabledParentIds.includes(id)),...newSelected]
    console.log("queryParams.deptIds=====",queryParams.deptIds)
  }
}
/** 查询 */
function handleQuery() {
  loading.value = true;
  RoleAPI.getRolePage(queryParams)
    .then((data) => {
      roleList.value = data.records;
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleEditQueryTable() {
    if(dialog.title == 'roleManagement.title.addRoleTitle'){
        queryParams.page = 1;
        queryParams.limit = 20;
    }
    handleQuery();
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}

/** 打开角色弹窗 */
async function handleOpenDialog(row?: IObject) {
  if (row) {
    dialog.title = t('roleManagement.title.editRoleTitle');
    editRoleRef.value.setEditType("edit");
    editRoleRef.value.setFormData(row)
    editRoleRef.value.queryRoleMenuList()
    editRoleRef.value.getDeptList()
  } else {
    dialog.title = t('roleManagement.title.addRoleTitle');
    editRoleRef.value.setEditType("add");
    editRoleRef.value.queryRoleMenuList()
    editRoleRef.value.getDeptList()
  }
  dialog.visible = true;
}

/** 修改角色状态 */
function changeRoleStatus(row: IObject) {
  let flag = row.status
  row.status = row.status === 0 ? 1 : 0//保持switch点击前的状态
  let data = {
    roleId: row.roleId,
    status: flag
  }
  if(flag===0){
    ElMessageBox.confirm(t('roleManagement.message.disableTips'), t('common.tipTitle'), {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: "warning",
    }).then(() => {
      RoleAPI.updateEnableStatus(data).then(res => {
        ElMessage.success(t('roleManagement.message.disableSucess'))
        handleQuery()
      })
    })
  }else{
    RoleAPI.updateEnableStatus(data).then(res => {
      ElMessage.success(t('roleManagement.message.enableSucess'))
      handleQuery()
    })
  }
}

/** 删除角色 */
function handleDelete(row?: string) {
  if(row.status==1){
      return  ElMessage.error(t('roleManagement.message.deleteNotTips'))
  }
  ElMessageBox.confirm(t('roleManagement.message.deleteTips'), t('common.tipTitle'), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      let data = {
          roleId:row.roleId
      }
      RoleAPI.delete(data)
        .then(() => {
          ElMessage.success(t('roleManagement.message.deleteSucess'));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t('roleManagement.message.deleteConcel'));
    }
  );
}
function getDeptList() {
  UserAPI.allDeptList().then((data) => {
    deptList.value = data;
    queryParams.deptIds = []
    if(userStore.user.baseDeptVOList && userStore.user.baseDeptVOList.length > 0){
      userStore.user.baseDeptVOList.forEach(list=>{
        queryParams.deptIds.push(list.id)
      })
    }
  });
}

onActivated(() => {
  handleQuery();
  getDeptList()
});
</script>
