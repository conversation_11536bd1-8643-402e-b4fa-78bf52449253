<template>
  <div class="app-container">
    <div class="container-wrapper">
      <div class="search-container">
        <el-form
          ref="queryFormRef"
          :model="queryParams"
          :inline="true"
          label-width="96px"
        >
          <el-form-item
            prop="sortingCode"
            :label="$t('pickOrder.label.sortingCode')"
          >
            <el-input
              v-model="queryParams.sortingCode"
              :placeholder="$t('pickOrder.placeholder.pickingCodeTip')"
              clearable
              class="!w-[256px]"
            />
          </el-form-item>

          <el-form-item
            prop="sortingStatus"
            :label="$t('pickOrder.label.sortingStatus')"
          >
            <el-select
              v-model="queryParams.sortingStatus"
              :placeholder="$t('common.placeholder.selectTips')"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              class="!w-[256px]"
            >
              <el-option
                v-for="(item, index) in sortingStatusOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-form-item prop="queryTimeType" style="margin-right: 5px">
              <el-select
                v-model="queryParams.queryTimeType"
                :placeholder="$t('common.placeholder.selectTips')"
                class="!w-[200px] ml5px"
                @change="changeTimeType"
              >
                <el-option
                  v-for="(item, index) in dateTypOption"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="dateRange">
              <el-date-picker
                :editable="false"
                class="!w-[370px]"
                v-model="queryParams.dateRange"
                type="datetimerange"
                :range-separator="$t('pickOrder.label.to')"
                :start-placeholder="$t('pickOrder.label.startTime')"
                :end-placeholder="$t('pickOrder.label.endTime')"
                :default-time="defaultTime"
                :placeholder="$t('common.placeholder.selectTips')"
              />
              <span
                class="ml16px mr14px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(1)"
              >
                {{ $t("pickOrder.label.today") }}
              </span>
              <span
                class="mr14px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(2)"
              >
                {{ $t("pickOrder.label.yesterday") }}
              </span>
              <span
                class="mr16px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(3)"
              >
                {{ $t("pickOrder.label.weekday") }}
              </span>
            </el-form-item>
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPerm="['wms:storeManagement:quickPickOrder:search']"
              type="primary"
              @click="handleQuery"
            >
              {{ $t("common.search") }}
            </el-button>
            <el-button
              v-hasPerm="['wms:storeManagement:quickPickOrder:reset']"
              @click="handleResetQuery"
            >
              {{ $t("common.reset") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-card shadow="never" class="table-container">
        <template #header>
          <div class="flex-center-but">
            <el-button
              v-hasPerm="['wms:storeManagement:quickPickOrder:add']"
              type="primary"
              @click="handleDetail(null, 'add')"
            >
              {{ $t("pickOrder.button.actionAdd") }}
            </el-button>
          </div>
        </template>

        <el-table
          v-loading="loading"
          :data="tableData"
          highlight-current-row
          stripe
        >
          <template #empty>
            <Empty />
          </template>
          <el-table-column type="index" :label="$t('common.sort')" width="60" />
          <el-table-column
            :label="$t('pickOrder.label.sortingCode')"
            prop="sortingCode"
            show-overflow-tooltip
            min-width="160px"
          ></el-table-column>
          <el-table-column
            :label="$t('pickOrder.label.sortingType')"
            prop="sortingType"
            show-overflow-tooltip
            min-width="190px"
          >
              <template #default="scope">
                  {{filterSortingType(scope.row.sortingType)}}

              </template>
          </el-table-column>
          <el-table-column
            :label="$t('pickOrder.label.useGoods')"
            prop="receiptProductCount"
            show-overflow-tooltip
            min-width="160px"
          >
            <template #default="scope">
              <div class="item">
                {{ $t("pickOrder.label.totalQuantity") }}:{{
                  scope.row.receiptProductQty
                }}
              </div>
              <div class="item">
                {{ $t("pickOrder.label.conversionQuantity") }}:{{
                  scope.row.receiptProductWeight
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('pickOrder.label.sortedGoods')"
            prop="sortingProductCount"
            show-overflow-tooltip
            min-width="160px"
          >
            <template #default="scope">
              <div class="item">
                {{ $t("pickOrder.label.totalQuantity") }}:{{
                  scope.row.sortingProductQty
                }}
              </div>
              <div class="item">
                {{ $t("pickOrder.label.conversionQuantity") }}:{{
                  scope.row.sortingProductWeight
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('pickOrder.label.inventoryGoods')"
            prop="inventoryGoods"
            show-overflow-tooltip
            min-width="160px"
          >
            <template #default="scope">
              <div class="item">
                {{ $t("pickOrder.label.totalQuantity") }}:{{
                  scope.row.totalInWarehouseQty
                }}
              </div>
              <div class="item">
                {{ $t("pickOrder.label.conversionQuantity") }}:{{
                  scope.row.totalInWarehouseWeight
                }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('pickOrder.label.lossOrderCode')"
            prop="lossOrderCode"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>
          <el-table-column
            :label="$t('pickOrder.label.createUserName')"
            prop="createUserName"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>
          <el-table-column
            :label="$t('pickOrder.label.createTime')"
            prop="createTime"
            show-overflow-tooltip
            min-width="120px"
          >
            <template #default="scope">
              {{ parseDateTime(scope.row.createTime, "dateTime") }}
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('pickOrder.label.sortingUserName')"
            prop="handleUserName"
            show-overflow-tooltip
            min-width="120px"
          ></el-table-column>
          <el-table-column
            :label="$t('pickOrder.label.sortingTime')"
            prop="handleTime"
            show-overflow-tooltip
            min-width="120px"
          >
            <template #default="scope">
              {{ parseDateTime(scope.row.handleTime, "dateTime") }}
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            :label="$t('pickOrder.label.sortingStatus')"
            prop="sortingStatus"
            show-overflow-tooltip
            min-width="120px"
          >
            <template #default="scope">
              <div
                class="purchase"
                v-html="filterSortingStatus(scope.row.sortingStatus)"
              ></div>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            :label="$t('common.handle')"
            width="160"
          >
            <template #default="scope">
              <template v-if="scope.row.sortingStatus === 0">
                <el-button
                  v-hasPerm="['wms:storeManagement:quickPickOrder:edit']"
                  type="primary"
                  link
                  @click="handleDetail(scope.row, 'edit')"
                >
                  {{ $t("pickOrder.button.actionSorting") }}
                </el-button>
                  <el-button
                    v-hasPerm="['wms:storeManagement:quickPickOrder:delete']"
                    type="danger"
                    link
                    @click="handleDelete(scope.row)"
                  >
                      {{ $t("pickOrder.button.actionDelete") }}
                  </el-button>
              </template>
                <template v-else>
                    <el-button
                      v-hasPerm="['wms:storeManagement:quickPickOrder:detail']"
                      type="primary"
                      link
                      @click="handleDetail(scope.row, 'detail')"
                    >
                        {{ $t("common.detailBtn") }}
                    </el-button>
                </template>

            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "quickPickOrder",
  inheritAttrs: false,
});
import {
  changeDateRange,
  convertToTimestamp,
  parseDateTime,
  isEmpty,
} from "@/core/utils";
import { emitter } from "@/core/utils/eventBus";
import moment from "moment";
import { useUserStore } from "@/core/store";

import API from "@/modules/wms/api/quickPickOrder";

import { useNavigation } from "@/core/composables/useNavigation";
const { refreshAndNavigate } = useNavigation();

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();

const state = reactive({
  loading: false,
  queryFormRef: null,
  queryParams: {
    page: 1,
    limit: 20,
    sortingCode: "",
    sortingStatus: [],
    queryTimeType: 2,
    dateRange: [],
  },
  defaultTime: [
    new Date(2000, 1, 1, 0, 0, 0),
    new Date(2000, 2, 1, 23, 59, 59),
  ],
  total: 0,
  tableData: [],
    sortingStatusOption: [
    { label: t("pickOrder.label.quickSortingStatusOption[0]"), value: 0 },
    { label: t("pickOrder.label.quickSortingStatusOption[2]"), value: 2 },

  ],
    sortingTypeOption: [
        { label: t("pickOrder.label.sortingTypeOption[0]"), value: 0 },
        { label: t("pickOrder.label.sortingTypeOption[1]"), value: 1 },

    ],
  dateTypOption: [
    { label: t("pickOrder.label.sortingTime"), value: 2 },
  ],
}) as any;

const {
  loading,
  queryFormRef,
  queryParams,
  total,
  tableData,
    sortingStatusOption,
    sortingTypeOption,
  dateTypOption,
  defaultTime,
} = toRefs(state);

/**
 * 时间类型切换
 * @param val
 */
function changeTimeType(val: any) {
  queryParams.value.dateRange = [
    moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
    moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
  ];
}

/**
 * 时间转换
 * @param val
 */
function handleChangeDateRange(val: number) {
  queryParams.value.dateRange = changeDateRange(val);
  console.log(queryParams.value.dateRange);
}

/**
 * 搜索
 */
function handleQuery() {
  if (
    queryParams.value.sortingCode &&
    queryParams.value.sortingCode.length < 4
  ) {
    return ElMessage.error(t("pickOrder.rules.sortingCode"));
  }

  loading.value = true;
  const [startTime, endTime] = queryParams.value.dateRange || [];
  let params = {
    ...queryParams.value,
    startTime: convertToTimestamp(startTime + " 00:00:00"),
    endTime: convertToTimestamp(endTime + " 23:59:59"),
  };
  delete params.dateRange;

  API.getPageList(params)
    .then((res: any) => {
      const { records, pages } = res;
      tableData.value = records || [];
      total.value = parseInt(res.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 重置
 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.value.page = 1;
  queryParams.value.limit = 20;
  handleQuery();
}



/**
 * 详情
 * @param row
 * @param type
 */
function handleDetail(row: any, type: string) {
    refreshAndNavigate({
    path: "/wms/storeManagement/editQuickPickOrder",
    query: {
      sortingCode: row ?row.sortingCode : '',
      pageType: type,
    },
  });
}

/**
 * 删除
 */
function handleDelete(row: any) {

    ElMessageBox.confirm(
      t("pickOrder.message.deleteTips"),
      t("common.tipTitle"),
      {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: 'warning',
      }
    )
      .then(() => {
        let sortingCode= row.sortingCode
                  API.deleteOrder(sortingCode)
                    .then((res: any) => {
                        ElMessage.success(t("pickOrder.message.actionSucess"));
                        handleQuery();
                    })

              })
              .catch(() => {

      })
}


/**
 * 分拣状态
 * @param val
 */

function filterSortingStatus(val: any) {
  let html;
  switch (val) {
      case 0:
          html = `<span class="purchase-status  purchase-status-color1">${t(`pickOrder.label.quickSortingStatusOption[0]`)}</span>`;
          break;
      case 2:
          html = `<span class="purchase-status  purchase-status-color3">${t(`pickOrder.label.quickSortingStatusOption[2]`)}</span>`;
          break;
      default:
          html = `-`;
  }
  return html;
}

/**
 * 分拣类型
 * @param val
 */
function filterSortingType(val: any) {
    if (!isEmpty(val)) {
        return sortingTypeOption.value.find((item: any) => item.value === val)?.label || '';
    }
}

onActivated(() => {
  handleQuery();
});

emitter.on("reloadListByWarehouseId", (e) => {
  nextTick(() => {
    handleQuery();
  });
});
</script>

<style lang="scss" scoped>
.container-wrapper {
}
</style>
