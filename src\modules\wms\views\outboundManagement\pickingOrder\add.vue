<script setup lang="ts">
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import ProductMgAPI from "@/modules/wms/api/pickingOrder";
import { parseTime } from "@/core/utils/index";
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
import { useI18n } from "vue-i18n"; // 导入国际化
import type { FormInstance } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import { de } from "element-plus/es/locale";
import { useForm } from "./composables";
const { statusClass } = useForm();
import {PickingListVO, PickingListProductVO, PickingListProductDetailVO} from "@/modules/wms/types/pickingOrder";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const isEdit = ref(route.query.type === "edit");
const formRef = ref<FormInstance>();
export interface MapObject {
  key?: { [key: string]: any };
  [property: string]: any;
}
const pickingListCode = ref(route.query.pickingListCode);

const tagsViewStore = useTagsViewStore();
const handleClose = async () => {
  await tagsViewStore.delView(route);
  router.push("/wms/pickingOrder");
};

const standardOptions = [
  { value: 1, label: t('WMSPickingOrder.label.is') },
  { value: 0, label: t('WMSPickingOrder.label.not') },
];

const detail = ref<PickingListVO>({});
const productList = ref<PickingListProductVO[]>([]);
const loading = ref(false);
const pickingFailureDialogVisible = ref(false);
const failureMessages = ref([]);
const fetchDetail = (data: Record<string, any> = {}) => {
  loading.value = true;
  ProductMgAPI.getProductDetailById(data)
    .then((res) => {
      detail.value = res;
      productList.value = res?.pickingListProductVOS || [];
    })
    .catch((err) => { })
    .finally(() => {
      loading.value = false;
    });
};

 // 检查格式：支持小数点前8位，小数点后3位
 const regex = /^([0-9]{1,8})(\.[0-9]{1,3})?$/;
const handleConfirm = () => {
  // 检查每个商品的本次拣货数量是否输入actualPickThisTime，注意productList的数据结构组织
  const hasError = productList.value.some((item) => {
    return item?.pickingListProductDetailVOList.some((detail) => {
      return detail.actualPickThisTime === "" || detail.actualPickThisTime <= 0 || !regex.test(String(detail.actualPickThisTime));
    });
  });
  if (hasError) {
    // "请检查每个商品的本次拣货数量"
    ElMessage.error(t('WMSPickingOrder.rules.pickQuantityRequired'));
    return;
  }
  ElMessageBox.confirm(
    /*"完成拣货后系统会扣除锁定库存，并自动对商品出库，是否继续完成拣货"*/t('WMSPickingOrder.confirm.message'),
    /*"提示"*/t('WMSPickingOrder.confirm.title'),
    {
      confirmButtonText: /*"确定"*/t('WMSPickingOrder.confirm.confirmText'),
      cancelButtonText: /*"取消"*/t('WMSPickingOrder.confirm.cancelText'),
      type: "warning",
    }
  )
    .then(() => {
      loading.value = true;
      const data = {
        pickListId: detail.value.id,
        deliveryNoticeCode: detail.value.deliveryNoticeCode,
        pickingListCode: detail.value.pickingListCode,
        warehouseCode: detail.value.warehouseCode,
        tenantId: detail.value.tenantId,
        completePickProductDTOList: productList.value.map((item) => {
          return {
            pickListProductId: item.id,
            productCode: item.productCode,
            productName: item.productName,
            completePickProductDetailDTOList:
              item?.pickingListProductDetailVOList.map((pickItem) => {
                return {
                  pickListProductDetailId: pickItem.id,
                  actualPickThisTime: pickItem.actualPickThisTime,
                  warehouseAreaPlanedPick: pickItem.warehouseAreaPlanedPick,
                  warehouseAreaCode: pickItem.warehouseAreaCode,
                  warehouseLocationCode: pickItem.warehouseLocationCode,
                  warehouseAreaActualPickWeight: pickItem.warehouseAreaActualPickWeight,
                  warehouseAreaPlanedPickWeight: pickItem.warehouseAreaPlanedPickWeight,


                };
              }),
          };
        }),
      };
      ProductMgAPI.completePick(data)
        .then((res) => {
          if (res?.length > 0) {
            failureMessages.value = res;
            pickingFailureDialogVisible.value = true;
          } else {
            ElMessage.success(/*"拣货成功"*/t('WMSPickingOrder.confirm.success'));
            handleClose();
          }
        })
        .finally(() => {
          loading.value = false;
        });
    })
    .catch(() => {
      // 取消操作
    });
};

const handleCancel = () => {
  handleClose();
};

interface Location {
  actualPickThisTime: number;
  availableStockQty: number;
  inputError: string;
}

const validatePickInput = (location: Location) => {
  // 检查是否为空
  if (
    !(
      location.actualPickThisTime === "" ||
      location.actualPickThisTime === null ||
      location.actualPickThisTime === undefined
    )
  ) {
    // 检查格式：支持小数点前8位，小数点后3位
    const regex = /^([0-9]{1,8})(\.[0-9]{1,3})?$/;
    if (!regex.test(String(location.actualPickThisTime))) {
      location.inputError =/* "拣货数量格式错误，支持小数点前8位，小数点后3位"*/t('WMSPickingOrder.rules.invalidFormat');
      return;
    }

    // 检查是否大于0
    if (parseFloat(location.actualPickThisTime) <= 0) {
      location.inputError = /*"拣货数量必须大于0"*/t('WMSPickingOrder.rules.mustBePositive');
      return;
    } else if (
      parseFloat(location.actualPickThisTime) >
      parseFloat(location.availableStockQty) +
      parseFloat(location.warehouseAreaPlanedPick)
    ) {
      location.inputError = /*"库存不足，完成拣货失败"*/t('WMSPickingOrder.rules.insufficientStock');
      // ElMessage.error("库存不足，完成拣货失败");
      return;
    } else {
      location.inputError = "";
    }
  }
  else {
    location.inputError = "";
  }
};

onMounted(() => {
  fetchDetail({ id: route.query.id });
});
</script>

<template>
  <div class="product-add">
    <div class="card-header mb-24px">
      <img @click="handleClose" src="@/core/assets/images/arrow-left.png" alt="" class="back-btn" />
      <span @click="handleClose" class="code">
        {{$t('WMSPickingOrder.label.pickPageTitle')}}{{ pickingListCode }}
      </span>
    </div>
    <div class="card-title mb-24px">{{$t('WMSPickingOrder.label.orderInfo')}}</div>
    <div class="card-content">
      <div class="order-info-grid">
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.pickingListCode')}}</div>
          <div class="info-value">{{ detail.pickingListCode || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.pickingType')}}</div>
          <div class="info-value">{{ detail.pickingType === 1 ? $t('WMSPickingOrder.label.pickingTypeSingle') : "--" }}</div>
        </div>
<!--        <div class="info-item">
          <div class="info-label">提单号码</div>
          <div class="info-value">{{ detail.billOfLadingCode || "&#45;&#45;" }}</div>
        </div>-->
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.createUserName')}}</div>
          <div class="info-value">{{ detail.createUserName || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.createTime')}}</div>
          <div class="info-value">
            {{ parseTime(detail.createTime) || "--" }}
          </div>
        </div>

        <div class="info-item">
          <div class="info-label">计划发货时间</div>
          <div class="info-value">
            {{ parseTime(detail.planDeliveryTime) || "--" }}
          </div>
        </div>

        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.plannedReceivedTime')}}</div>
          <div class="info-value">
            {{ parseTime(detail.plannedReceivedTime) || "--" }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.deliveryNoticeCode')}}</div>
          <div class="info-value">{{ detail.deliveryNoticeCode || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">{{$t('WMSPickingOrder.label.pickingListCode')}}</div>
          <div class="info-value">{{ detail.billOfLadingCode || "--" }}</div>
        </div>


        <div class="info-item">
          <div class="info-label">是否领单</div>
          <div class="info-value">{{ detail.receivingStatus === 1 ? "是" : "否" || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">领单人</div>
          <div class="info-value">{{ detail.receivingUserName || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">领单时间</div>
          <div class="info-value">{{ parseTime(detail.receivingTime, '{y}-{m}-{d} {h}:{i}:{s}') || "--" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">状态</div>

          <!-- 状态:1 初始 2 拣货失败 3 拣货完成 4 已撤销 -->
          <span class="custom-status status" :class="statusClass(detail.status)">{{ detail.statusStr}}</span>
        </div>
      </div>
    </div>
    <div class="card-title mb-24px">{{$t('WMSPickingOrder.label.productDetail')}}</div>
    <el-table :data="productList" border style="width: 100%;min-width: 0;flex-grow: 1;" v-loading="loading">
      <el-table-column type="index" :label="$t('WMSPickingOrder.label.sort')" width="60" align="center" fixed="left"/>
      <el-table-column :label="$t('WMSPickingOrder.label.productInfo')" min-width="180" fixed="left">
        <template #default="scope">
          <div class="product-info">
            <div class="product-name">{{ scope.row.productName }}</div>
            <div class="product-code">
              <span class="label">{{$t('WMSPickingOrder.label.productCode')}}:</span>
              <span class="value">{{ scope.row.productCode }}</span>
            </div>
            <div class="product-spec">
              <span class="label">{{$t('WMSPickingOrder.label.productSpecs')}}:</span>
              <span class="value">{{ scope.row.productSpecs }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalPlanedPick" :label="$t('WMSPickingOrder.label.totalPlanedPick')" width="150" align="center" />

      <el-table-column prop="totalPlanedPick" :label="'计划拣货总重量(Kg)'" width="150" align="center" />
      <!-- <el-table-column prop="totalActualPick" label="实际拣货总量" width="120" align="center" /> -->
       <!-- 可用库存重量 availableStockWeight-->
       <el-table-column :label="'可用库存重量(Kg)'" width="150" align="center">
        <template #default="scope">
          <div v-for="(location, index) in scope.row
            .pickingListProductDetailVOList" :key="index" class="location-item">
            <div class="">
              {{ location.availableStockWeight || "--" }}
            </div>
          </div>
        </template>
      </el-table-column>
       <!-- 拣货库区 -->
      <el-table-column :label="$t('WMSPickingOrder.label.warehouseArea')" min-width="180">
        <template #default="scope">
          <div v-for="(location, index) in scope.row
            .pickingListProductDetailVOList" :key="index" class="location-item disabled">
            <div class="location-code">
              {{ location.warehouseAreaName || "--" }}
            </div>
            |
            <div class="location-quantity width-auto">
              <span>{{ location.warehouseAreaCode || "--" }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('WMSPickingOrder.label.plannedPick')" align="center" min-width="110">
        <template #default="scope">
          <div v-for="(location, index) in scope.row
            .pickingListProductDetailVOList" :key="index" class="location-item">
            <div class="location-quantity">
              {{ location.warehouseAreaPlanedPick || "--" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- 计划拣货重量(Kg) warehouseAreaPlanedPickWeight-->
      <el-table-column :label="'计划拣货重量(Kg)'" width="150" align="center">
        <template #default="scope">
          <div v-for="(location, index) in scope.row
            .pickingListProductDetailVOList" :key="index" class="location-item">
            <div class="">
              {{ location.warehouseAreaPlanedPickWeight || "--" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- 库区可用库存 -->
      <!-- <el-table-column :label="$t('WMSPickingOrder.label.availableStock')" align="center">
        <template #default="scope">
          <div v-for="(location, index) in scope.row
            .pickingListProductDetailVOList" :key="index" class="location-item">
            <div class="location-quantity">
              {{ location.availableStockQty || "--" }}
            </div>
          </div>
        </template>
      </el-table-column> -->
     
      <el-table-column :label="$t('WMSPickingOrder.label.currentPick')" align="center" width="200">
        <template #default="scope">
          <div v-for="(location, index) in scope.row
            .pickingListProductDetailVOList" :key="index" class="location-item">
            <div class="location-quantity">
              <el-input v-model="location.actualPickThisTime" @input="validatePickInput(location)" :min="0"
                :max="location.availableStockQty" class="location-input">
                <template #suffix>
                  {{ scope.row.productUnitName }}
                </template>
              </el-input>
              <div v-if="location.inputError" class="error-message-pick">
                {{ location.inputError }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- 本次拣货重量(Kg) warehouseAreaActualPickWeight-->
      <el-table-column :label="'本次拣货重量(Kg)'" align="center" width="200">
        <template #default="scope">
          <div v-for="(location, index) in scope.row
            .pickingListProductDetailVOList" :key="index" class="location-item">
            <div class="location-quantity">
              <el-input v-model="location.warehouseAreaActualPickWeight" @input="validatePickInput(location)" :min="0"
                :max="location.warehouseAreaActualPickWeight" class="location-input">
                <template #suffix>
                  {{ scope.row.productUnitName }}
                </template>
              </el-input>
              <!-- <div v-if="location.inputError" class="error-message-pick">
                {{ location.inputError }}
              </div> -->
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="$t('WMSPickingOrder.label.status')" width="100" align="center">
        <template #default="scope">
          <span class="custom-status status" :class="statusClass(scope.row.status)">
            {{ scope.row.statusStr }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <div class="card-footer">
      <el-button @click="handleClose">{{$t('WMSPickingOrder.label.cancel')}}</el-button>
      <el-button type="primary" @click="handleConfirm" v-loading="loading">
        {{$t('WMSPickingOrder.label.completePicking')}}
      </el-button>
    </div>
    <el-dialog :title="$t('WMSPickingOrder.label.tip')" v-model="pickingFailureDialogVisible" width="500px" :close-on-click-modal="false"
      :show-close="true">
      <div class="dialog-content">
        <div class="icon-container">
          <el-icon>
            <Warning />
          </el-icon>
        </div>
        <div class="dialog-title">{{$t('WMSPickingOrder.title.pickFail')}}！</div>
        <div class="failure-content">
          <div class="failure-reason-label">{{$t('WMSPickingOrder.label.failReason')}}：</div>
          <div v-for="(message, index) in failureMessages" :key="index" class="error-message">
            {{ message }}
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="pickingFailureDialogVisible = false">
            {{$t('WMSPickingOrder.button.close')}}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
:deep(.location-input .el-input__suffix-inner) {
  background: #f8f9fc;
  border-radius: 0px 2px 2px 0px;
  border: 1px solid #d7dbdf;
  display: inline-block;
  width: 32px;
}

:deep(.location-input .el-input__wrapper) {
  padding-right: 0;
}

.product-add {
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;

  display: flex;
  flex-direction: column;
  height: calc(100% - 30px);

  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;

    .code {
      font-family: "PingFangSC", "PingFang SC";
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .status {
      font-family: "PingFangSC", "PingFang SC";
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }

  .card-content {
    margin-bottom: 24px;

    .order-info-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      padding: 0 20px;
      border-radius: 4px;

      .info-item {
        display: flex;

        .info-label {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #90979e;
          line-height: 32px;
          text-align: center;
          font-style: normal;
          margin-bottom: 8px;
          margin-right: 8px;
        }

        .info-value {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #151719;
          line-height: 32px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }

  .upload-container {
    display: flex;
    flex-direction: column;
  }

  .tip {
    font-family: "PingFangSC", "PingFang SC";
    font-weight: 400;
    font-size: 14px;
    color: #90979e;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }

  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
    text-align: right;
    border-top: 1px solid #ebeef5;
  }

  .conversion-relation {
    display: flex;
    align-items: center;
    gap: 8px;

    .equal-sign {
      margin: 0 4px;
    }
  }

  .dimensions-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .dimension-input {
      width: 80px;
    }

    .volume-input {
      width: 100px;
    }
  }
}

.product-info {
  padding: 8px 0;

  .product-name {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #151719;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    margin-bottom: 4px;
  }

  .product-code,
  .product-spec {
    margin-bottom: 4px;

    .label {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #90979e;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }

    .value {
      color: #52585f;
    }
  }
}

.location-item {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 4px;
  padding: 6px 0;
  border-bottom: 1px dashed #f0f0f0;
  box-sizing: border-box;

  &.disabled {
    padding: 6px 12px;
    background: #f8f9fc;
    border-radius: 2px;
    border: 1px solid #d7dbdf;
  }

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .location-code {
    color: #151719;
    font-size: 13px;
    white-space: nowrap;
  }

  .location-quantity {
    width: 100%;
    color: #151719;
    font-size: 13px;
    font-weight: 500;

    .location-input {
      width: 100%;
    }
  }
}

.card-footer {
  padding: 16px 30px;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-top: 1px solid #ebeef5;
  text-align: right;
}

.error-message {
  color: #f56c6c;
  margin: 5px 0;
}

.error-message-pick {
  position: absolute;
  color: #f56c6c;
  // margin: 5px 0;
  text-align: left;
  line-height: 16px;
  font-size: 12px;
}

.dialog-content {
  text-align: center;
  margin-bottom: 20px;
}

.icon-container {
  color: #f56c6c;
  font-size: 50px;
  margin-bottom: 10px;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.failure-content {
  text-align: left;
}

.failure-reason-label {
  font-weight: 600;
  font-size: 14px;
  color: #c00c1d;
}

.error-message {
  color: #f56c6c;
  margin: 5px 0;
}

.dialog-footer {
  text-align: right;
}
.width-auto{
  width: auto!important;
}
</style>
<!-- End of file -->
