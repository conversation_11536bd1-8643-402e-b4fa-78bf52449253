export default {
  profitAndLossManagement: {
    label: {
      profitLossCode: "损益单号",
      profitLossType: "损益类型",
      checkCode: "盘点单号",
      status: "状态",
      createTime: "创建时间",
      remark: "备注",
      createUserName: "创建人",
      handlerUserName: "处理人",
      handlerTime: "处理时间",
      basicInformation: "基本信息",
      lossDetail: "损益明细",
      productInformation: "商品信息",
      productCode: "商品编码",
      productSpec: "规格",
      warehouseAreaCode: "库区",
      beforeInventoryQty: "库区总库存",
      afterInventoryQty: "损益后库存总量",
      afterInventoryWeight: "损益后库存总重量",
      afterInventoryWeightUnit: "kg",
      lossQty: "损益数量",
      lossWeight: "损益重量",
      productCategory: "商品分类",
      product: "商品",
      startTime: "开始时间",
      endTime: "结束时间",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      receiptStatus: "是否领单",
      afterProfitLossQty: "损益个数",
      profitLossTotalQty: "损益数量",
      profitLossTotalWeight: "损益总重量(kg)",
      warehouseAreaName: "损益库区",
      useMessage:'领用信息',
      handleUserName: "领用人",
      handleUserNameCopy: "领单人",
      handleTime: "领用时间",
      handleTimeCopy: "领单时间",
      beforeCheckTotalQty: "盘点前数量",
      afterCheckTotalQty: "盘点后数量",
      lossInfo: "损益明细",
      increaseProfitLossQty: "损益增加数量",
      decreaseProfitLossQty: "损益减少数量",
      beforeProfitLossQty: "损益前商品个数",
      afterProfitLossQtyCopy: "损益后商品个数",
      beforeProfitLossTotalQty: "损益前商品数量",
      afterProfitLossTotalQty: "损益后商品数量",
      beforeProfitLossTotalQtyCopy: "损益前数量",
      beforeProfitLossWeightCopy: "损益前重量(kg)",
      afterProfitLossTotalQtyCopy: "损益后数量",
      afterProfitLossWeightCopy: "损益后重量(kg)",
      qty: "数量",
      weight: "重量(kg)",
      ysnCode: "YSN码",
      changeType: "变动方式",
      profitLossDetailYsnAdd: "库区增加YSN",
      profitLossDetailYsnSub: "库区减少YSN",
      beforeProfitLossWeight: "商品原重量(kg)",
      afterProfitLossWeight: "商品损益后重量(kg)",
      totalProfitAndLossConversion: "损益总转换量",
      lossConversion: "损益转换量",
      inventoryConversionAfterProfitAndLoss: "损益后库存转换量",
      unitPrice: "单价",
      amount: "金额",
      totalQty: "总库存量",
      totalWeight: "转换量",
    },
    placeholder:{
      productCategory: "请选择",
      product: "请输入商品编码或商品名称",
      codeValideTips:"请输入大于等于4位的单号",
    },
    statusList: {
      draft: "草稿",
      dealWidth: "处理中",
      finish: "已完成",
    },
    profitAndLossTypeList: {
      stockCount: "库存盘点",
      inventoryCheck: "按盘点结果损益",
      singleProduct: "按单包装商品损益",
      ysnCode: "按YSN损益",
      fastProfitAndLoss: "快速损益",
    },
    profitAndLossTypeListCopy: {
        inventoryCheck: "按盘点结果损益",
        fastProfitAndLoss: "快速损益",
    },
    dateTypeList:{
      createDate: "创建时间",
      handlerDate: "处理时间",
    },
    claimTypeList: {
        yes: "是",
        no: "否",
    },
    button: {
      addMaterial: "新增损益",
      addBtn: "添加损益商品",
      saveDraft: "保存草稿",
      addProfitAndLoss: "新增损益",
      editProfitAndLoss: "编辑损益",
      profitAndLossDetail: "损益详情",
      claim: "领单",
      cancelClaim: '取消领单',
      inventoryLoss: '去损益',
      openDetail:'查看明细'
    },
    title: {
      addProduct: "添加损益商品",
      lossInfo: "损益商品YSN-",
    },
    message: {
      deleteTips:"是否确认删除已创建的损益单，删除后不可恢复",
      deleteCancel: "已取消删除！",
      deleteSuccess: "删除成功！",
      addOrEditInventoryLossTips: "损益明细不能为空！",
      sameData:"商品加库区唯一",
      selectNumTips1: "已选",
      selectNumTips2: "个商品",
      addSuccess: "保存成功！",
      saveTips: "提交后损益库存内容不可更改，是否继续",
      saveCancel:"已取消保存！",
      codeValideTips:"查询单号必须大于等于4位",
      receiveOrCancelTipTitle: "温馨提示",
      receiveOrCancelTips: "确认取消领取单据？",
      receiveOrCancelConcel: "已取消领单！",
      receiveSucess: "领单成功！",
      receiveOrCancelSucess: "取消领单成功！",
      sameDataTips: "商品加库区需唯一",
    },
    rules: {
      // lossType: "请选择损益类型",
        profitLossType: "请选择损益类型",
      checkCode: "请选择盘点单号",
      warehouseAreaCode:"请选择库区",
      afterInventoryQtyNull: "请输入损益后库存总量",
      // afterInventoryQty: "请输入大于等于0的数字，支持小数点前8位后3位",
      afterInventoryQty: "损益后库存总数量支持0-9999整数",
      afterInventoryWeightNull: "请输入损益后库存转换量",
      afterInventoryWeight: "请输入大于等于0的数字，支持小数点前8位后3位",
    },
  },
};
