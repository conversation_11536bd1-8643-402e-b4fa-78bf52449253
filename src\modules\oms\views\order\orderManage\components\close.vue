<template>
    <el-dialog v-model="props.visible" :title="props.title" :close-on-click-modal="false" width="500px" @close="close">
        <div>
            <el-form ref="concleFromRef" :rules="rules" :model="concleFrom" label-position="top">
                    <el-form-item :label="$t('omsOrder.label.concleReason')" prop="remark">
                            <el-input
                                    :rows="4"
                                    type="textarea"
                                    show-word-limit
                                    v-model="concleFrom.remark"
                                    :placeholder="$t('omsOrder.placeholder.concleReason')"
                                    maxlength="100"
                                    clearable
                            />
                    </el-form-item>
            </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" :loading="submitLoading" @click="submitForm">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
    import OrderAPI, { CancelOrderFrom} from "@/modules/oms/api/order";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "关闭",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();
    const  concleFromRef= ref()
    const  submitLoading= ref(false)
    const concleFrom = reactive<CancelOrderFrom>({
    });
    const rules = reactive({
        remark: [{ required: true, message: t("omsOrder.rules.concleReason"), trigger: "blur" },],
    });

    function close() {
        emit("update:visible", false);
        reset();
    }

    function reset() {
        concleFromRef.value.resetFields();
    }

    function submitForm() {
        concleFromRef.value.validate((valid) => {
            if (!valid) return;
            submitLoading.value = true;
            let params = {
                ...concleFrom
            };
            OrderAPI.concelOrder(params)
                .then((res) => {
                    ElMessage.success(t("omsOrder.message.closeOrderSucess"));
                    close();
                    emit("onSubmit");
                })
                .finally(() => {
                    submitLoading.value = false;
                });
        })
    }

    function setFormData(data) {
        concleFrom.orderId = data.orderId;
    }

    defineExpose({
        setFormData,
    });
</script>

<style scoped lang="scss">
    .supplier-div{
        width: calc(100% - 150px);
    }
</style>
<style lang="scss">
</style>
