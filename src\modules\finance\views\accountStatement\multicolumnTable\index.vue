<template>
  <div class="delivery-method-container-block">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true" label-width="84px">
        <el-form-item :label="t('cashlumenDetailTable.label.accountingPeriod')">
          <el-date-picker
            :clearable="false"
            @change="monthRangeChange"
            v-model="searchForm.period"
            type="monthrange"
            range-separator="至"
            :start-placeholder="t('bankJournal.label.startDate')"
            :end-placeholder="t('bankJournal.label.endDate')"
            format="YYYY-MM"
            value-format="YYYYMM"
            :disabled-date="
              (date) => {
                const start = loadPeriodList.periodYearMonthStart;
                const end = loadPeriodList.periodYearMonthEnd;
                if (!start || !end) return false;
                const y = date.getFullYear();
                const m = (date.getMonth() + 1).toString().padStart(2, '0');
                const ym = `${y}${m}`;
                return ym < start || ym > end;
              }
            "
          />
        </el-form-item>
        <el-form-item :label="t('cashlumenDetailTable.label.voucher')" prop="voucherId">
          <el-select clearable v-model="searchForm.voucherId" style="width: 100px; margin-right: 10px">
            <el-option v-for="item in voucherTypeList" :key="item.id" :label="item.voucherWord" :value="item.id" />
          </el-select>
          <el-input v-model="searchForm.voucherNumberStart" placeholder="" clearable style="width: 80px" />
          <span style="margin: 0 8px">{{ t('detailsTable.label.to') }}</span>
          <el-input v-model="searchForm.voucherNumberEnd" placeholder="" clearable style="width: 80px" />
        </el-form-item>
        <el-form-item :label="t('cashlumenDetailTable.label.accountingSubject')" prop="subjectId">
          <el-select :clearable="false" filterable v-model="searchForm.subjectId" :placeholder="t('cashlumenDetailTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="item in cashAccountList" :key="item.id" :label="item.subjectCode + '_' + item.subjectName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('cashlumenDetailTable.label.isReviewed')" prop="auditStatus1">
          <el-select clearable v-model="searchForm.auditStatus1" :placeholder="t('cashlumenDetailTable.label.pleaseSelect')" style="width: 200px">
            <el-option v-for="item in statusList" :key="item.statusId" :label="item.statusName" :value="item.statusId" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('cashlumenDetailTable.label.summary')" prop="summary">
          <el-input clearable v-model="searchForm.summary" :placeholder="t('cashlumenDetailTable.label.summaryInput')" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchHandlerEvent">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="onResetHandlerEvent">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <section class="delivery-method-container">
      <div class="right-panel">
        <div class="table-container">
          <el-table ref="tableRef" v-loading="loading" :data="tableData" highlight-current-row stripe border>
            <template #empty>
              <Empty />
            </template>
            <el-table-column type="index" :label="t('cashlumenDetailTable.table.serialNo')" width="80" align="center" />
            <el-table-column :label="t('cashlumenDetailTable.table.voucherDate')" prop="voucherDate" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.voucherNo')" show-overflow-tooltip
              ><template #default="{ row }">
                <span style="color: #762adb">{{ row.voucherWord }}<span>-</span>{{ row.voucherNumber }}</span>
              </template></el-table-column
            >
            <el-table-column :label="t('cashlumenDetailTable.table.voucherSummary')" prop="summary" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.account')" prop="methodName" show-overflow-tooltip min-width="120">
              <template #default="{ row }">
                <span>{{ `${row.subjectCode}_${row.subjectFullName}` }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="t('cashlumenDetailTable.table.debitAmount')" prop="debitAmount" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.creditAmount')" prop="creditAmount" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.flowAmount')" prop="occurAmount" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.item')" prop="auxiliaryName" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 现金流量项目明细
import { voucher } from '@/modules/finance/api/index';
import tableMixin from '@/modules/finance/mixins/table';
import API from '@/modules/finance/api/accountStatementApi';
import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import type { TVoucherTypeItem } from '@/modules/finance/types/voucher';
// defineOptions({
//   name: "MulticolumnTable",
//   inheritAttrs: false,
// })
const loadPeriodList = reactive({
  periodYearMonthEnd: '',
  periodYearMonthStart: '',
});
const loadPeriod = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    loadPeriodList.periodYearMonthStart = String(response.periodYearMonthStart);
    loadPeriodList.periodYearMonthEnd = String(response.periodYearMonthEnd);
    searchForm.period = [loadPeriodList.periodYearMonthStart, loadPeriodList.periodYearMonthEnd];
    searchForm.periodYearMonthStart = loadPeriodList.periodYearMonthStart;
    searchForm.periodYearMonthEnd = loadPeriodList.periodYearMonthEnd;
    loadcashAccountList();
  } catch (error) {
    loading.value = false;
  }
};
const statusList = ref([
  {
    statusId: -1 ,
    statusName: t('cashlumenDetailTable.label.all'),
  },
  {
    statusId: 0,
    statusName: t('cashlumenDetailTable.label.pendingReview'),
  },
  {
    statusId: 2,
    statusName: t('cashlumenDetailTable.label.approved'),
  },
]);
const searchForm = reactive({
  period: [loadPeriodList.periodYearMonthStart, loadPeriodList.periodYearMonthEnd],
  voucherId: undefined,
  subjectId: undefined,
  voucherNumberStart: undefined,
  voucherNumberEnd: undefined,
  summary: undefined,
  auditStatus: undefined,
  auditStatus1: undefined,
  periodYearMonthStart: loadPeriodList.periodYearMonthStart,
  periodYearMonthEnd: loadPeriodList.periodYearMonthEnd,
});
const { loading, tableData, total, paginationInfo, headFormRef, router, path, onSearchHandler, onResetHandler, onPaginationChangeHandler, onDeleteHandler, onStatusChangeHandler } = tableMixin({
  searchForm,
  isLimit: false,
  tableGetApi: API.getCashFlowList,
});
const monthRangeChange = (val: [string, string]) => {
  if (val?.length) {
    searchForm.periodYearMonthStart = val[0];
    searchForm.periodYearMonthEnd = val[1];
  } else {
    searchForm.periodYearMonthStart = '';
    searchForm.periodYearMonthEnd = '';
  }
};
const onSearchHandlerEvent = () => {
  if (searchForm.auditStatus1 === -1) {
    searchForm.auditStatus = undefined;
  } else {
    searchForm.auditStatus = searchForm.auditStatus1;
  }
  onSearchHandler()
}
const onResetHandlerEvent = () => {
  searchForm.period = [loadPeriodList.periodYearMonthStart, loadPeriodList.periodYearMonthEnd];
  searchForm.periodYearMonthStart = loadPeriodList.periodYearMonthStart;
  searchForm.periodYearMonthEnd = loadPeriodList.periodYearMonthEnd;
  searchForm.voucherNumberStart = undefined;
  searchForm.voucherNumberEnd = undefined;
  searchForm.auditStatus = undefined;
  onResetHandler();
};
/* 凭证字 */
const voucherTypeList = reactive<TVoucherTypeItem[]>([]);
const getVoucherTypeList = async () => {
  try {
    const res = await voucher.queryVoucherTypeList({});
    voucherTypeList.push(...res);
  } catch (e) {}
};
/* 会计科目 */
interface CashAccountItem {
  id: number | string;
  subjectCode: string;
  subjectName: string;
  statusId: number;
  statusName: string;
}
const cashAccountList = ref<CashAccountItem[]>([]);
const loadcashAccountList = async () => {
  try {
    const response = await API.getCashFlowSubjectSelect({
      periodYearMonthEnd: loadPeriodList.periodYearMonthEnd,
      periodYearMonthStart: loadPeriodList.periodYearMonthStart,
    });
    cashAccountList.value = response || [];
    onSearchHandler();
  } catch (error) {
    cashAccountList.value = [];
  }
};
onMounted(() => {
  loadPeriod();
  getVoucherTypeList();
});
</script>

<style lang="scss" scoped>
.delivery-method-container {
  .right-panel {
    padding: 20px;
    background-color: #fff;

    .search-card,
    .toolbar-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }

    .search-card {
      :deep(.el-form--inline .el-form-item) {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 20px;
    }
  }
}

:deep(.el-table) {
  .el-switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
  }
}
</style>
