import request from "@/core/utils/request";

// 合同列表查询参数接口
interface ContractQueryParams {
  name?: string;
  party?: string;
  status?: string;
  pageNum: number;
  pageSize: number;
}

// 合同数据接口
interface ContractData {
  name: string;
  party: string;
  code: string;
  status: string;
  type: string;
  period: string;
  createTime: string;
  // 可以根据实际需求添加更多字段
}

//合同审核列表查询参数
export interface ContractReviewQueryParams extends PageQuery{
  contractName?: string;//合同名称
  contractPartner?: string;//签订对方
}
//合同审核列表数据参数
export interface ContractReviewResp {
  contractId?: string;//合同ID
  contractName?: string;//合同名称
  contractPartner?: string;//签订对方
  contractCode?: string;//合同编码
  signType?: number;//签订类型
  createTime?: number;//录入时间
  approveStatus?: number;//审核状态
  auditTime?: number;//审核时间
  auditPerson?: string;//审核人
}

/**
 * 获取合同列表
 */
const CONTRACT_BASE_URL = "/supply-pms";
export function getContractList(data: ContractQueryParams) {
  return request({
    url: `${CONTRACT_BASE_URL}/contract/page`,
    method: "post",
    data,
  });
}

/**
 * 获取合同详情
 */
export function getContractDetail(data: any) {
  return request({
    url: `${CONTRACT_BASE_URL}/contract/detail`,
    method: "post",
    data,
  });
}

/**
 * 新增合同
 */
export function createContract(data: ContractData) {
  return request({
    url: `${CONTRACT_BASE_URL}/contract/save`,
    method: "post",
    data,
  });
}

/**
 * 更新合同
 */
export function updateContract(data: ContractData) {
  return request({
    url: `${CONTRACT_BASE_URL}/contract/update`,
    method: "post",
    data,
  });
}

/**
 * 作废合同
 */
export function voidContract(data: any) {
  return request({
    url: `${CONTRACT_BASE_URL}/contract/cancel`,
    method: "post",
    data,
  });
}

/**
 * 删除合同
 */
export function deleteContract(data: any) {
  return request({
    url: `${CONTRACT_BASE_URL}/contract/delete`,
    method: "post",
    data,
  });
}

/**
 * 审核通过
 */
export function approved(data: any) {
  return request({
    url: `${CONTRACT_BASE_URL}/contract/approved`,
    method: "post",
    data,
  });
}

/**
 * 审核驳回
 */
export function rejected(data: any) {
  return request({
    url: `${CONTRACT_BASE_URL}/contract/rejected`,
    method: "post",
    data,
  });
}

/**
 * 获取销售人员
 */
export function querySalesPersonUser() {
  return request({
    url: `/supply-base/user/all`,
    method: "get",
  });
}
