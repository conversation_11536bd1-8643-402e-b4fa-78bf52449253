<template>
  <div class="app-container">
    <div class="afterSaleDetail">
      <div class="page-title">
        <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
        <div style="display: flex;justify-content: flex-start;align-items: center">
          <div><span style="margin-right: 16px">{{ $t("afterSaleManagement.label.orderReturnCode") }}：{{form.orderReturnCode}}</span></div>
          <div class="purchase">
            <span class="purchase-status purchase-status-color1" v-if="form.orderReturnStatus==1">{{$t('afterSaleManagement.statusList.inReview')}}</span>
            <span class="purchase-status purchase-status-color5" v-if="form.orderReturnStatus==3">{{$t('afterSaleManagement.statusList.declined')}}</span>
            <span class="purchase-status purchase-status-color3" v-if="form.orderReturnStatus==2">{{$t('afterSaleManagement.statusList.agreed')}}</span>
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" ref="formRef" label-width="82px" label-position="right">
          <div class="title-lable">
            <div class="title-content">
              {{ $t("afterSaleManagement.label.afterSalesInformation") }}
            </div>
            <div class="ml10px">
              <el-icon
                v-if="!form.isNameDecrypted || !form.isMobileDecrypted"
                @click="handleViewInfo()"
                color="#762ADB "
                size="16"
              >
                <View />
              </el-icon>
            </div>
          </div>
          <div>
            <el-row>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.orderCode')"
                  prop="orderCode"
                >
                  <span>{{form.orderCode}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.orderBatchCode')"
                  prop="orderBatchCode"
                >
                  <span>{{form.orderBatchCode}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.customerName')"
                  prop="customerName"
                >
                  <span>{{form.customerName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.receiveName')"
                  prop="contactPerson"
                >
                  <span>{{form.contactPerson}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.contactMobile')"
                  prop="contactMobile"
                >
                  <span style="word-break: break-all" v-if="form.contactMobile">{{form.contactAreaCode}}-{{form.contactMobile}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.createUserName')"
                  prop="createUserName"
                >
                  <span>{{form.createUserName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.createTime')"
                  prop="createTime"
                >
                  <span>{{parseDateTime(form.createTime, "dateTime")}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.returnType')"
                  prop="returnType"
                >
                  <span v-if="form.returnType == 3">{{$t('afterSaleManagement.typeList.refund')}}</span>
                  <span v-else-if="form.returnType == 2">{{$t('afterSaleManagement.typeList.returnAndRefund')}}</span>
                  <span v-else-if="form.returnType == 1">{{$t('afterSaleManagement.typeList.returnGoods')}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="5" v-show="form.returnType == 2 || form.returnType == 1">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.returnAddress')"
                  prop="returnAddress"
                >
                  <span>{{form.address}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="4" v-show="form.returnType == 2 || form.returnType == 1">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.returnMethod')"
                  prop="returnType"
                >
                  <span style="word-break: break-all">{{form.returnMethodName}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-show="form.returnType == 2 || form.returnType == 1">
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.operationReturnTime')"
                  prop="operationReturnTime"
                >
                  <span>{{form.operationReturnTime ? parseDateTime(form.operationReturnTime, "dateTime") : '-'}}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">
              {{ $t("afterSaleManagement.label.refundGoods") }}
            </div>
          </div>
          <div>
            <el-table
              v-loading="loading"
              :data="form.orderReturnDetailList"
              highlight-current-row
              stripe
            >
<!--              <el-table-column :label="$t('afterSaleManagement.label.productInformation')" min-width="230">-->
<!--                <template #default="scope">-->
<!--                  <div class="product-div">-->
<!--                    <div class="picture">-->
<!--                      <img :src="scope.row.imagesUrls" alt="">-->
<!--                    </div>-->
<!--                    <div class="product">-->
<!--                      <div>-->
<!--                        <span class="product-key">{{$t('afterSaleManagement.label.productCode')}}：</span>-->
<!--                        <span class="product-value">{{scope.row.productCode}}</span>-->
<!--                      </div>-->
<!--                      <div class="product-name">{{scope.row.productName}}</div>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </template>-->
<!--              </el-table-column>-->
                <el-table-column :label="$t('afterSaleManagement.label.productCode')" prop="productCode"  min-width="160">
                </el-table-column>
                <el-table-column :label="$t('afterSaleManagement.label.productInformation')" prop="productName"  min-width="230">
                </el-table-column>
              <el-table-column :label="$t('afterSaleManagement.label.productSpec')" prop="productSpecs" min-width="100" show-overflow-tooltip/>
              <el-table-column :label="$t('afterSaleManagement.label.quantityShipped')" prop="productQty" min-width="100" show-overflow-tooltip/>
              <el-table-column :label="$t('afterSaleManagement.label.returnQuantity')" prop="returnQuantity" min-width="120" show-overflow-tooltip/>
              <el-table-column :label="$t('afterSaleManagement.label.applyReturnAmount')" prop="returnAmount" min-width="120" align="right" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="form.currencyCode == 'CNY'">￥{{scope.row.returnAmount}}</span>
                  <span v-else>${{scope.row.returnAmount}}</span>
                </template>
              </el-table-column>
            </el-table>
            <div  class="table-title">
              <div>
                <span>{{t('afterSaleManagement.label.total')}}：</span>
              </div>
              <div>
                <span class="mr16px">{{t('afterSaleManagement.label.productCount')}}：<span  style="font-size: 18px; color: #C00C1D " v-if="form.orderReturnDetailList && form.orderReturnDetailList.length > 0">{{form.refundQty}}</span></span>
                <span>{{t('afterSaleManagement.label.totalMoney')}}：
                        <span  style="font-size: 18px; color: #C00C1D ">
                             <template v-if="form.orderReturnDetailList && form.orderReturnDetailList.length>0">
                               <span v-if="form.currencyCode == 'CNY'">￥</span>
                               <span v-else>$</span>
                             </template>
                          {{form.totalAmount}}
                        </span>
                </span>
              </div>
            </div>
          </div>
          <div class="title-lable">
            <div class="title-content">
              {{ $t("afterSaleManagement.label.afterSaleSummary") }}
            </div>
          </div>
          <div>
            <el-row>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.returnAmount')"
                  prop="orderCode"
                >
                  <span v-if="form.currencyCode == 'CNY'">￥</span><span v-else>$</span>{{form.returnAmount}}
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item
                  class="form-item"
                  :label="$t('afterSaleManagement.label.refundQty')"
                  prop="orderCode"
                >
                  <span>{{form.refundQty}}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">
              {{ $t("afterSaleManagement.label.negotiationRecord") }}
            </div>
          </div>
          <div style="position: relative">
            <div v-if="form.orderReturnStatus != 1">
              <div style="display: flex;justify-content: flex-start;width: 100%">
                <div style="margin-right: 16px;color: #52585F;font-size: 14px;width: 140px">{{parseDateTime(form.returnTime, "dateTime")}}</div>
                <div style="width: 28px" v-if="form.orderReturnStatus == 3"><img style="width: 28px;height: 28px" src="@/core/assets/images/refuseRefund.png" alt=""></div>
                <div style="width: 28px" v-if="form.orderReturnStatus == 2"><img style="width: 28px;height: 28px" src="@/core/assets/images/agreeRefund.png" alt=""></div>
                <div style="width: calc(100% - 168px);">
                  <div style="color: rgba(0, 0, 0, 1);font-size: 20px;line-height: 28px;font-weight: 600;" v-if="form.orderReturnStatus == 3">{{$t('afterSaleManagement.button.refused')}}</div>
                  <div style="color: rgba(0, 0, 0, 1);font-size: 20px;line-height: 28px;font-weight: 600;" v-if="form.orderReturnStatus == 2">{{$t('afterSaleManagement.button.agree')}}</div>
                  <div><span class="label-span">{{$t('afterSaleManagement.label.returnRemark')}}：</span><span class="message-span">{{form.returnRemark}}</span></div>
                </div>
              </div>
            </div>
            <div v-if="form.orderReturnStatus != 1" style="position: absolute;height: 75px;width: 1px;left: 167px;top: 21px;background: #EDEFF2;"></div>
            <div :style="form.orderReturnStatus != 1 ? 'margin-top: 40px' : ''">
              <div style="display: flex;justify-content: flex-start;width: 100%">
                <div style="margin-right: 16px;color: #52585F;font-size: 14px;width: 140px">{{parseDateTime(form.createTime, "dateTime")}}</div>
                <div style="width: 28px"><img style="width: 28px;height: 28px" src="@/core/assets/images/applyRefund.png" alt=""></div>
                <div style="width: calc(100% - 168px);">
                  <div style="color: rgba(82, 88, 95, 1);font-size: 20px;line-height: 28px;font-weight: 600;">{{$t('afterSaleManagement.label.initiateRefundRequestsAndWaitForProcessing')}}</div>
                  <div><span class="label-span">{{$t('afterSaleManagement.label.refundAmount')}}：</span><span class="message-span"><span v-if="form.currencyCode == 'CNY'">￥</span><span v-else>$</span>{{form.returnAmount}}</span> </div>
                  <div><span class="label-span">{{$t('afterSaleManagement.label.returnReason')}}：</span><span class="message-span">{{form.returnReasonName}}</span></div>
                  <div><span class="label-span">{{$t('afterSaleManagement.label.returnReasonRemark')}}：</span><span class="message-span">{{form.returnReasonRemark}}</span></div>
                  <div style="display: flex;justify-content: flex-start;">
                    <div class="label-span">{{$t('afterSaleManagement.label.returnUrls')}}：</div>
                    <div v-if="form.returnAttachmentUrls && form.returnAttachmentUrls.length > 0" style="width: calc(100% - 70px)">
                      <!--<upload-multiple
                        :tips="`图片比例1:1`"
                        :isPrivate="`public-read`"
                        :modelValue="form.returnAttachmentUrls"
                        ref="detailPicsRef"
                        :limit="form.returnAttachmentUrls?.length"
                        :formRef="formUpdateRef"
                        class="modify-multipleUpload"
                        name="detailPic"
                        :isDelete="false">
                        <template #default="{ file }">
                          点击上传
                        </template>
                      </upload-multiple>-->
                      <!--<upload-multiple
                        :isDelete="false"
                        :showUploadBtn="false"
                        :isShowTip="false"
                        :listType="`text`"
                        :tips="''"
                        :fileSize="20"
                        :fileType="['jpg','jpeg','png','pdf','zip','rar']"
                        :isPrivate="`public-read`"
                        :modelValue="form.returnAttachmentUrls"
                        @update:model-value="onChangeMultiple"
                        ref="detailPicsRef"
                        :limit="form.returnAttachmentUrls?.length"
                        :formRef="formUpdateRef"
                        class="modify-multipleUpload"
                        name="detailPic"
                      >
                        <template #default="{ file }">点击上传</template>
                      </upload-multiple>-->
                      <upload-multiple
                        :tips="''"
                        :fileSize="20"
                        :fileType="['jpg','jpeg','png','pdf','zip','rar']"
                        :isPrivate="`public-read`"
                        :modelValue="form.returnAttachmentUrls"
                        ref="detailPicsRef"
                        @update:model-value="onChangeMultiple"
                        :limit="form.returnAttachmentUrls?.length"
                        :formRef="formUpdateRef"
                        class="modify-multipleUpload"
                        name="detailPic"
                        :showUploadBtn="false"
                        :listType="`icon-card`"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="title-lable" v-if="form.orderReturnStatus != 1">
            <div class="title-content">
              {{ $t("afterSaleManagement.label.operationLog") }}
            </div>
          </div>
          <div v-if="form.orderReturnStatus != 1">
            <el-table
              :data="operationLogList"
              highlight-current-row
              stripe
            >
              <el-table-column :label="$t('afterSalesReason.label.createTime')" prop="createTime" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.createTime">{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('afterSalesReason.label.operationName')" prop="operationName" show-overflow-tooltip></el-table-column>
              <el-table-column :label="$t('afterSalesReason.label.createUserName')" prop="createUserName" show-overflow-tooltip></el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button type="primary" @click="handleClose">{{ $t("common.reback") }}</el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
    import afterSaleManagementApi, {AfterSaleDetailDto} from "@/modules/oms/api/afterSaleManagement";
    import { parseDateTime } from "@/core/utils/index.js";
    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    defineOptions({
        name: "AfterSaleDetail",
        inheritAttrs: false,
    });
    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const type = route.query.type;
    const orderReturnCode = route.query.orderReturnCode;
    const submitLoading = ref(false)
    const formRef = ref(ElForm);
    const form = reactive<AfterSaleDetailDto>({});
    const operationLogList = ref([])
    const handleViewInfo = async () => {
      console.log("form.id====",form.id)
      try {
        const data = await afterSaleManagementApi.querySrcInfo({id: form.id});
        form.contactPerson = data.contactPerson;
        form.contactMobile = data.contactMobile;
        form.isNameDecrypted = true;
        form.isMobileDecrypted = true;
      } catch (e) {}
    };
    async function handleClose() {
        await tagsViewStore.delView(route);
        router.go(-1);
    };
    function queryDetail(){
        let params = {
            orderReturnCode:orderReturnCode
        }
        afterSaleManagementApi.orderReturnDetail(params).then((data) => {
            Object.assign(form,data)
            if(form.returnType == 2 || form.returnType == 1){
              form.address = form.countryName+form.provinceName+form.cityName+form.districtName+form.address
            }
            if (form.returnAttachmentUrls && typeof form.returnAttachmentUrls === "string") {
              form.returnAttachmentUrls = JSON.parse(form.returnAttachmentUrls);
            }
            if(form.orderReturnStatus != 1){
              operationLog()
            }
        })
    }
    //操作记录
    function operationLog() {
        let params = {
            orderReturnCode:orderReturnCode
        }
        afterSaleManagementApi.getLogByBusinessCode(params).then((data)=>{
            operationLogList.value = data
        }).finally(() => {

        });
    }
    onMounted(() => {
        queryDetail()
    })

</script>
<style scoped lang="scss">
  .afterSaleDetail {
    background: #FFFFFF;
    border-radius: 4px;
    .page-content {
      padding: 0px 20px 9px 20px;
      .item_content {
        border-bottom: 1px solid #e5e7f3 !important;
      }
      .select_goods:hover {
        color: var(--el-color-primary);
      }
      .title-lable {
        padding: 20px 0px 15px 0px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        &::before {
          content: " ";
          display: inline-block;
          width: 4px;
          height: 16px;
          background: var(--el-color-primary);
          border-radius: 2px;
          margin-right: 12px;
        }
      }
      .form-item{
        .el-form-item--default .el-form-item__label{
          color: #90979E !important;
        }
      }
      .table-title{
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 50px;
        background: #F4F6FA;
        box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #52585F;
        font-style: normal;
        padding: 15px 12px;
      }
    }
    .label-span{
      color: rgba(37, 40, 41, 0.50);
      font-size: 14px;
      line-height: 20px;
      width: 70px;
    }
    .message-span{
      color: rgba(37, 40, 41, 0.90);
      font-size: 14px;
      line-height: 20px;
    }
    ::v-deep .el-upload__tip{
      display: none !important;
    }
    .el-form-item--default{
      margin-bottom: 0px !important;
    }
    ::v-deep .el-upload {
      display: none;
    }
  }
</style>
