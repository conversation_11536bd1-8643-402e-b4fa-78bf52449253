
export default {
  qualityInspectionOrder: {
    label: {
      inspectionCode: "质检单号",
      inspectionType: "质检类型",
      status: "状态",
      receivingStatus: "是否领单",
      statusCopy: "质检状态",
      inspectionResultType: "质检结果",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      receivingOrderCode: "收运单号",
      warehouseArea: "库区",
      inspectionTime: "质检时间",
      inspector: "质检人",
      remark: "备注",
      createType:'创建类型',
      workOrderNum:'工单号',
      createUserName:'创建人',
      createTime: '创建时间',

      basicInformation: "基本信息",
      qualityInspectionInformation: "质检信息",
      productInformation:"商品信息",
      productCode:"商品编码",
      productSpec: "规格",
      productActualQty: "收运数量",
      totalStockQty: "总库存数量",
      qualityInspectionQty: "质检数量",
      qualityInspectionQtyWei: "质检重量(kg)",
      abnormalQty: "异常数量",
      abnormalWei: "异常重量(kg)",
      abnormalCause: "异常原因",
      isAbnormal: "是否有异常",
      weightUnitName: "kg",
      abnormalYsn: "异常YSN",

      activeBtn: "异常",
      inactiveBtn: "无异常",

      qualityInspectionInfo: "质检量",
      productCount: "数量",
      productWeight: "重量(kg)",
      receivingInfo: "领用信息",
      receivingUserName: "领单人",
      receivingTime: "领单时间",
      qualityInspectionProductCount: "质检总数量",
      qualityInspectionProductWeight: "质检总重量(kg)",
      ysnCode: "YSN码",
      boxCode: "箱码",
      quantity:'数量',
      scanTime: "扫描时间",
      productUnitName: '单位'
    },
    placeholder:{
        inspectionCode :'请输入大于等于4位的单号',
        keywords: "请输入商品名称/编码",
        supplierName: "请输入供应商名称",
        productCategory: "请输入关键词搜索分类",
        weight: "请输入(单位：KG)",
        lossRatio: "请输入0-100%",
    },
    inspectionTypeList: {
         collectionTransportationQualityInspection: "收运质检",
         inWarehouseQualityInspection: "库内巡检",
    },
    createTypeList: {
        createManually: "手动创建",
    },
    statusList: {
        draft: "草稿",
        QualityInspectionFinish: "质检完成",
        qualityInspectioning: "质检中",
    },
      claimTypeList: {
          yes: "是",
          no: "否",
      },
      inspectionResultTypeList: {
          abnormal: "异常",
          NoExceptions: "无异常",
      },
     dateTypeList: {
         createDate: "创建时间",
         claimDate: "领用时间",
         qualityInspectionDate: "质检时间",
     },
    shelfLifeUnitList:{
        day: "天",
        month: "月",
        year: "年",
    },
    button: {
      addQualityInspectionOrder: "新建质检单",
      editQualityInspectionOrder: "编辑质检单",
      qualityInspection: "质检",
      addProduct: "添加商品",
      saveDraft: "保存草稿",
      finishQualityInspection: "完成质检",
      upload: "上传",
      attachment: "附件",
      claim: "领单",
      cancelClaim: '取消领单'
    },
    title: {
        addProduct: "添加商品",
        productYsn: "商品YSN码-",
    },
    message: {
      receiveOrCancelTipTitle: "温馨提示",
      receiveOrCancelTips: "确认取消领取单据？",
      receiveOrCancelConcel: "已取消领单！",
      receiveSucess: "领单成功！",
      receiveOrCancelSucess: "取消领单成功！",
      deleteTips: "是否确认删除已创建的质检单？",
      deleteConcel: "已取消删除！",
      deleteSucess: "删除成功！",
      saveDraftSucess: "保存草稿成功！",
      finishQualityInspectionTips: "完成质检后不能再次进行修改，是否继续？",
      finishQualityInspectionSucess: "完成质检成功！",
      finishQualityInspectionConcel: "已取消完成质检！",
      qualityInspectionOrderTips: "质检信息不能为空！",
      qualityInspectionLessTips: "质检信息中最少有一条异常数量！",
      codeValideTips: "查询单号必须大于等于4位",
    },
    rules: {
        inspectionType: "请选择质检类型",
        receivingOrderCode: "请选择收运单号",
        inspectionResultType: "请选择是否有异常",
        warehouseAreaCode: "请选择库区",
        abnormalQty: "请输入异常数量",
        // abnormalQtyFormat: '请输入大于0的数字，支持小数点前8位后3位',
        abnormalQtyFormat: '异常数量支持大于0的整数',
        abnormalWeight: '请输入异常重量',
        abnormalWeightFormat: '请输入大于0的数字，支持小数点前8位后3位',
        abnormalCause: "请选择异常原因",
        imageUrlList: "请上传商品图片",
    },
  },
};
