/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-14 10:58:12
 * @LastEditors: ch<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-10 14:29:53
 * @FilePath: \supply-manager-web\src\modules\wms\api\productManagement.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/core/utils/request";
const PRODUCTBRAND_BASE_URL = "/supply-biz-common";

class ProductMgAPI {
  /** 分页查询 */
  static getPageList(data?: PageQuery) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/product/page`,
      method: "post",
      data,
    });
  }

  // 根据商品名称或者编码查询商品
  static getProductCodeAndName(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/product/codeSelect`,
      method: "post",
      data,
    });
  }

  // 商品分类查询
  static queryCategoryTreeList(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/category/queryCategoryTreeList`,
      method: "post",
      data,
    });
  }

  // 商品单位查询
  static queryProductUnitList(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/unit/select`,
      method: "post",
      data,
    });
  }

  // 商品品牌查询
  static queryProductBrandList(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/brand/select`,
      method: "post",
      data,
    });
  }

  // 保存商品
  static saveProduct(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/product/save`,
      method: "post",
      data,
    });
  }

  // 根据id查询商品详情
  static getProductDetailById(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/product/detail`,
      method: "post",
      data,
    });
  }

  // 修改商品
  static updateProduct(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/product/update`,
      method: "post",
      data,
    });
  }

  // 商品启用禁用
  static updateEnableStatus(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/product/updateEnableStatus`,
      method: "post",
      data,
    });
  }
}

export default ProductMgAPI;
