import { defineMockWms } from "./base";

export default defineMockWms([
  {
    url: "productStock/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
             {
               id: '1',
               productCode: "商品编码",
               productName: "商品名称",
               warehouseAreaName: "库区",
               totalStockQty: "总库存",
               availableStockQty: "可用库存",
               lockedStockQty: "锁定库存",
               productUnitName: "单位",
               productSpec: "规格",
               fullCategoryName: "商品分类",
             },
             {
               id: '2',
               productCode: "商品编码",
               productName: "商品名称",
               warehouseAreaName: "库区",
               totalStockQty: "总库存",
               availableStockQty: "可用库存",
               lockedStockQty: "锁定库存",
               productUnitName: "单位",
               productSpec: "规格",
               fullCategoryName: "商品分类",
             },
             {
               id: '3',
               productCode: "商品编码",
               productName: "商品名称",
               warehouseAreaName: "库区",
               totalStockQty: "总库存",
               availableStockQty: "可用库存",
               lockedStockQty: "锁定库存",
               productUnitName: "单位",
               productSpec: "规格",
               fullCategoryName: "商品分类",
             },
             {
               id: '4',
               productCode: "商品编码",
               productName: "商品名称",
               warehouseAreaName: "库区",
               totalStockQty: "总库存",
               availableStockQty: "可用库存",
               lockedStockQty: "锁定库存",
               productUnitName: "单位",
               productSpec: "规格",
               fullCategoryName: "商品分类",
             },
        ],
        total: '4',
      },
      msg: "一切ok",
    },
  },

  //  根据id查询库存操作日志
  {
      url: "productStockLog/page",
      method: ["POST"],
      body: {
          code: 0,
          data:{
            records: [
              {
                operationType: 1,
                sourceOrderCode: "对应单据号",
                operationQty: "操作库存",
                changeType: 0,
                originalTotalStockQty: 100,
                totalStockQty: 90,
                availableStockQty: 90,
                lockedStockQty: 10,
                warehouseAreaName: "库区",
                operatorName: "操作人",
                operationTime: 1740473528000,
              },
              {
                operationType: 5,
                sourceOrderCode: "对应单据号",
                operationQty: "操作库存",
                changeType: 0,
                originalTotalStockQty: 100,
                totalStockQty: 90,
                availableStockQty: 90,
                lockedStockQty: 10,
                warehouseAreaName: "库区",
                operatorName: "操作人",
                operationTime: 1740473528000,
              },
              {
                operationType: 4,
                sourceOrderCode: "对应单据号",
                operationQty: "操作库存",
                changeType: 0,
                originalTotalStockQty: 100,
                totalStockQty: 90,
                availableStockQty: 90,
                lockedStockQty: 10,
                warehouseAreaName: "库区",
                operatorName: "操作人",
                operationTime: 1740473528000,
              },
              {
                operationType: 3,
                sourceOrderCode: "对应单据号",
                operationQty: "操作库存",
                changeType: 1,
                originalTotalStockQty: 100,
                totalStockQty: 90,
                availableStockQty: 90,
                lockedStockQty: 10,
                warehouseAreaName: "库区",
                operatorName: "操作人",
                operationTime: 1740473528000,
              },
            ],
            total: '4',
            lockedStockQty: 200,
            availableStockQty: 200,
            totalStockQty: 400,
          },
          msg: "一切ok",
      },
  },

    //  添加采购商品
    {
        url: "product/product/save",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

    //  编辑采购商品
    {
        url: "product/product/update",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

    // 设置供应商
    {
        url: "product/product/batchUpdateProductSupplier",
        method: ["POST"],
        body: {
            code: 0,
            data: null,
            msg: "设置供应商成功",
        },
    },

    // 设置默认供应商
    {
        url: "product/product/batchUpdateProductDefaultSupplier",
        method: ["POST"],
        body: {
            code: 0,
            data: null,
            msg: "设置默认供应商成功",
        },
    },


  // 新增采购商品
  {
    url: "product/add",
    method: ["POST"],
    body: {
        code: 0,
        data: null,
        msg: "新增采购商品成功",
    },
  },

]);
