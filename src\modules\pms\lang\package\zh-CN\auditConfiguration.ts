export default {
  pmsAuditConfiguration: {
    name: "姓名",
    mobile: "手机号",
    ruleName: "规则名称",
    ruleType: "规则类型",
    status: "状态",
    creator: "创建人",
    createTime: "创建时间",
    updater: "更新人",
    updateTime: "更新时间",
    remark: "备注",
    action: "操作",
    add: "新增",
    edit: "编辑",
    delete: "删除",
    detail: "详情",
    confirmDelete: "确认删除吗？",
    pleaseInputRuleName: "请输入规则名称",
    pleaseInput: '请输入',
    account: '账号',
    scope: '审核范围',
    department: '部门',
    createdAt: '创建时间',
    search: '搜索',
    reset: '重置',
    addAuditor: '添加审核人',
    dialogTitle: '添加审核人',
    selectUser: '选择人员',
    auditScope: '审核范围',
    order: '订单',
    afterSaleOrder: '售后单',
    supplierContract: '供应商合同',
    pleaseSelect: '请选择',
    cancel: '取消',
    confirm: '确定',
    prompt: '提示',
    confirmDeletePrompt: '确定删除此审核人吗？',
    puchaseOrder: '采购单',
    purchaseReturns: '采购退货单',
  }
};
