import request from "@/core/utils/request";

const AUTH_BASE_URL = "/supply-base";

class AuthAPI {
  /** 登录 接口*/
  static login(data: LoginData) {
    /*const formData = new FormData();
    formData.append("username", data.username);
    formData.append("password", data.password);
    formData.append("captchaKey", data.captchaKey);
    formData.append("captchaCode", data.captchaCode);*/
    const tenantId = data.tenantId;
    delete data.tenantId;
    return request<any, LoginResult>({
      url: `${AUTH_BASE_URL}/login/supplyToken`,
      method: "post",
      data,
      headers: {
        "X-Tenant-ID": tenantId,
      },
      // headers: {
      //   "Content-Type": "multipart/form-data",
      // },
    });
  }

  /** 注销 接口*/
  static logout(params: {}) {
    return request({
      url: `${AUTH_BASE_URL}/logout/token`,
      method: "delete",
      params
    });
  }

  /** 获取验证码 接口*/
  static getCaptcha() {
    return request<any, CaptchaResult>({
      url: `${AUTH_BASE_URL}/captcha`,
      method: "get",
    });
  }
}

export default AuthAPI;

/** 登录请求参数 */
export interface LoginData {
  /** 用户名 */
  userName: string;
  /** 密码 */
  password: string;
  /** 验证码缓存key */
  // captchaKey: string;
  // /** 验证码 */
  // captchaCode: string;
  /** 企业编码 */
  tenantId?: string;
  loginType: string;
}

/** 登录响应 */
export interface LoginResult {
  /** 访问token */
  accessToken?: string;
  /** 过期时间(单位：毫秒) */
  expires?: number;
  /** 刷新token */
  refreshToken?: string;
  /** token 类型 */
  tokenType?: string;
  /** 系统ID */
  systemId: string;
  token_type?: string;
  access_token?: string;
  loginResultVO?: any;
}

/** 验证码响应 */
export interface CaptchaResult {
  /** 验证码缓存key */
  captchaKey: string;
  /** 验证码图片Base64字符串 */
  captchaBase64: string;
}
