export default {
  afterSaleManagement:{
    label:{
      orderReturnCode:'售后单号',
      orderCode:'订购单号',
      orderBatchCode:'批次单号',
      deliveryBatch: '交付批次',
      startTime: "开始时间",
      endTime: "结束时间",
      dateType: "时间选择",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      orderReturnStatus: "状态",
      productNames: "商品",
      customerName: "客户",
      returnReasonName: "售后原因",
      returnAmount: "售后金额",
      createTime:"申请时间",
      returnTime:"处理时间",
      orderInformation:"订单信息",
      customer:'客户',
      warehouse: "配货仓库",
      returnType:'售后类型',
      returnAddress:'退货地址',
      returnMethod:'退货方式',
      operationReturnTime:'退货时间',
      refundGoods:"商品清单",
      productInformation:"商品",
      productSpec:"规格",
      quantityShipped:"发货数量",
      totalAmount:"合计金额",
      returnQuantity:"申请数量",
      applyReturnAmount:"申请金额",
      totalMoney:"金额合计",
      total:'合计',
      productCount:"商品数量合计",
      afterSalesInformation:"售后信息",
      refundAmount:'退款金额',
      returnAttachmentUrls:"上传凭证",
      returnReasonRemark:"退款说明",
      returnReason: "退款原因",
      returnUrls: "售后凭证",
      returnRemark: "处理说明",
      productName:"商品",
      productCode:"商品编码",
      quantityOrdered:"订购数量",
      createUserName:"申请人",
      receiveName:"收货人",
      contactMobile:"联系方式",
      negotiationRecord:"协商记录",
      afterSaleHandel:"售后处理",
      operationLog:"操作日志",
      initiateRefundRequestsAndWaitForProcessing: "发起售后申请，等待处理",
      approveUser:'审核人',
      inventoryStatus:'入库状态',
      afterSaleSummary:'售后汇总',
      refundQty:'售后数量',
    },
    placeholder:{
      selectBatchCode:"选择交付批次",
      productName:"请输入商品名称",
      automaticCalculation:"自动计算",
      pleaseChooseDate:'请选择日期',
      chooseTime:'送达时间',
    },
    dateTypeList:{
      createDate: "申请时间",
      handlerDate: "处理时间",
    },
    statusList:{
      all:"全部",
      inReview:'审核中',
      agreed:'已同意',
      toBeTreated:"待处理",
      declined:"已拒绝",
      complete:"已完成",
      refuseRefund:"拒绝退款",
      agreeRefund:"同意退款",
    },
    typeList:{
      refund:'退款',
      returnGoods:'退货',
      returnAndRefund:'退货退款',
    },
    button:{
      afterSaleApply:'售后申请',
      detailBtn:"查看详情",
      edit:"售后处理",
      afterSaleDetail:"售后详情",
      addRefundGoods:"添加售后商品",
      refuseRefund:"拒绝退款",
      agreeRefund:"同意退款",
      refused:"拒绝",
      agree:"同意",
      confirm:"确定",
      submit:"提交",
    },
    rules:{
      returnQuantityNull:"请输入申请退款数量",
      returnQuantity:"请输入大于0的数字，支持小数点后两位",
      returnAmountNull:"请输入申请退款金额",
      returnAmount:"请输入大于等于0的数字，支持小数点后两位",
      returnReasonId:"请选择售后原因",
      returnReasonRemark:"请输入退款说明",
      orderCode:"请输入订购单号",
      orderBatchCode:"请选择交付批次",
      customer:'请选择客户',
      returnType:'请选择售后类型',
      returnMethod:'请选择退货方式',
      approveUser:'请选择审核人',
      refundAmount:'请输入退款金额',
      refundAmountFormat:'请输入大于等于0的数字，支持小数点后两位',
      returnDate:'请选择退货日期',
      returnTime:'请选择退货时间',
      address:'请输入地址',
      districtId:'请选择区县',
      cityId:'请选择市',
      provinceId:'请选择省',
      countryId:'请选择国家',
    },
    message:{
      saveTips:'确定同意退款？',
      addSuccess:'提交成功！',
      saveCancel:'已取消！',
      refundProductTips:'退款商品不能为空',
      sameProduct:"商品重复",
      processRemark:"请输入售后处理",
      handelSuccess:"处理成功！",
      returnQuantityTips:"申请退款数量不能超过发货数量",
      returnAmountTips:"申请退款金额不能超过合计金额",
      operationReturnTime: "请完善退货时间",
      chooseData: "请至少选择一条数据进行同步",
      chooseData20: "最大勾选条数不能超过20条，请重新选择",
      syncData: '您所勾选的',
      syncTips: '不符合同步条件，请重新选择',
      syncSuccess: "同步成功",
      returnTimeExplain:'退货时间特指退货商品预计入库时间',
    },
    title:{
      addProduct:'选择商品',
    }
  }
}
