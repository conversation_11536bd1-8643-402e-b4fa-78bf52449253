export default {
  quickWarehousing: {
    label: {
      freight: "运费",
      productRemark: "备注",
      basicInformation: "基本信息",
      receiptNoticeCode: "入库通知单号",
      themeDesc: "主题描述", 
      receiptType: "入库类型",
      status: "状态",
      sourceOrderCode: "来源单号",
      queryType: "查询类型",
      plannedDeliveryTime: "计划交货时间",
      orderSyncTime: "创建时间",
      createTime: "创建时间",
      createUserName: "创建人",
      warehouseName: "仓库",
      entryOperator: "入库操作员",
      entryTime: "入库时间",
      businessPerson: "业务员",
      salesman: "业务员",
      salesmanName: "采购员/销售员",
      supplier: "供应商",
      supplierName: "供应商",
      supplierAddress: "供应商地址",
      supplierContact: "供应商联系人",
      supplierPhone: "供应商联系电话",
      customer: "客户",
      customerName: "客户",
      customerAddress: "客户地址",
      customerContact: "客户联系人",
      customerPhone: "客户联系电话",
      contactPerson: "联系人",
      mobile: "联系电话",
      address: "地址",
      detailAddress: "详细地址",
      contractName: "合同名称",
      contractCode: "合同编号",
      contractType: "合同分类",
      weighbridgeNo: "磅单编号",
      vehicleNo: "入库车号",
      entryOrderRemark: "入库备注",
      remark: "备注",
      sourceDocumentRemark: "来源单据备注",
      originOrderCode: "原单据号",
      productDetails: "商品明细",
      productInfo: "商品信息",
      productCode: "商品编码",
      productName: "商品名称",
      productCategory: "商品分类",
      productSpecs: "规格",
      specification: "规格",
      productPackaging: "商品包装",
      planQty: "计划量",
      inQty: "入库量",
      planTotalQty: "商品计划总量",
      planTotalWeight: "商品计划总转换量",
      expectedQty: "商品计划总量",
      expectedWeight: "商品计划总转换量",
      remainingQty: "剩余量",
      remainingWeight: "剩余转换量",
      remainingInQty: "剩余量",
      remainingInWeight: "剩余转换量",
      actualInQty: "入库量",
      actualInWeight: "入库转换量",
      convertedQty: "转换量",
      quantity: "数量",
      proportion: "占比",
      unitPrice: "入库单价(RMB)",
      amount: "入库金额(RMB)",
      costUnitPrice: "成本单价(RMB)",
      costAmount: "成本金额(RMB)",
      warehouseArea: "入库库区",
      warehouseAreaCode: "库区",
      qualityCheck: "质检",
      qualityInspection: "质检信息",
      qualityCheckProduct: "质检商品",
      productInfoSection: "商品信息",
      deductionAmount: "扣款金额",
      deductionDesc: "扣款说明",
      deductionRemark: "扣款说明",
      attachment: "附件",
      entryDetails: "入库明细",
      entryOrderCode: "入库单号",
      entryOrderList: "入库明细",
      detailList: "明细列表",
      to: "至",
      startTime: "开始时间",
      endTime: "结束时间",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      initial: "初始",
      received: "已收运",
      draft: "草稿",
      cancel: "取消",
      processing: "入库中",
      completed: "完结",
      canceled: "已取消",

      purchaseInventory: "采购入库",
      returnStorage: "退货入库",
      transferInventory: "调拨入库",
      directStorage: "直接入库",
      groundStorage: "地头售卖",
      // cancel: "取消",
      // processing: "入库中",
      week: "近七天",
      month: "近30天",
      year: "近一年",
      all: "全部",
      // completed: "完结",
      // canceled: "已取消",
      // planQty: "计划量",
      // inQty: "入库量",
      entryOperatorList: "入库人",
      // salesmanName: "采购员/销售员",
      // customerName: "客户",
      // supplierName: "供应商",
      // remark: "备注",
      sourceList: "来源",
      manualCreate: "手动创建",
      sync: "同步",
      purchaseContract: "采购合同",
      saleContract: "销售合同",

      // purchaseInventory: "采购入库",
      // returnStorage: "退货入库",
      // transferInventory: "调拨入库",
      // directStorage: "直接入库",
      // groundStorage: "地头入库",
      // sourceList: "来源",
      // manualCreate: "手动创建",
      // sync: "同步",
      // purchaseContract: "采购合同",
      // saleContract: "销售合同",
      serialNumber: "序号",
      operation: "操作",
      view: "查看",
      selected: "已选",
      selectedCount: "个商品",
      // week: "近七天",
      // month: "近30天",
      // year: "近一年",
      // all: "全部",
      total: "合计",
      // entryOperatorList: "入库人",
      expectedWeight2: "计划转换量",
      inWarehouseQty: "入库量",
      costAmount2: "成本金额(RMB)",
      inWarehouseWeight: "入库转换量",
      inWarehouseDetails: "入库明细",
      inWarehouseCode: "入库单号",
      weighbridgeNo2: "榜单编号",
      inWarehouseArea: "入库库区",
      inWarehouseAmount: "入库金额(RMB)",
      qualityInspectionInfo: "质检信息",
      billNo: "单据号",
      originPlace: "产地",
      print: "打印",
      planQtyTransfer: "计划转换量",
      inQtyTransfer: "入库转换量",
    },
    button: {
      addBtn: "新增",
      add: "添加",
      edit: "编辑",
      delete: "删除",
      cancel: "取消",
      confirm: "确定",
      submit: "提交",
      save: "保存",
      saveDraft: "保存草稿",
      close: "关闭",
      back: "返回",
      goWarehousing: "去入库",
      complete: "完结",
      search: "搜索",
      reset: "重置",
      print: "打印",
      detail: "详情",
      view: "查看",
      addProduct: "添加商品",
      selectProduct: "选择商品",
      qualityCheck: "质检",
      upload: "上传"
    },
    title: {
      quickWarehousingManagement: "快速入库管理",
      addQuickWarehousing: "新增快速入库",
      editQuickWarehousing: "编辑快速入库",
      quickWarehousingDetail: "快速入库详情",
      warehousePage: "去入库",
      detail: "详情",
      addProductTitle: "添加商品",
      selectProductTitle: "选择商品",
      qualityCheckTitle: "质检商品",
      uploadTitle: "上传附件"
    },
    message: {
      deleteTips: "确定删除已创建的快速入库单？",
      deleteSuccess: "删除成功！",
      deleteFail: "删除失败！",
      deleteCancel: "取消删除！",
      completeTips: "完结后不可再对当前单据进行入库",
      completeSuccess: "操作成功",
      completeFail: "操作失败",
      addSuccess: "添加成功",
      editSuccess: "编辑成功",
      saveSuccess: "保存成功",
      saveFailed: "保存失败",
      submitSuccess: "提交成功",
      submitFailed: "提交失败",
      pleaseSelectSupplier: "请选择供应商",
      pleaseSelectBusinessPerson: "请选择业务员",
      pleaseSelectDeliveryTime: "请选择计划交货时间",
      pleaseAddProductDetails: "请添加商品明细",
      supplierAddSuccess: "供应商添加成功",
      noDataSelected: "未选择数据",
      operationSuccess: "操作成功",
      operationFailed: "操作失败"
    },
    placeholder: {
      inputTips: "请输入",
      selectTips: "请选择",
      enterInput: "请输入",
      pleaseSelect: "请选择",
      enterReceiptNoticeCode: "请输入入库通知单号",
      enterThemeDesc: "请输入主题描述",
      selectReceiptType: "请选择入库类型",
      selectStatus: "请选择状态",
      enterSourceOrderCode: "请输入来源单号",
      selectSupplier: "请选择供应商",
      enterContactPerson: "请输入联系人",
      enterMobile: "请输入联系电话",
      enterWeighbridgeNo: "请输入磅单编号",
      enterVehicleNo: "请输入入库车号",
      enterRemark: "请输入备注",
      selectWarehouseArea: "请选择库区",
      enterSpecification: "请输入规格",
      enterProductPackaging: "请输入商品包装",
      systemGenerated: "系统生成",
      autoTime: "自动时间",
      productInputTips: "请输入商品名称/编码"
    },
    rules: {
      receiptTypeRequired: "请选择入库类型",
      deliveryTimeRequired: "请选择计划交货时间",
      supplierRequired: "请选择供应商",
      unitPriceRequired: "单价不能为空",
      qtyRequired: "入库量不能为空",
      weightRequired: "入库转换量不能为空",
      amountRequired: "入库金额不能为空",
      warehouseAreaRequired: "入库库区不能为空",
      unitPriceFormat: "整数位限长7位，小数后4位",
      qtyFormat: "整数位限长8位，小数后3位",
      amountFormat: "整数位限长9位，小数后2位",
      proportionFormat: "请输入0-100之间的数值",
      supplierNameLength: "供应商名称长度在 2 到 50 个字符",
      supplierNameRequired: "请输入供应商名称"
    }
  },
}; 
