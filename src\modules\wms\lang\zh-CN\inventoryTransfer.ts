export default {
    inventoryTransfer: {
        label: {
            transferOrderCode: "移库单号",
            transferType: "移库类型",
            transferStatus: "状态",
          receiptStatus: "是否领单",
            today: "今天",
            yesterday: "昨天",
            weekday: "近七天",
            sourceWarehouseArea: '源库区',
            transferReasonName: '移库原因',
          transferAmount: '移库商品量',
          receiveInfo: '领单信息',
            remark: '备注',
            createUserName: '创建人',
            createTime: '创建时间',
            transferUserName: '移库人',
            transferTime: '移库时间',
            basicInformation: "基本信息",
            inventoryTransferInformation: "库存转移明细",
            outWarehouseArea: "出库库区",
            product: "商品",
            productCategory: "商品分类",

            productInformation:"商品信息",
            productCode:"商品编码",
            productSpec: "规格",
          productUnit: " 单位",
            totalStockQty: "总库存",
          totalStockWeight: "总库存重量",
            availableStockQty: "可用库存",
          availableStockWeight: "可用库存重量",
            targetWarehouseArea: "目标库区",
            transferQty: "移库数量",
          transferWeight: "移库重量",
          totality: "商品个数",
          quantity:  "数量",
          weight:"重量",
          receiveName:  "领单人",
          receiveTime:"领单时间",
          receiveDetail:"移库明细",
          ysnCodeTitle:"商品YSN码",
          ysnCode:"YSN码",
          scanTime:"扫描时间"

        },
        placeholder:{
            transferOrderCode :'请输入大于等于4位的单号',
            product: "请输入商品名称/编码",
            productCategory: "请输入关键词搜索分类",
        },
        dateTypeList: {
            createDate: "创建时间",
          receiveDate: "领用时间",
            inventoryTransferDate: "移库时间",
        },
        inventoryTransferTypeList: {
            normalTransfer: "普通移库",
        },
        statusList: {
            draft: "草稿",
            movement: "移库中",
            finish: "完成",

        },
      whetherOption: {
        yes: "是",
        no: "否",
      },
        shelfLifeUnitList:{
            day: "天",
            month: "月",
            year: "年",
        },
        button: {
            addInventoryTransfer: "新建库存转移",
            editInventoryTransfer: "编辑库存转移",
            addInventoryTransferProduct: "添加移库商品",
            saveDraft: "保存草稿",
            confirmTransfer: "确认转移",
          actionReceive: "领单",
          cancelTransfer: "取消领单",
        },
        title: {
        },
        message: {
            selectNumTips1: "已选",
            selectNumTips2: "个商品",
            deleteTips: "是否确认删除已创建的库存转移？",
            deleteConcel: "已取消删除！",
            deleteSucess: "删除成功！",
            saveDraftSucess: "保存草稿成功！",
            confirmTransferTips: "转移后商品库存位置不可修改，是否确认转移？",
            confirmTransferSucess: "确认转移成功！",
            confirmTransferConcel: "已取消确认转移！",
            transferQtyTips:'移库量不能大于可用库存数量!',
          transferWeightTips:'移库重量不能大于可用库存重量!',
            transferQtyTotalTips:'商品移库总量不能大于可用库存数量!',
            addOrEditInventoryTransferTips: "库存转移明细不能为空！",
            codeValideTips: "查询单号必须大于等于4位",
            inventoryTransferInfoDetailListReapetTips: "库存转移明细重复(商品+库区为唯一标识)！",
          actionSucess: "操作成功",
          receiveTips:  "是否确认取消领单？",
        },
        rules: {
            sourceWarehouseAreaCode: "请选择出库库区",
            transferType: "请选择移库类型",
            transferReasonId: "请选择移库原因",
            targetWarehouseAreaCode: "请选择目标库区",
            transferQty: "请输入移库量",
            transferQtyFormat: '请输入大于0的正整数,支持4位',
          TransferWeightFormat: '请输入大于0的数字，支持小数点前8位后3位',
          transferWeight: "请输入移库重量",
        },
    },
};
