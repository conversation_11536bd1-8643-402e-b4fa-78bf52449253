import request from "@/core/utils/request";
const CUSTOMER_BASE_URL = "/supply-oms/customer";

// 列表查询参数接口
export interface CustomerListQueryParams extends PageQuery {
  customerName?: string;
  contactMobile?: string;
  customerTypeCode?: number;
  enableStatus?: number;
}

// 表格数据接口
export interface customerForm {
  id?: string;
  customerName?: string;
  customerShortName?: string;
  customerTypeCode?: number;
  contact?: string;
  contactCountryAreaCode?: string;
  contactMobile?: string;
  receiverName?: string;
  receiverCountryAreaCode?: string;
  receiverMobile?: string;
  receiverCountryCode?: string;
  receiverProvinceCode?: string;
  receiverCityCode?: string;
  receiverDistrictCode?: string;
  receiverAddress?: string;
  contractList?: any;
  financialBank?: string;
  financialAccount?: string;
  financialName?: string;
  financialContact?: string;
  financialCountryAreaCode?: string;
  financialMobile?: string;
  financialSettlementCurrency?: string;
  businessLicenseUrl?: string;
  creditCode?: string;
  areaInfo?: any;
  receiverCountryName?: string;
  receiverProvinceName?: string;
  receiverCityName?: string;
  receiverDistrictName?: string;
  contractIds?: any;
  mobilePhoneShow?: boolean;
  receiverMobileShow?: boolean;
  addressShow?: boolean;
  addressFormat?: string;
  financialMobileShow?: boolean;
  fullReceiverAddress?: string;
}

class CustomerApi {
  /**
   * 客户下拉列表获取
   */
  static getCustomerSelectList(data: { enableStatus?: number }) {
    return request({
      url: `${CUSTOMER_BASE_URL}/queryList`,
      method: "get",
      params: data,
    });
  }

  /**
   * 获取列表
   */

  static getCustomerList(data: CustomerListQueryParams) {
    return request({
      url: `${CUSTOMER_BASE_URL}/queryPageList`,
      method: "post",
      data,
    });
  }
  // 新增
  static add(data: customerForm) {
    return request({
      url: `${CUSTOMER_BASE_URL}/add`,
      method: "post",
      data,
    });
  }

  // 编辑
  static update(data: customerForm) {
    return request({
      url: `${CUSTOMER_BASE_URL}/edit`,
      method: "post",
      data,
    });
  }

  // 查询详情
  static queryDetail(data: any) {
    return request({
      url: `${CUSTOMER_BASE_URL}/queryDetail`,
      method: "post",
      data,
    });
  }

  // 删除
  static delete(data: any) {
    return request({
      url: `${CUSTOMER_BASE_URL}/delete`,
      method: "post",
      data,
    });
  }

  // 更改状态
  static updateStatus(data: any) {
    return request({
      url: `${CUSTOMER_BASE_URL}/updateStatus`,
      method: "post",
      data,
    });
  }

  // 更新联系人
  static updateContact(data: any) {
    return request({
      url: `${CUSTOMER_BASE_URL}/updateContact`,
      method: "post",
      data,
    });
  }

  // 详情查询(手机号解密)
  static queryDetailMobileEncrypt(data: any) {
    return request({
      url: `${CUSTOMER_BASE_URL}/queryDetailMobileEncrypt`,
      method: "post",
      data,
    });
  }

  /**
   * 获取国际国家列表-含港澳台
   *
   */
  static getAllCountry() {
    return request({
      url: `/supply-base/country/all`,
      method: "get",
    });
  }

  /**
   * 获取部门用户树
   */
  static getDeptUserTree() {
    return request({
      url: `/supply-base/user/getDeptUserTree`,
      method: "get",
    });
  }

  /**
   * 通过客户id查询共享列表
   * @param data
   */
  static queryUserListByCustomerId(data: any) {
    return request({
      url: `${CUSTOMER_BASE_URL}/queryUserListByCustomerId`,
      method: "get",
      params: data,
    });
  }

  /**
   * 更新共享用户
   * @param data
   */
  static updateCustomerUser(data: any) {
    return request({
      url: `${CUSTOMER_BASE_URL}/updateCustomerUser`,
      method: "post",
      data,
    });
  }
  /**
   * 获取主账号信息
   * @param data
   */
  static getPhoneNum(data: any) {
    return request({
      url: `/supply-base/tenant/getPhoneNum`,
      method: "get",
      params: data,
    });
  }
}

export default CustomerApi;
