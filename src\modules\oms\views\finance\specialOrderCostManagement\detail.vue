<template>
  <div class="finance-detail-container">
    <el-card>
      <div class="title-lable">
        <div class="title-line"></div>
        <div class="title-content">基础信息</div>
      </div>
      <el-descriptions :column="4">
        <!--状态-->
        <el-descriptions-item label="状态">
          <span class="accounting-status-done" v-if="detailData?.accountingStatus === 1">已核算</span>
          <span class="accounting-status-undo" v-else>未核算</span>
        </el-descriptions-item>
        <!--核算时间-->
        <el-descriptions-item label="核算时间">
          <span v-if="detailData?.accountingStatus === 1"> {{ detailData?.accountingTime ? parseDateTime(detailData?.accountingTime, "dateTime"):'-' }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <!--核算人-->
        <el-descriptions-item label="核算人">
          <span v-if="detailData?.accountingStatus === 1">{{ detailData?.accounting || '-'}}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <!--订单类型-->
        <el-descriptions-item label="订单类型">
          {{ getOrderTypeLabel }}
        </el-descriptions-item>
        <!--总成本金额-->
        <el-descriptions-item label="总成本金额">
          <span class="price-text-color"> {{ currencyCodeLabel }}{{ formatPrice(detailData?.totalCostAmount) }}</span>
        </el-descriptions-item>
      </el-descriptions>

      <div class="title-lable">
        <div class="title-line"></div>
        <div class="title-content">订单基础信息</div>
      </div>

      <el-descriptions :column="4">
        <!--订单号-->
        <el-descriptions-item label="订单号">
          {{ detailData?.orderCode || '-' }}
        </el-descriptions-item>
        <!--销售人员-->
        <el-descriptions-item label="销售人员">
          {{ detailData?.salesName || '-' }}
        </el-descriptions-item>
        <!--经办人-->
        <el-descriptions-item label="经办人">
          {{ detailData?.handler || '-'}}
        </el-descriptions-item>
        <!--制单人-->
        <el-descriptions-item label="制单人">
          {{ detailData?.submitter || '-'}}
        </el-descriptions-item>
        <!--下单时间-->
        <el-descriptions-item label="下单时间">
          {{ detailData?.submitterTime ? parseDateTime(detailData?.submitterTime, "dateTime"):'-' }}
        </el-descriptions-item>
        <!--客户属性-->
        <el-descriptions-item label="客户属性">
          <span v-if="detailData?.customerAttributes === 1">大客户</span>
          <span v-else-if="detailData?.customerAttributes === 2">散客</span>
          <span v-else-if="detailData?.customerAttributes === 3">其他</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <!--客户-->
        <el-descriptions-item label="客户">
          {{ detailData?.customerName || '-' }}
        </el-descriptions-item>
        <!--关联单号-->
        <el-descriptions-item label="关联单号">
          {{ detailData?.originalOrderCode || '-' }}
        </el-descriptions-item>
        <!--订单备注-->
        <el-descriptions-item label="订单备注">
          {{ detailData?.orderRemark || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="title-lable">
        <div class="title-line"></div>
        <div class="title-content">成本明细</div>
      </div>

      <el-table ref="tableRef"  :data="detailData?.specialOrderCostManagementDetailVOList || []" highlight-current-row stripe>
        <template #empty><Empty/></template>
        <!--商品-->
        <el-table-column fixed="left" label="商品" prop="productName" show-overflow-tooltip min-width="170"></el-table-column>
        <!--规格-->
        <el-table-column label="规格" prop="productSpecs" show-overflow-tooltip min-width="170"></el-table-column>
        <!--数量-->
        <el-table-column label="数量" prop="productQty" show-overflow-tooltip min-width="170"></el-table-column>
        <!--实际出库数量-->
        <el-table-column label="实际出库数量" prop="outboundQty" show-overflow-tooltip min-width="170"></el-table-column>
        <!--成本价-->
        <el-table-column label="成本价" prop="costPrice" show-overflow-tooltip min-width="170">
          <template #default="scope">
            <span class="price-text-color"> {{ currencyCodeLabel }}{{ formatPrice(scope.row.costPrice) }}</span>
          </template>
        </el-table-column>
        <!--合计  -->
        <el-table-column label="合计" show-overflow-tooltip prop="totalCostAmount" min-width="170">
          <template #default="scope">
            <span class="price-text-color"> {{ currencyCodeLabel }}{{ formatPrice(scope.row.totalCostAmount) }}</span>
          </template>
        </el-table-column>
        <!--备注  -->
        <el-table-column label="备注" prop="remark" show-overflow-tooltip min-width="170"></el-table-column>
      </el-table>
      <div class="total-amount-container">总成本金额：{{currencyCodeLabel}} {{formatPrice(detailData?.totalCostAmount)}}</div>


      <div class="title-lable">
        <div class="title-line"></div>
        <div class="title-content">出库单据</div>
      </div>

      <el-table ref="tableRef"  :data="detailData?.specialOrderCostManagementDetailVOList || []" highlight-current-row stripe>
        <template #empty><Empty/></template>
        <!--单号-->
        <el-table-column fixed="left" label="单号" prop="outboundNoticeCode" show-overflow-tooltip min-width="170">
          {{ detailData?.outboundNoticeCode || '-' }}
        </el-table-column>
        <!--状态-->
        <el-table-column label="状态" prop="productSpecs" show-overflow-tooltip min-width="170">完结</el-table-column>
        <!--完结时间-->
        <el-table-column label="完结时间" prop="outFinishTime" show-overflow-tooltip min-width="170">
          {{ detailData?.outFinishTime ? parseDateTime(detailData?.outFinishTime, "dateTime"):'-' }}
        </el-table-column>
      </el-table>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("common.reback") }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { formatPrice, parseDateTime } from "@/core/utils";
import SpecialOrderCostAPI, { getSpecialOrderCostDetail } from "@/modules/oms/api/specialOrderCost";
import {useRoute, useRouter} from "vue-router";

defineOptions({
  name: "SpecialDetail",
  inheritAttrs: false,
})
const route = useRoute();
const router = useRouter();
const { t } = useI18n();

const detailData = ref(null)
const receivableAccountId = computed(() => {
  return route.query?.id
})

/*获取金额单位*/
const currencyCodeLabel = computed(() => {
  let defaultCurrencyCode = '(￥)';
  defaultCurrencyCode = detailData.value?.currencyCode === 'CNY' ? '￥' : '$'
  return defaultCurrencyCode
})

const orderTypeOptionList = ref([
  {
    label: '业务招待',
    value: 3
  },
  {
    label: '补发',
    value: 4
  },
  {
    label: '参展',
    value: 9
  },
])
const getOrderTypeLabel = computed(() => {
  return orderTypeOptionList.value?.find(item => item.value === detailData.value?.orderType)?.label || '-'
})

/*获取详情汇总*/
const queryDetail = () => {
  SpecialOrderCostAPI.getSpecialOrderCostDetail({id: receivableAccountId.value}).then((data) => {
    detailData.value = data
  })
}

async function handleClose(){
  router.push({ path: "/oms/finance/specialOrderCoast" })
}

onMounted(() => {
  queryDetail()
})
</script>

<style scoped lang="scss">

.total-amount-container {
  font-size: 16px;
  color: #C00C1D;
  margin-top: 20px;
  font-weight: 600;
}
:deep(.el-descriptions__label){
  display: inline-block;
  width: 120px;
  color: #90979E;
  text-align: right;
}
.accounting-status-done{
  background: rgba(41,182,16,0.08);
  border-radius: 2px;
  border: 1px solid rgba(41,182,16,0.2);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #29B610;
  line-height: 16px;
  text-align: center;
  font-style: normal;
  padding: 4px 6px;
}
.accounting-status-undo {
  background: rgba(255,156,0,0.08);
  border-radius: 2px;
  border: 1px solid rgba(255,156,0,0.2);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FF9C00;
  line-height: 16px;
  text-align: center;
  font-style: normal;
  padding: 4px 6px;
}
.page-footer{
  margin-top: 40px;
  overflow: hidden;
}
</style>
