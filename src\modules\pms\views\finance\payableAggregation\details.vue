<template>
  <div class="app-container">
    <div class="container-wrapper">
      <div class="container-head">
        <div class="page-title">
          <div class="purchase-title">
            <div
              @click="handleClose()"
              class="cursor-pointer mr8px"
            >
              <el-icon>
                <Back />
              </el-icon>
            </div>
            <div class="title">
              <span>{{ $t("payableAggregation.title.details") }}</span>
            </div>
          </div>
        </div>
        <div class="statistical-dashboard">
          <div class="dashboard-head">
            <div class="title">
              <i class="icon-company"></i>
              <template v-if="detailData.payableAccountType === 1">
                {{ detailData.supplierName }}
              </template>
              <template v-if="detailData.payableAccountType === 2">
                {{ $t("payableAggregation.label.purchaseUserName") }}
                {{ detailData.purchaseUserName }}
              </template>
            </div>
            <div class="action">
              <el-button
                type="primary"
                plain
                class="primary"
                @click="handlePay"
              >
                <i class="icon-payment"></i>
                {{ $t("payableAggregation.button.actionPay") }}
              </el-button>
            </div>
          </div>
          <div class="dashboard-body">
            <div class="item">
              <i class="icon icon-1"></i>
              <div class="value">
                {{ formatPrice(detailData.payableAmount) }}
              </div>
              <div class="title">
                {{ $t("payableAggregation.label.totalPayableAmount") }}
                ({{
                  !detailData.currencyCode || detailData.currencyCode === "CNY"
                    ? "元"
                    : "$"
                }})
              </div>
            </div>
            <div class="item">
              <i class="icon icon-2"></i>
              <div class="value">
                {{ formatPrice(detailData.actualPayAmount) }}
              </div>
              <div class="title">
                {{ $t("payableAggregation.label.totalAmountPaid") }}
                ({{
                  !detailData.currencyCode || detailData.currencyCode === "CNY"
                    ? "元"
                    : "$"
                }})
              </div>
            </div>
            <div class="item">
              <i class="icon icon-3"></i>
              <div class="value">
                {{ formatPrice(detailData.remainingPayAmount) }}
              </div>
              <div class="title">
                {{ $t("payableAggregation.label.totaResiduePayableAmount") }}
                ({{
                  !detailData.currencyCode || detailData.currencyCode === "CNY"
                    ? "元"
                    : "$"
                }})
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-card
        shadow="never"
        class="table-container"
      >
        <template #header>
          <el-tabs
            :model-value="tabsType"
            class="menu-list"
            @tab-change="handleTabsTypeOption"
          >
            <el-tab-pane
              v-for="(item, index) in tabsTypeOption"
              :key="index"
              :label="item.label"
              :name="item.value"
            ></el-tab-pane>
          </el-tabs>

          <div class="tabs-search">
            <el-form
              ref="queryFormRef"
              :model="queryParams"
              :inline="true"
              label-width="96px"
            >
              <template v-if="tabsType === 1">
                <el-form-item
                  :label="$t('payableAggregation.label.paymentTime')"
                  prop="dateRange"
                >
                  <el-date-picker
                    v-model="queryParams.dateRange"
                    type="daterange"
                    :range-separator="
                      $t('payableAggregation.label.rangeSeparator')
                    "
                    :start-placeholder="
                      $t('payableAggregation.label.startDate')
                    "
                    :end-placeholder="$t('payableAggregation.label.endDate')"
                    value-format="x"
                    :default-time="defaultTime"
                    class="!w-[256px]"
                  />
                </el-form-item>

                <el-form-item
                  prop="paymentUserName"
                  :label="$t('payableAggregation.label.paymentUserName')"
                >
                  <el-input
                    v-model="queryParams.paymentUserName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
              </template>
              <template v-if="tabsType === 2">
                <el-form-item
                  :label="$t('payableAggregation.label.orderTime')"
                  prop="dateRange"
                >
                  <el-date-picker
                    v-model="queryParams.dateRange"
                    type="daterange"
                    :range-separator="
                      $t('payableAggregation.label.rangeSeparator')
                    "
                    :start-placeholder="
                      $t('payableAggregation.label.startDate')
                    "
                    :end-placeholder="$t('payableAggregation.label.endDate')"
                    value-format="x"
                    :default-time="defaultTime"
                    class="!w-[256px]"
                  />
                </el-form-item>
                <el-form-item
                  prop="orderCode"
                  :label="$t('payableAggregation.label.purchaseOrderCode')"
                >
                  <el-input
                    v-model="queryParams.orderCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
                <el-form-item
                  v-if="query.payableAccountType === '1'"
                  prop="purchaseUserName"
                  :label="$t('payableAggregation.label.purchaseUserName')"
                >
                  <el-input
                    v-model="queryParams.purchaseUserName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
                <el-form-item
                  prop="purchaseTheme"
                  :label="$t('payableAggregation.label.purchaseTheme')"
                >
                  <el-input
                    v-model="queryParams.purchaseTheme"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
              </template>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleQuery('search')"
                >
                  {{ $t("common.search") }}
                </el-button>
                <el-button @click="handleResetQuery">
                  {{ $t("common.reset") }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </template>

        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableData"
          highlight-current-row
          stripe
          :summary-method="getSummaries"
          show-summary
          @sort-change="handleSortChange"
        >
          <template #empty>
            <Empty />
          </template>
          <template
            v-for="(item, index) in tableHeadData()"
            :key="index"
          >
            <el-table-column
              v-if="item.prop"
              :class-name="item.className"
              :label-class-name="item.labelClassName"
              :type="item.type"
              :prop="item.prop"
              :key="item.prop"
              :label="item.label"
              :width="item.width"
              :min-width="item.minWidth"
              :fixed="item.fixed"
              :align="item.align"
              :header-align="item.headerAlign"
              :show-overflow-tooltip="item.showOverflowTooltip"
              :formatter="item.formatter"
              :sortable="item.sortable"
            >
              <template
                #default="scope"
                v-if="item.prop === 'paymentVoucher'"
              >
                <el-button
                  v-if="scope.row.paymentVoucher"
                  type="primary"
                  link
                  @click="handleView(scope.row)"
                >
                  {{ $t("payableAggregation.button.actionView") }}
                </el-button>
              </template>
              <template
                #default="scope"
                v-else-if="item.prop === 'operator'"
              >
                <el-button
                  type="primary"
                  link
                  @click="handleDetail(scope.row)"
                >
                  {{ $t("payableAggregation.button.actionView") }}
                </el-button>
              </template>
            </el-table-column>
          </template>
        </el-table>

        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
    </div>
    <Payment
      ref="paymentRef"
      v-model:drawer-visible="paymentDrawer.visible"
      :title="paymentDrawer.title"
      @on-submit="paymentSubmit"
    />
    <ImageViewer
      v-model:visible="imageViewer.visible"
      :imagesList="imageViewer.imagesList"
    ></ImageViewer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "PayableAggregationDetails",
  inheritAttrs: false,
});
import { parseDateTime, isEmpty, formatPrice } from "@/core/utils";

import { useTagsViewStore } from "@/core/store";

import API from "@/modules/pms/api/payableAggregation";

import { useNavigation } from "@/core/composables/useNavigation";
import ImageViewer from "@/core/components/ImageViewer/index.vue";

const { refreshAndNavigate } = useNavigation();
import Payment from "./components/payment.vue";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();

const { query } = route;

const defaultTime: [Date, Date] = [
    new Date(2000, 1, 1, 0, 0, 0),
    new Date(2000, 2, 1, 23, 59, 59),
];

const initQueryParams = () => ({
  page: 1,
  limit: 20,
  dateRange: [],
  paymentUserName: undefined,
  orderCode: undefined,
  purchaseUserName: undefined,
  purchaseTheme: undefined,
  sortType: undefined,
  sortField: undefined,
  payableAccountId: query.id,
});
const state = reactive({
  loading: false,
  queryFormRef: null,
  queryParams: initQueryParams(),
  total: 0,
  tableData: [],
  tabsTypeOption: [
    { label: t("payableAggregation.label.tabsTypeOption[1]"), value: 1 },
    { label: t("payableAggregation.label.tabsTypeOption[2]"), value: 2 },
  ],
  tabsType: 1,
  summaryData: "",
  detailData: "",
  paymentRef: null,
  paymentDrawer: {
    title: "",
    visible: false,
  },
  tableRef: null,
  uploadRef: null,
  imageViewer: {
    visible: false,
    imagesList: [],
  },
}) as any;

const {
  loading,
  queryFormRef,
  queryParams,
  total,
  tableData,
  tabsTypeOption,
  tabsType,
  summaryData,
  detailData,
  paymentRef,
  paymentDrawer,
  tableRef,
  imageViewer,
} = toRefs(state);

/**
 * Tabs切换
 */

function handleTabsTypeOption(value: any) {
  tabsType.value = value;
  queryParams.value = initQueryParams();
  tableRef.value?.clearSort();
  handleQuery();
}

/**
 * 表头数据
 */

function tableHeadData() {
  let symbol;
  if (
    tableData.value?.length === 0 ||
    tableData.value[0]?.currencyCode === "CNY"
  ) {
    symbol = "￥";
  } else {
    symbol = "$";
  }
  let tableHead: any = [];
  switch (tabsType.value) {
    case 1:
      tableHead = [
        {
          prop: "paymentTime",
          label: t("payableAggregation.label.paymentTime"),
          showOverflowTooltip: true,
          minWidth: 180,
          formatter: formatterTimestamp,
        },
        {
          prop: "paymentAmount",
          label: t("payableAggregation.label.paymentAmount") + `(${symbol})`,
          showOverflowTooltip: true,
          minWidth: 180,
          sortable: "custom",
          formatter: formatterAmount,
          align: "right",
        },
        {
          prop: "paymentMethodName",
          label: t("payableAggregation.label.paymentMethod"),
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          prop: "paymentVoucher",
          label: t("payableAggregation.label.paymentVoucher"),
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          prop: "paymentUserName",
          label: t("payableAggregation.label.paymentUserName"),
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          prop: "remark",
          label: t("payableAggregation.label.remark"),
          showOverflowTooltip: true,
          minWidth: 180,
        },
      ];
      break;
    case 2:
      tableHead = [
        {
          prop: "orderCode",
          label: t("payableAggregation.label.purchaseOrderCode"),
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          prop: "purchaseTheme",
          label: t("payableAggregation.label.purchaseTheme"),
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          prop: "submitTime",
          label: t("payableAggregation.label.orderTime"),
          showOverflowTooltip: true,
          minWidth: 180,
          formatter: formatterTimestamp,
        },
        {
          prop: query.payableAccountType === "1" ? "purchaseUserName" : "",
          label: t("payableAggregation.label.purchaseUserName"),
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          prop: "totalPurchaseAmount",
          label:
            t("payableAggregation.label.plannedPurchaseAmount") + `(${symbol})`,
          showOverflowTooltip: true,
          minWidth: 180,
          sortable: "custom",
          formatter: formatterAmount,
          align: "right",
        },
        {
          prop: "totalReceivedAmount",
          label: t("payableAggregation.label.receivingAmount") + `(${symbol})`,
          showOverflowTooltip: true,
          minWidth: 180,
          sortable: "custom",
          formatter: formatterAmount,
          align: "right",
        },
        {
          prop: "deductionAmount",
          label: t("payableAggregation.label.deductionAmount") + `(${symbol})`,
          showOverflowTooltip: true,
          minWidth: 180,
          sortable: "custom",
          formatter: formatterAmount,
          align: "right",
        },
        {
          prop: "refundAmount",
          label: t("payableAggregation.label.refundAmount") + `(${symbol})`,
          showOverflowTooltip: true,
          minWidth: 180,
          sortable: "custom",
          formatter: formatterAmount,
          align: "right",
        },
        {
          prop: "payableAmount",
          label: t("payableAggregation.label.payableAmount") + `(${symbol})`,
          showOverflowTooltip: true,
          minWidth: 180,
          sortable: "custom",
          formatter: formatterAmount,
          align: "right",
        },
        {
          prop: "operator",
          label: t("payableAggregation.label.operator"),
          fixed: "right",
          width: 160,
        },
      ];
      break;
    default:
      break;
  }
  return tableHead;
}

/**
 * 搜索
 * @param type
 */
function handleQuery(type?: string) {
  if (type === "search") {
    queryParams.value.sortType = undefined;
    queryParams.value.sortField = undefined;
    tableRef.value?.clearSort();
  }

  loading.value = true;
  const [startTime, endTime] = queryParams.value?.dateRange || [];
  let params = {
    ...queryParams.value,
  };
  let request;

  if (tabsType.value === 1) {
    params.paymentStartTime = startTime;
    params.paymentEndTime = endTime;
    request = API.queryPaymentRecordList;
  } else {
    params.startSubmitTime = startTime;
    params.endSubmitTime = endTime;
    request = API.queryPurchaserOrderList;
  }
  delete params.dateRange;

  request(params)
    .then((res: any) => {
      const { records, pages, totalPaymentAmount } = res;
      tableData.value = records || [];
      total.value = parseInt(res.total);
      if (tabsType.value === 1) {
        summaryData.value = {
          totalPaymentAmount,
        };
      }
      if (tabsType.value === 2) {
        API.queryPurchaserOrderTotal(params).then((res: any) => {
          summaryData.value = res || "";
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 重置
 */
function handleResetQuery() {
  queryParams.value = initQueryParams();
  tableRef.value?.clearSort();
  handleQuery();
}

/**
 * 排序
 */

function handleSortChange({ column, prop, order }: any) {
  // console.log(column, prop, order);
  let sortType;
  if (order === "descending") {
    sortType = 1;
  } else if (order === "ascending") {
    sortType = 0;
  } else {
    sortType = undefined;
  }
  queryParams.value.sortType = sortType;
  queryParams.value.sortField = prop;

  handleQuery();
}

/**
 * 时间戳格式化
 * @param row
 * @param column
 * @param cellValue
 */
function formatterTimestamp(row: any, column: any, cellValue: any) {
  if (isEmpty(cellValue)) {
    return "";
  }
  return parseDateTime(cellValue, "dateTime");
}

/**
 * 金额格式化
 */

function formatterAmount(row: any, column: any, cellValue: any) {
  if (isEmpty(cellValue)) {
    return "";
  }

  let val: any = formatPrice(cellValue);
  if (column.property === "paymentAmount") {
    val = h("div", { class: "highlight" }, [val]);
  }
  if (column.property === "payableAmount") {
    val = h("div", { class: "highlight" }, [val]);
  }

  return val;
}

/**
 * 合计
 * @param param
 */
function getSummaries(param: any) {
  const { columns, data } = param;
  const sums: any = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = h("div", { class: "summary" }, [
        t("payableAggregation.label.summation"),
      ]);
      return;
    } else {
      sums[index] = h("div", { class: "summary" }, []);
    }
  });
  if (tabsType.value === 1) {
    sums[1] = h("div", { class: "highlight" }, [
      `${formatPrice(summaryData.value.totalPaymentAmount)}`,
    ]);
  }
  if (tabsType.value === 2) {
    let offset = 0;
    if (query.payableAccountType === "2") {
      offset = 1;
    }
    sums[4 - offset] = h("div", { class: "summary" }, [
      `${formatPrice(summaryData.value.totalPurchaseAmount)}`,
    ]);
    sums[5 - offset] = h("div", { class: "summary" }, [
      `${formatPrice(summaryData.value.totalReceivedAmount)}`,
    ]);
    sums[6 - offset] = h("div", { class: "summary" }, [
      `${formatPrice(summaryData.value.deductionAmount)}`,
    ]);
    sums[7 - offset] = h("div", { class: "summary" }, [
      `${formatPrice(summaryData.value.refundAmount)}`,
    ]);
    sums[8 - offset] = h("div", { class: "highlight" }, [
      `${formatPrice(summaryData.value.payableAmount)}`,
    ]);
  }

  return sums;
}

/**
 * 付款
 */
function handlePay() {
  paymentDrawer.value.title = t("payableAggregation.button.actionPay");
  paymentDrawer.value.visible = true;
  paymentRef.value.setData(query);
}

/**
 * 保存付款
 */
function paymentSubmit() {
  queryDetail();
  handleQuery();
}

/**
 * 查询详情
 */
function queryDetail() {
  loading.value = true;
  let params = {
    id: query.id,
  };
  API.queryDetail(params)
    .then((data: any) => {
      detailData.value = data;
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 详情
 * @param row
 */
function handleDetail(row: any) {
  router.push({
    path: "/pms/purchase/purchaseOrderDetail",
    query: {
      id: row.orderId,
    },
  });
}

/**
 * 查看附件
 * @param row
 */

function handleView(row: any) {
  imageViewer.value.visible = true;
  imageViewer.value.imagesList = row.paymentVoucher;
}

/**
 * 关闭窗口
 */

function handleClose() {
  tagsViewStore.delView(route);
  router.go(-1);
}

// onMounted(() => {
//   queryDetail();
//   handleQuery();
// });

onActivated(() => {
  queryDetail();
  handleQuery();
});
</script>

<style lang="scss" scoped>
.container-wrapper {
  .container-head {
    background: #ffffff;
    border-radius: 4px;
    margin-bottom: 10px;

    .statistical-dashboard {
      padding: 20px;

      .dashboard-head {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
          font-weight: 500;
          font-size: 24px;
          color: #151719;

          .icon-company {
            display: inline-block;
            width: 24px;
            height: 24px;
            background: url("@/core/assets/images/icon_company.png") center
              center no-repeat;
            vertical-align: -4px;
            margin-right: 12px;
          }
        }

        .action {
          .primary {
            background-color: #f1e9fb !important;
          }

          .icon-payment {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url("@/core/assets/images/icon_payment.png") center
              center no-repeat;
            vertical-align: -4px;
            margin-right: 4px;
          }
        }
      }

      .dashboard-body {
        display: flex;

        .item {
          flex: 1;
          margin: 32px 0;
          padding: 0 0 0 144px;
          position: relative;
          border-left: 1px solid #f1e9fb;

          &:first-child {
            border-left: none;
          }

          .icon {
            position: absolute;
            top: 0;
            left: 64px;
            display: inline-block;
            width: 64px;
            height: 64px;

            &.icon-1 {
              background: url("@/core/assets/images/icon_all.png") center center
                no-repeat;
            }

            &.icon-2 {
              background: url("@/core/assets/images/icon_received.png") center
                center no-repeat;
            }

            &.icon-3 {
              background: url("@/core/assets/images/icon_receivable.png") center
                center no-repeat;
            }
          }

          .value {
            margin-top: -10px;
            font-weight: bold;
            font-size: 32px;
            color: #151719;
          }

          .title {
            font-weight: 400;
            font-size: 16px;
            color: #90979e;
          }
        }
      }
    }
  }

  .tabs-search {
    padding-top: 10px;
  }

  :deep(.highlight) {
    color: #c00c1d;
  }

  :deep(.summary) {
    font-weight: 500;
    font-size: 16px;
    color: #52585f;
  }
}
</style>
