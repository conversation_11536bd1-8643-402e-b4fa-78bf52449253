<template>
  <PrintTemplate ref="printRef">
    <div class="print-container">
      <div class="print-header">
        <h1 class="print-title">{{ printData.title }}</h1>
      </div>

      <div class="print-info">
        <div class="info-row">
          <div class="info-item-3">
            <span class="info-label">
              {{ t("purchaseReceivables.print.billNumber") }}：
            </span>
            <span class="info-value">
              {{ printData.billCode || "" }}
            </span>
          </div>
          <div class="info-item-3">
            <span class="info-label">
              {{ t("purchaseReceivables.print.purchaseOrderNo") }}：
            </span>
            <span class="info-value">
              {{ printData.purchaseCode || "" }}
            </span>
          </div>
          <div class="info-item-3">
            <span class="info-label">
              {{ t("purchaseReceivables.print.purchaser") }}：
            </span>
            <span class="info-value">
              {{ printData.purchaser || "" }}
            </span>
          </div>
        </div>

        <div class="info-row">
          <!--          <div class="info-item-3">
            <span class="info-label">备注：</span>
            <span class="info-value ecllipse">{{ printData.remark || "-" }}</span>
          </div>-->
          <div class="info-item-3">
            <span class="info-label">
              {{ t("purchaseReceivables.print.status") }}：
            </span>
            <span class="info-value">{{ printData.status || "" }}</span>
          </div>
          <div class="info-item-3" v-if="!!printData.supplierName">
            <span class="info-label">
              {{ t("purchaseReceivables.print.supplier") }}：
            </span>
            <span class="info-value">
              {{ printData.supplierName || "" }}
            </span>
          </div>
        </div>

        <div class="info-item-3">
          <span class="info-label">
            {{ t("purchaseReceivables.print.remarks") }}：
          </span>
          <span class="info-value ecllipse">{{ printData.remark || "-" }}</span>
        </div>
      </div>

      <div class="print-table-container">
        <table class="print-table">
          <thead>
            <tr>
              <th>{{ t("purchaseReceivables.print.sequence") }}</th>
              <th>{{ t("purchaseReceivables.print.workOrderNo") }}</th>
              <th>{{ t("purchaseReceivables.print.receivableAmount") }}</th>
              <th>{{ t("purchaseReceivables.print.applicant") }}</th>
              <th>{{ t("purchaseReceivables.print.remarks") }}</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in printData.items || defaultItems"
              :key="index"
            >
              <td>{{ index + 1 }}</td>
              <td>{{ item.productName }}</td>
              <td>{{ item.specification }}</td>
              <td>{{ item.unit }}</td>
              <td>{{ item.quantity }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="print-info">
        <div class="info-row">
          <div class="info-item-3">
            <span class="info-label">
              {{ t("purchaseReceivables.print.receiptType") }}：
            </span>
            <span class="info-value">{{ printData.receiptType || "-" }}</span>
          </div>
          <div class="info-item-3">
            <span class="info-label">
              {{ t("purchaseReceivables.print.receiptMethod") }}：
            </span>
            <span class="info-value">{{ printData.receiptMethod || "-" }}</span>
          </div>
          <div class="info-item-3">
            <span class="info-label">
              {{ t("purchaseReceivables.print.receiptAmount") }}：
            </span>
            <span class="info-value">{{ printData.receiptAmount || "0" }}</span>
          </div>
          <div class="info-item-3">
            <span class="info-label">
              {{ t("purchaseReceivables.print.receiver") }}：
            </span>
            <span class="info-value">{{ printData.receiver || "-" }}</span>
          </div>
        </div>

        <!-- 新增收款时间行 -->
        <div class="info-row">
          <div class="info-item-2">
            <span class="info-label">
              {{ t("purchaseReceivables.print.receiptTime") }}：
            </span>
            <span class="info-value">{{ printData.receiptTime || "-" }}</span>
          </div>
          <div class="info-item-2">
            <span class="info-label">
              {{ t("purchaseReceivables.print.voucherUploadTime") }}：
            </span>
            <span class="info-value">
              {{ printData.voucherUploadTime || "-" }}
            </span>
          </div>
          <div class="info-item">
            <!-- 留空，保持对齐 -->
          </div>
          <div class="info-item">
            <!-- 留空，保持对齐 -->
          </div>
        </div>
      </div>
    </div>
  </PrintTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import PrintTemplate from "@/core/components/Print/PrintTemplate.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 接收父组件传递的打印数据
/* const props = defineProps({
  printData: {
    type: Object,
    default: () => ({}),
  },
}); */

const printData = ref({});
const printRef = ref<InstanceType<typeof PrintTemplate>>();
// 默认商品数据（用于预览或测试）
const defaultItems = ref([
  {
    productName: "--",
    specification: "--",
    unit: "--",
    quantity: "--",
    unitPrice: "--",
    amount: "--",
  },
]);

// 格式化日期
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 暴露打印方法给父组件
const handlePrint = (data) => {
  printData.value = data;
  nextTick(() => {
    printRef.value.print();
  });
};

defineExpose({
  handlePrint,
});
</script>

<style lang="scss">
/* 基础样式 - 最小化但保留结构 */
.print-container {
}

/* 打印媒体查询样式 */
@media print {
  .print-container {
    /* position: absolute !important;
    width: 100% !important;
    max-width: none !important;
    margin: 20px !important;
    padding: 20px !important;
    left: 0 !important;
    right: 0 !important; */
    display: block;
    width: calc(100vw - 40px) !important; /* 使用!important确保优先级 */
    max-width: 100% !important; /* 添加最大宽度限制 */
    height: calc(100vh - 40px) !important;
    margin: 20px !important; /* 确保没有外边距 */
    padding: 20px;
    box-sizing: border-box;
    font-family: SimSun, "宋体", Arial, sans-serif;
    color: black;
    border: 1px solid #000;
    position: fixed !important; /* 使用fixed定位 */
    top: 0 !important;
    left: 0 !important;
    right: 0 !important; /* 确保右侧贴合 */
  }

  /* 确保打印时页面没有默认边距 */
  @page {
    margin: 0;
    size: auto;
  }

  body {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 标题样式 */
  .print-header {
    text-align: center;
    margin-bottom: 15px;

    .print-title {
      font-size: 18pt;
      font-weight: 700;
      margin: 0;
      color: #000;
    }
  }

  /* 信息区域样式 */
  .print-info {
    margin-bottom: 15px;

    .info-row {
      display: flex;
      margin-bottom: 8px;
      flex-wrap: wrap;
      page-break-inside: avoid;
      .info-item-2 {
        width: 50% !important;
        display: flex;
        margin-bottom: 5px;
      }
      .info-label {
        font-weight: bold;
        word-break: keep-all;
      }

      .info-value {
        flex: 1;
        &.ecllipse {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .info-item-4 {
        width: 25% !important;
        display: flex;
        margin-bottom: 5px;
      }
      .info-item-3 {
        width: 33.33% !important;
        display: flex;
        margin-bottom: 5px;
      }
    }
  }

  /* 表格样式 */
  .print-table-container {
    margin-bottom: 15px;

    .print-table {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
      page-break-inside: avoid;

      th,
      td {
        border: 1px solid black;
        padding: 5px;
        text-align: center;
        font-size: 10pt;
      }

      th {
        // background-color: #e0e0e0;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      tfoot td {
        font-weight: bold;
      }

      .text-right {
        text-align: right;
      }
    }
  }
}
</style>
