<template>
  <div>
    <el-dialog
      :model-value="visible"
      title="上传文件"
      :close-on-click-modal="false"
      @close="close"
      width="1012px"
    >
      <div>
        <upload-multiple
          :tips="''"
          :fileSize="10"
          :fileType="['rar', 'zip', 'pdf', 'jpg', 'png', 'jpeg']"
          :modelValue="imagesUrls"
          ref="detailPicsRef"
          @update:model-value="onChangeMultiple"
          :limit="10"
          :formRef="fileRef"
          class="modify-multipleUpload"
          name="detailPic"
          :ifDrag="true"
          :showUploadBtn="showUploadBtn"
          :listType="`icon-card`"
          :disabledVal="editType == 'detail' ? true : false"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["update:visible", "change"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  showUploadBtn: {
    type: Boolean,
    default: true,
  },
});

const imagesUrls = ref();
const fileRef = ref();
const editType = ref();

function close() {
  emit("update:visible", false);
  reset();
}
function reset() {
  imagesUrls.value = [];
}

function onChangeMultiple(val: any) {
  imagesUrls.value = val;
  emit('change', imagesUrls.value)
}

function setFormData(data: any) {
  imagesUrls.value = data;
}

function setEditType(data: any) {
  editType.value = data;
}

defineExpose({
  setFormData,
  setEditType,
});
</script>

<style lang="scss"></style>
