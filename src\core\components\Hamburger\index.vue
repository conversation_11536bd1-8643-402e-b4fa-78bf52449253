<template>
    <div class="collape-div">
        <div class="collape" @click="toggleClick">
            <svg-icon
                    class="hamburger"
                    :class="{ 'is-active': isActive }"
                    icon-class="collape1"
            />
        </div>
    </div>
</template>
<script setup lang="ts">
defineProps({
  isActive: {
    required: true,
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["toggleClick"]);

function toggleClick() {
  emit("toggleClick");
}
</script>

<style scoped lang="scss">
    .collape-div{
        height: 50px;
        line-height: 50px;
        text-align: center;
        border-top:1px solid #E5E7F3 ;
        .collape{
            .hamburger {
                cursor: pointer;
                transform: scaleX(-1);
                width: 16px !important;
                height: 16px !important;
            }
            .hamburger.is-active {
                transform: scaleX(1);
            }
        }
    }
</style>
