{"name": "企业管家", "version": "2.13.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit & vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.11.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "ali-oss": "^6.22.0", "animate.css": "^4.1.1", "axios": "^1.7.3", "codemirror": "^5.65.17", "codemirror-editor-vue3": "^2.7.0", "color": "^4.2.3", "crypto-js": "^4.2.0", "decimal.js": "^10.5.0", "echarts": "^5.5.1", "el-table-infinite-scroll": "^3.0.6", "element-plus": "2.10.4", "exceljs": "^4.4.0", "jsbarcode": "^3.11.6", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "moment": "^2.30.1", "net": "^1.0.2", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.2", "pinia": "^2.2.0", "qs": "^6.13.0", "sockjs-client": "1.6.1", "sortablejs": "^1.15.2", "stompjs": "^2.3.3", "vue": "^3.4.35", "vue-i18n": "9.9.1", "vue-router": "^4.4.2", "vue3-print-nb": "^0.1.4"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@iconify-json/ep": "^1.1.15", "@types/codemirror": "^5.60.15", "@types/color": "^3.0.6", "@types/lodash": "^4.17.7", "@types/node": "^20.14.14", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.2", "@types/qs": "^6.9.15", "@types/sockjs-client": "^1.5.4", "@types/sortablejs": "^1.15.8", "@types/stompjs": "^2.3.9", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-vue": "^5.1.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.20", "commitizen": "^4.3.0", "cz-git": "^1.9.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "fast-glob": "^3.3.2", "husky": "^9.1.4", "lint-staged": "^15.2.8", "postcss": "^8.4.40", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "sass": "^1.77.8", "stylelint": "^16.8.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "terser": "^5.31.3", "typescript": "^5.5.4", "unocss": "^0.58.9", "unplugin-auto-import": "^0.17.8", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.3.5", "vite-plugin-mock-dev-server": "^1.6.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.3.7", "vue-tsc": "^2.2.0"}, "engines": {"node": ">=18.0.0"}}