import { defineMockPms } from "./base";
import {ProductAllPageVO} from "@/modules/pms/api/purchaseOrder";

export default defineMockPms([
    // 仓库列表
    {
        url: "purchaseOrder/getStorehouseList",
        method: ["GET"],
        body: {
            code: 0,
            data: [
                    {
                        key: '111',
                        value: '仓库1',
                    },
                    {
                        key: '222',
                        value: '仓库2',
                    },
             ],
            msg: "一切ok",
        },
    },

    // 采购单分页列表
    {
    url: "purchaseOrder/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
          {
            id: '1',
            orderCode: "wqewreeeee11111111111111",
            orderType: 1,
            orderSource: 1,
            purchaseUserName: '采购员1',
            supplierName: "供应商1，供应商2",
            productNumber: 5,
            planDeliveryDate:'2021-03-25',
            createUserName:'xiaohua',
            remark:'xiddddddddddddddaohua',
            orderPurchaseStatus: 1,
            applicationStatus:1,
            applicationStatusStr:'未发送',
            createTime: "2021-03-25 12:39:54",
          },
             {
                 id: '2',
                 orderCode: "wqewreeeee11111111111111",
                 orderType: 1,
                 orderSource: 2,
                 purchaseUserName: '采购员2',
                 supplierName: "供应商3",
                 productNumber: 5,
                 planDeliveryDate:'2021-03-25',
                 createUserName:'xiaohua',
                 remark:'xiddddddddddddddaohua',
                 orderPurchaseStatus: 2,
                 applicationStatus:2,
                 applicationStatusStr:'已发送',
                 createTime: "2021-03-25 12:39:54",
             },
             {
                 id: '3',
                 orderCode: "wqewreeeee11111111111111",
                 orderType: 2,
                 orderSource:1,
                 purchaseUserName: '采购员3',
                 supplierName: "供应商4",
                 productNumber: 5,
                 planDeliveryDate:'2021-03-25',
                 createUserName:'xiaohua',
                 remark:'xiddddddddddddddaohua',
                 orderPurchaseStatus: 3,
                 applicationStatus:1,
                 applicationStatusStr:'未发送',
                 createTime: "2021-03-25 12:39:54",
             },
             {
                 id: '4',
                 orderCode: "wqewreeeee11111111111111",
                 orderType: 2,
                 orderSource: 2,
                 purchaseUserName: '采购员3',
                 supplierName: "供应商4",
                 productNumber: 5,
                 planDeliveryDate:'2021-03-25',
                 createUserName:'xiaohua',
                 remark:'xiddddddddddddddaohua',
                 orderPurchaseStatus: 0,
                 purchaseStatusStr: '已关闭',
                 applicationStatus:1,
                 applicationStatusStr:'未发送',
                 createTime: "2021-03-25 12:39:54",
                 shutdownReason: "关闭原因",
             },
        ],
        total: '4',
      },
      msg: "一切ok",
    },
  },

  // 发送采购单
  {
      url: "purchaseOrder/send",
      method: ["POST"],
      body:{
          code: 0,
          data: null,
          msg: "发送成功",
      },
  },

  //关闭采购单
  {
      url: "purchaseOrder/shutdown",
      method: ["POST"],
      body:{
          code: 0,
          data: null,
          msg: "关闭成功",
      },
  },

  //查询所有可选的商品列表（分页）
  {
        url: "purchaseOrder/queryProductAll",
        method: ["POST"],
        body: {
            code: 0,
            data:{
                records: [
                    {
                        id: '1',
                        imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                        productCode: "1000",
                        productName: "采购商品1",
                        specificationName: '规格名称1',
                        productUnitName: "瓶",
                        productCategory: "商品分类",
                        unit: 'kg',
                        number: 10,
                        planPurchasePrice:1.33
                    },
                    {
                        id: '2',
                        imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                        productCode: "2000",
                        productName: "采购商品2",
                        specificationName: '规格名称2',
                        productUnitName: "袋",
                        productCategory: "商品分类",
                        unit: 'kg',
                        number: 5,
                        planPurchasePrice:2.33
                    },
                    {
                        id: '3',
                        imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                        productCode: "3000",
                        productName: "采购商品3",
                        specificationName: '规格名称3',
                        productUnitName: "斤",
                        productCategory: "商品分类",
                        unit: 'kg',
                        number: 8,
                        planPurchasePrice:3.33
                    },
                ],
                total: '3',
            },
            msg: "一切ok",
        },
    },

  //  根据商品id查询采购单编辑详情
  {
      url: "purchaseOrder/detail",
      method: ["POST"],
      body: {
          code: 0,
          data:{
                  id: '2',
                  purchaseType: '1',
                  storehouse: '222',
                  planDeliveryDate: '2024-03-11',
                  purchasePersonnel: "3",
                  appendix: "",
                  remark:'备注',
                  product:[
                      {
                          id: '001',
                          imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                          productCode: '001',
                          productName: '西瓜1',
                          specificationName: '1斤',
                          productCategory: '水果',
                          unit: '斤',
                          number: 5,
                          planPurchaseCount: '100',
                          planPurchasePrice: '1.00',
                          planPurchaseAmount: '100.99',
                      },
                      {
                          id: '002',
                          imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                          productCode: '002',
                          productName: '西瓜2',
                          specificationName: '1斤',
                          productCategory: '水果',
                          unit: '斤',
                          number: 8,
                          planPurchaseCount: '100',
                          planPurchasePrice: '1.00',
                          planPurchaseAmount: '100.99',
                      }
                  ]
          },
          msg: "一切ok",
      },
  },

    //  添加采购单
    {
        url: "purchaseOrder/save",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

    // 编辑采购单
    {
        url: "purchaseOrder/update",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

]);
