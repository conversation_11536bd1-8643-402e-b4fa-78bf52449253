<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" size="600" @close="close">
        <div>
            <el-form ref="querySupplierFromRef" :model="querySupplierFrom" label-position="top" v-if="supplierType !== 3">
                <div class="flex-center-but">
                    <div class="supplier-div">
                        <el-form-item prop="supplierName">
                            <el-input
                                    v-model="querySupplierFrom.supplierName"
                                    :placeholder="$t('purchase.placeholder.supplierName')"
                                    clearable
                            />
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item>
                            <el-button type="primary" @click="querySupplierAll">
                                {{$t('common.search')}}
                            </el-button>
                            <el-button  @click="reset">
                                {{$t('common.reset')}}
                            </el-button>
                        </el-form-item>
                    </div>
                </div>
            </el-form>
            <el-table
                    :class="supplierType === 4?'':'supplier-selection'"
                    v-loading="loading"
                    :data="supplierTable"
                    highlight-current-row
                    stripe
                    @selection-change="handleSelectionSupplierChange"
            >
                <el-table-column type="selection" width="60" align="center" v-if="supplierType == 1 || supplierType == 2">
                    <template #default="scope">
                        <el-checkbox
                                v-model="scope.row.selected"
                                @change="selectable1(scope.row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column type="selection" width="60" align="center" v-if="supplierType === 4"></el-table-column>
                <el-table-column :label="$t('purchase.label.supplierName')" min-width="150">
                    <template #default="scope">
                        <el-tooltip
                                :content="scope.row.supplierName"
                                placement="top"
                                effect="dark"
                        >
                            <span>{{ scope.row.supplierName }}</span>
                        </el-tooltip>
                        <span v-if="supplierType === 3 && scope.row.isDefault==1" class="default-supplier">{{$t('purchase.label.defaults')}}</span>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('purchase.label.supplierCode')" min-width="120" prop="supplierCode" show-overflow-tooltip/>
                <el-table-column :label="$t('purchase.label.supplierCategoryName')" min-width="120" prop="supplierCategoryName" show-overflow-tooltip/>
                <el-table-column :label="$t('purchase.label.supplierWarehouseName')" min-width="120" prop="supplierWarehouseName" show-overflow-tooltip/>
            </el-table>
            <pagination
                    v-if="supplierType !== 3 && supplierTotal > 0"
                    v-model:total="supplierTotal"
                    v-model:page="querySupplierFrom.page"
                    v-model:limit="querySupplierFrom.limit"
                    @pagination="querySupplierAll"
            />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button v-if="supplierType !== 3" type="primary" :loading="submitLoading" @click="submitForm" :disabled="multipleSelectionSupplier.length==0">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-drawer>
</template>
<script setup lang="ts">
    import supplierAPI from "@/modules/pms/api/supplier";
    import PurchaseAPI, { SetSupplierPageQuery,supplierList} from "@/modules/pms/api/purchase";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();

    const  supplierType= ref()
    const  productIds = ref([])
    const  submitLoading= ref(false)
    const loading = ref(false);
    const supplierTotal = ref(0);
    const querySupplierFromRef = ref();
    const multipleSelectionSupplier = ref([]);
    const supplierTable = ref<supplierList[]>()

    const querySupplierFrom = reactive<SetSupplierPageQuery>({
        page: 1,
        limit: 20,
    });

    function close() {
        emit("update:visible", false);
        if (supplierType.value !== 3){
            reset();
        }
    }

    function reset() {
        multipleSelectionSupplier.value = [];
        querySupplierFromRef.value.resetFields();
        querySupplierFrom.page = 1;
        querySupplierFrom.limit = 20;
        querySupplierAll()
    }

    function selectable1(row){
        supplierTable.value.forEach(item=>{
            if(row.supplierId!==item.supplierId){
                item.selected=false
            }else{
                item.selected=true
                multipleSelectionSupplier.value = [item];
            }
        })
    }

    function handleSelectionSupplierChange(val: any) {
        if(supplierType.value === 4){
            multipleSelectionSupplier.value = val;
        }else{
            multipleSelectionSupplier.value = val;
        }
    }

    function submitForm() {
        if (supplierType.value === 1) {
            submitSetSuppliers();
        } else if (supplierType.value === 2) {
            submitSetDefaultSuppliers();
        }else if(supplierType.value == 4){
            //假添加
            submitAddSuppliers();
        }
    }

    function submitSetSuppliers() {
        if(multipleSelectionSupplier.value.length>1){
            return ElMessage.error(t("purchase.message.setSuppliersTips"));
        }
        submitLoading.value = true;
        let suppliersIds = multipleSelectionSupplier.value.map((item) => item.supplierId);
        let params = {
            productIds:productIds.value,
            supplierId:suppliersIds[0]
        };
        PurchaseAPI.setSuppliers(params)
            .then((res) => {
                ElMessage.success(t("purchase.message.setSuppliersSucess"));
                close();
                emit("onSubmit");
            })
            .finally(() => {
                submitLoading.value = false;
            });
    }
    function submitAddSuppliers() {
        submitLoading.value = true;
        const  collection = multipleSelectionSupplier.value
        const removeField = (collection, field) => collection.map(item => {
            const { [field]: removed, ...rest } = item;
            return rest;
        });
        const newCollection = removeField(collection, 'id');
        // ElMessage.success(t("purchase.message.setSuppliersSucess"));
        close();
        emit("onSubmit",newCollection);
        submitLoading.value = false;
    }


    function submitSetDefaultSuppliers() {
        if(multipleSelectionSupplier.value.length>1){
            return  ElMessage.error(t("purchase.message.setDefaultSuppliersTips"));
        }
        submitLoading.value = true;
        let suppliersIds = multipleSelectionSupplier.value.map((item) => item.supplierId);
        let params = {
            productIds:productIds.value,
            supplierId:suppliersIds[0]
        };
        PurchaseAPI.setDefaultSuppliers(params)
        .then((res) => {
          ElMessage.success(t("purchase.message.setDefaultSuppliersSucess"));
          close();
          emit("onSubmit");
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }

    function querySupplierAll() {
        loading.value=true
        submitLoading.value=true
        let params = {
            ...querySupplierFrom
        }
        supplierAPI.getPage(params).then(data => {
            supplierTable.value = data.records;
            supplierTotal.value = parseInt(data.total);
            if(supplierTable.value && supplierTable.value.length>0){
                supplierTable.value.forEach(item=>{
                    item.selected=false
                })
            }
        }).finally(()=>{
                loading.value=false
                submitLoading.value=false
        })
    }

    function setFormData(data) {
        supplierType.value = data.supplierType;
        productIds.value = data.productIds;
        if(supplierType.value===3){
            supplierTable.value = data.supplierList;
        }else{
            querySupplierAll()
        }
    }

    defineExpose({
        setFormData,
    });
</script>

<style scoped lang="scss">
    .supplier-div{
       width: calc(100% - 150px);
    }
    .default-supplier{
        margin-left: 8px;
        padding: 2px 7px;
        background: #FE8200;
        border-radius: 4px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 16px;
        text-align: left;
        font-style: normal;
    }
</style>
<style lang="scss">
    .supplier-selection{
        .el-checkbox__inner {
            border-radius: 50% ;
        }
       .el-table__header-wrapper .el-table-column--selection>.cell {
            display: none;
        }
    }
</style>
