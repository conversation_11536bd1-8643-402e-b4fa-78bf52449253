<script setup lang="ts">

defineOptions({
    name: "AddContract",
    inheritAttrs: false,
});
import {
  getContractDetail,
  updateContract,
  createContract,
  querySalesPersonUser
} from "@pms/api/contract";
import { useRouter } from "vue-router";
import { parseTime } from "@/core/utils/index";
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
import { useI18n } from "vue-i18n"; // 导入国际化
import AddProduct from "./components/addProduct.vue";
const { t } = useI18n();
// Form data
const formData = ref({
  contractName: "", // 合同名称
  signType: "", // 签订类型 0:签订 1:续签
  contractCode: "", // 合同编号
  contractOwner: "", // 签订我方
  contractPartner: "", // 签订对方
  contractAmount: "", // 合同金额
  contractDeposit: "", // 合同保证金
  signDate: "", // 签订日期
  dateRange: [],
  startDate: "", // 合同开始日期
  endDate: "", // 合同结束日期
  contractNote: "", // 合同说明
  attachment: "", // 合同附件
  attachmentFileName: "",
  salespersonUser:"",
  enableProductSwitch:0,
  productList: [],
  approveUser:'',
  approveUserName:'',
});
const personList = ref([]);

function queryPersonList() {
  querySalesPersonUser().then((res)=>{
    personList.value = res
  }).finally(()=>{

  })
}

const fileInput = ref();
const route = useRoute();
const isEdit = ref(route.query.type === "edit");
const contractId = ref(route.query.contractId);
const router = useRouter();
const dialog = reactive({
  title: "",
  visible: false,
});
const addProductRef = ref();

// Form validation rules
const rules = ref({
  contractName: [
    {
      required: true,
      message: t("contract.rules.contractNameRequired"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,50}$/,
      message: t("contract.rules.contractNamePattern"),
      trigger: ["blur", "change"],
    },
  ],
  contractCode: [
    {
      required: true,
      message: t("contract.rules.contractCodeRequired"),
      trigger: "blur",
    },
    {
      pattern: /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};:'",.<>/?\\|]{1,50}$/,
      message: t("contract.rules.contractCodePattern"),
      trigger: ["blur", "change"],
    },
  ],
  contractPartner: [
    {
      required: true,
      message: t("contract.rules.contractPartnerRequired"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,100}$/,
      message: t("contract.rules.contractPartnerPattern"),
      trigger: ["blur", "change"],
    },
  ],
  contractOwner: [
    {
      required: true,
      message: t("contract.rules.contractOwnerRequired"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,100}$/,
      message: t("contract.rules.contractOwnerPattern"),
      trigger: ["blur", "change"],
    },
  ],
  contractAmount: [
    {
      pattern: /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,15}|\d{1,16}\.\d{1,2})$/,
      message: t("contract.rules.contractAmountPattern"),
      trigger: ["blur", "change"],
    },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }

        // 检查是否为0或负数
        if (parseFloat(value) <= 0) {
          callback(new Error(t("contract.rules.amountGreaterThanZero")));
          return;
        }

        // 检查整数部分位数
        const parts = value.toString().split(".");
        const integerPart = parts[0];
        if (integerPart.length > 16) {
          callback(new Error(t("contract.rules.integerMaxLength")));
          return;
        }

        // 检查小数位数
        if (parts.length > 1 && parts[1].length > 2) {
          callback(new Error(t("contract.rules.decimalMaxLength")));
          return;
        }

        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
  contractDeposit: [
    {
      pattern: /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,15}|\d{1,16}\.\d{1,2})$/,
      message: t("contract.rules.contractAmountPattern"),
      trigger: ["blur", "change"],
    },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }

        // 检查是否为0或负数
        if (parseFloat(value) <= 0) {
          callback(new Error(t("contract.rules.amountGreaterThanZero")));
          return;
        }

        // 检查整数部分位数
        const parts = value.toString().split(".");
        const integerPart = parts[0];
        if (integerPart.length > 16) {
          callback(new Error(t("contract.rules.integerMaxLength")));
          return;
        }

        // 检查小数位数
        if (parts.length > 1 && parts[1].length > 2) {
          callback(new Error(t("contract.rules.decimalMaxLength")));
          return;
        }

        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
  contractNote: [
    {
      required: true,
      message: t("contract.rules.contractNoteRequired"),
      trigger: "blur",
    },
    {
      pattern:
        /^[\u4e00-\u9fa5a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};:'",.<>/?\\|]{0,200}$/,
      message: t("contract.rules.contractNotePattern"),
      trigger: ["blur", "change"],
    },
  ],
  startDate: [
    {
      required: true,
      message: t("contract.rules.startDateRequired"),
      trigger: "change",
    },
  ],
  endDate: [
    {
      required: true,
      message: t("contract.rules.endDateRequired"),
      trigger: "change",
    },
  ],
  attachment: [
    {
      required: false,
    },
  ],
  signType: [
    {
      required: true,
      message: t("contract.rules.signTypeRequired"),
      trigger: "change",
    },
  ],
  signDate: [
    {
      required: true,
      message: t("contract.rules.signDateRequired"),
      trigger: "change",
    },
  ],
  dateRange: [
    {
      required: true,
      message: t("contract.rules.dateRangeRequired"),
      trigger: "change",
    },
    {
      validator: (rule, value, callback) => {
        if (value && value.length > 0) {
          callback();
        } else {
          callback(new Error(t("contract.rules.dateRangeRequired")));
        }
      },
      trigger: "change",
    },
  ],
  enableProductSwitch: [
    {
      required:true,
      message: t('contract.rules.enableProductSwitch'),
      trigger: "change",
    }
  ],
  productList: [
    {
      required:true,
      message: t('contract.rules.productList'),
    }
  ],
  approveUser: [
    {
      required:true,
      message: t('contract.rules.approveUser'),
    }
  ],
});

const formRef = ref();
/** 添加商品 */
async function addProduct() {
  dialog.title = t('contract.title.addProduct');
  let data = {
  }
  addProductRef.value.setFormData({queryParams:data });
  addProductRef.value.queryManagerCategoryList();
  dialog.visible = true;
}
function onSubmit(data) {
  data.forEach(item=>{
    item.unitAmount =item.saleAmount
    item.productId = item.id
  })
  let arr = data.concat(formData.value.productList);
  console.log("===productList==="+arr);
  let uniqueArr =[...new Map(arr.map(item => [item.productCode, item])).values()];
  formData.value.productList=uniqueArr;
  console.log("===productList==="+formData.value.productList);
  formRef.value.clearValidate('productList')
}
function handleDelete(index) {
  formData.value.productList.splice(index, 1)
}
function changeProduct(){
  if(!formData.value.enableProductSwitch){
    formData.value.productList = []
  }
}
// Fetch contract details
const fetchContractDetails = async () => {
  if (isEdit.value && contractId.value) {
    try {
      const data = await getContractDetail({
        contractId: contractId.value,
      });

      // Only pick fields that exist in formData
      const filteredData = Object.keys(formData.value).reduce((acc, key) => {
        acc[key] = data[key];
        return acc;
      }, {});

      // Populate form data
      formData.value = {
        ...formData.value,
        ...filteredData,
        productList: data.contractProudctList?data.contractProudctList:[],
        dateRange: [
          parseTime(data?.startDate, "{y}-{m}-{d}"),
          parseTime(data?.endDate, "{y}-{m}-{d}"),
        ],
        productList:data.contractProudctList,
        attachment: (() => {
          if (data?.attachment) {
            if (typeof data.attachment === "string") {
              try {
                return JSON.parse(data.attachment);
              } catch (e) {
                console.error(
                  "Error parsing attachment as JSON:",
                  e,
                  data.attachment
                );
                ElMessage.error(t("contract.message.attachmentParseFailed"));
                return [];
              }
            } else if (typeof data.attachment === "object") {
              return data.attachment;
            } else {
              console.error(
                "attachment is not string or object:",
                data.attachment
              );
              ElMessage.error(t("contract.message.attachmentError"));
              return [];
            }
          } else {
            return [];
          }
        })(),
      };
    } catch (error) {
      // console.error("Failed to fetch contract details:", error);
      // ElMessage.error("获取合同详情失败");
    }
  }
};

const previewFile = async (
  bucket: string,
  fileName: string,
  originalFileName: string
) => {
  try {
    const res = await previewSingle(bucket, fileName, originalFileName);
    console.log("previewFile------", res);
  } catch (error) {
    console.error("Failed to preview file:", error);
    ElMessage.error("预览文件失败");
  }
};
const fileType = ["jpg", "jpeg", "png", "pdf", "zip", "rar"];
const onChangeMultiple = (val) => {
  formData.value.attachment = val;
  if (val.length > 0) {
    formRef.value.clearValidate("attachment");
  }
};
onMounted(() => {
  fetchContractDetails();
  queryPersonList();
});

function setName() {
  if(formData.value.approveUser && formData.value.approveUser != null && formData.value.approveUser != '' && formData.value.approveUser != undefined){
    let data: any = personList.value.find(
      (item: any) => item.userId === formData.value.approveUser
    );
    if (data) {
      formData.value.approveUserName = data.nickName ? data.nickName : "";
    } else {
      formData.value.approveUserName = "";
    }
  }else{
    formData.value.approveUserName = ''
  }
}

// Form submission
const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();

    const submitData = {
      ...formData.value,
      // Ensure proper date formatting if needed
      signDate: new Date(formData.value.signDate).getTime(),
      startDate: new Date(formData.value.dateRange[0] + " 00:00:00").getTime(),
      endDate: new Date(formData.value.dateRange[1] + " 23:59:59").getTime(),
      attachment: JSON.stringify(formData.value.attachment),
    };

    if(formData.value.productList && formData.value.productList.length > 0){
      submitData.contractProudctList = []
      formData.value.productList.forEach(list=>{
        let obj = {
          productId:list.productId,
          productCode:list.productCode,
          // unitAmount:list.unitAmount
        }
        submitData.contractProudctList.push(obj)
      })
    }
    delete submitData.productList;
    delete submitData.dateRange;

    if (isEdit.value) {
      // Add contract ID for editing
      submitData.contractId = contractId.value;
      await updateContract(submitData);
      ElMessage.success("合同更新成功");
    } else {
      await createContract(submitData);
      ElMessage.success("合同创建成功");
    }

    // Navigate back to contract list
    handleClose();
  } catch (error) {
    /* if (error.message) {
      ElMessage.error(error.message);
    } */ /* else {
      ElMessage.error(isEdit.value ? "更新失败" : "添加失败");
    } */
    console.error("Form submission failed:", error);
  }
};

import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
const handleClose = async () => {
  await tagsViewStore.delView(route);
  router.push("/pms/contract/manage");
};
</script>

<template>
  <div class="contract-add">
    <div class="card-header mb-20px">
      <img
        @click="handleClose"
        src="@/core/assets/images/arrow-left.png"
        alt=""
        class="back-btn"
      />
      <span @click="handleClose" class="code">
        {{ isEdit ? $t('contract.title.edit') : $t('contract.title.add') }}
      </span>
    </div>
    <div class="card-title mb-13px">{{ $t("contract.label.basicInfo") }}</div>

    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="$t('contract.label.contractName')" prop="contractName">
            <el-input
              v-model="formData.contractName"
              maxlength="50"
              :placeholder="$t('common.placeholder.inputTips')"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="$t('contract.label.signType')" prop="signType">
            <el-select v-model="formData.signType" :placeholder="$t('common.placeholder.selectTips')">
              <el-option :value="0" :label="$t('contract.label.typeSign')" />
              <el-option :value="1" :label="$t('contract.label.typeRenew')" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="$t('contract.label.contractCode')" prop="contractCode">
            <el-input
              v-model="formData.contractCode"
              maxlength="50"
              :placeholder="$t('common.placeholder.inputTips')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('contract.label.contractOwner')" prop="contractOwner">
            <el-input
              v-model="formData.contractOwner"
              maxlength="100"
              :placeholder="$t('common.placeholder.inputTips')"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="$t('contract.label.contractPartner')" prop="contractPartner">
            <el-input
              v-model="formData.contractPartner"
              maxlength="100"
              :placeholder="$t('common.placeholder.inputTips')"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="$t('contract.label.contractAmount')" prop="contractAmount">
            <el-input v-model="formData.contractAmount" :placeholder="$t('common.placeholder.inputTips')" />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="$t('contract.label.contractDeposit')" prop="contractDeposit">
            <el-input v-model="formData.contractDeposit" :placeholder="$t('common.placeholder.inputTips')" />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="$t('contract.label.salesPerson')" prop="salespersonUser">
            <el-select v-model="formData.salespersonUser" :placeholder="$t('common.placeholder.selectTips')" filterable clearable>
              <el-option v-for="item in personList" :key="item.userId" :value="item.userId" :label="item.nickName"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('contract.label.dateRange')" prop="dateRange">
            <el-date-picker
              v-model="formData.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('contract.label.signDate')" prop="signDate">
            <el-date-picker
              v-model="formData.signDate"
              type="date"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('contract.label.enableProductSwitch')" prop="enableProductSwitch">
            <el-switch
              v-model="formData.enableProductSwitch"
              active-text="开启"
              inactive-text="关闭"
              :active-value="1"
              :inactive-value="0"
              @change="changeProduct">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('contract.label.contractNote')" prop="contractNote">
            <el-input
              v-model="formData.contractNote"
              type="textarea"
              :placeholder="$t('common.placeholder.inputTips')"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="card-title mb-13px">{{ $t('contract.label.attachment')}}</div>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('contract.label.uploadAttachment')" prop="attachment">
            <UploadMultiple
              @update:model-value="onChangeMultiple"
              ref="detailPicsRef"
              v-model="formData.attachment"
              :limit="1"
              :formRef="formRef"
              :fileSize="20"
              :fileType="fileType"
              class="modify-multipleUpload"
              name="detailPic"
              listType="text"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="formData.enableProductSwitch">
        <div class="card-title mb-13x">{{ $t('contract.label.enableProductSwitch')}}</div>
        <el-form-item
          :label="$t('contract.label.productDetail')"
          prop="productList"
        >
          <div class="button-add cursor-pointer" @click="addProduct()">
            {{$t('contract.button.addGoods')}}
          </div>
        </el-form-item>
        <div style="margin-left: 120px;">
          <el-table :data="formData.productList" highlight-current-row stripe>
            <el-table-column type="index" :label="$t('contract.label.sort')" width="60"></el-table-column>
            <el-table-column prop="productName" :label="$t('contract.label.productName')"></el-table-column>
            <el-table-column prop="productUnitName" :label="$t('contract.label.productUnit')"></el-table-column>
            <!--<el-table-column prop="unitAmount" :label="$t('contract.label.price')">
              <template #default="scope">
                <el-form-item class="mt15px" label-width="0px" :prop="'productList.'+scope.$index +'.unitAmount'" :rules="[{required:true,message: t('contract.rules.price'),trigger: ['blur']},
                {pattern: /(^[1-9](\d{1,7})?(\.\d{1,2})?$)|(^0\.[1-9]\d{0,1}$)|(^0\.0[1-9]$)/,message:t('contract.rules.priceFormat'),trigger: ['blur','change'],}]">
                  <el-input v-model="scope.row.unitAmount" :placeholder="$t('common.placeholder.inputTips')" clearable>
                    <template #prefix>
                      <span v-if="scope.row.saleCurrency == 'CNY'">￥</span>
                      <span v-else>$</span>
                    </template>
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>-->
            <el-table-column :label="t('contract.button.operation')">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  link
                  @click="handleDelete(scope.$index)"
                >
                  {{$t('common.delete')}}
                </el-button>
              </template>
            </el-table-column>

          </el-table>
        </div>
      </div>

      <div class="card-title mb-13px">
        {{ $t("contract.label.audit") }}
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="$t('contract.label.auditPerson')"
                        prop="approveUser">
            <el-select v-model="formData.approveUser" :placeholder="$t('common.placeholder.selectTips')" @change="setName" filterable clearable>
              <el-option v-for="item in personList" :key="item.userId" :value="item.userId" :label="item.nickName"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="form-actions">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </el-form>

    <AddProduct
      ref="addProductRef"
      v-model:visible="dialog.visible"
      :title="dialog.title"
      @onSubmit="onSubmit"
    />
  </div>
</template>

<style scoped lang="scss">
.contract-add {
  padding: 13px 20px;
  background-color: #fff;
  border-radius: 4px;
  .card-header {
    padding: 0px 10px 13px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;
    .code {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    .status {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }
  .upload-container {
    display: flex;
    flex-direction: column;
  }
  .tip {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #90979e;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
  .form-actions {
    margin-top: 32px;
    padding-top: 13px;
    text-align: right;
    border-top: 1px solid #ebeef5;
  }
  .button-add{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
    color: var(--el-color-primary)
  }
}
</style>
