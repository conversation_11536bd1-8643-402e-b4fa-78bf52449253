<template>
  <div class="app-container">
    <div class="addPage">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon><Back /></el-icon>
          <span>{{ $t("quickOutbound.label.outboundNoticeCode") }}:{{form.outboundNoticeCode}}</span>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" ref="formRef" label-width="112px" label-position="right">
          <div class="basicInformation item_content">
            <div class="flex-center-but">
              <div class="title">
                <div>{{ $t("quickOutbound.label.documentInformation") }}</div>
              </div>
              <div v-if="firstTableShow" @click="closeFirstTable" style="font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 14px;color: #762ADB;line-height: 22px;text-align: left;font-style: normal;cursor: default">收起</div>
              <div v-if="!firstTableShow" @click="openFirstTable" style="font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 14px;color: #762ADB;line-height: 22px;text-align: left;font-style: normal;cursor: default">展开</div>
            </div>

            <table v-if="firstTableShow" style="border: 1px solid #E5E7F3  !important;border-collapse: collapse;width: 100%;table-layout: fixed;-webkit-print-color-adjust: exact;">
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.outboundNoticeCode')}}</td>
                <td class="td-value">{{ form.outboundNoticeCode ? form.outboundNoticeCode : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.outboundType')}}</td>
                <td class="td-value">
                  <span v-if="form.outboundType == 1">{{ $t("quickOutbound.outboundTypeList.procurementOutbound") }}</span>
                  <span v-else-if="form.outboundType == 2">{{ $t("quickOutbound.outboundTypeList.pickupCardRedemption") }}</span>
                  <span v-else-if="form.outboundType == 3">{{ $t("quickOutbound.outboundTypeList.businessReception") }}</span>
                  <span v-else-if="form.outboundType == 4">{{ $t("quickOutbound.outboundTypeList.reissue") }}</span>
                  <span v-else-if="form.outboundType == 5">{{ $t("quickOutbound.outboundTypeList.sellAtTheFieldEdge") }}</span>
                  <span v-else-if="form.outboundType == 6">{{ $t("quickOutbound.outboundTypeList.returnOutbound") }}</span>
                  <span v-else-if="form.outboundType == 7">{{ $t("quickOutbound.outboundTypeList.allotOutbound") }}</span>
                  <span v-else-if="form.outboundType == 8">{{ $t("quickOutbound.outboundTypeList.directOutbound") }}</span>
                  <span v-else-if="form.outboundType == 9">{{ $t("quickOutbound.outboundTypeList.participateInTheExhibition") }}</span>
                  <span v-else>-</span>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.themeDescription')}}</td>
                <td class="td-value">{{ form.orderTheme ? form.orderTheme : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.sourceOrderCode')}}</td>
                <td class="td-value">{{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}</td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.salesperson')}}</td>
                <td class="td-value">{{ form.purchaseSalesPerson ? form.purchaseSalesPerson : "-" }}</td>
                <td class="td-label">{{ form.outboundType == 2 ? '提货卡号' : $t('quickOutbound.label.originalOrderNumber')}}</td>
                <td class="td-value">{{ form.sourceCode ? form.sourceCode : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.createUserName')}}</td>
                <td class="td-value">{{ form.createUserName ? form.createUserName : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.createTime')}}</td>
                <td class="td-value">{{ form.createTime ? parseDateTime(form.createTime, "dateTime") : "-" }}</td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.customerName')}}</td>
                <td class="td-value">{{ form.customerName ? form.customerName : "-" }}</td>
                <td class="td-label">{{ $t('quickOutbound.label.customerAddress')}}</td>
                <td class="td-value">
                  <span class="encryptBox">
                    <span v-if="form.addressShow">
                      {{ form.addressFormat }}
                      <el-icon
                        v-if="form.fullAddress"
                        @click="form.addressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.addressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.fullAddress ? form.fullAddress : "-" }}
                    </span>
                  </span>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.customerPerson')}}</td>
                <td class="td-value">
                  {{ form.nameShow ? (form.contactPerson ? form.contactPerson : '-') : encryptName(form.contactPerson) }}
                  <el-icon
                    v-if="form.contactPerson"
                    @click="form.contactPerson ? getRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.nameShow ? '' : 'View'" />
                  </el-icon>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.customerMobile')}}</td>
                <td class="td-value">
                  <span class="encryptBox">
                    <span v-if="form.customerMobile">
                      {{ form.customerAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.customerMobile && form.customerMobile.length <= 4
                      "
                    >
                      {{ form.customerMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.customerMobile && form.customerMobile.length > 4
                      "
                    >
                      {{ form.customerMobile }}
                      <el-icon
                        v-if="form.customerMobile"
                        @click="form.mobilePhoneShow ? getRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.supplierName')}}</td>
                <td class="td-value">{{ form.supplierName ? form.supplierName : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.supplierAddress')}}</td>
                <td class="td-value">
                  <span class="encryptBox">
                    <span v-if="form.supplierAddressShow">
                      {{ form.supplierAddressFormat }}
                      <el-icon
                        v-if="form.supplierFullAddress"
                        @click="form.supplierAddressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierAddressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.supplierFullAddress ? form.supplierFullAddress : "-" }}
                    </span>
                  </span>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.supplierPerson')}}</td>
                <td class="td-value">
                  {{ form.supplierNameShow ? (form.supplierContactPerson ? form.supplierContactPerson : '-') : encryptName(form.supplierContactPerson) }}
                  <el-icon
                    v-if="form.supplierContactPerson"
                    @click="form.supplierContactPerson ? getSupplierRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.supplierNameShow ? '' : 'View'" />
                  </el-icon>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.supplierMobile')}}</td>
                <td class="td-value">
                  <span class="encryptBox">
                    <span v-if="form.supplierContactMobile">
                      {{ form.supplierContactAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.supplierContactMobile && form.supplierContactMobile.length <= 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.supplierContactMobile && form.supplierContactMobile.length > 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                      <el-icon
                        v-if="form.supplierContactMobile"
                        @click="form.supplierMobilePhoneShow ? getSupplierRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierMobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.contractName')}}</td>
                <td class="td-value">
                  <div style="color: #762ADB;" v-if="form.contractName">{{form.contractName}}</div>
                  <div v-else>-</div>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.contractCode')}}</td>
                <td class="td-value">
                  <div style="color: #762ADB;" v-if="form.contractCode">{{form.contractCode}}</div>
                  <div v-else>-</div>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.contractClassification')}}</td>
                <td class="td-value">
                  <div v-if="form.contractType == 1">销售合同</div>
                  <div v-else-if="form.contractType == 2">采购合同</div>
                  <div v-else>-</div>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.settlementMethod')}}</td>
                <td class="td-value">
                  {{ form.paymentType == 1 ? $t('quickOutbound.label.presentSettlement') : form.paymentType == 2 ? $t('quickOutbound.label.accountHanging') : form.paymentType == 3 ? $t('quickOutbound.label.noSettlementRequired') : "-" }}
                </td>
              </tr>
              <tr class="input-tr">
                <td class="td-label">{{$t('quickOutbound.label.plannedReceivedTime')}}</td>
                <td class="td-value">{{ form.plannedReceivedTime ? parseDateTime(form.plannedReceivedTime,"dateTime") : '-' }}</td>
                <td class="td-label"><span style="color:#FF192F">*</span>{{$t('quickOutbound.label.plannedDeliveryTime')}}</td>
                <td class="td-value">
                  <el-form-item prop="plannedDeliveryTime" label-width="0" :rules="form.outboundNoticeStatus == 1 ? [{required:true,message:t('quickOutbound.rules.plannedDeliveryTime'),trigger:['change','blur']}] : []">
                    <el-date-picker
                      class="!w-[256px]"
                      v-if="form.outboundNoticeStatus == 1"
                      v-model="form.plannedDeliveryTime"
                      type="datetime"
                      :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                    </el-date-picker>
                    <span v-else>{{form.plannedDeliveryTime ? parseDateTime(form.plannedDeliveryTime,"dateTime") : '-'}}</span>
                  </el-form-item>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.plannedDistributionMethod')}}</td>
                <td class="td-value">{{ form.deliveryName ? form.deliveryName : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.sourceOrderRemark')}}</td>
                <td class="td-value">{{form.remark ? form.remark : "-"}}</td>
              </tr>
              <tr class="input-tr">
                <td class="td-label"><span style="color:#FF192F">*</span>{{$t('quickOutbound.label.actualDistributionMethod')}}</td>
                <td class="td-value">
                  <el-form-item label-width="0" prop="actualDeliveryType" :rules="[{required:true,message:t('quickOutbound.rules.actualDeliveryType'),trigger:['change','blur']}]">
                    <el-select
                      v-model="form.actualDeliveryType"
                      :placeholder="$t('quickOutbound.placeholder.actualDeliveryType')"
                      clearable
                      class="!w-[256px]"
                      @change="setName"
                    >
                      <el-option
                        v-for="(item, index) in deliveryTypeList"
                        :key="index"
                        :label="item.name"
                        :value="item.code"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td class="td-label"><span style="color:#FF192F">*</span>{{$t('quickOutbound.label.actualOutboundTime')}}</td>
                <td class="td-value">
                  <el-form-item prop="outboundTime" label-width="0" :rules="[{required:true,message:t('quickOutbound.rules.actualOutboundTime'),trigger:['change','blur']}]">
                    <el-date-picker
                      class="!w-[256px]"
                      v-model="form.outboundTime"
                      type="datetime"
                      :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                    </el-date-picker>
                  </el-form-item>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.carrier')}}</td>
                <td class="td-value">
                  <el-form-item label-width="0" prop="carrier">
                    <el-input
                      v-model="form.carrier"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="30"
                      clearable
                      class="!w-[256px]"
                    />
                  </el-form-item>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.vehicleNumber')}}</td>
                <td class="td-value">
                  <el-form-item label-width="0" prop="carNumber">
                    <el-input
                      v-model="form.carNumber"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="30"
                      clearable
                      class="!w-[256px]"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr class="input-tr">
                <td class="td-label">{{$t('quickOutbound.label.weighbillNumber')}}</td>
                <td class="td-value">
                  <el-form-item label-width="0" prop="poundCode">
                    <el-input
                      v-model="form.poundCode"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="30"
                      clearable
                      class="!w-[256px]"
                    >
                      <template #append>
                        <div v-if="form.fileNum">
                          <el-badge :value="form.fileNum" :offset="[10, 8]" class="item" type="primary">
                            <!-- 上传 -->
                            <el-button type="primary" link @click="handleUpload(form.poundAttachmentFiles)">
                              <span style="color: #762ADB">{{ $t("quickOutbound.button.upload") }}</span>
                            </el-button>
                          </el-badge>
                        </div>
                        <div v-else>
                          <!-- 上传 -->
                          <el-button type="primary" link @click="handleUpload(form.poundAttachmentFiles)">
                            <span style="color: #762ADB">{{ $t("quickOutbound.button.upload") }}</span>
                          </el-button>
                        </div>
                      </template>
                    </el-input>
                  </el-form-item>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.outboundRemark')}}</td>
                <td class="td-value" colspan="5">
                  <el-form-item label-width="0" prop="outboundRemark">
                    <el-input
                      v-model="form.outboundRemark"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      type="text"
                      maxlength="200"
                      show-word-limit
                    />
                  </el-form-item>
                </td>
              </tr>
            </table>
            <!--<el-row>
              &lt;!&ndash; 出库通知单号 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.outboundNoticeCode')">
                  {{ form.outboundNoticeCode ? form.outboundNoticeCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash; 出库类型:1:销售出库、2:提货卡兑换、3：业务招待、4：补发、5：地头售卖、6:采购退货、7：调拨出库、8：直接出库 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.outboundType')">
                  <span v-if="form.outboundType == 1">{{ $t("quickOutbound.outboundTypeList.procurementOutbound") }}</span>
                  <span v-else-if="form.outboundType == 2">{{ $t("quickOutbound.outboundTypeList.pickupCardRedemption") }}</span>
                  <span v-else-if="form.outboundType == 3">{{ $t("quickOutbound.outboundTypeList.businessReception") }}</span>
                  <span v-else-if="form.outboundType == 4">{{ $t("quickOutbound.outboundTypeList.reissue") }}</span>
                  <span v-else-if="form.outboundType == 5">{{ $t("quickOutbound.outboundTypeList.sellAtTheFieldEdge") }}</span>
                  <span v-else-if="form.outboundType == 6">{{ $t("quickOutbound.outboundTypeList.returnOutbound") }}</span>
                  <span v-else-if="form.outboundType == 7">{{ $t("quickOutbound.outboundTypeList.allotOutbound") }}</span>
                  <span v-else-if="form.outboundType == 8">{{ $t("quickOutbound.outboundTypeList.directOutbound") }}</span>
                  <span v-else-if="form.outboundType == 9">{{ $t("quickOutbound.outboundTypeList.participateInTheExhibition") }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              &lt;!&ndash; 主题描述 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.themeDescription')">
                  {{ form.orderTheme ? form.orderTheme : "-" }}
                </el-form-item>
              </el-col>
              &lt;!&ndash; 来源单号 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.sourceOrderCode')">
                  {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              &lt;!&ndash; 业务员 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.salesperson')">
                  {{ form.purchaseSalesPerson ? form.purchaseSalesPerson : "-" }}
                </el-form-item>
              </el-col>
              &lt;!&ndash; 原单号 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="form.outboundType == 2 ? '提货卡号' : $t('quickOutbound.label.originalOrderNumber')">
                  {{ form.sourceCode ? form.sourceCode : "-" }}
                </el-form-item>
              </el-col>
              &lt;!&ndash; 申请人 &ndash;&gt;
              <el-col :span="6">
                <el-form-item
                  :label="$t('quickOutbound.label.createUserName')"
                >
                  {{ form.createUserName ? form.createUserName : "-" }}
                </el-form-item>
              </el-col>
              &lt;!&ndash; 申请时间 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.createTime')">
                  <span v-if="form.createTime">
                    {{ parseDateTime(form.createTime, "dateTime") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              &lt;!&ndash; 客户 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.customerName')">
                  {{ form.customerName ? form.customerName : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash; 客户地址 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.customerAddress')">
                  <span class="encryptBox">
                    <span v-if="form.addressShow">
                      {{ form.addressFormat }}
                      <el-icon
                        v-if="form.fullAddress"
                        @click="form.addressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.addressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.fullAddress ? form.fullAddress : "-" }}
                    </span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash; 客户联系人 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.customerPerson')">
                  {{ form.nameShow ? (form.contactPerson ? form.contactPerson : '-') : encryptName(form.contactPerson) }}
                  <el-icon
                    v-if="form.contactPerson"
                    @click="form.contactPerson ? getRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.nameShow ? '' : 'View'" />
                  </el-icon>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash; 客户联系电话 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.customerMobile')">
                  <span class="encryptBox">
                    <span v-if="form.customerMobile">
                      {{ form.customerAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.customerMobile && form.customerMobile.length <= 4
                      "
                    >
                      {{ form.customerMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.customerMobile && form.customerMobile.length > 4
                      "
                    >
                      {{ form.customerMobile }}
                      <el-icon
                        v-if="form.customerMobile"
                        @click="form.mobilePhoneShow ? getRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              &lt;!&ndash; 供应商 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.supplierName')" >
                  {{ form.supplierName ? form.supplierName : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash; 供应商地址 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.supplierAddress')">
                  <span class="encryptBox">
                    <span v-if="form.supplierAddressShow">
                      {{ form.supplierAddressFormat }}
                      <el-icon
                        v-if="form.supplierFullAddress"
                        @click="form.supplierAddressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierAddressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.supplierFullAddress ? form.supplierFullAddress : "-" }}
                    </span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash; 供应商联系人 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.supplierPerson')">
                  {{ form.supplierNameShow ? (form.supplierContactPerson ? form.supplierContactPerson : '-') : encryptName(form.supplierContactPerson) }}
                  <el-icon
                    v-if="form.supplierContactPerson"
                    @click="form.supplierContactPerson ? getSupplierRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.supplierNameShow ? '' : 'View'" />
                  </el-icon>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash; 供应商联系电话 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.supplierMobile')">
                  <span class="encryptBox">
                    <span v-if="form.supplierContactMobile">
                      {{ form.supplierContactAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.supplierContactMobile && form.supplierContactMobile.length <= 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.supplierContactMobile && form.supplierContactMobile.length > 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                      <el-icon
                        v-if="form.supplierContactMobile"
                        @click="form.supplierMobilePhoneShow ? getSupplierRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierMobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              &lt;!&ndash; 合同名称&ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.contractName')" >
                  <span style="color: #762ADB;" v-if="form.contractName">{{form.contractName}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              &lt;!&ndash; 合同编码&ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.contractCode')" >
                  <span style="color: #762ADB;" v-if="form.contractCode">{{form.contractCode}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              &lt;!&ndash; 合同分类&ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.contractClassification')" >
                  <span v-if="form.contractType == 1">销售合同</span>
                  <span v-else-if="form.contractType == 2">采购合同</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              &lt;!&ndash; 结算方式&ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.settlementMethod')" >
                  {{ form.paymentType == 1 ? $t('quickOutbound.label.presentSettlement') : form.paymentType == 2 ? $t('quickOutbound.label.accountHanging') : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              &lt;!&ndash; 要求到货时间 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.plannedReceivedTime')">
                  <span v-if="form.plannedReceivedTime">{{ parseDateTime(form.plannedReceivedTime,"dateTime") }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              &lt;!&ndash; 计划发货时间 &ndash;&gt;
              <el-col :span="6">
                <el-form-item prop="plannedDeliveryTime" :label="$t('quickOutbound.label.plannedDeliveryTime')" :rules="form.outboundNoticeStatus == 1 ? [{required:true,message:t('quickOutbound.rules.plannedDeliveryTime'),trigger:['change','blur']}] : []">
                  <el-date-picker
                    class="!w-[256px]"
                    v-if="form.outboundNoticeStatus == 1"
                    v-model="form.plannedDeliveryTime"
                    type="datetime"
                    :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                  </el-date-picker>
                  <span v-else>{{form.plannedDeliveryTime ? parseDateTime(form.plannedDeliveryTime,"dateTime") : '-'}}</span>
                </el-form-item>
              </el-col>
              &lt;!&ndash; 计划配送方式 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.plannedDistributionMethod')">
                  {{ form.deliveryName ? form.deliveryName : "-" }}
                </el-form-item>
              </el-col>
              &lt;!&ndash; 来源单备注 &ndash;&gt;
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.sourceOrderRemark')">
                  {{form.remark ? form.remark : "-"}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                &lt;!&ndash; 实际配送方式 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.actualDistributionMethod')" prop="actualDeliveryType" :rules="[{required:true,message:t('quickOutbound.rules.actualDeliveryType'),trigger:['change','blur']}]">
                  <el-select
                    v-model="form.actualDeliveryType"
                    :placeholder="$t('quickOutbound.placeholder.actualDeliveryType')"
                    clearable
                    class="!w-[256px]"
                    @change="setName"
                  >
                    <el-option
                      v-for="(item, index) in deliveryTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              &lt;!&ndash; 实际出库时间 &ndash;&gt;
              <el-col :span="6">
                <el-form-item prop="outboundTime" :label="$t('quickOutbound.label.actualOutboundTime')" :rules="[{required:true,message:t('quickOutbound.rules.actualOutboundTime'),trigger:['change','blur']}]">
                  <el-date-picker
                    class="!w-[256px]"
                    v-model="form.outboundTime"
                    type="datetime"
                    :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash;  承运商 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.carrier')" prop="carrier">
                  <el-input
                    v-model="form.carrier"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                &lt;!&ndash;  车号 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.vehicleNumber')" prop="carNumber">
                  <el-input
                    v-model="form.carNumber"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                &lt;!&ndash;  磅单编号 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.weighbillNumber')" prop="poundCode">
                  <el-input
                    v-model="form.poundCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    class="!w-[256px]"
                  >
                    <template #append>
                      <div v-if="form.fileNum">
                        <el-badge :value="form.fileNum" :offset="[10, 8]" class="item" type="primary">
                          &lt;!&ndash; 上传 &ndash;&gt;
                          <el-button type="primary" link @click="handleUpload(form.poundAttachmentFiles)">
                            <span style="color: #762ADB">{{ $t("quickOutbound.button.upload") }}</span>
                          </el-button>
                        </el-badge>
                      </div>
                      <div v-else>
                        &lt;!&ndash; 上传 &ndash;&gt;
                        <el-button type="primary" link @click="handleUpload(form.poundAttachmentFiles)">
                          <span style="color: #762ADB">{{ $t("quickOutbound.button.upload") }}</span>
                        </el-button>
                      </div>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                &lt;!&ndash; 出库备注 &ndash;&gt;
                <el-form-item :label="$t('quickOutbound.label.outboundRemark')" prop="outboundRemark">
                  <el-input
                    v-model="form.outboundRemark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    type="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>-->
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>{{ $t("quickOutbound.label.waitOutboundProductDetail") }}</div>
              </div>
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0">
                  <el-table
                    :data="form.warehouseOutboundDetailVOList"
                    v-loading="formLoading"
                    stripe
                    highlight-current-row
                    show-summary
                    :summary-method="getSummaries"
                    border
                  >
                    <el-table-column fixed="left" type="index" :label="$t('common.sort')" width="60" align="center" />
                    <el-table-column :label="$t('quickOutbound.label.productInfo')" width="150">
                      <template #default="scope">
                        <div style="word-break: break-all">
                          <span style="color: #90979E ">{{ scope.row.productCode }} | </span> {{ scope.row.productName }}
                          <!--<div style="color: #90979E;">{{ scope.row.productCode }}</div>
                          <div style="color:#52585F">{{ scope.row.productName }}</div>-->
                        </div>
                      </template>
                    </el-table-column>

                    <!--&lt;!&ndash; 计划量 &ndash;&gt;
                    <el-table-column min-width="120" :label="$t('quickOutbound.label.plannedQuantity')">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">
                          <div>{{scope.row.planProductQty == null ? '-' : scope.row.planProductQty}}</div>
                        </div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    &lt;!&ndash; 计划转换量 &ndash;&gt;
                    <el-table-column min-width="120" :label="$t('quickOutbound.label.plannedConversionQuantity')">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">
                          <div>{{scope.row.planProductWeight == null ? '-' : scope.row.planProductWeight}}</div>
                        </div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    &lt;!&ndash; 剩余转换量 &ndash;&gt;
                    <el-table-column min-width="120" :label="$t('quickOutbound.label.residualConversionQuantity')">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">
                          <div>{{scope.row.surplusWeight == null ? '-' : scope.row.surplusWeight}}</div>
                        </div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>-->
                    <!-- 出库库区 -->
                    <el-table-column width="130" :label="$t('quickOutbound.label.outboundStorageArea')">
                      <template #header>
                        <el-dropdown trigger="click" @command="handleFilterChange" placement="bottom-end" :disabled="!outWarehouseAreaList || outWarehouseAreaList.length ===0">
                                        <span class="dropdown-trigger">
                                          {{ $t('quickOutbound.label.outboundStorageArea') }}
                                          <svg-icon style="height: 1em;width: 1em;" icon-class="iconBottom"></svg-icon>
                                        </span>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item
                                v-for="item in outWarehouseAreaList"
                                :key="item.areaCode"
                                :command="item.areaCode"
                                :class="{ 'is-selected': chooseWarehouse === item.areaCode }">
                                {{ item.areaName }}
                              </el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </template>
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.warehouseAreaCode'"
                          :rules="(!scope.row.outboundQty || scope.row.outboundQty == 0) && (!scope.row.outboundWeight || scope.row.outboundWeight == 0) ? [] : [{required: true,message: t('quickOutbound.rules.warehouseAreaCode'),trigger: ['blur'],},]"
                        >
                          <!--@focus="getOutWarehouseAreaList(scope.row.productCode,scope.row.isDiscreteUnit,scope.$index)"-->
                          <el-select
                            v-model="scope.row.warehouseAreaCode"
                            :placeholder="$t('common.placeholder.selectTips')"
                            @change="getWarehouseAreaDetail($event, scope.$index)"
                            clearable
                            :loading="scope.row.areaLoading"
                          >
                            <el-option
                              v-for="item in scope.row.outWarehouseAreaList"
                              :key="item.areaCode"
                              :label="item.areaName"
                              :value="item.areaCode"
                            />
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 出库量 -->
                    <el-table-column min-width="240" :label="$t('quickOutbound.label.outboundQuantity')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.outboundQty'"
                          :rules="[
                            {
                              required: false,
                            },
                            {
                              pattern:
                                /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                              message: t(
                                'quickOutbound.rules.outboundQtyFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]"
                        >
                          <el-input v-model="scope.row.outboundQty" :disabled="form.outboundType == 7" @change="setOutboundWeight(scope.$index)" clearable :placeholder="$t('common.placeholder.inputTips')"><template #append>{{ scope.row.productUnitName }}</template></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 单价 -->
                    <el-table-column v-if="form.outboundType == 7" min-width="240" align="right" :label="'*' + $t('quickOutbound.label.unitPrice')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.salePrice'" :rules="[
                            {
                              required: true,
                              message: t(
                                'quickOutbound.rules.salePrice'
                              ),
                              trigger: ['blur'],
                            },
                            {
                              pattern:
                                /^(-?0(?:\.\d{1,4})?|-?[1-9]\d{0,6}(?:\.\d{1,4})?)$/,
                              message: t(
                                'quickOutbound.rules.salePriceFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]">
                          <el-input v-model="scope.row.salePrice" :disabled="form.outboundType == 7 && true" @change="setSaleAmount(scope.row)">
                            <template #append>元/{{scope.row.pricingScheme == 0 ? scope.row.productUnitName : scope.row.conversionRelSecondUnitName}}</template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 金额 -->
                    <el-table-column v-if="form.outboundType == 7" min-width="200" align="right" :label="'*' + $t('quickOutbound.label.amount')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.saleAmount'" :rules="[
                            {
                              required: true,
                              message: t(
                                'quickOutbound.rules.saleAmount'
                              ),
                              trigger: ['blur'],
                            },
                            {
                              pattern:
                                /^(-?0(?:\.\d{1,2})?|-?[1-9]\d{0,8}(?:\.\d{1,2})?)$/,
                              message: t(
                                'quickOutbound.rules.saleAmountFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]">
                          <el-input v-model="scope.row.saleAmount" :disabled="form.outboundType == 7 && true" @change="getTotalAmount"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 剩余可用库存 -->
                    <!--<el-table-column min-width="200" :label="$t('quickOutbound.label.remainingAvailableInventory')">
                      <template #default="scope">
                        <div>{{$t('quickOutbound.label.remainingAvailableQuantity')}}：<span style="color: #90979E ">{{scope.row.availableQty == null ? '-' : scope.row.availableQty}}</span></div>
                        <div>{{$t('quickOutbound.label.remainingConversionQuantity')}}：<span style="color: #90979E ">{{scope.row.availableWeight == null ? '-' : scope.row.availableWeight}}</span></div>
                      </template>
                    </el-table-column>-->
                    <!-- 商品包装 -->
                    <el-table-column min-width="130" :label="$t('quickOutbound.label.goodsPackaging')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.productPackage'">
                          <el-input v-model="scope.row.productPackage" clearable :placeholder="$t('common.placeholder.inputTips')" maxlength="30"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 出库转换量 -->
                    <el-table-column min-width="240" :label="$t('quickOutbound.label.outboundConversionQuantity')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.outboundWeight'"
                                      :rules="[
                            {
                              required: false,
                            },
                            {
                              pattern:
                                /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                              message: t(
                                'quickOutbound.rules.outboundWeightFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]"
                        >
                          <el-input v-model="scope.row.outboundWeight" :disabled="form.outboundType == 7" @change="changeWeight(scope.$index)" clearable :placeholder="$t('common.placeholder.inputTips')"><template #append>{{ scope.row.conversionRelSecondUnitName }}</template></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!--可用库存-->
                    <el-table-column width="200" label="可用库存" prop="available">
                      <template #default="scope">
                        <div style="word-break: break-all">{{scope.row.available}}</div>
                      </template>
                    </el-table-column>
                    <!-- 剩余量 -->
                    <el-table-column min-width="120" :label="$t('quickOutbound.label.residualQuantity')">
                      <template #default="scope">
                        <div>{{scope.row.surplusQty == null ? '-' : scope.row.surplusQty}}</div>
                      </template>
                    </el-table-column>
                    <!-- 备注 -->
                    <el-table-column min-width="200" :label="$t('quickOutbound.label.remark')" prop="remark" show-overflow-tooltip>
                      <template #default="scope">
                        <div>{{ scope.row.remark ? scope.row.remark : '-' }}</div>
                      </template>
                    </el-table-column>
                    <!-- 规格 -->
                    <el-table-column :label="$t('quickOutbound.label.productSpecs')" prop="productSpecs" min-width="100">
                      <template #default="scope">
                        <div>{{ scope.row.productSpecs ? scope.row.productSpecs : '-' }}</div>
                      </template>
                    </el-table-column>
                    <!-- 商品属性 -->
                    <el-table-column :label="$t('quickOutbound.label.commodityProperty')" prop="attributeTypeName" min-width="100">
                      <template #default="scope">
                        <div>{{ scope.row.attributeTypeName ? scope.row.attributeTypeName : '-' }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="140" v-if="form.outboundType != 7">
                      <template #default="scope">
                        <el-button
                          type="success"
                          circle
                          plain
                          size="small"
                          @click="handleAdd(scope.row.productCode,scope.$index)"
                        >
                          {{ $t("quickOutbound.button.add") }}
                        </el-button>
                        <el-button
                          type="danger"
                          circle
                          plain
                          size="small"
                          v-show="!scope.row.isParent"
                          @click="handleDelete(scope.$index)"
                        >
                          {{ $t("quickOutbound.button.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("quickOutbound.button.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            {{ $t("quickOutbound.button.submit") }}
          </el-button>
        </div>
      </div>
    </div>
    <UploadDialog
      v-model:visible="uploadDialog.visible"
      ref="uploadDialogRef"
      @on-submit="onSubmitUpload"
    />
  </div>
</template>

<script setup lang="ts">
import {parseDateTime} from "@/core/utils";

defineOptions({
  name: "ConfirmOutbound",
  inheritAttrs: false,
});
import CommonAPI from "@/modules/wms/api/common";
import OutboundNoticeAPI from "@/modules/wms/api/outboundNotice";
import QuickOutboundApi, {confirmOutboundRequest} from "@/modules/wms/api/quickOutbound"
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import UploadDialog from "./components/uploadDialog.vue";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const type = route.query.type;
const uploadDialog = reactive({
  visible: false,
});
const uploadDialogRef = ref();
const deliveryTypeList = ref([]);
const formRef = ref(ElForm);

const loading = ref(false);
const formLoading = ref(false);
const firstTableShow = ref(true);

// 表单
const form = reactive<confirmOutboundRequest>({
  id: "",
  fileNum:'',
  warehouseOutboundDetailVOList: [],
  totalPlanProductQty:'',//计划量合计
  totalPlanProductWeight:'',//计划转换量合计
  totalOutboundQty:'',//出库量合计
  totalOutboundWeight:'',//出库转换量合计
  totalAmount:'',//金额合计
});
const outWarehouseAreaList = ref([]);
function queryAllHasStockListByReq() {
  if(form.warehouseOutboundDetailVOList && form.warehouseOutboundDetailVOList.length > 0){
    let productQueryDTOList = []
    form.warehouseOutboundDetailVOList.forEach(list=>{
      let obj = {
        productCode:list.productCode,
        isDiscreteUnit:list.isDiscreteUnit
      }
      productQueryDTOList.push(obj)
    })
    let params = {
      productQueryDTOList:productQueryDTOList
    }
    CommonAPI.queryAllHasStockListByReq(params).then((res)=>{
      outWarehouseAreaList.value = res ? res : []
    }).catch(()=>{
      outWarehouseAreaList.value = []
    })
  }
}
const chooseWarehouse = ref('')
function handleFilterChange(val) {
  console.log("val====",val)
  if(val){
    chooseWarehouse.value = val
    form.warehouseOutboundDetailVOList.forEach((list,index)=>{
      let warehouseList = []
      if(list.outWarehouseAreaList && list.outWarehouseAreaList.length > 0){
        list.outWarehouseAreaList.forEach(areaList=>{
          warehouseList.push(areaList.areaCode)
        })
      }
      if(!list.warehouseAreaCode && warehouseList.length > 0 && warehouseList.includes(chooseWarehouse.value)){
        list.warehouseAreaCode = chooseWarehouse.value
        getWarehouseAreaDetail(list.warehouseAreaCode,index)
      }
    })
  }
}
function closeFirstTable() {
  firstTableShow.value = false
}
function openFirstTable() {
  firstTableShow.value = true
}
// 上传
function handleUpload() {
  uploadDialog.visible = true;
  uploadDialogRef.value.setEditType("add");
  if (form.poundAttachmentFiles) {
    uploadDialogRef.value.setFormData(JSON.parse(form.poundAttachmentFiles));
  }
}

//上传提交
function onSubmitUpload(data: any) {
  if (data) {
    form.poundAttachmentFiles = JSON.stringify(data);
    form.fileNum = data.length;
  }
}
function getSummaries(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    /**
     * else if(index === 4){
      sums[index] = form.totalPlanProductQty;
      return;
    }else if(index === 5){
      sums[index] = form.totalPlanProductWeight;
      return;
    }*/
    if (index === 2) {
      sums[index] = '合计:';
      return;
    }else if(form.outboundType != 7 && index === 3){
      sums[index] = form.totalOutboundQty;
      return;
    }else if(form.outboundType != 7 && index === 5){
      sums[index] = form.totalOutboundWeight;
      return;
    }else if(form.outboundType == 7 && index === 3){
      sums[index] = form.totalOutboundQty;
      return;
    }else if(form.outboundType == 7 && index === 7){
      sums[index] = form.totalOutboundWeight;
      return;
    }else if(form.outboundType == 7 && index === 5){
      sums[index] = form.totalAmount;
      return;
    }else{
      sums[index] = ' '
      return
    }
  });
  return sums;
}
//出库量合计
function getTotalOutboundQty(){
  let totalOutboundQty = '0'
  form.warehouseOutboundDetailVOList.forEach(list=>{
    if(list.outboundQty){
      totalOutboundQty = (parseFloat(totalOutboundQty) + parseFloat(list.outboundQty)).toFixed(3)
    }
    form.totalOutboundQty = totalOutboundQty
  })
}
//出库转换量合计
function getTotalOutboundWeight() {
  let totalOutboundWeight = '0'
  form.warehouseOutboundDetailVOList.forEach(list=>{
    if(list.outboundWeight){
      totalOutboundWeight = (parseFloat(totalOutboundWeight) + parseFloat(list.outboundWeight)).toFixed(3)
    }
    form.totalOutboundWeight = totalOutboundWeight
  })
}
//查询当前商品总库存大于0的库区
function getOutWarehouseAreaList(productCode,isDiscreteUnit,index) {
  form.warehouseOutboundDetailVOList[index].areaLoading = true
  let params = {
    productCode:productCode,
    isDiscreteUnit:isDiscreteUnit,
  }
  CommonAPI.queryHasStockListByReq(params).then((res)=>{
    form.warehouseOutboundDetailVOList[index].outWarehouseAreaList = res
    let available = ''
    if(form.warehouseOutboundDetailVOList[index].outWarehouseAreaList && form.warehouseOutboundDetailVOList[index].outWarehouseAreaList.length > 0){
      let totalQty = '0'
      let totalWeight = '0'
      form.warehouseOutboundDetailVOList[index].outWarehouseAreaList.forEach(list=>{
        available = available + (available === '' ? list.areaName + '：' + list.availableStockQty+form.warehouseOutboundDetailVOList[index].productUnitName+'/'+list.availableStockWeight+form.warehouseOutboundDetailVOList[index].conversionRelSecondUnitName : '，' + list.areaName + '：' +list.availableStockQty+form.warehouseOutboundDetailVOList[index].productUnitName+'/'+list.availableStockWeight+form.warehouseOutboundDetailVOList[index].conversionRelSecondUnitName)
        if(list.availableStockQty){
          totalQty = (parseFloat(totalQty)+ parseFloat(list.availableStockQty)).toFixed(3)
        }
        if(list.availableStockWeight){
          totalWeight = (parseFloat(totalWeight)+ parseFloat(list.availableStockWeight)).toFixed(3)
        }
      })
      available = available+'，总库存：' + totalQty+form.warehouseOutboundDetailVOList[index].productUnitName+'/'+totalWeight+form.warehouseOutboundDetailVOList[index].conversionRelSecondUnitName
    }else{
      available = '总库存：无库存'
    }
    form.warehouseOutboundDetailVOList[index].available = available
  }).finally(()=>{
    form.warehouseOutboundDetailVOList[index].areaLoading = false
  })

}
//查询所选库区详情
function getWarehouseAreaDetail(e,index) {
  if(e){
    form.warehouseOutboundDetailVOList[index].outWarehouseAreaList.forEach(list=>{
      if(list.areaCode == e){
        form.warehouseOutboundDetailVOList[index].warehouseAreaName = list.areaName
        form.warehouseOutboundDetailVOList[index].availableStockQty = list.availableStockQty
        form.warehouseOutboundDetailVOList[index].availableStockWeight = list.availableStockWeight
      }
    })
  }else{
    form.warehouseOutboundDetailVOList[index].warehouseAreaName = ''
    form.warehouseOutboundDetailVOList[index].availableStockQty = ''
    form.warehouseOutboundDetailVOList[index].availableStockWeight = ''
  }
  getAvailableQty(index)
  getAvailableWeight(index)
}
//计算剩余可用库存数量
function getAvailableQty(index) {
  if(form.warehouseOutboundDetailVOList[index].availableStockQty && form.warehouseOutboundDetailVOList[index].outboundQty){
    form.warehouseOutboundDetailVOList[index].availableQty = (parseFloat(form.warehouseOutboundDetailVOList[index].availableStockQty) - parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty)).toFixed(3)
  }else if(form.warehouseOutboundDetailVOList[index].availableStockQty && !form.warehouseOutboundDetailVOList[index].outboundQty){
    form.warehouseOutboundDetailVOList[index].availableQty = form.warehouseOutboundDetailVOList[index].availableStockQty
  }
}
//计算剩余可用库存转换量
function getAvailableWeight(index) {
  if(form.warehouseOutboundDetailVOList[index].availableStockWeight && form.warehouseOutboundDetailVOList[index].outboundWeight){
    form.warehouseOutboundDetailVOList[index].availableWeight = (parseFloat(form.warehouseOutboundDetailVOList[index].availableStockWeight) - parseFloat(form.warehouseOutboundDetailVOList[index].outboundWeight)).toFixed(3)
  }else if(form.warehouseOutboundDetailVOList[index].availableStockWeight && !form.warehouseOutboundDetailVOList[index].outboundWeight){
    form.warehouseOutboundDetailVOList[index].availableWeight = form.warehouseOutboundDetailVOList[index].availableStockWeight
  }
}
//一级单位转二级单位
function setOutboundWeight(index) {
  getTotalOutboundQty()
  getAvailableQty(index)
  if(form.warehouseOutboundDetailVOList[index].isDiscreteUnit){
    if(form.warehouseOutboundDetailVOList[index].outboundQty){
      let params = {
        convertUnitTypeEnum:'FIRST_TO_SECOND',
        originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty).toFixed(3),
        productCode:form.warehouseOutboundDetailVOList[index].productCode
      }
      CommonAPI.convertProductUnit(params).then((res)=>{
        form.warehouseOutboundDetailVOList[index].outboundWeight = res.convertedValue
        if(form.outboundType == 7){
          calculateAmount(index)
        }
        clearWarehouseAreaCodeValidate(index)
        getTotalOutboundWeight()
        getAvailableWeight(index)
      })
    }
  }else{
    if(form.warehouseOutboundDetailVOList[index].outboundQty && !form.warehouseOutboundDetailVOList[index].outboundWeight){
      let params = {
        convertUnitTypeEnum:'FIRST_TO_SECOND',
        originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty).toFixed(3),
        productCode:form.warehouseOutboundDetailVOList[index].productCode
      }
      CommonAPI.convertProductUnit(params).then((res)=>{
        form.warehouseOutboundDetailVOList[index].outboundWeight = res.convertedValue
        if(form.outboundType == 7){
          calculateAmount(index)
        }
        clearWarehouseAreaCodeValidate(index)
        getTotalOutboundWeight()
        getAvailableWeight(index)
      })
    }else{
      if(form.outboundType == 7){
        calculateAmount(index)
      }
    }
  }
  if(!form.warehouseOutboundDetailVOList[index].outboundQty){
    clearWarehouseAreaCodeValidate(index)
  }
}
function clearWarehouseAreaCodeValidate(index){
  if((!form.warehouseOutboundDetailVOList[index].outboundQty || form.warehouseOutboundDetailVOList[index].outboundQty == 0) && (!form.warehouseOutboundDetailVOList[index].outboundWeight || form.warehouseOutboundDetailVOList[index].outboundWeight == 0)){
    formRef.value?.clearValidate('warehouseOutboundDetailVOList.'+index+'.warehouseAreaCode')
  }
}
//改变出库转换量联动计算
function changeWeight(index) {
  getTotalOutboundWeight()
  getAvailableWeight(index)
  if(!form.warehouseOutboundDetailVOList[index].isDiscreteUnit && form.warehouseOutboundDetailVOList[index].outboundWeight && !form.warehouseOutboundDetailVOList[index].outboundQty){
    let params = {
      convertUnitTypeEnum:'SECOND_TO_FIRST',
      originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundWeight).toFixed(3),
      productCode:form.warehouseOutboundDetailVOList[index].productCode
    }
    CommonAPI.convertProductUnit(params).then((res)=>{
      form.warehouseOutboundDetailVOList[index].outboundQty = res.convertedValue
      if(form.outboundType == 7){
        calculateAmount(index)
      }
      clearWarehouseAreaCodeValidate(index)
      getTotalOutboundQty()
      getAvailableQty(index)
    })
  }else{
    if(form.outboundType == 7){
      calculateAmount(index)
    }
  }
  if(!form.warehouseOutboundDetailVOList[index].outboundWeight){
    clearWarehouseAreaCodeValidate(index)
  }
}
function handleAdd(productCode,index) {
  for(let i = 0; i < form.warehouseOutboundDetailVOList.length; i++){
    let item = form.warehouseOutboundDetailVOList[i]
    if(item.productCode == productCode && i == index){
      let num = i + 1
      form.warehouseOutboundDetailVOList.splice(num, 0, {
        productCode:item.productCode,
        productName:item.productName,
        productSpecs:item.productSpecs,
        productUnitName:item.productUnitName,
        conversionRelSecondUnitName:item.conversionRelSecondUnitName,
        attributeTypeName:item.attributeTypeName,
        pricingScheme:item.pricingScheme,
        isDiscreteUnit:item.isDiscreteUnit,
        salePrice:item.salePrice,
        surplusQty:item.surplusQty,
        available:item.available,
        remark:item.remark,
        outWarehouseAreaList:item.outWarehouseAreaList,
        isParent:false,
      })
      break
    }
  }
}

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.go(-1);
};
/* 姓名加密 */
function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

// 获取真实联系人
function getRealName() {
  form.nameShow = true;
}
//获取真实供应商联系人
function getSupplierRealName() {
  form.supplierNameShow = true;
}
// 获取真实电话号码
function getRealPhone() {
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.customerMobile = data.mobile;
      form.mobilePhoneShow = false;
    })
    .finally(() => {});
}
//获取真实供应商联系电话
function getSupplierRealPhone(){
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.supplierContactMobile = data.supplierContactMobile;
      form.supplierMobilePhoneShow = false;
    })
    .finally(() => {});
}

function handleDelete(index?: number) {
  form.warehouseOutboundDetailVOList.splice(index, 1);
  getTotalOutboundQty()
  getTotalOutboundWeight()
  if(form.outboundType == 7){
    getTotalAmount()
  }
}

// 提交
function handleSubmit() {
  formRef.value.validate((valid) => {
    if (!valid) return;
    loading.value = true
    let sameArr = []
    form.warehouseOutboundDetailVOList.forEach(item=>{
      let obj = {
        productCode:item.productCode,
        warehouseAreaCode:item.warehouseAreaCode
      }
      sameArr.push(obj)
    })
    const uniqueItems = new Map();
    for(let i = 0;i<sameArr.length;i++){
      const key = JSON.stringify(sameArr[i]); // 将对象转换为字符串
      if (!uniqueItems.has(key)) {
        uniqueItems.set(key, sameArr[i]); // 如果尚未存在，则添加到Map中
      }else {
        loading.value = false
        return  ElMessage.error(t('quickOutbound.message.sameProduct'));
      }
    }
    let arr = form.warehouseOutboundDetailVOList.filter(function (item) {
      return (item.isDiscreteUnit && Number(item.availableQty) < 0) || (!item.isDiscreteUnit && Number(item.availableWeight) < 0)
    })
    if(arr.length > 0){
      loading.value = false
      return  ElMessage.error(t('quickOutbound.message.availableTip'));
    }
    ElMessageBox.confirm(t("quickOutbound.message.submitWarning"), t("common.tipTitle"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }).then(() => {
      let fastOutboundProductInfoDTOList = []
      let arr = form.warehouseOutboundDetailVOList.filter(function (list) {
        return list.isParent
      })
      arr.forEach(item => {
        let fastOutboundProductDetailDTOList = []
        if((item.outboundQty && Number(item.outboundQty) > 0) || (item.outboundWeight && Number(item.outboundWeight) > 0)){
          let obj = {
            actualPickThisTime:item.outboundQty || 0,
            warehouseAreaActualPickWeight:item.outboundWeight || 0,
            productPackage:item.productPackage,
            warehouseAreaCode:item.warehouseAreaCode,
            warehouseAreaName:item.warehouseAreaName,
          }
          if(form.outboundType == 7){
            obj.salePrice = item.salePrice
            obj.saleAmount = item.saleAmount
          }
          fastOutboundProductDetailDTOList.push(obj)
        }
        form.warehouseOutboundDetailVOList.forEach(list=>{
          if(item.productCode == list.productCode && !list.isParent){
            if((list.outboundQty && Number(list.outboundQty) > 0)  || (list.outboundWeight && Number(list.outboundWeight) > 0)){
              let obj1 = {
                actualPickThisTime:list.outboundQty || 0,
                warehouseAreaActualPickWeight:list.outboundWeight || 0,
                productPackage:list.productPackage,
                warehouseAreaCode:list.warehouseAreaCode,
                warehouseAreaName:list.warehouseAreaName,
              }
              if(form.outboundType == 7){
                obj1.salePrice = list.salePrice
                obj1.saleAmount = list.saleAmount
              }
              fastOutboundProductDetailDTOList.push(obj1)
            }
          }
        })
        item.fastOutboundProductDetailDTOList = fastOutboundProductDetailDTOList
      })
      arr.forEach(item=>{
        if(item.fastOutboundProductDetailDTOList && item.fastOutboundProductDetailDTOList.length > 0){
          let obj = {
            outboundProductId:item.id,
            productCode:item.productCode,
            productName:item.productName,
            firstCategoryId:item.firstCategoryId,
            firstCategoryName:item.firstCategoryName,
            productSpecs:item.productSpecs,
            productUnitId:item.productUnitId,
            productUnitName:item.productUnitName,
            conversionRelSecondUnitId:item.conversionRelSecondUnitId,
            conversionRelSecondUnitName:item.conversionRelSecondUnitName,
            secondCategoryId:item.secondCategoryId,
            secondCategoryName:item.secondCategoryName,
            thirdCategoryId:item.thirdCategoryId,
            thirdCategoryName:item.thirdCategoryName,
            totalPlanedPick:item.planProductQty,
            totalPlanedPickWeight:item.planProductWeight,
            pricingScheme:item.pricingScheme,
            isDiscreteUnit:item.isDiscreteUnit,
            fastOutboundProductDetailDTOList:item.fastOutboundProductDetailDTOList
          }
          fastOutboundProductInfoDTOList.push(obj)
        }
      })
      let params = {
        deliveryNoticeId:route.query.id,
        deliveryNoticeCode:form.outboundNoticeCode,
        carNumber:form.carNumber,
        carrier:form.carrier,
        deliveryType:form.actualDeliveryType,
        deliveryName:form.actualDeliveryName,
        planDeliveryTime:new Date(form.plannedDeliveryTime).getTime(),
        outboundTime:new Date(form.outboundTime).getTime(),
        poundCode:form.poundCode,
        remark:form.outboundRemark,
        poundAttachmentFiles:form.poundAttachmentFiles,
        fastOutboundProductInfoDTOList:fastOutboundProductInfoDTOList
      }
      QuickOutboundApi.submitOutbound(params).then((res)=>{
        ElMessage.success(t("quickOutbound.message.operationSuccess"))
        handleCancel()
      }).finally(()=>{
        loading.value = false
      })
    }, () => {
        loading.value = false
        ElMessage.info(t("quickOutbound.message.cancelTip"));
    });
  })
}

function queryDetail() {
  formLoading.value = true;
  let params = {
    id: route.query.id,
  };
  QuickOutboundApi.queryFastDetailEncrypt(params)
    .then((data: any) => {
      Object.assign(form, data);
      if(form.warehouseOutboundDetailVOList.length > 0){
        form.warehouseOutboundDetailVOList.forEach((list,index)=>{
          list.isParent = true
          list.index = index+1
          list.surplusQty = list.alreadyOutboundQty ? (parseFloat(list.planProductQty) - parseFloat(list.alreadyOutboundQty)).toFixed(3) : list.planProductQty
          list.surplusWeight = list.alreadyOutboundWeight ? (parseFloat(list.planProductWeight) - parseFloat(list.alreadyOutboundWeight)).toFixed(3) : list.planProductWeight
          list.outboundQty = list.surplusQty
          list.outboundWeight = list.surplusWeight
          list.saleAmount = null
          list.salePrice = null
          getOutWarehouseAreaList(list.productCode,list.isDiscreteUnit,index)
          if(form.outboundType == 7){
            calculateWeightedAveragePriceAndAmount(index)
          }
        })
        getTotalOutboundQty()
        getTotalOutboundWeight()
      }
      queryAllHasStockListByReq()
      form.mobilePhoneShow = true;
      form.supplierMobilePhoneShow = true;
      form.fullAddress = ` ${form.countryName || ''}${form.provinceName || ''}${form.cityName || ''}${form.districtName || ''}${form.address || ''}`;
      form.supplierFullAddress = `${form.supplierCountryName  || ''}${form.supplierProvinceName  || ''}${form.supplierCityName  || ''}${form.supplierDistrictName  || ''}${form.supplierAddress || ''}`;
      if (form.contactPerson?.length > 1) {
        form.nameShow = false;
      } else {
        form.nameShow = true;
      }
      if(form.supplierContactPerson?.length > 1){
        form.supplierNameShow = false;
      }else {
        form.supplierNameShow = true;
      }

      // 地址包含数字
      if (form.fullAddress && containsNumber(form.fullAddress)) {
        form.addressShow = true;
        form.addressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      } else {
        // 不包含数字
        form.addressShow = false;
      }
      if(form.supplierFullAddress  && containsNumber(form.supplierFullAddress)) {
        form.supplierAddressShow = true;
        form.supplierAddressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      }else {
        form.supplierAddressShow = false;
      }
      if(form.outboundNoticeStatus == 1 && form.plannedDeliveryTime == null){
        form.plannedDeliveryTime = parseDateTime(new Date().getTime(),'dateTime')
      }
      form.outboundTime = parseDateTime(new Date().getTime(),'dateTime')
      form.actualDeliveryType = form.deliveryType
      form.actualDeliveryName = form.deliveryName
    })
    .finally(() => {
      formLoading.value = false;
    });
}
//获取加权平均价及金额接口
function calculateWeightedAveragePriceAndAmount(index) {
  let params = {
    productCode:form.warehouseOutboundDetailVOList[index].productCode,
    pricingScheme:form.warehouseOutboundDetailVOList[index].pricingScheme,
    convertedQty:form.warehouseOutboundDetailVOList[index].outboundWeight,
    qty:form.warehouseOutboundDetailVOList[index].outboundQty,
  }
  CommonAPI.calculateWeightedAveragePriceAndAmount(params).then((res)=>{
    form.warehouseOutboundDetailVOList[index].salePrice = res.unitPrice
    form.warehouseOutboundDetailVOList[index].saleAmount = res.amount
    getTotalAmount()
  }).finally(()=>{

  })
}
//计算合计金额
function getTotalAmount() {
  let totalAmount = '0'
  form.warehouseOutboundDetailVOList.forEach(list=>{
    if(list.saleAmount){
      totalAmount = (parseFloat(totalAmount) + parseFloat(list.saleAmount)).toFixed(2)
    }
  })
  form.totalAmount = totalAmount
}
//计算金额
function calculateAmount(index) {
  if(form.warehouseOutboundDetailVOList[index].outboundQty && form.warehouseOutboundDetailVOList[index].outboundWeight && form.warehouseOutboundDetailVOList[index].salePrice) {
    let params = {
      qty: form.warehouseOutboundDetailVOList[index].outboundQty,
      convertedQty: form.warehouseOutboundDetailVOList[index].outboundWeight,
      productCode: form.warehouseOutboundDetailVOList[index].productCode,
      unitPrice: form.warehouseOutboundDetailVOList[index].salePrice
    }
    CommonAPI.calculateAmount(params).then((res) => {
      form.warehouseOutboundDetailVOList[index].saleAmount = res.amount
      getTotalAmount()
    })
  }
}
function setSaleAmount(row){
  if(row.salePrice){
    form.warehouseOutboundDetailVOList.forEach((item,index) =>{
      if(item.productCode == row.productCode){
        item.salePrice = row.salePrice
        calculateAmount(index)
      }
    })

  }
}
function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}
function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}
function queryDeliveryMethodList(){
  deliveryTypeList.value = []
  let data = {
    page:1,
    limit:100,
    enableStatus:1
  }
  QuickOutboundApi.queryDeliveryMethodList(data).then((res)=>{
    if(res.records && res.records.length > 0){
      res.records.forEach(item=>{
        let obj = {
          code:item.id,
          name:item.deliveryMethodsCategoryVO.deliveryMethodsCategoryName + '/' + item.methodName,
          methodName:item.methodName
        }
        deliveryTypeList.value.push(obj)
      })
    }else {
      deliveryTypeList.value = []
    }
  })
}
function setName(){
  if(form.actualDeliveryType){
    deliveryTypeList.value.forEach(list=>{
      if(list.code == form.actualDeliveryType){
        form.actualDeliveryName = list.name
      }
    })
  }else{
    form.actualDeliveryName = ''
  }
}

onMounted(() => {
  queryDeliveryMethodList()
  queryDetail();
});
</script>
<style scoped lang="scss">
.addPage {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .td-label{
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #90979E;
      line-height: 24px;
      text-align: right;
      font-style: normal;
      background: #F4F6FA;
      width: 110px;
      text-align: right;
    }
    .td-value{
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-align: left;
      word-break: break-word;
    }
    td{
      border: 1px solid #E5E7F3;
      padding: 4px 8px;
    }
    .input-tr{
      td{
        padding: 18px 8px;
      }
    }
    .title {
      padding: 20px 0px 14px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .mt15px {
    margin-bottom: 15px !important;
  }
}
:deep(.el-dropdown){
  line-height: 23px;
  width: 100%;
}
:deep(.el-dropdown.is-disabled){
  color: #52585f;
}

.dropdown-trigger {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
:deep(.el-dropdown-menu__item.is-selected) {
  color: #409EFF;
  background-color: #f5f7fa;
}
</style>
<style lang="scss">
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
}
.addPage {
  .input-tr{
    .el-form-item--default {
      margin-bottom: 0px;
    }
  }
}
</style>
