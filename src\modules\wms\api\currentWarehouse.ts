import request from "@/core/utils/request";

const CURRENTWAREHOUSE_BASE_URL = "/supply-wms/current/warehouse";

class CurrentWarehouseAPI {
    /** 根据用户查询选择仓库列表 */
    static checkedUserById() {
        return request({
            url: `${CURRENTWAREHOUSE_BASE_URL}/listContainsChecked`,
            method: "get",
        });
    }
    /** 勾选仓库 */
    static checkedwarehouse(data: Array<string>) {
        return request({
            url: `${CURRENTWAREHOUSE_BASE_URL}/checked`,
            method: "post",
            data: data,
        });
    }
    // 获取当前租户下仓库列表(可选0:停用 1:正常) /warehouse/allListByTenant
    static allListByTenant(data: any) {
        return request({
            url: `supply-wms/warehouse/allListByTenant`,
            method: "get",
            params: data,
        });
    }
}

/** 仓库列表对象 */
export interface warehouseInfo {
    /** 仓库编码 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 状态 */
    checked: boolean;
}

export default CurrentWarehouseAPI;
