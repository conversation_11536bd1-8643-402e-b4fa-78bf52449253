<template>
  <div class="dashboard-container">
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true">
        <!-- 库区名称 -->
        <el-form-item prop="areaName" :label="$t('storeArea.label.areaName')">
          <el-input
            v-model="searchForm.areaName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            class="!w-[256px]"
          />
        </el-form-item>
        <!-- 状态 -->
        <el-form-item prop="status" :label="$t('warehouse.label.status')">
          <el-select
            v-model="searchForm.status"
            :placeholder="$t('common.placeholder.selectTips')"
            clearable
            class="!w-[256px]"
          >
            <el-option
              v-for="item in statusList"
              :key="item.statusId"
              :label="item.statusName"
              :value="item.statusId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="onSearchHandler"
            v-hasPerm="['wms:areaManager:search']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button
            @click="onResetHandler"
            v-hasPerm="['wms:areaManager:reset']"
          >
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <template #header>
        <!-- 新增 -->
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPerm="['wms:areaManager:add']"
        >
          {{ $t("storeArea.button.addWarehouse") }}
        </el-button>
        <!-- 状态 0:禁用 1:启用 -->
        <el-button
          type="primary"
          plain
          @click="handleStatusChange(1)"
          v-hasPerm="['wms:areaManager:updateStatus']"
        >
          {{ $t("storeArea.button.enable") }}
        </el-button>
        <el-button
          @click="handleStatusChange(0)"
          v-hasPerm="['wms:areaManager:updateStatus']"
        >
          {{ $t("storeArea.button.deactivate") }}
        </el-button>
      </template>
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="tableData"
        highlight-current-row
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          :label="$t('storeArea.label.sort')"
          width="55"
        />
        <el-table-column
          :label="$t('storeArea.label.areaCode')"
          prop="areaCode"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('storeArea.label.areaName')"
          prop="areaName"
          show-overflow-tooltip
          min-width="160"
        />
        <!-- 库区类型 -->
        <el-table-column
          :label="$t('storeArea.label.areaType')"
          prop="areaType"
          show-overflow-tooltip
          min-width="160"
        >
          <template #default="scope">
            <!-- 库区类型：1->常规库；2->残次品库 -->
            <span v-if="scope.row.areaType == 1">
              {{ $t("storeArea.label.conventionalStorageArea") }}
            </span>
            <span v-else-if="scope.row.areaType == 2">
              {{ $t("storeArea.label.defectiveProductStorageArea") }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
          <el-table-column
            :label="$t('storeArea.label.areaNewType')"
            prop="areaTypeName"
            show-overflow-tooltip
            min-width="160"
          />
        <el-table-column
          :label="$t('storeArea.label.warehouseCoding')"
          prop="warehouseCode"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('storeArea.label.warehouseName')"
          prop="warehouseName"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('storeArea.label.contact')"
          prop="contactPerson"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('storeArea.label.contactNumber')"
          prop="mobile"
          show-overflow-tooltip
          min-width="160"
        >
          <template #default="scope">
            <span v-if="scope.row.mobile">{{ scope.row.countryAreaCode }}</span>
            {{ scope.row.mobile ? scope.row.mobile : "-" }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('storeArea.label.remark')"
          prop="notes"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('storeArea.label.status')"
          width="120"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <el-switch
              :active-text="$t('common.activeBtn')"
              :inactive-text="$t('common.deactivateBtn')"
              inline-prompt
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              v-hasPerm="['wms:areaManager:updateStatus']"
              @change="
                handleStatusChange(scope.row.status, [scope.row.id], scope.row)
              "
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('storeArea.label.operate')"
          fixed="right"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              v-hasPerm="['wms:areaManager:update']"
              @click="handleEditHandler(scope.row)"
            >
              {{ $t("common.edit") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="paginationInfo.pageNo"
        v-model:limit="paginationInfo.pageSize"
        @pagination="onPaginationChangeHandler"
      />
    </el-card>
    <el-drawer
      v-model="showDialog"
      :title="diglogTitle"
      :close-on-click-modal="false"
      width="1800px"
      @close="handleCloseDialog"
    >
      <el-form
        :model="contentForm"
        :rules="contentFormRules"
        ref="contentFormRef"
        label-position="top"
      >
        <el-form-item
          :label="$t('storeArea.label.ownWarehouse')"
          prop="warehouseCode"
        >
          <el-select
            class="my-select"
            v-model="contentForm.warehouseCode"
            :placeholder="$t('common.placeholder.selectTips')"
            disabled
          >
            <el-option
              v-for="item in warehouseData"
              :key="item.warehouseCode"
              :label="item.warehouseName"
              :value="item.warehouseCode"
            >
              {{ item.warehouseName }} | {{ item.warehouseCode }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('storeArea.label.areaCode')" prop="areaCode">
          <el-input
            type="text"
            :placeholder="$t('storeArea.message.systemGenerated')"
            v-model="contentForm.areaCode"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item :label="$t('storeArea.label.areaName')" prop="areaName">
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.areaName"
            :maxlength="30"
            clearable
          />
        </el-form-item>
        <!-- 库区属性 -->
        <el-form-item :label="$t('storeArea.label.areaType')" prop="areaType">
          <el-select
            class="my-select"
            v-model="contentForm.areaType"
            :placeholder="$t('common.placeholder.selectTips')"
          >
            <el-option
              v-for="item in areaTypeList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
          <!-- 库区类型 -->
          <el-form-item :label="$t('storeArea.label.areaNewType')" prop="areaNewType">
              <el-select
                class="my-select"
                v-model="contentForm.areaNewType"
                :placeholder="$t('common.placeholder.selectTips')"
                value-key="value"
                @change="handleAreaNewType"
              >
                  <el-option
                    v-for="item in areaNewTypeOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item"
                  />
              </el-select>
          </el-form-item>
        <el-form-item
          :label="$t('storeArea.label.contactPerson')"
          prop="contactPerson"
        >
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.contactPerson"
            :maxlength="30"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('storeArea.label.contactNumber')"
          prop="mobile"
        >
          <el-row style="width: 100%">
            <el-col :span="6">
              <el-select
                v-model="contentForm.countryAreaCode"
                :placeholder="$t('storeArea.label.countryAreaCode')"
                clearable
              >
                <el-option
                  v-for="(item, index) in areaList"
                  :key="index"
                  :label="item.internationalCode"
                  :value="item.internationalCode"
                />
              </el-select>
            </el-col>
            <el-col :span="18">
              <el-input
                oninput="value=value.replace(/^0|[^0-9]/g, '')"
                v-model="contentForm.mobile"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                maxlength="30"
              />
            </el-col>
          </el-row>

          <!-- <el-select
            v-model="contentForm.countryAreaCode"
            :placeholder="$t('warehouseEntryNotice.label.areaCode')"
            clearable
          >
            <el-option
              v-for="(item, index) in areaList"
              :key="index"
              :label="item.internationalCode"
              :value="item.internationalCode"
            />
          </el-select>
          <el-input
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.mobile"
            :maxlength="30"
            clearable
          /> -->
        </el-form-item>
        <el-form-item :label="$t('storeArea.label.status')" prop="status">
          <el-switch
            :active-text="$t('common.activeBtn')"
            :inactive-text="$t('common.inactiveBtn')"
            inline-prompt
            v-model="contentForm.status"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <!-- 固定电话 -->
        <el-form-item
          :label="$t('storeArea.label.contactLandline')"
          prop="contactLandline"
        >
          <el-input
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.contactLandline"
            :maxlength="30"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('warehouse.label.remark')" prop="roleDesc">
          <el-input
            type="textarea"
            show-word-limit
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.notes"
            :maxlength="200"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDialog()">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="onSaveHandler"
          >
            {{ $t("common.confirm") }}
          </el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { emitter } from "@/core/utils/eventBus";
import { useWarehouseStore } from "@/core/store";
const warehouseStore = useWarehouseStore();
defineOptions({
  name: "ReservoirAreaManager",
  inheritAttrs: false,
});
import formMixin from "@/modules/wms/mixins/form";
import tableMixin from "@/modules/wms/mixins/table";
import type { FormRules } from "element-plus";
import StoreAreaAPI, { storeAreaForm } from "@/modules/wms/api/storeArea";
import CurrentWarehouseAPI from "@/modules/wms/api/currentWarehouse";
import CommonAPI from "@/modules/wms/api/common";

const { t } = useI18n();
const warehouseData = ref([]);
const diglogTitle = computed(() =>
  formType.value === "add"
    ? t("storeArea.title.addStoreAreaTitle")
    : t("storeArea.title.editStoreAreaTitle")
);
const searchForm = reactive({
  areaName: "",
  status: "",
  warehouseCode: warehouseStore.getSelectedWarehouseId,
});
const contentForm = reactive<storeAreaForm>({
  id: "",
  warehouseCode: "",
  areaCode: "",
  areaName: "",
  contactPerson: "",
  mobile: "",
  notes: "",
  status: 1,
  contactLandline: "",
  countryAreaCode: "",
  areaType: undefined,
    areaNewType: undefined,
    areaTypeCode: "",
    areaTypeName: "",
});
const contentFormRules = reactive<FormRules>({
  warehouseCode: [
    {
      required: true,
      message: t("storeArea.rules.warehouseTip"),
      trigger: "change",
    },
  ],
  areaName: [
    {
      required: true,
      message: t("storeArea.rules.areaNameTip"),
      trigger: "blur",
    },
  ],
  areaType: [
    {
      required: true,
      message: t("storeArea.rules.areaTypeTip"),
      trigger: "change",
    },
  ],
    areaNewType: [
        {
            required: true,
            message: t("storeArea.rules.areaNewTypeTip"),
            trigger: "change",
        },
    ],

  // contactLandline: [
  //   {
  //     pattern: /^((0\d{2,3})-?(\d{7,8})|((\+|00)[1-9]\d{0,2}-?\d{1,14}))$/,
  //     message: t("storeArea.rules.contactLandline"),
  //     trigger: "blur",
  //   },
  // ],
});
const multipleSelection = ref([]);
const areaList = ref([]);
const areaTypeList = ref([
  {
    key: 1,
    value: t("storeArea.label.conventionalStorageArea"),
  },
  {
    key: 2,
    value: t("storeArea.label.defectiveProductStorageArea"),
  },
]);

const areaNewTypeOption = ref([
    {
        label: t("storeArea.label.conventionalStorageArea"),
        value: 1,
    },
    {
        label: t("storeArea.label.defectiveProductStorageArea"),
        value: 2,
    },
]);



function saveCallbackHandler() {
  onSearchHandler();
}
function editCallbackHandler() {
  onSearchHandler();
}
const {
  showDialog,
  dialogLoading,
  formType,
  contentFormRef,
  onAddHandler,
  onEditHandler,
  onSaveHandler,
  onCloseHandler,
} = formMixin({
  contentForm,
  idName: "id",
  formAddApi: StoreAreaAPI.addStoreArea,
  formEditApi: StoreAreaAPI.editStoreArea,
  saveCallback: saveCallbackHandler,
  editCallback: editCallbackHandler,
    uselessParams: ["areaNewType"],
});
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  router,
  path,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
  onStatusChangeHandler,
} = tableMixin({
  searchForm,
  tableGetApi: StoreAreaAPI.getStoreArea,
  tableDeleteApi: StoreAreaAPI.deleteStoreArea,
  tableEnableApi: StoreAreaAPI.updateStatus,
});

function handleAdd() {
  StoreAreaAPI.queryIsDisable()
    .then((data: any) => {
      if (data == false) {
        onAddHandler();
      } else {
        return ElMessage.error(t("storeArea.message.addTips"));
      }
    })
    .finally(() => {});
}

function handleEditHandler(row: any) {
    let params ={...row}
    params.areaNewType = {  label:row.areaTypeName, value:row.areaTypeCode}
    onEditHandler(params)
}


function handleCloseDialog() {
  contentForm.areaCode = "";
  contentForm.areaType = undefined;
  contentForm.contactLandline = "";
  contentForm.notes = "";
  contentForm.countryAreaCode = "";
    contentForm.areaNewType = "";
    contentForm.areaTypeCode = "";
    contentForm.areaTypeName = "";
  onCloseHandler();
}

function onEnableHandler() {}
function onLinkHandler(id: string) {}
function getUserWareHouse() {
  warehouseData.value = [];
  CurrentWarehouseAPI.checkedUserById()
    .then((data: any) => {
      if (data) {
        data.forEach((item: any) => {
          if (item.checked) {
            warehouseData.value.push(item);
          }
        });
      }
      if (warehouseData.value.length === 0) {
        warehouseData.value = data;
      }
      if (warehouseData.value && warehouseData.value.length === 1) {
        contentForm.warehouseCode = warehouseData.value[0].warehouseCode;
      }
    })
    .finally(() => {});
}
const statusList = ref([
  {
    statusId: 0,
    statusName: t("common.statusEmun.deactivate"),
  },
  {
    statusId: 1,
    statusName: t("common.statusEmun.enable"),
  },
]);

function handleSelectionChange(val: any) {
  multipleSelection.value = val;
}

// 获取区号
function getAreaList() {
  StoreAreaAPI.getAllCountry()
    .then((data: any) => {
      areaList.value = data;
    })
    .finally(() => {});
}

function handleStatusChange(status: any, ids?: any, row?: any) {
  let params = {
    ids: [],
    status: status,
  };
  if (row) {
    row.status = row.status === 0 ? 1 : 0; //保持switch点击前的状态
  }
  if (ids) {
    params.ids = ids;
  } else {
    if (multipleSelection.value.length == 0) {
      return ElMessage.error(t("storeArea.message.setWarehouseTips"));
    }
    params.ids = multipleSelection.value.map((item: any) => item.id);
  }

  let message =
    status == 1
      ? t("storeArea.message.enableSuccessTips")
      : t("storeArea.message.deactivateSuccessTips");
  if (status == 0) {
    ElMessageBox.confirm(
      t("storeArea.message.statusChangeTips"),
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }
    ).then(
      () => {
        statusChangeOpt(params, message);
      },
      () => {
        ElMessage.info(t("storeArea.message.optCancel"));
      }
    );
  } else {
    statusChangeOpt(params, message);
  }
}
function statusChangeOpt(params: any, message: any) {
  StoreAreaAPI.updateStatus(params).then((res) => {
    ElMessage.success(message);
    onSearchHandler();
    // nextTick(() => {
    //   onSearchHandler();
    // });
  });
}

function handleAreaNewType(item:any) {
    contentForm.areaTypeCode = item.value;
    contentForm.areaTypeName = item.label;
}



function getAreaNewTypeList() {
    let params = {
        page: 1,
        limit: 1000,
        enableStatus: 1
    }
    CommonAPI.queryWarehouseAreaStorageTypes(params).then(({ records }:any) => {
        console.log(records)
        areaNewTypeOption.value = records.map((item:any) => {
            return {
                label: item.typeName,
                value: item.typeCode
            }
        })
    })
}


onActivated(() => {
  onSearchHandler();
  getUserWareHouse();
  getAreaList();
    getAreaNewTypeList()
});
emitter.on("reloadListByWarehouseId", (e) => {
  searchForm.warehouseCode = e.warehouseCode;
  nextTick(() => {
    onSearchHandler();
    getUserWareHouse();
  });
});
</script>

<style lang="scss" scoped></style>
