// table.ts  该文件主要封装了 列表请求数据--可抽离的交互逻辑
import type { FormInstance } from "element-plus";
import type {
  MerchantDataListType,
  TResponseData,
} from "@/modules/wms/types/common";
import type { MessageParamsWithType } from "element-plus";
/*
 * @description usePage 接收一个 opts 参数，返回列表所需数据
 * @param {Object} opts.searchForm - 默认查询参数
 * @param {Boolean} opts.isLimit - 是否分页参数
 * @param {Function} opts.tableGetApi  - 获取列表数据的接口
 * @param {Function} opts.tableDeleteApi  - 删除列表数据的接口
 * @param {Function} opts.tableEnableApi  - 启停用列表数据的接口
 * @param {Function} opts.searchCallback  - 查询事件回调函数
 * @param {Function} opts.resetCallback  - 重置事件回调函数
 * @param {Function} opts.removeCallback  - 删除事件回调函数
 * @param {Function} opts.enableCallback  - 启停用事件回调函数
 * @param {Function} opts.tableCallback  - 列表事件回调函数
 * @param {Function} opts.formatParamsCallback  - 列表请求参数处理
 */
export default function (opts?: any) {
  const {
    searchForm = {},
    isLimit = true,
    tableGetApi,
    tableDeleteApi,
    tableEnableApi,
    searchCallback = () => {},
    resetCallback = () => {},
    removeCallback = () => {},
    enableCallback = () => {},
    enableFailCallback = () => {},
    tableCallback = () => {},
    formatParamsCallback = () => {},
  } = opts;
  const { t } = useI18n();

  const paginationInfo = reactive({
    pageNo: 1,
    pageSize: 20,
  });
  const loading = ref(false);
  const total = ref(0);
  const tableData: any = ref([]);
  const headFormRef = ref<FormInstance>();
  const searchMerchantList = ref<MerchantDataListType[]>([]);
  const route = useRoute();
  const path = route.path;
  const router = useRouter();
  // 查询事件
  const onSearchHandler = () => {
    if (searchForm.page) {
      paginationInfo.pageNo = searchForm.page;
    }
    if (searchForm.limit) {
      paginationInfo.pageSize = searchForm.limit;
    }
    searchCallback();
    getTableData();
  };
  // 重置事件
  const onResetHandler = () => {
    if (!headFormRef.value) return;
    headFormRef.value.resetFields();
    resetCallback();
    onSearchHandler();
  };
  // 分页参数改变调取接口
  const onPaginationChangeHandler = (
    type: "pageSize" | "pageNo",
    value: number
  ) => {
    paginationInfo[type] = value;
    if (type === "pageSize") {
      paginationInfo.pageNo = 1;
    }
    getTableData();
  };
  // 列表请求
  const getTableData = () => {
    loading.value = true;
    let params = {};
    if (isLimit) {
      params = {
        ...searchForm,
        page: paginationInfo.pageNo,
        limit: paginationInfo.pageSize,
        pageFlag: true,
      };
    } else {
      params = {
        ...searchForm,
      };
    }
    formatParamsCallback(params);
    tableGetApi(params)
      .then((res: TResponseData) => {
        loading.value = false;
        tableData.value = res.records || [];

        if (isLimit) {
          total.value = parseInt(res.total) || 0;
        }
        tableCallback();
      })
      .catch((err: MessageParamsWithType) => {
        loading.value = false;
        // ElMessage.error(err);
      });
  };
  // 状态切换事件
  const onStatusChangeHandler = (
    status: number,
    paramKey: string,
    value: Array<string>
  ) => {
    const params = {
      status: status ? 1 : 0,
      [paramKey]: value,
    };
    const msg = status ? "已成功停用!" : "已成功启用！";
    tableEnableApi(params)
      .then((res: TResponseData) => {
        getTableData();
        enableCallback();
      })
      .catch((err: MessageParamsWithType) => {
        ElMessage.error(err);
      });
  };
  // 删除事件
  const onDeleteHandler = (
    tipMessage: string,
    paramKey: string,
    value: string
  ) => {
    ElMessageBox.confirm(tipMessage, t("purchasePayable.message.reconciled"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    })
      .then(() => {
        onRemoveHandler({ [paramKey]: value });
      })
      .catch(() => {
        // ElMessage.info(t('common.cancelDelete'));
      });
  };
  // 删除事件处理
  const onRemoveHandler = (params: object) => {
    tableDeleteApi(params)
      .then((res: TResponseData) => {
        ElMessage.success(t("common.successDelete"));
        const totalPage = Math.ceil(
          (total.value - 1) / paginationInfo.pageSize
        ); // 总页数
        paginationInfo.pageNo =
          paginationInfo.pageNo > totalPage ? totalPage : paginationInfo.pageNo;
        getTableData();
        removeCallback();
      })
      .catch((err: MessageParamsWithType) => {
        // ElMessage.error(err);
      });
  };
  return {
    router,
    route,
    path,
    loading,
    total,
    paginationInfo,
    tableData,
    headFormRef,
    searchMerchantList,
    onSearchHandler,
    onResetHandler,
    onDeleteHandler,
    onStatusChangeHandler,
    onPaginationChangeHandler,
  };
}
