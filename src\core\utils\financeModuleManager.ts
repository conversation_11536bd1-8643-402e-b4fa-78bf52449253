/**
 * 财务模块管理工具
 * 用于管理财务模块的账套清空状态和相关逻辑
 */

import UserAPI from "@/core/api/accountManagement";

// SessionStorage 键名
const FINANCE_ACCOUNT_CLEARED_KEY = 'finance_account_cleared';

/**
 * 检查当前会话是否已经执行过财务账套清空
 */
export function hasFinanceAccountCleared(): boolean {
  return sessionStorage.getItem(FINANCE_ACCOUNT_CLEARED_KEY) === 'true';
}

/**
 * 标记当前会话已执行财务账套清空
 */
export function markFinanceAccountCleared(): void {
  sessionStorage.setItem(FINANCE_ACCOUNT_CLEARED_KEY, 'true');
}

/**
 * 清除财务账套清空标记（用于登录时重置状态）
 */
export function clearFinanceAccountClearedFlag(): void {
  sessionStorage.removeItem(FINANCE_ACCOUNT_CLEARED_KEY);
}

/**
 * 检查路由是否属于财务模块
 * @param path 路由路径
 * @param systemId 系统ID
 */
export function isFinanceRoute(path: string, systemId?: string): boolean {
  // 检查路径是否以 /finance 开头
  if (path.startsWith('/finance')) {
    return true;
  }
  
  // 检查系统ID是否为 finance
  if (systemId === 'finance') {
    return true;
  }
  
  // 检查路径是否包含财务相关的系统前缀
  if (path.includes('/finance/')) {
    return true;
  }
  
  return false;
}

/**
 * 执行财务账套清空逻辑
 * 只有在当前会话未执行过的情况下才会执行
 */
export async function executeFinanceAccountClear(): Promise<boolean> {
  // 检查是否已经执行过
  if (hasFinanceAccountCleared()) {
    console.log('[财务模块] 当前会话已执行过账套清空，跳过');
    return false;
  }
  
  try {
    console.log('[财务模块] 开始执行账套清空逻辑');
    await UserAPI.refreshLoginExt({});

    // 标记已执行
    markFinanceAccountCleared();
    console.log('[财务模块] 账套清空执行成功');
    return true;
  } catch (error) {
    console.error('[财务模块] 账套清空执行失败:', error);
    return false;
  }
}

/**
 * 在进入财务模块时调用的主要函数
 * @param path 当前路由路径
 * @param systemId 当前系统ID
 */
export async function handleFinanceModuleEntry(path: string, systemId?: string): Promise<void> {
  // 检查是否为财务模块路由
  if (!isFinanceRoute(path, systemId)) {
    return;
  }
  
  console.log('[财务模块] 检测到进入财务模块:', { path, systemId });
  
  // 执行账套清空逻辑
  await executeFinanceAccountClear();
}
