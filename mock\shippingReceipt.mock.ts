import { defineMockWms } from "./base";

export default defineMockWms([

    // 获取入库通知单下拉数据
    {
        url: "receivingOrders/queryInventoryListByNoticeCode",
        method: ["POST"],
        body: {
            code: 0,
            data: [
                    {
                        receiptNoticeId: '1',
                        receiptNoticeCode: '入库通知单1',
                    },
                    {
                        receiptNoticeId: '2',
                        receiptNoticeCode: '入库通知单2',
                    },
            ],
            msg: "一切ok",
        },
    },

    //根据拆装单号查询拆装商品列表
    {
        url: "receivingOrders/queryInventoryProductListByNoticeCode",
        method: ["POST"],
        body: {
            code: 0,
            data:[
                    {
                        id: '1',
                        productCode: '001',
                        productName: 'productName',
                        productSpecs: '商品规格',
                        productUnitName: '斤',
                        productExpectQty: 5,
                        productInventoryQty: 5,
                    },
            ],
            msg: "一切ok",
        },
    },

    // 获取收运单下拉数据
    {
        url: "receivingOrders/queryInspectionListByOrderCode",
        method: ["POST"],
        body: {
            code: 0,
            data: [
                {
                    receivingOrderCode: '收运单1',
                },
                {
                    receivingOrderCode: '收运单2',
                },
            ],
            msg: "一切ok",
        },
    },

    {
        url: "receivingOrders/queryInspectionProductListByOrderCode",
        method: ["POST"],
        body: {
            code: 0,
            data:[
                {
                    id: '1',
                    productCode: '001',
                    productName: 'productName',
                    productSpecs: '商品规格',
                    productUnitName: '斤',
                    productActualQty: 4,
                },
            ],
            msg: "一切ok",
        },
    },

    {
        url: "dict/findTreeListByDictKey",
        method: ["GET"],
        body: {
            code: 0,
            data:[
                    {
                        "dictName": "外观异常",
                        "dictValue": "1",
                        "subList":[
                            {
                                "dictName": "外观异常",
                                "dictValue": "11",
                            }
                        ]
                    },
                    {
                        "dictName": "数量异常",
                        "dictValue": "2",
                        "subList":[
                            {
                                "id":'21',
                                "dictName": "数量减少",
                                "dictValue": "21",
                            },
                            {
                                "id":'22',
                                "dictName": "数量增加",
                                "dictValue": "22",
                            }
                        ]
                    },
            ],
            msg: "一切ok",
        },
    },

]);
