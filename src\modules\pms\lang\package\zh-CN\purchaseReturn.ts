export default {
  purchaseReturn:{
    label: {
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      to:'~',
      startTime:'开始时间',
      endTime:'截止时间',
      orderType: "采购类型",
      orderCode: "关联采购单号",
      returnCode: '退货单号',
      totalReturnAmount:'退货总金额',
      supplierName: "供应商",
      purchaseUserName: "采购员",
      returnCreateTime:'退货发起时间',
      createTime:'创建时间',
      createUserName: "制单人",
      warehouse:'退货仓库',
      returnAddress:'退货地址',
      returnUserName:'收货人姓名',
      returnUserMobile:'手机号',
      areaCode:'区号',
      receiveAddress:'收货地址',
      remark: "备注",
      basicInformation: "基本信息",
      otherInformation: "其他信息",
      productListInformation: "商品清单",
      productName: "商品名称",
      unitName: "采购单位",
      receiveCount:'收货数量',
      returnCount:'退货数量',
      returnPrice:'单价',
      returnAmount:'退货金额',
      expectedReturnAmount:'预计退货金额',
      total: '合计',
      approveStatus: '审核状态',
      approveTime:"审核时间",
      approveRemark: '审核意见',
      approveUserName: '审核人',
      approveReject: '审核拒绝',
      synStatus:  '同步状态',
      approve: '审核',
      attachmentFiles:'附件',
      returnMethod:'退货方式',
      planDeliveryTime:'计划送达时间',
    },
    placeholder:{
      returnAddress:'请输入详细地址',
      chooseDate:'请选择日期',
      deliveryTime:'送达时间',
    },
    dateTypeList: {
      returnCreateDate: "退货发起日期",
      auditDate: "审核日期",
    },
    orderTypeList: {
      marketDirectSupply: "市场自采",
      suppliersDirectSupply: "供应商直供",
    },
    synStatusOption: {
      success:'成功',
      fail:'失败',
    },
    approveStatusOption: {
      0: "全部",
      1: "待审核",
      2: "已通过",
      3: "已驳回",

    },
    button: {
      manualSync: '手动同步',
      add: '新增',
      addPurchaseReturn:'新增采购退货',
      editPurchaseReturn:'编辑采购退货',
      refused:'驳回',
      agree:'同意',
    },
    message: {
      maxSelect:  "最大勾选条数不能超过20条，请重新选择",
      inconformitySelect:  "您所勾选的{message}不符合同步条件，请重新选择",
      deleteWarning:'删除当前采购退货记录？',
      cancelTip:'已取消操作！',
      syncSuccess:'同步操作成功！',
      deleteSuccess:'删除操作成功！',
      approveRemark:'请输入审核意见',
      approveSuccess:'审核成功！',
      saveSuccess:'提交成功！',
      totalReturnCount:'退货总数量应大于0',
      returnCountTips:'退货数量不能大于收货数量',
      cannotReturn:'当前采购单关联的收运单尚未确认，请确认后再提交退单申请。',
      detailListNull:'商品清单不能为空',
    },
    rules: {
      orderCodeTip:'请输入关联采购单号',
      warehouseCodeTip:'请选择退货仓库',
      returnUserNameTip:'请输入收货人姓名',
      returnUserNameFormat:'收货人姓名支持中文和英文',
      returnUserAreaTip: '请选择区号',
      returnUserMobileTip:'请输入手机号',
      countryId:'请选择国家',
      provinceId:'请选择省',
      cityId:'请选择市',
      districtId:'请选择区县',
      address:'请输入详细地址',
      remarkFormat:'备注支持中文、英文和数字',
      returnAmount:'请输入退货金额',
      returnAmountFormat:'请输入数字，支持小数点前9位后两位',
      returnCount:'请输入退货数量',
      returnCountFormat:'请输入大于等于0的数字，支持小数点前8位后3位',
      returnPrice:'请输入退货单价',
      returnPriceFormat:'请输入数字，支持小数点前7位后4位',
      returnMethod:'请选择退货方式',
      returnDate:'请选择日期',
      returnTime:'请选择送达时间',
    }
  }
}
