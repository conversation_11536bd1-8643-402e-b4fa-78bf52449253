import { defineMockPms } from "./base";

export default defineMockPms([

    // 异常工单分页列表
    {
    url: "workOrder/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
          {
            id: '1',
            orderType: 1,
            workOrderCode: "123456111",
            purchaseCode: "wwww111",
            receiptCode: "wwww111",
            supplierName: "供应商1",
            imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
            productCode: "1000",
            productName: "采购商品1",
            purchaseAmount: "234.98",
            workOrderAmount: "234.00",
            createUserName:'申请人',
            createTime: "2021-03-25 12:39:54",
            generateStatus:1,
            workOrderType: 1,
            workOrderStatus:1,
          },
          {
              id: '2',
              orderType: 1,
              workOrderCode: "123456112",
              purchaseCode: "wwww111",
              receiptCode: "wwww111",
              supplierName: "供应商1",
              imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
              productCode: "1000",
              productName: "采购商品2",
              purchaseAmount: "234.98",
              returnAmount: "234.98",
              workOrderAmount: "234.00",
              createUserName:'申请人',
              createTime: "2021-03-25 12:39:54",
              generateStatus:1,
              workOrderType: 1,
              workOrderStatus:3,
              handleTime: "2021-03-25 12:39:54",
          },
          {
              id: '3',
              orderType: 2,
              workOrderCode: "123456113",
              purchaseCode: "wwww111",
              receiptCode: "wwww111",
              purchaseUser: '采员1',
              imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
              productCode: "1000",
              productName: "采购商品3",
              purchaseAmount: "234.98",
              returnAmount: "234.98",
              workOrderAmount: "234.00",
              createUserName:'申请人',
              createTime: "2021-03-25 12:39:54",
              generateStatus:1,
              workOrderType: 1,
              workOrderStatus:4,
              handleTime: "2021-03-25 12:39:54",
          },
        ],
        total: '4',
      },
      msg: "一切ok",
    },
  },

    //  异常工单详情查询
    {
        url: "workOrder/detail",
        method: ["GET"],
        body: {
            code: 0,
            data:{
                id: '2',
                workOrderStatus: 1,
                workOrderCode: '00001',
                receiptCode: "wwww111",
                supplierName: '00001',
                purchaseUser: '采购员',
                workOrderType: 1,
                purchaseCode: '12334444',
                createUserName:'申请人',
                createUserPhone:'17803978667',
                createTime: "2022-13-13 11:12:12",
                workOrderProductDetailList: [
                    {
                        productImage: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                        attachmentInfo: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                        productCode: "1000",
                        productName: "采购商品1",
                        productUnit: "斤",
                        workOrderQuantity: 100,
                        workOrderAmount: 100.99,
                        workOrderRemark: '备注',
                        handleType: 1,
                    },
                    {
                        productImage: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                        attachmentInfo: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                        productCode: "1000",
                        productName: "采购商品2",
                        productUnit: "斤",
                        workOrderQuantity: 100,
                        workOrderAmount: 100.99,
                        workOrderRemark: '备注',
                        handleType:2,
                    },
                ],
                workOrderAmount: 100.99,
                returnAmount: 100.99,
               handleOpinion:'wwww',
            },
            msg: "一切ok",
        },
    },

    // 异常工单处理
    {
        url: "workOrder/handle",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "处理成功",
        },
    },

]);
