import locals from "@/core/lang/package/zh-CN/index";
import wmsLocals from "@/modules/wms/lang/zh-CN/index";
import pmsLocals from "@/modules/pms/lang/package/zh-CN/index";
import omsLocals from "@/modules/oms/lang/zh-CN/index";
import goodsLocals from "@/modules/goods/lang/package/zh-CN/index";
import finance from "@/modules/finance/lang/package/zh-CN/index";

export default {
  ...locals,
  ...wmsLocals,
  ...pmsLocals,
  ...omsLocals,
  ...goodsLocals,
  ...finance,
  // 路由国际化
  route: {
    dashboard: "首页",
    document: "项目文档",
  },
  common: {
    statusYesOrNo: {
      yes: "是",
      no: "否",
    },
    tips: "提示",
    placeholder: {
      inputTips: "请输入",
      selectTips: "请选择",
    },
    statusYesOrNo: {
      // all: "全部",
      yes: "是",
      no: "否",
    },
    statusEmun: {
      all: "全部",
      enable: "启用",
      disable: "禁用",
      deactivate: "停用",
    },
    add: "添加",
    edit: "编辑",
    delete: "删除",
    cancelDelete: "已取消删除",
    successDelete: "删除成功",
    batchDelete: "批量删除",
    activeBtn: "启用",
    inactiveBtn: "禁用",
    deactivateBtn: "停用",
    cancel: "取消",
    confirm: "确认",
    tipTitle: "提示",
    search: "搜索",
    reset: "重置",
    handle: "操作",
    sort: "序号",
    import: "导入",
    export: "导出",
    detailBtn: "详情",
    pickOrder:'领单',
    cancelOrder:'取消领单',
    print: "打印",
    select: "查看",
    audit: "审核",
    upload: "上传附件",
    uploadFiLe: "上传文件",
    exportSequence: "导出序列",
    tryAgain: "重试",
    downLoad: "下载",
    exportTips:
      "已创建导出任务，导出任务正在进行，等待导出完成后，再进行下载！",
    exportConcel: "已取消导出！",
    reback: "返回",
    warning: "系统提示",
    addSuccess: "新增成功！",
    editSuccess: "编辑成功！",
    deleteSuccess: "删除成功！",
    manualSynchronization: "手动同步",
    syncStatus: "同步状态",
    success: "成功",
    fail: "失败",
    close: "关闭",
    uploadBtn: "上传",
  },
  // 登录页面国际化
  login: {
    username: "用户名",
    mobile: "手机号",
    password: "密码",
    login: "登 录",
    captchaCode: "验证码",
    capsLock: "大写锁定已打开",
    tenantIdLabel: "企业编码",
    forgotPassword: "忘记密码",
    contactAdmin: "请联系管理员处理！",
    message: {
      tenantId: {
        required: "请输入企业编码",
        noChinese: "企业编码不能输入中文",
      },
      username: {
        required: "请输入用户名",
      },
      password: {
        required: "请输入密码",
        min: "密码不能少于6位",
      },
      mobile: {
        required: "请输入手机号",
      },
      captchaCode: {
        required: "请输入验证码",
      },
      agreePrivacyPolicy: "请先同意相关隐私协议",
    },
  },
  // 修改密码弹窗国际化
  updatePassword: {
    label: {
      oldPassword: "原始密码",
      newPassword: "新密码",
      confirmPassword: "确认密码",
    },
    title: {
      updatePasswordTitle: "修改密码",
    },
    message: {
      passwordNoSameTips: "确认密码与新密码不一致！",
      updateSucess: "修改成功",
    },
    rules: {
      oldPassword: "请输入原始密码",
      newPassword: "请输入新密码",
      confirmPassword: "请输入确认密码",
      passwordFomart: "密码需包含数字英文和特殊字符(!{'@'}$%^&_=.)",
    },
  },
  // 导航栏国际化
  navbar: {
    dashboard: "首页",
    logout: "退出登录",
    updatePassword: "修改密码",
    document: "项目文档",
    gitee: "项目地址",
    personal: "个人资料",
  },
  sizeSelect: {
    tooltip: "布局大小",
    default: "默认",
    large: "大型",
    small: "小型",
    message: {
      success: "切换布局大小成功！",
    },
  },
  langSelect: {
    message: {
      success: "切换语言成功！",
    },
  },
  settings: {
    project: "项目配置",
    theme: "主题设置",
    interface: "界面设置",
    navigation: "导航设置",
    themeColor: "主题颜色",
    tagsView: "开启 Tags-View",
    fixedHeader: "固定 Header",
    sidebarLogo: "侧边栏 Logo",
    watermark: "开启水印",
  },
  systemSettings: {
    title: "设置",
  },
  applicationCenter: {
    title: "应用中心",
    button: {
      learnMore: "了解更多",
      contactUs: "立即联系",
    },
    cardTitle: {
      customerStewardTitle: "客户管家ERP",
      WMSTitle: "伽蓝WMS",
      JYTitle: "圆通集运平台",
      contactUsTitle: "联系我们"
    },
    message: {
      customerStewardMsg:
        "面向电商企业的专业ERP系统，为中小商家、仓发商家、行业定制商家等提供一站式电商全渠道经营管理解决方案。",
      WMSMsg:
        "是圆通速递旗下的仓储管理软件，为品牌商家、新锐国货、社交电商，直播品牌企业全球专业的仓配供应链一站式合作伙伴。",
      JYMsg: "圆通集运平台是面向货主、承运商、供应链企业的物流平台。我们为用户提供智能解决方案及运力支持。",
      contactUsMsg: "有任何问题或建议，欢迎联系我们的客服团队"
    },
  },
  contactForm: {
    title: "联系我们",
    subtitle: "客户管家ERP",
    fields: {
      enterpriseName: "企业名称",
      contactName: "联系人姓名",
      phoneNumber: "手机号",
      email: "邮箱",
      message: "意向描述"
    },
    placeholders: {
      enterpriseName: "请输入企业名称",
      contactName: "请输入",
      phoneNumber: "请输入",
      email: "请输入",
      message: "请输入"
    },
    buttons: {
      submit: "提交",
      cancel: "取消"
    },
    messages: {
      submitSuccess: "提交成功，我们会尽快联系您",
      submitError: "提交失败，请稍后重试"
    },
    validation: {
      contactNameRequired: "请输入联系人姓名",
      contactNameLength: "联系人姓名最多输入20位",
      phoneNumberRequired: "请输入手机号码",
      phoneNumberInvalid: "手机号只能输入数字",
      emailInvalid: "请输入有效的邮箱地址",
      emailLength: "邮箱长度限制50位",
      messageLength: "意向描述最多200字"
    }
  },
  components: {
      parameterError: "参数错误",
  },
};
