<template>
    <div class="print-nb">
        <div class="header-title">{{ title }}</div>
        <el-form class="print_form" inline label-position="right">
            <el-row>
                <el-col>
                    <el-form-item label="姓名" class="item—form">
                        <span class="detail" style="width: auto"> 张三 </span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col>
                    <el-form-item label="本地图片:" class="item—form">
                        <img :src='imageIcon' class="image" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="10">
                    <el-form-item label="地址:" class="item—form">
                        <span class="detail" style="width: auto">
                            陕西省西安市雁塔区天谷八路1号 </span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="描述:" class="item—form">
                        <span class="detail" style="width: auto"> 描述 </span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-descriptions class="ui-descriptions" :column="2" border>
            <el-descriptions-item label="供应商">
                供应商
            </el-descriptions-item>
            <el-descriptions-item label="采购方">
                上海圆越进出口贸易有限公司
            </el-descriptions-item>
            <el-descriptions-item label="采购数量">
                12
            </el-descriptions-item>
            <el-descriptions-item label="预计交货时间">
                2020-12-12
            </el-descriptions-item>
            <el-descriptions-item label="发货地">
                中国
            </el-descriptions-item>
            <el-descriptions-item label="办理人">
                张三
            </el-descriptions-item>
        </el-descriptions>

        <table border="1" class="custom-table" cellpadding="5" cellspacing="0">
            <thead>
                <tr>
                    <td>序号</td>
                    <td>姓名</td>
                    <td>图片</td>
                    <td>日工工资</td>
                    <td>网络图片</td>
                </tr>
            </thead>
            <tbody class="paging">
                <tr v-for="(item, index) in tableData" :key="`akl-${index}`">
                    <td>{{ index + 1 }}</td>
                    <td>{{ item['skuCode'] }}</td>
                    <td><img src="https://yt-canteen-def.oss-cn-shanghai.aliyuncs.com/yt-canteen-def/image/06899fe9-bab1-428c-ac6b-174cfb49e4b6.jpg?Expires=1736494014&OSSAccessKeyId=LTAI5tHQk5YqkPcv3NhBhARZ&Signature=%2BQ06QGUKt5yS0dcH%2FXJ4VD3SpGg%3D"
                            class="image" /></td>
                    <td>{{ item['skuCode'] }}</td>
                    <td>{{ item['skuCode'] }}</td>
                </tr>
            </tbody>
        </table>

    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import imageIcon from '@/core/assets/images/Logo.png'
const loading = ref(false);
const title = ref('详细信息');

const tableData = ref([{ "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }, { "id": "1877587780347785217", "purchaseId": "1877587780238733313", "skuId": "1877539734139162626", "detailRemark": null, "skuName": "越南榴莲金枕统规", "skuSpec": "统规", "totalWeights": 0, "quantity": 2000, "skuPrice": 4, "skuAmount": 8000, "skuUnit": "公斤", "skuCode": "B00000053" }])

</script>

<style lang="scss">
.print-nb {
    margin: 0;
    background-color: rgb(255, 255, 255);
    padding: 16px;
    box-sizing: border-box;

    .header-title {
        font-size: 32px;
        font-weight: 500;
        color: #000000;
        line-height: 20px;
        text-align: center;
    }

    .image {
        width: 87px;
        height: 87px;
        border: 1px solid #dcdfe6;
        margin-top: 10px;
    }

    /* 设置表格的线条颜色 */
    .custom-table {
        border-collapse: collapse;
        /* 使表格的边框线看起来是单一的线条 */
        border: 1px solid #ff0000;
        width: 100%;
        margin-top: 20px;

        /* 表头颜色 */
        thead {
            background: rgba(240, 241, 245, .85)
        }

        th {
            border: 1px solid #DCDCDC;
        }

        tr {
            border: 1px solid #DCDCDC;
        }

        td {
            border: 1px solid #DCDCDC;
        }
    }



    .ui-descriptions {
        --el-border-color-lighter: #DCDCDC;

        .el-descriptions__table {
            table-layout: fixed;
            --el-descriptions-item-bordered-label-background: rgba(240, 241, 245, .85);

            .el-descriptions__label {
                background: rgba(240, 241, 245, .85);
                width: 135px;
                font-weight: 400;
                color: #909090;
                line-height: 24px;
                font-size: 18px;
                padding: 2px;
            }

            .el-descriptions__content {
                font-weight: 400;
                color: #252829;
                word-wrap: break-word;
                word-break: break-all;
                line-height: 24px;
                padding: 2px;
                font-size: 18px;
            }
        }
    }
}
</style>