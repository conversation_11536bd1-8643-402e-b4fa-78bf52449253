<!-- 复制组件 -->
<template>
  <el-button link @click="handleClipboard" :style="style">
    <slot>
      <span v-if="showCopyText" style="color:var(--el-color-primary);font-size: 18px"> {{t('omsOrder.button.copy')}}</span>
      <el-icon v-else><DocumentCopy color="var(--el-color-primary)" /></el-icon>
    </slot>
  </el-button>
</template>

<script setup lang="ts">
    const { t } = useI18n();
defineOptions({
  name: "CopyButton",
  inheritAttrs: false,
});

const props = defineProps({
  showCopyText: {
      type: Boolean,
      default: false,
  },
  text: {
    type: String,
    default: "",
  },
  style: {
    type: Object,
    default: () => ({}),
  },
});

function handleClipboard() {
  if (navigator.clipboard && navigator.clipboard.writeText) {
    // 使用 Clipboard API
    navigator.clipboard
      .writeText(props.text)
      .then(() => {
        ElMessage.success(t('omsOrder.message.copySuccess'));
      })
      .catch((error) => {
        ElMessage.error(t('omsOrder.message.copyFail'));
        console.log("[CopyButton] Copy failed", error);
      });
  } else {
    // 兼容性处理（useClipboard 有兼容性问题）
    const input = document.createElement("input");
    input.style.position = "absolute";
    input.style.left = "-9999px";
    input.setAttribute("value", props.text);
    document.body.appendChild(input);
    input.select();
    try {
      const successful = document.execCommand("copy");
      if (successful) {
        ElMessage.success(t('omsOrder.message.copySuccess'));
      } else {
        ElMessage.error(t('omsOrder.message.copyFail'));
      }
    } catch (err) {
      ElMessage.error(t('omsOrder.message.copyFail'));
      console.log("[CopyButton] Copy failed.", err);
    } finally {
      document.body.removeChild(input);
    }
  }
}
</script>
