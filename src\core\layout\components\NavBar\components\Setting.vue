<script setup lang="ts">
import {
  useUserStore,
  useTagsViewStore,
  usePermissionStore,
} from "@/core/store";
const router = useRouter();
const userStore = useUserStore();
const permissionStore = usePermissionStore();
const tagsViewStore = useTagsViewStore();
const handleSysSettingsMenu = (id: String) => {
  permissionStore.setSystemId(id);
  userStore.setSystemId(id);
  // 恢复指定系统的tagsview状态
  tagsViewStore.restoreSystemState(id);
  router.push(`/supply/system/role`);
};
</script>

<template>
  <div class="system-setting" @click="handleSysSettingsMenu('supply')">
    {{ $t("systemSettings.title") }}
  </div>
</template>

<style scoped lang="scss">
.system-setting {
  display: flex;
  align-items: center;
  // padding: 0 10px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
  word-break: keep-all;
}
</style>
