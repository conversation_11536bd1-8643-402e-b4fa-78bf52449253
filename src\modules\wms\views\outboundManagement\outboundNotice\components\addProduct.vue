<template>
  <div class="add-product">
    <el-drawer
      :model-value="visible"
      :title="$t('outboundNotice.title.addProduct')"
      :close-on-click-modal="false"
      :size="850"
      @close="close"
    >
      <el-form :model="goodsQueryParams" ref="goodsQueryFormRef">
        <el-row :gutter="20">
          <el-col :span="9">
            <!-- 商品名称 -->
            <el-form-item
              :label="$t('outboundNotice.label.productNameCopy')"
              prop="productName"
            >
              <el-input
                v-model="goodsQueryParams.productName"
                :placeholder="$t('outboundNotice.placeholder.productInputTips')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <!-- 商品分类 -->
            <el-form-item
              :label="$t('outboundNotice.label.goodsCategory')"
              prop="category"
            >
              <el-cascader
                ref="cascaderRef"
                :props="propsCategory"
                :options="categoryList"
                :placeholder="$t('common.placeholder.selectTips')"
                v-model="goodsQueryParams.category"
                @change="handleChange"
                clearable
                filterable
                collapse-tags
                collapse-tags-tooltip
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <el-button type="primary" @click="queryGoodList">
              {{ $t("common.search") }}
            </el-button>
            <el-button @click="handleResetQuery">
              {{ $t("common.reset") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <div class="selected-info">
        已选
        <span class="selected-count">{{ multipleSelection.length }}</span>
        个商品
      </div>

      <el-table
        ref="dataTableRef"
        v-loading="productLoading"
        :data="productList"
        highlight-current-row
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          :label="$t('outboundNotice.label.goodsInfor')"
          min-width="200"
        >
          <template #default="scope">
            <div class="product-code">
              {{ $t("outboundNotice.label.productCode") }}：
              {{ scope.row.productCode }}
            </div>
            <div class="product-name">
              {{ scope.row.productName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('outboundNotice.label.goodsCategory')"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.firstCategoryName }}/ {{ scope.row.secondCategoryName }}/
            {{ scope.row.thirdCategoryName }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('outboundNotice.label.productSpecs')"
          prop="productSpecs"
          min-width="100"
        />
      </el-table>
      <pagination
        v-if="productListTotal > 0"
        v-model:total="productListTotal"
        v-model:page="goodsQueryParams.page"
        v-model:limit="goodsQueryParams.limit"
        @pagination="queryGoodList"
      />

      <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">
          {{ $t("outboundNotice.button.cancel") }}
        </el-button>
        <el-button
          type="primary"
          :disabled="!multipleSelection.length"
          @click="submitForm"
        >
          {{ $t("outboundNotice.button.comfirm") }}
        </el-button>
      </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import OutboundNoticeAPI from "@/modules/wms/api/outboundNotice";
import type { CascaderProps } from "element-plus";
import CommonAPI from "@/modules/wms/api/common";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();

const cascaderRef = ref();
const goodsQueryFormRef = ref(ElForm);
const productListTotal = ref(0);
const goodsQueryParams = reactive({
  page: 1,
  limit: 20,
  status:1,
  productName: "",
  category: [], // 商品分类
  firstCategoryIds: [],
  secondCategoryIds: [],
  thirdCategoryIds: [],
});

const categoryList = ref([]);

const productLoading = ref(false);
const productList = ref([]);
const multipleSelection = ref([]);
const propsCategory: CascaderProps = {
  multiple: true,
  checkStrictly: false,
  value: "id",
  label: "categoryName",
  children: "children",
};

// const propsCategory: CascaderProps = {
//   lazy: true,
//   checkStrictly: false,
//   multiple: true,
//   async lazyLoad(node, resolve) {
//     const { level, data } = node;
//     let arr: any = [];
//     if (level == 0) {
//       arr = await getCategoryList(null);
//     } else {
//       arr = await getCategoryList(data.value);
//     }
//     const nodes = arr.map((item: any) => ({
//       value: item.id,
//       label: item.categoryName,
//       parentId: item.parentId,
//       leaf: level >= 2,
//     }));
//     resolve(nodes);
//   },
// };
function handleChange() {
  let valueArr = goodsQueryParams.category;
  let firstCategoryIds: any = [];
  let secondCategoryIds: any = [];
  let thirdCategoryIds: any = [];

  if (valueArr && valueArr.length > 0) {
    valueArr.forEach((item: any) => {
      if (item[0]) {
        firstCategoryIds.push(item[0]);
      }
      if (item[1]) {
        secondCategoryIds.push(item[1]);
      }
      if (item[2]) {
        thirdCategoryIds.push(item[2]);
      }
    });
  }

  goodsQueryParams.firstCategoryIds = Array.from(new Set(firstCategoryIds));
  goodsQueryParams.secondCategoryIds = Array.from(new Set(secondCategoryIds));
  goodsQueryParams.thirdCategoryIds = Array.from(new Set(thirdCategoryIds));
  // goodsQueryParams.thirdCategoryIds = [];
  // if (cascaderRef.value.getCheckedNodes()) {
  //   const checkedNodes = cascaderRef.value.getCheckedNodes();
  //   checkedNodes.forEach((node: any) => {
  //     if (node.level == 3) {
  //       goodsQueryParams.thirdCategoryIds.push(node.value);
  //     }
  //   });
  // }
}

function queryManagerCategoryList(id?: any) {
  CommonAPI.queryCategoryTreeList({}).then((data: any) => {
    categoryList.value = data;
  });
  // return new Promise((resolve, reject) => {
  //   let params: any = {};
  //   if (id) {
  //     params.id = id;
  //   }
  //   OutboundNoticeAPI.queryCategoryTreeList(params)
  //     .then((data: any) => {
  //       resolve(data);
  //     })
  //     .catch((error) => {
  //       reject(error);
  //     });
  // });
}

function close() {
  emit("update:visible", false);
  reset();
}

function reset() {
  goodsQueryFormRef.value.clearValidate();
  goodsQueryFormRef.value.resetFields();
  goodsQueryParams.page = 1;
  goodsQueryParams.limit = 20;
  goodsQueryParams.status = 1;
  goodsQueryParams.productName = "";
  goodsQueryParams.category = [];
  goodsQueryParams.thirdCategoryIds = [];
  goodsQueryParams.firstCategoryIds = [];
  goodsQueryParams.secondCategoryIds = [];
}

function handleSelectionChange(val: any) {
  multipleSelection.value = val;
}

function queryGoodList() {
  productLoading.value = true;
  let params: any = {
    ...goodsQueryParams,
  };
  delete params.category;
  OutboundNoticeAPI.queryProductPageList(params)
    .then((res: any) => {
      productList.value = res.records.map((item: any) => {
        item.productSpecs = item.productSpec;
        return { ...item };
      });
      productListTotal.value = parseInt(res.total) || 0;
    })
    .finally(() => {
      productLoading.value = false;
    });
}

function handleResetQuery() {
  goodsQueryParams.page = 1;
  goodsQueryParams.limit = 20;
  goodsQueryParams.status = 1;
  goodsQueryParams.productName = "";
  goodsQueryParams.category = [];
  goodsQueryParams.thirdCategoryIds = [];
  goodsQueryParams.firstCategoryIds = [];
  goodsQueryParams.secondCategoryIds = [];
  queryGoodList();
}

function submitForm() {
  emit("onSubmit", multipleSelection.value);
  close();
}

// 初始化
onMounted(() => {
  // getCategoryList();
});

defineExpose({
  queryManagerCategoryList,
  queryGoodList,
});
</script>

<style scoped lang="scss">
.input_style {
  width: calc(100% - 180px);
}
.product-code {
  color: #90979e;
}
.product-name {
  color: #151719;
}
.selected-info {
  margin: 16px 0;
  color: #606266;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #52585f;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  margin-bottom: 10px;

  .selected-count {
    color: #762adb;
  }
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.product-info {
  display: flex;
  align-items: center;

  img {
    width: 64px;
    height: 64px;
    border-radius: 4px;
  }

  .product-info-content {
    margin-left: 10px;

    .product-info-content-item {
      &.code {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #90979e;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      &.name {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #151719;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>
<style lang="scss">
  .add-product{
    .el-drawer__body{
      overflow: hidden;
      .el-table{
        height: calc(100% - 130px);
        overflow: auto;
      }
    }
  }
</style>
