<template>
    <div class="app-container">
        <div class="purchaseOrderAudit">
          <el-card class="mb-12px search-card">
            <div class="search-form">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
                    <el-form-item prop="dateRange">
                        <el-select
                                v-model="queryParams.dateType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[180px] ml5"
                        >
                            <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                        <el-date-picker
                                :editable="false"
                                class="!w-[370px]"
                                v-model="queryParams.dateRange"
                                type="daterange"
                                range-separator="~"
                                start-placeholder="开始时间"
                                end-placeholder="截止时间"
                                value-format="YYYY-MM-DD"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                        <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(1)">{{$t('purchaseOrder.label.today')}}</span>
                        <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(2)">{{$t('purchaseOrder.label.yesterday')}}</span>
                        <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(3)">{{$t('purchaseOrder.label.weekday')}}</span>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.orderType')" prop="orderType">
                        <el-select
                                v-model="queryParams.orderType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in orderTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="orderCode" :label="$t('purchaseOrder.label.orderCode')">
                        <el-input
                                v-model="queryParams.orderCode"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="purchaseTheme" :label="$t('purchaseOrder.label.purchaseTheme')">
                        <el-input
                          v-model="queryParams.purchaseTheme"
                          :placeholder="$t('common.placeholder.inputTips')"
                          clearable
                          class="!w-[256px]"
                        />
                    </el-form-item>
<!--                    <el-form-item :label="$t('purchaseOrder.label.orderSource')" prop="orderSource">-->
<!--                        <el-select-->
<!--                                v-model="queryParams.orderSource"-->
<!--                                :placeholder="$t('common.placeholder.selectTips')"-->
<!--                                clearable-->
<!--                                class="!w-[256px]"-->
<!--                        >-->
<!--                            <el-option v-for="item in orderSourceList" :key="item.key" :label="item.value" :value="item.key"></el-option>-->
<!--                        </el-select>-->
<!--                    </el-form-item>-->
                    <el-form-item :label="$t('purchaseOrder.label.supplierName')" prop="supplierId">
                        <el-select
                                v-model="queryParams.supplierId"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                filterable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.orderPurchaseStatus')" prop="orderPurchaseStatus">
                        <el-select
                                v-model="queryParams.orderPurchaseStatus"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in orderPurchaseStatusList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.purchaseUserName')" prop="purchaseUserId">
                        <el-select
                                v-model="queryParams.purchaseUserId"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                filterable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in purchasePersonnelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('purchaseOrder.label.synStatus')" prop="approveSynStatus">
                        <el-select
                          v-model="queryParams.approveSynStatus"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                          filterable
                          class="!w-[256px]"
                        >
                            <el-option v-for="(item,index) in synStatusOption" :key="index" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
		                <el-button v-hasPerm="['pms:purchase:purchaseOrderAudit:sync']" type="primary" plain @click="handleSync"  :disabled="multipleSelection.length===0">
                            {{$t('purchaseOrder.button.manualSync')}}
                        </el-button>
                        <el-button v-hasPerm="['pms:purchase:purchaseOrderAudit:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button v-hasPerm="['pms:purchase:purchaseOrderAudit:reset']"  @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
          </el-card>

            <el-card class="content-card">
              <div class="panelContent">
                <div class="panelItem" @click="changePanel(item)" :class="{ active: item.active }" v-for="(item, index) in approveStatusOption" :key="index">
                  {{ item.label }}
                </div>

              </div>

                <el-table
                        v-loading="loading"
                        :data="purchaseOrderList"
                        highlight-current-row
                        stripe
                        @selection-change="handleSelectionChange"
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column type="selection" width="60" align="center" />
                    <el-table-column :label="$t('purchaseOrder.label.purchaseTheme')" prop="purchaseTheme"  width="150">
                      <template #default="scope">
                        <div style="word-break: break-all;">{{scope.row.purchaseTheme ? scope.row.purchaseTheme : '-'}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.orderCode')" prop="orderCode" width="180"/>
                    <el-table-column :label="$t('purchaseOrder.label.orderType')" prop="orderType" width="100" show-overflow-tooltip>
                        <template #default="scope">
                            <span v-if="scope.row.orderType==1">{{t('purchaseOrder.orderTypeList.suppliersDirectSupply')}}</span>
                            <span v-if="scope.row.orderType==2">{{t('purchaseOrder.orderTypeList.marketDirectSupply')}}</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column :label="$t('purchaseOrder.label.orderSource')" prop="orderSource" show-overflow-tooltip>-->
<!--                        <template #default="scope">-->
<!--                            <span v-if="scope.row.orderSource==1">{{t('purchaseOrder.orderSourceList.manuallyAddPurchaseOrder')}}</span>-->
<!--                            <span v-if="scope.row.orderSource==2">{{t('purchaseOrder.orderSourceList.purchaseTask')}}</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column :label="$t('purchaseOrder.label.purchaseUserName')" prop="purchaseUserName" show-overflow-tooltip/>
                    <el-table-column :label="$t('purchaseOrder.label.supplierName')" prop="supplierName" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.productNumber')" prop="totalPurchaseCount" show-overflow-tooltip align="right"></el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.planDeliveryDate')" prop="planDeliveryDate" width="130">
                        <template #default="scope">
                            <span>{{ scope.row.planDeliveryDate ? parseDateTime(scope.row.planDeliveryDate, "date") : '-' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.createUserName')" prop="createUserName" show-overflow-tooltip></el-table-column>
<!--                    <el-table-column :label="$t('purchaseOrder.label.remark')" prop="remark" show-overflow-tooltip></el-table-column>-->
                  <el-table-column label="创建时间" prop="createTime" width="180">
                    <template #default="scope">
                      <span>{{ scope.row.createTime ? parseDateTime(scope.row.createTime, "dateTime") : '-' }}</span>
                    </template>
                  </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.approveStatus')" min-width="100"  show-overflow-tooltip>
                        <template #default="scope">
                            <div class="purchase">
                                <div class="purchase-status purchase-status-color1" v-if="scope.row.approveStatus==1">{{t('purchaseOrder.approveStatusOption[1]')}}</div>
                                <div class="purchase-status purchase-status-color3" v-if="scope.row.approveStatus==2">{{t('purchaseOrder.approveStatusOption[2]')}}</div>
                                <div class="purchase-status purchase-status-color5" v-if="scope.row.approveStatus==3">{{t('purchaseOrder.approveStatusOption[3]')}}</div>
                            </div>
                            <el-tooltip
                              v-if="scope.row.approveStatus==3"
                              class="box-item"
                              effect="dark"
                              :content="scope.row.approveRemark"
                              placement="right"
                            >
                                <div class="close-reason cursor-pointer">{{$t('purchaseOrder.label.rejectReason')}}：{{scope.row.approveRemark}}</div>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseOrder.label.approveTime')" prop="approveTime" width="180">
                        <template #default="scope">
                            <span>{{ scope.row.approveTime ? parseDateTime(scope.row.approveTime, "dateTime") : '-' }}</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column :label="$t('purchaseOrder.label.orderPurchaseStatus')" min-width="100"  show-overflow-tooltip>-->
<!--                        <template #default="scope">-->
<!--                                <div class="purchase">-->
<!--                                    <div class="purchase-status purchase-status-color1" v-if="scope.row.orderPurchaseStatus==1">{{t('purchaseOrder.orderPurchaseStatusList.wattingPurchase')}}</div>-->
<!--                                    <div class="purchase-status purchase-status-color2" v-if="scope.row.orderPurchaseStatus==2">{{t('purchaseOrder.orderPurchaseStatusList.partialPurchase')}}</div>-->
<!--                                    <div class="purchase-status purchase-status-color3" v-if="scope.row.orderPurchaseStatus==3">{{t('purchaseOrder.orderPurchaseStatusList.allPurchase')}}</div>-->
<!--                                    <div class="purchase-status purchase-status-color0" v-if="scope.row.orderPurchaseStatus==0">{{t('purchaseOrder.orderPurchaseStatusList.closePurchase')}}</div>-->
<!--                                    <div class="purchase-status purchase-status-color4" v-if="scope.row.orderPurchaseStatus==4">{{t('purchaseOrder.orderPurchaseStatusList.waitReceive')}}</div>-->
<!--                                </div>-->
<!--                            <el-tooltip-->
<!--                                    v-if="scope.row.orderPurchaseStatus===0 && scope.row.shutdownReason"-->
<!--                                    class="box-item"-->
<!--                                    effect="dark"-->
<!--                                    :content="scope.row.shutdownReason"-->
<!--                                    placement="right"-->
<!--                            >-->
<!--                                <div class="close-reason cursor-pointer">{{$t('purchaseOrder.label.closeReason')}}：{{scope.row.shutdownReason}}</div>-->
<!--                            </el-tooltip>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
<!--                    <el-table-column :label="$t('purchaseOrder.label.sendStatus')" show-overflow-tooltip>-->
<!--                        <template #default="scope">-->
<!--                                <div v-if="scope.row.sendStatus==1" class="circle-div">-->
<!--                                    <div class="circle circle-color1"></div>-->
<!--                                    <div>{{t('purchaseOrder.sendStatusList.send')}}</div>-->
<!--                                </div>-->
<!--                                <div v-if="scope.row.sendStatus==0" class="circle-div">-->
<!--                                    <div class="circle circle-color0"></div>-->
<!--                                    <div>{{t('purchaseOrder.sendStatusList.noSend')}}</div>-->
<!--                                </div>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column :label="$t('purchaseOrder.label.synStatus')" min-width="100"  show-overflow-tooltip>
                        <template #default="scope">
                            <div class="purchase">
                                <div class="purchase-status purchase-status-color3" v-if="scope.row.approveSynStatus==1">{{t('purchaseOrder.synStatusOption.success')}}</div>
                                <div class="purchase-status purchase-status-color5" v-if="scope.row.approveSynStatus==2">{{t('purchaseOrder.synStatusOption.fail')}}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="120">
                        <template #default="scope">

                            <!--  审核  -->
                            <el-button
                              v-hasPerm="['pms:purchase:purchaseOrderAudit:audit']"
                              v-if=" scope.row.approveStatus==1 && userStore.user.userId==scope.row.approveUserId"
                              type="primary"
                              link
                              @click="handleAudit(scope.row,'audit')"
                            >
                                {{$t('common.audit')}}
                            </el-button>

                            <!-- 查看  -->
                            <el-button
                              v-else
                                    v-hasPerm="['pms:purchase:purchaseOrderAudit:view']"
                                    type="primary"
                                    link
                                    @click="handleAudit(scope.row,'detail')"
                            >
                                {{$t('common.select')}}
                            </el-button>

                        </template>
                    </el-table-column>
                </el-table>

              <div class="pagination-container">
                <pagination
                        v-if="total > 0"
                        v-model:total="total"
                        v-model:page="queryParams.page"
                        v-model:limit="queryParams.limit"
                        @pagination="handleQuery"
                />
              </div>
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">

    defineOptions({
        name: "PurchaseOrderAudit",
        inheritAttrs: false,
    });

    import supplierAPI from "@/modules/pms/api/supplier";
    import PurchasePersonnelAPI from "@/modules/pms/api/purchasePersonnel";
    import PurchaseOrderAPI, {
        PurchaseOrderPageVO,
        PurchaseOrderPageQuery,
        PurchaseOrderFrom
    } from "@/modules/pms/api/purchaseOrder";
    import {IObject} from "@/core/components/CURD/types";
    import {useRouter} from "vue-router";
    import {convertToTimestamp, parseDateTime} from "@/core/utils/index.js";
    import moment from 'moment';
    import { useUserStore } from "@/core/store";
    const userStore = useUserStore();

    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const dialogVisible = ref(false);
    const exportSequenceRef= ref()
    const loading = ref(false);
    const total = ref(0);
    const multipleSelection = ref([]);
    const productCategoryList = ref([])
    const purchasePersonnelList = ref([])
    const supplierList = ref([])
    const dateTypeList = ref([
        {
            key: 1,
            value:t('purchaseOrder.dateTypeList.auditDate')
        },
        {
            key: 2,
            value: t('purchaseOrder.dateTypeList.createDate')
        }

    ])
    const orderTypeList = ref([
        {
            key: 2,
            value: t('purchaseOrder.orderTypeList.marketDirectSupply')
        },
        {
            key: 1,
            value:t('purchaseOrder.orderTypeList.suppliersDirectSupply')
        }
    ])
    const orderSourceList = ref([
        {
            key: 2,
            value: t('purchaseOrder.orderSourceList.purchaseTask')
        },
        {
            key: 1,
            value:t('purchaseOrder.orderSourceList.manuallyAddPurchaseOrder')
        }
    ])
    const orderPurchaseStatusList = ref([
        {
            key: 1,
            value: t('purchaseOrder.orderPurchaseStatusList.wattingPurchase')
        },
        {
            key: 2,
            value:t('purchaseOrder.orderPurchaseStatusList.partialPurchase')
        },
        {
            key: 3,
            value:t('purchaseOrder.orderPurchaseStatusList.allPurchase')
        },
        {
            key: 0,
            value:t('purchaseOrder.orderPurchaseStatusList.closePurchase')
        },
        {
            key: 4,
            value:t('purchaseOrder.orderPurchaseStatusList.waitReceive')
        }
    ])
    const sendStatusList = ref([
        {
            key: 1,
            value: t('purchaseOrder.sendStatusList.send')
        },
        {
            key: 0,
            value:t('purchaseOrder.sendStatusList.noSend')
        },
    ])


    const approveStatusOption= ref([
        {
            value: '',
            label: t('purchaseOrder.approveStatusOption[0]'),
            active: true
        },
        {
            value: 1,
            label:t('purchaseOrder.approveStatusOption[1]'),
            active: false
        },
        {
            value: 2,
            label: t('purchaseOrder.approveStatusOption[2]'),
            active: false
        },
        {
            value: 3,
            label:t('purchaseOrder.approveStatusOption[3]'),
            active: false
        },
    ])

    const synStatusOption  = ref([
        {
            value: 1,
            label: t('purchaseOrder.synStatusOption.success')
        },
        {
            value: 2,
            label:t('purchaseOrder.synStatusOption.fail')
        },
    ])

    const queryParams = reactive<PurchaseOrderPageQuery>({
        dateType:1,
        page: 1,
        limit: 20,
        approveStatus:'',
    });

    const purchaseOrderList = ref<PurchaseOrderPageVO[]>();

    const  closeRef= ref()
    const  printRef= ref()

    const dialog = reactive({
        title: "关闭",
        visible: false,
    });
    const form = reactive<PurchaseOrderFrom>({
        purchaseOrderDetailVOList:[]
    });


    /** 查询采购员列表 */
    function getPerchasePersonnelList() {
        PurchasePersonnelAPI.getPerchasePersonnelList()
            .then((data) => {
                purchasePersonnelList.value = data;
            })
    }

    /** 查询供应商列表 */
    function getSupplierList() {
        supplierAPI.getSupplierListAll()
            .then((data) => {
                supplierList.value = data;
            })
    }

    /** 时间转换 */
    function changeDateRange(val) {
        if(val===1) {
            // var date = moment(new Date()).format('YYYY-MM-DD')
            var date1 = moment().subtract('days', 0).format('YYYY-MM-DD')
            queryParams.dateRange = [date1,date1]
        }else if(val===2){
            // var date = moment(new Date().getTime() - 3600 * 24 * 1000).format('YYYY-MM-DD')
            var date1 = moment().subtract('days', 1).format('YYYY-MM-DD')
            queryParams.dateRange = [date1,date1]
        }else if(val===3){
            // var endDate = moment(new Date().getTime() - 3600 * 24 * 1000 * 6).format('YYYY-MM-DD')
            var endDate1 = moment(new Date()).format('YYYY-MM-DD')
            var startDate = moment().subtract('days', 6).format('YYYY-MM-DD')
            queryParams.dateRange = [startDate,endDate1]
        }
    }

    /** 查询 */
    function handleQuery() {
        loading.value = true;
        let params = {
            ...queryParams
        }
        if(queryParams.dateType==1 && queryParams.dateRange && queryParams.dateRange.length>0){
            params.startApproveDate=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.endApproveDate=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')

        }
        if(queryParams.dateType==2 && queryParams.dateRange && queryParams.dateRange.length>0){
            params.startCreateTime=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.endCreateTime=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        delete params.dateType
        delete params.dateRange
        PurchaseOrderAPI.getPurchaseOrderApprovePage(params)
            .then((data) => {
                purchaseOrderList.value = data.records;
                total.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 重置查询 */
    function handleResetQuery() {
        queryFormRef.value.resetFields();
        queryParams.dateType=1
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    /** 行复选框选中记录选中ID集合 */
    function handleSelectionChange(selection: any[]) {
        multipleSelection.value = selection;
    }







    /** 审核*/
    function handleAudit(row:any,type:string){
        router.push({
            path: "/pms/purchase/purchaseOrderAuditDetail",
            query: {
                id:row.id,
                pageType:type
            }
        });
    }

    /**
     * 审核状态
     */

    const changePanel = (data) => {
      approveStatusOption.value.map(item => item.active = false);
      data.active =true;
      queryParams.approveStatus = data.value;
      queryParams.page = 1;
      queryParams.limit = 20;
      handleQuery();
    }

    /**
     * 手动同步
     */
    function handleSync() {

        const multiple=multipleSelection.value
        if(multiple.length>20){
            return ElMessage.error(t('purchaseOrder.message.maxSelect'))
        }

        let message = ''; // 提示消息
        let validOrderCodes:any = []; // 符符合条件的orderCode
        let validIds:any = []; // 符合条件的id

        multiple.forEach(item => {
            const { approveSynStatus, approveStatus, orderCode, id } = item;
            if (approveSynStatus!==1 && approveStatus === 2) { //审核通过且同步不成功的
                validOrderCodes.push(orderCode);
                validIds.push(id);
            }
        });
        console.log('所有的orderCode:', multiple.map(item => item.orderCode));
        console.log('符合条件的orderCode:', validOrderCodes);
        console.log('符合条件的id:', validIds);


        //不符合条件的code
        const unqualifiedCodes =  multiple.map(item => item.orderCode).filter(code => !validOrderCodes.includes(code));
        console.log('不符合条件的orderCode:',unqualifiedCodes)


        if (unqualifiedCodes.length>0){
            message = unqualifiedCodes.map(code => `${code}`).join('、');
            console.log(message)
            return ElMessage.error(t('purchaseOrder.message.inconformitySelect',{message:message}))
        }

        if (validIds.length==0){
            return
        }



        PurchaseOrderAPI.syncApprove({ids:validIds}).then(res => {
            ElMessage.success(t('purchaseOrder.message.syncSucess'))
            handleQuery()
        })
    }

    onActivated(() => {
        getSupplierList();
        getPerchasePersonnelList();
        handleQuery();
    });
</script>

<style lang="scss" scoped>
    .purchaseOrderAudit{
      height: 100%;
      display: flex;
      flex-direction: column;

      .search-card {
        flex-shrink: 0;
      }
      .content-card {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        :deep(.el-card__body) {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .el-table {
          flex: 1;
          overflow: auto;
        }

        .pagination-container {
          margin-top: 20px;
          display: flex;
          justify-content: center;
        }
      }
      .panelContent {
        display: flex;
        border-bottom: 1px solid #F2F3F4;
        width: 100%;
        margin-bottom: 16px;
        .panelItem {
          font-size: 14px;
          color: #151719;
          padding: 10px 39px;
          cursor: pointer;
          &.active {
            color: var(--el-color-primary);
            border-bottom: 2px solid var(--el-color-primary);
          }
        }
      }
        .gred{
            color: #90979E;
        }
        .close-reason{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #90979E;
        }
        .circle-div{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .circle{
                width: 6px;
                height: 6px;
                border-radius: 3px;
                margin-right: 7px;
            }
            .circle-color1{
                background: #29B610;
            }
            .circle-color0{
                background: #D7DBDF;
            }
        }
    }
</style>



