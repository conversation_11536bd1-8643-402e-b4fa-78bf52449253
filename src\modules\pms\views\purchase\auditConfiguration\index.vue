<template>
  <div class="audit-config-page">
    <!-- 顶部筛选栏 -->
    <el-form :inline="true" :model="searchForm" class="filter-form">      
      <el-form-item :label="t('pmsAuditConfiguration.name')">
        <el-input v-model="searchForm.nickName" :placeholder="t('pmsAuditConfiguration.pleaseInput')" clearable />
      </el-form-item>
      <el-form-item :label="t('pmsAuditConfiguration.mobile')">
        <el-input v-model="searchForm.mobile" :placeholder="t('pmsAuditConfiguration.pleaseInput')" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">{{ t('pmsAuditConfiguration.search') }}</el-button>
      </el-form-item>
      <el-form-item style="float:right; margin-left:auto;">
        <el-button type="primary" @click="resetAddForm();showAddDialog = true">{{ t('pmsAuditConfiguration.addAuditor') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 主表格 -->
    <el-table :data="tableData" border stripe style="width: 100%; margin-top: 16px" v-loading="loading">
      <el-table-column prop="userName" :label="t('pmsAuditConfiguration.account')" width="120" />
      <el-table-column prop="nickName" :label="t('pmsAuditConfiguration.name')" width="200" />
      <el-table-column prop="mobile" :label="t('pmsAuditConfiguration.mobile')" width="140" />
      <el-table-column prop="auditRange" :label="t('pmsAuditConfiguration.scope')" />
      <el-table-column prop="deptNames" :label="t('pmsAuditConfiguration.department')" width="" />
      <el-table-column prop="createTime" :label="t('pmsAuditConfiguration.createdAt')" width="180">
        <template #default="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('pmsAuditConfiguration.action')" width="80">
        <template #default="scope">
          <el-button type="text" style="color: #f56c6c" @click="handleDelete(scope.row)">{{ t('pmsAuditConfiguration.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="margin: 16px 0; text-align: right">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-sizes="[10, 20, 50]"
        v-model:current-page="page"
        v-model:page-size="pageSize"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 添加审核人弹窗 -->
    <el-drawer v-model="showAddDialog" :title="t('pmsAuditConfiguration.dialogTitle')" width="480px" :close-on-click-modal="false">
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="90px" label-position="top">
        <el-form-item :label="t('pmsAuditConfiguration.selectUser')" prop="userId" required>
          <el-select v-model="addForm.userId" :placeholder="t('pmsAuditConfiguration.pleaseSelect')" filterable style="width:100%">
            <el-option v-for="user in userList" :key="user.userId" :label="user.nickName" :value="user.userId" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('pmsAuditConfiguration.auditScope')" prop="rangeCodos" required>
          <el-checkbox-group v-model="addForm.rangeCodos">
            <el-checkbox value="101" :label="t('pmsAuditConfiguration.puchaseOrder')" />
            <el-checkbox value="102" :label="t('pmsAuditConfiguration.purchaseReturns')" />
            <el-checkbox value="103" :label="t('pmsAuditConfiguration.supplierContract')" />
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">{{ t('pmsAuditConfiguration.cancel') }}</el-button>
        <el-button type="primary" @click="handleAdd">{{ t('pmsAuditConfiguration.confirm') }}</el-button>
      </template>
    </el-drawer>

    <!-- 删除确认弹窗 -->
    <el-dialog v-model="showDeleteDialog" :title="t('pmsAuditConfiguration.prompt')" width="320px" :show-close="false">
      <div>{{ t('pmsAuditConfiguration.confirmDeletePrompt') }}</div>
      <template #footer>
        <el-button @click="showDeleteDialog = false">{{ t('pmsAuditConfiguration.cancel') }}</el-button>
        <el-button type="primary" @click="confirmDelete">{{ t('pmsAuditConfiguration.confirm') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import PurchaseAuditConfigurationAPI from '@/modules/pms/api/auditConfiguration';
import { parseTime } from "@/core/utils";
const { t } = useI18n();

// 筛选表单
const searchForm = reactive({
  nickName: '',
  mobile: ''
});

// 表格数据
const tableData = ref([
]);
const total = ref(200);
const page = ref(1);
const pageSize = ref(20);
const userList = ref([]);
const loading = ref(false);
function handleSearch() {
  // TODO: 搜索逻辑
  handleQuery();
}

function getUserList() {
  PurchaseAuditConfigurationAPI.getUserList().then(res => {
    userList.value = res;
  });
}

function handleQuery() {
  loading.value = true;
  PurchaseAuditConfigurationAPI.getPage({
    page: page.value,
    limit: pageSize.value,
    nickName: searchForm.name,
    mobile: searchForm.mobile
  }).then(res => {
    tableData.value = res.records;
    total.value = Number(res.total);
  }).finally(() => {
    loading.value = false;
  });
}
function handleSizeChange(val) {
  pageSize.value = val;
  handleQuery();
}
function handlePageChange(val) {
  page.value = val;
  handleQuery();
}

// 添加审核人弹窗
const showAddDialog = ref(false);
const addForm = reactive({
  userId: '',
  rangeCodos: []
});
const addFormRef = ref();
const addFormRules = {
  userId: [
    { required: true, message: t('pmsAuditConfiguration.pleaseSelect'), trigger: 'change' }
  ],
  rangeCodos: [
    { required: true, type: 'array', min: 1, message: t('pmsAuditConfiguration.pleaseSelect'), trigger: 'change' }
  ]
};
function handleAdd() {
  addFormRef.value.validate((valid) => {
    if (!valid) return;
    PurchaseAuditConfigurationAPI.update(addForm).then(() => {
      ElMessage.success(t('common.addSuccess'));
      showAddDialog.value = false;
      handleQuery();
    });
  });
}

// 删除确认弹窗
const showDeleteDialog = ref(false);
let deleteRow = null;
function handleDelete(row) {
  deleteRow = row;
  showDeleteDialog.value = true;
}
function confirmDelete() {
  PurchaseAuditConfigurationAPI.delete({
    userId: deleteRow.userId,
  }).then(() => {
    ElMessage.success(t('common.deleteSuccess'));
    showDeleteDialog.value = false;
    handleQuery();
  });
}
function handleResetQuery() {
  searchForm.name = '';
  searchForm.nickName = '';
  handleQuery();
}

function resetAddForm() {
  addForm.userId = '';
  addForm.rangeCodos = [];
}
onMounted(() => {
  getUserList();
  handleQuery();
});
</script>

<style scoped>
.audit-config-page {
  background: #fff;
  padding: 24px;
}
.filter-form {
  margin-bottom: 0;
}
</style>
