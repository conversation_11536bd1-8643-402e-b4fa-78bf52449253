/** 全局SCSS变量 */

:root {
  --menu-background: #FFFFFF;
  --menu-text: #90979E ;

  --el-menu-item-bg-color: #F8F9FC;
  --menu-item-text: #151719;

  --menu-hover: #F9F5FF ;
  --menu-hover-text: #762ADB ;

  --menu-active-text: #ffffff;
  --menu-active-background: #762ADB;

  --sidebar-logo-background: #762ADB;

  --el-bg-header-color: #762ADB;
  --el-tags-bg-color: #fff;


  // Element Plus menu variables
  --el-menu-hover-bg-color: var(--menu-hover);
  --el-menu-hover-text-color: var(--menu-active-text);

  // 修复表格 fixed 列被选中后由于透明色导致叠字的 bug
  .el-table {
    --el-table-current-row-bg-color: rgb(235 243 250);
  }
  // --el-color-primary: #762ADB ;
}

/** 暗黑主题 */
html.dark {
  --menu-background: var(--el-bg-color-overlay);
  --menu-text: #fff;
  --menu-active-text: var(--el-menu-active-color);
  --menu-hover: #762ADB ;
  //--sidebar-logo-background: rgb(0 0 0 / 20%);
  --sidebar-logo-background: #762ADB;
}

$menu-background: var(--menu-background); // 一级菜单背景色
$menu-text: var(--menu-text); // 一级菜单文字颜色

$menu-item-background: var(--el-menu-item-bg-color); // 二级菜单背景色
$menu-item-text: var(--menu-item-text); // 二级菜单文字颜色

$menu-hover: var(--menu-hover); // 菜单悬停背景色
$menu-hover-text: var(--menu-hover-text); // 菜单悬停文字颜色

$menu-active-text: var(--menu-active-text); // 菜单激活文字颜色
$menu-active-background: var(--menu-active-background); // 菜单激活文字颜色

$sidebar-logo-background: var(--sidebar-logo-background); // 侧边栏 Logo 背景色

$sidebar-width: 220px; // 侧边栏宽度
$sidebar-width-collapsed: 54px; // 侧边栏收缩宽度
$navbar-height: 50px; // 导航栏高度
$tags-view-height: 41px; // TagsView 高度
