/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-08 17:50:26
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-04-10 13:22:32
 * @FilePath: \supply-manager-web\src\modules\pms\api\productCategory.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/core/utils/request";

const PRODUCTCATE_BASE_URL = "/supply-biz-common/product/category";

class productCategoryAPI {
  /** 分页查询 */
  static getPageList(queryParams?: PageQuery) {
    return request<any, PageResult<productCategoryInfo[]>>({
      url: `${PRODUCTCATE_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }
  /** 懒加载分类下拉列表 */
  static queryManagerCategoryList(data: { id?: string }) {
    return request({
      url: `${PRODUCTCATE_BASE_URL}/queryManagerCategoryList`,
      method: "post",
      data: data,
    });
  }

  /** 详情查询 */
  static async detailCategory(data: { id?: string }) {
    const res = await request({
      url: `${PRODUCTCATE_BASE_URL}/detail`,
      method: "post",
      data: data,
    });
    // try {
    //   if (res && res.imagesUrl && typeof res.imagesUrl === "string") {
    //     res.imagesUrl = JSON.parse(res.imagesUrl);
    //   }
    // } catch (err) {
    //   console.log(err);
    // }

    return res;
  }
  /** 添加分类 */
  static saveCate(data: productCategoryForm) {
    return request({
      url: `${PRODUCTCATE_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }
  /** 编辑分类 */
  static updateCate(data: productCategoryForm) {
    return request({
      url: `${PRODUCTCATE_BASE_URL}/update`,
      method: "post",
      data: data,
    });
  }
  /** 删除分类 */
  static deleteCate(data: { id?: string }) {
    return request({
      url: `${PRODUCTCATE_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }
  /** 修改排序 */
  static updateSort(data: { id?: string; sortNum: number }) {
    return request({
      url: `${PRODUCTCATE_BASE_URL}/updateSort`,
      method: "post",
      data: data,
    });
  }

  /**
   * 商品分类下拉树
   *
   */
  static queryCategoryTreeList(data: any) {
    return request({
      url: `${PRODUCTCATE_BASE_URL}/queryCategoryTreeList`,
      method: "post",
      data: data,
    });
  }
}
export interface productCategoryForm {
  id?: string;
  imagesUrl?: any;
  categoryName?: string;
  sort?: number;
  level?: number;
  parentName?: string;
  parentId?: string;
}
export interface productCategoryInfo {
  categoryName?: string;
  editFlag?: boolean;
  sort?: number;
  id?: string;
  records?: [];
  parentId?: string;
}

export default productCategoryAPI;
