/*
 * @Author: ch<PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-10 09:30:32
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-03-21 11:07:28
 * @FilePath: \supply-manager-web\src\modules\oms\router\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { RouteRecordRaw } from "vue-router";
import component from "*.vue";

export const Layout = () => import("@/core/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  // {
  //   path: "/",
  //   name: "/",
  //   component: Layout,
  //   redirect: "/dashboard",
  //   children: [
  //     {
  //       path: "dashboard",
  //       component: () => import("@/core/views/dashboard/index.vue"),
  //       // 用于 keep-alive 功能，需要与 SFC 中自动推导或显式声明的组件名称一致
  //       // 参考文档: https://cn.vuejs.org/guide/built-ins/keep-alive.html#include-exclude
  //       name: "Dashboard",
  //       meta: {
  //         title: "dashboard",
  //         icon: "homepage",
  //         affix: true,
  //         keepAlive: true,
  //       },
  //     },
  //     {
  //       path: "401",
  //       component: () => import("@/core/views/error-page/401.vue"),
  //       meta: { hidden: true },
  //     },
  //     {
  //       path: "404",
  //       component: () => import("@/core/views/error-page/404.vue"),
  //       meta: { hidden: true },
  //     },
  //   ],
  // },
  {
    path: "/oms/omsContract",
    component: Layout,
    children: [
      {
        path: "addContract",
        name: "OmsAddContract",
        component: () => import("@oms/views/omsContract/add.vue"),
        meta: {
          title: "新增合同",
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "contractDetail",
        component: () => import("@oms/views/omsContract/detail.vue"),
        meta: {
          title: "合同详情",
          hidden: true,
        },
      },
      {
        path: "contractAudit",
        component: () => import("@oms/views/omsContract/audit.vue"),
        meta: {
          title: "审核合同",
          hidden: true,
        },
      },
      {
        path: "reviewDetail",
        component: () => import("@oms/views/omsContract/reviewDetail.vue"),
        meta: {
          title: "审核详情",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/oms/omsCustomer",
    component: Layout,
    children: [
      {
        path: "addCustomer",
        name: "OmsAddCustomer",
        component: () => import("@oms/views/omsCustomer/addCustomer.vue"),
        meta: {
          title: "新增客户",
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "customerDetail",
        component: () => import("@oms/views/omsCustomer/customerDetail.vue"),
        meta: {
          title: "客户详情",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/oms/order",
    component: Layout,
    children: [
      {
        path: "addOrder",
        name: "OmsAddOrder",
        component: () => import("@oms/views/order/orderManage/addOrder.vue"),
        meta: {
          title: "新增订单",
          keepAlive: true,
          hidden: true,
        },
      },
      {
        path: "orderDetail",
        component: () => import("@oms/views/order/orderManage/orderDetail.vue"),
        meta: {
          title: "订单详情",
          hidden: true,
        },
      },
      {
        path: "deliveryPlan", // 调整交货计划
        component: () => import("@oms/views/order/orderManage/deliveryPlan.vue"),
        meta: {
          title: "调整交货计划",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/oms/finance",
    component: Layout,
    children: [
      {
        path: "accountReceivableSummaryDetail", // 应收账单汇总明细
        component: () => import("@oms/views/finance/accountReceivableSummary/detail.vue"),
        meta: {
          title: "应收账单明细",
          keepAlive: true,
          hidden: true,
        },
      },
      {
        path: "specialDetail", // 特定成本明细
        name: "SpecialDetail",
        component: () => import("@oms/views/finance/specialOrderCostManagement/detail.vue"),
        meta: {
          title: "特定成本明细",
          keepAlive: false,
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/oms/afterSale",
    component: Layout,
    children: [
      {
        path: "addAfterSales",
        name: "OmsAddAfterSales",
        component: () => import("@oms/views/afterSale/afterSaleManagement/addAfterSales.vue"),
        meta: {
          title: "售后申请",
          keepAlive: true,
          hidden: true
        }
      },
      {
        path: "afterSaleDetail",
        component: () => import("@oms/views/afterSale/afterSaleManagement/afterSaleDetail.vue"),
        meta: {
          title: "售后信息",
          hidden: true
        }
      },
      {
        path: "afterSaleAudit/afterSaleDetail",
        component: () => import("@oms/views/afterSale/afterSaleAudit/afterSaleDetail.vue"),
        meta: {
          title: "",
          hidden: true
        }
      },
    ]
  },
];
