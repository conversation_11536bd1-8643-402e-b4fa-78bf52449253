import request from "@/core/utils/request";
import { ProductVO } from "@/modules/wms/api/shippingReceipt";

const WAREHOUSR_BASE_URL = "/supply-wms/warehouseEntryOrders";

class warehouseEntryAPI {
  /** 获取入库通知单下拉数据 (拆装单创建页面)*/
  static queryInventoryListByNoticeCode() {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryNoticeListByNoticeCode`,
      method: "post",
      data: {},
    });
  }

  /** 根据入库通知单号查询拆装商品列表(拆装单创建页面) */
  static queryLossProductListByOrderId(data: { receiptNoticeCode?: string }) {
    return request<any, PageResult<ProductVO[]>>({
      url: `${WAREHOUSR_BASE_URL}/queryEntryProductListByNoticeCode`,
      method: "post",
      data: data,
    });
  }

  /** 获取分页数据 */
  static queryPageList(queryParams?: PageQuery) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /** 添加 */
  static addWarehouseOrder(data: addFormData) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }

  /** 详情 */
  static queryDetail(data: any) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryDetail`,
      method: "post",
      data: data,
    });
  }
  /** 全部商品-分页查询 */
  static queryProductPageList(data: any) {
    return request({
      url: `/supply-wms/receivingOrders/queryProductPageList`,
      method: "post",
      data: data,
    });
  }

  /** 根据当前仓库获取库区列表 */
  static queryListByCurrentWarehouse(data: any) {
    return request({
      url: `/supply-wms/storeArea/queryListByCurrentWarehouse`,
      method: "get",
      params: data,
    });
  }

  /** 领单 */
  static receiveOrder(data: { id: string }) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/pickOrder`,
      method: "post",
      data: data,
    });
  }

  /** 取消领单 */
  static cancelReceiveOrder(data: { id: string }) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/releaseOrder`,
      method: "post",
      data: data,
    });
  }


  // 查询入库单商品ysn列表
  static queryYSNList(data: any) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryProductYsnList`,
      method: "post",
      data: data,
    });
  }

  // 分页查询分拣结果商品列表
  static queryPickingResultProductPageList(data: any) {
    /* {
      "limit": 0,
      "page": 0,
      "productCode": "", // 商品编码
      "sortingCode": "" // 分拣单号
    } */
    return request({
      url: `/supply-wms/sorting/querySortingResultProductPage`,
      method: "post",
      data: data,
    });
  }
}

export interface addFormData {
  id?: string;
  receiptNoticeCode?: string;
  receiptType?: number;
  plannedDeliveryTime?: string;
  purchaseSalesPerson?: string;
  sourceOrderCode?: string;
  customerName?: string;
  supplierName?: string;
  contactPerson?: string;
  countryAreaCode?: string;
  mobile?: string;
  countryId?: string;
  provinceId?: string;
  cityId?: string;
  districtId?: string;
  address?: string;
  remark?: string;
  provinceName?: string;
  areaInfo?: any;
  cityName?: string;
  districtName?: string;
  countryName?: string;
  productList?: any;
  entryOrderCode?: string;
  entryOperator?: string;
  entryTime?: string;
  orderType?: number; // 订单类型：1:按分拣单入库;2:按商品入库
}

export interface WarehousePageQuery extends PageQuery {
  /** 状态 */
  status?: number;
  entryOrderCode?: string;
  entryOperator?: number;
  /** 查询类型:1:入库时间 2:创建时间 3:领用时间 */
  queryType?: number;
  /** 领用状态 0->否 1->是 */
  receivingStatus?: number;
}

export default warehouseEntryAPI;
