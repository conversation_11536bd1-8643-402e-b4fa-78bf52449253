<template>
  <div class="app-container">
    <div class="warehouseEntryNoticeDetail" v-loading="loading">
      <div class="page-title">
        <div class="purchase-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon><Back /></el-icon>
          </div>
          <div>
            {{ t("warehouseEntryNotice.label.receiptNoticeCode") }}：{{
              route.query.receiptNoticeCode
            }}
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" label-width="110px" label-position="right">
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("warehouseEntryNotice.label.basicInformation") }}
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
                >
                  {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.plannedDeliveryTime')"
                >
                  <span v-if="form.plannedDeliveryTime">
                    {{
                      parseTime(
                        form.plannedDeliveryTime,
                        "{y}-{m}-{d} {h}:{i}:{s}"
                      )
                    }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 入库类型:1:采购入库、2:退货入库 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receiptType')"
                >
                  <span v-if="form.receiptType == 1">
                    {{ t("warehouseEntryNotice.label.purchaseInventory") }}
                  </span>
                  <span v-else-if="form.receiptType == 2">
                    {{ t("warehouseEntryNotice.label.returnStorage") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.purchaseSalesPerson')"
                >
                  {{
                    form.purchaseSalesPerson ? form.purchaseSalesPerson : "-"
                  }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <span v-if="form.receiptType == 2">
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.customerName')"
                  >
                    {{ form.customerName ? form.customerName : "-" }}
                  </el-form-item>
                </span>
                <span v-else>
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.supplierName')"
                  >
                    {{ form.supplierName ? form.supplierName : "-" }}
                  </el-form-item>
                </span>
              </el-col>
              <el-col :span="6">
                <!-- 地址 -->
                <el-form-item :label="$t('warehouseEntryNotice.label.address')">
                  <!-- {{ form.countryName }}{{ form.provinceName }}{{ form.cityName
                  }}{{ form.districtName }} -->
                  <span class="encryptBox">
                    <span v-if="form.addressShow">
                      {{ form.addressFormat }}
                      <el-icon
                        v-if="form.fullAddress"
                        @click="form.addressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                      >
                        <component :is="form.addressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.fullAddress ? form.fullAddress : "-" }}
                    </span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 联系人 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.contactPerson')"
                >
                  <EncryptPhone :nameType="true" :name="form.contactPerson" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 联系电话 -->
                <el-form-item :label="$t('warehouseEntryNotice.label.mobile')">
                  <span class="encryptBox">
                    <span v-if="form.mobile">
                      {{ form.countryAreaCode }}
                      <span v-if="form.countryAreaCode">-</span>
                    </span>
                    <span v-if="form.mobile && form.mobile.length <= 4">
                      {{ form.mobile }}
                    </span>
                    <span v-else-if="form.mobile && form.mobile.length > 4">
                      {{ form.mobile }}
                      <el-icon
                        v-if="form.mobile"
                        @click="form.mobilePhoneShow ? getRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                      >
                        <component :is="form.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                  <!-- {{ form.mobile ? form.mobile : "-" }} -->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                  <el-form-item :label="$t('warehouseEntryNotice.label.expectedQty')">
                      {{form.expectedQty || "-" }}
                  </el-form-item>
              </el-col>
              <el-col :span="6">
                  <el-form-item :label="$t('warehouseEntryNotice.label.expectedWeight')">
                      {{ form.expectedWeight || "-" }}
                  </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('warehouseEntryNotice.label.remark')">
                  {{ form.remark ? form.remark : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("warehouseEntryNotice.label.goodsDetails") }}
            </div>
          </div>
          <div>
            <el-table
              ref="tableSumRef1"
              :data="form.productList"
              highlight-current-row
              stripe
            >
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
                align="center"
              />
              <el-table-column
                :label="$t('warehouseEntryNotice.label.goodsInfor')"
                min-width="150"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div class="product-code">
                    {{ $t("warehouseEntryNotice.label.productCode") }}：
                    {{ scope.row.productCode }}
                  </div>
                  <div class="product-name">
                    {{ scope.row.productName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('warehouseEntryNotice.label.goodsCategory')"
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ scope.row.firstCategoryName }}/
                  {{ scope.row.secondCategoryName }}/
                  {{ scope.row.thirdCategoryName }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('warehouseEntryNotice.label.productSpecs')"
                prop="productSpecs"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('warehouseEntryNotice.label.productPlanQty')"
                prop="productExpectQty"
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ scope.row.productExpectQty }}
                  <span v-if="scope.row.productExpectQty">
                    {{ scope.row.productUnitName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('warehouseEntryNotice.label.actualQuantityCopy')"
                prop="productActualQty"
                show-overflow-tooltip
              />
                <el-table-column
                        :label="$t('warehouseEntryNotice.label.productPlanWeightCopy')"
                        prop="expectedWeight"
                        show-overflow-tooltip
                />
                <el-table-column
                        :label="$t('warehouseEntryNotice.label.actualWeightCopy')"
                        prop="receivedWeight"
                        show-overflow-tooltip
                />
              <el-table-column
                :label="$t('warehouseEntryNotice.label.remark')"
                prop="remark"
                show-overflow-tooltip
              />
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">
          {{ $t("common.reback") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "WarehouseEntryNoticeDetail",
  inheritAttrs: false,
});
import warehouseEntryNoticeAPI, {
  addFormData,
} from "@/modules/wms/api/warehouseEntryNotice";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import { parseTime } from "@/core/utils/index.js";

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const loading = ref(false);
const tableSumRef1 = ref();
const form = reactive<addFormData>({
  productList: [],
});

async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}

/** 查询采购单详情 */
function queryDetail() {
  loading.value = true;
  let params = {
    id: route.query.id,
  };
  warehouseEntryNoticeAPI
    .queryDetailMobileEncrypt(params)
    .then((data: any) => {
      Object.assign(form, data);
      form.mobilePhoneShow = true;
      // 地址包含数字
      if (form.fullAddress && containsNumber(form.fullAddress)) {
        form.addressShow = true;
        form.addressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      } else {
        // 不包含数字
        form.addressShow = false;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}
function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

// 获取真实电话号码
function getRealPhone() {
  warehouseEntryNoticeAPI
    .queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.mobile = data.mobile;
      form.mobilePhoneShow = false;
    })
    .finally(() => {});
}

onMounted(() => {
  queryDetail();
});
</script>
<style scoped lang="scss">
.warehouseEntryNoticeDetail {
  background: #ffffff;
  border-radius: 4px;
}
</style>
<style lang="scss">
.warehouseEntryNoticeDetail {
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .cursor-pointer {
    cursor: pointer;
  }
}
</style>
