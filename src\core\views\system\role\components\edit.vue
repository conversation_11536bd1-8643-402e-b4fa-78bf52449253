<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" width="500px" @close="close">
        <el-form :model="roleForm" :rules="rules" ref="roleFormRef" label-position="top">
            <el-form-item :label="$t('roleManagement.label.roleName')" prop="roleName">
                <el-input type="text" :placeholder="$t('common.placeholder.inputTips')" v-model="roleForm.roleName" :maxlength="20" clearable/>
            </el-form-item>
            <el-form-item :label="$t('roleManagement.label.affiliatedDepartment')" prop="deptId">
              <el-cascader
                v-model="roleForm.deptId"
                :options="deptList"
                :props="{ value: 'id', label: 'deptName', checkStrictly: true}"
                clearable
                popper-class="role-management-dept-cascader"
                ref="deptRef"
                @change="setName"
              />
            </el-form-item>
            <el-form-item :label="$t('roleManagement.label.roleDesc')" prop="roleDesc">
                <el-input type="textarea" show-word-limit  :placeholder="$t('common.placeholder.inputTips')" v-model="roleForm.roleDesc" :maxlength="50" clearable/>
            </el-form-item>
            <el-form-item :label="$t('roleManagement.label.authoritySettings')"  class="demo-tabs">
                    <el-tabs v-model="activeName" type="card" @tab-click="handleClick" v-loading="loading" v-if="roleForm.authoritySettings && roleForm.authoritySettings.length>0">
                        <template v-for="(item, index) in roleForm.authoritySettings" :key="item.systemType">
                           <el-tab-pane :label="item.systemName" :name="item.systemType">
<!--                                <el-tree-->
<!--                                        :ref="`treeRef${index}`"-->
<!--                                        :data="item.roleAuthorityInfo.treeList"-->
<!--                                        show-checkbox-->
<!--                                        node-key="authorityId"-->
<!--                                        default-expand-all-->
<!--                                        :default-checked-keys="item.roleAuthorityInfo.authorityIds"-->
<!--                                        :props="defaultProps"-->
<!--                                />-->
                               <el-tree
                                       ref="treeRef"
                                       :data="item.roleAuthorityInfo.treeList"
                                       show-checkbox
                                       node-key="authorityId"
                                       default-expand-all
                                       :default-checked-keys="item.roleAuthorityInfo.authorityIds"
                                       :props="defaultProps"
                               />
                           </el-tab-pane>
                        </template>
                    </el-tabs>
            </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" :loading="submitLoading" @click="submitForm">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-drawer>
</template>
<script setup>
    import RoleAPI from "@/core/api/role";
    import UserAPI from "@/core/api/accountManagement.ts";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();
    let roleFormRef = ref();
    const loading = ref(false);

    const defaultProps = {
        children: 'children',
        label: 'authorityName',
    }
    const initForm = () => ({
            roleId: "",
            roleName: "",
            deptId: "",
            deptName: "",
            roleDesc: "",
            authoritySettings:[],
    });
    const data = reactive({
        roleForm: initForm()
    });
    const { roleForm } = toRefs(data);
    const rules = reactive({
        roleName: [
            {
                required: true, message: t("roleManagement.rules.roleName"), trigger: "blur",
            },
            {
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/, message: t("roleManagement.rules.roleNameFomart"), trigger: ["change", "blur"],
            },
        ],
    });
    const  submitLoading= ref(false)
    const  editType= ref("")

    const activeName = ref('1')
    const deptList = ref([])
    const deptRef = ref()

    const treeRef = ref()
    // const treeRef0 = ref()
    // const treeRef1 = ref()
    // const treeRef2 = ref()
    // const treeRef3 = ref()
    // const treeRef4 = ref()

    function close() {
        // props.visible = false;
        emit("update:visible", false);
        reset();
    }

    function getDeptList() {
      UserAPI.allDeptList().then((data) => {
        deptList.value = data;
        if (deptList.value && deptList.value.length > 0) {
          roleForm.value.deptId = findParentLabel(
            deptList.value,
            userForm.value.deptId
          );
        }
      });
    }
    function findParentLabel(list, str, parents = []) {
      for (let i = 0; i < list.length; i++) {
        const node = list[i];
        if (node.id === str) {
          return [...parents, node.id];
        } else if (node.children && node.children.length > 0) {
          const result = findParentLabel(node.children, str, [...parents, node.id]);
          if (result) {
            return result;
          }
        }
      }
      // 没有找到目标子节点
      return null;
    }
    function setName() {
      // 获取选中的nodeList
      let nodeList = deptRef.value.getCheckedNodes()
      // 解析node的deptName
      if(nodeList && nodeList.length > 0){
        roleForm.value.deptId = nodeList[0].value
        roleForm.value.deptName = nodeList[0].label
      }else{
        roleForm.value.deptName = null
        roleForm.value.deptId = null
      }
    }

    function reset() {
        roleFormRef.value.clearValidate();
        roleFormRef.value.resetFields();
        roleForm.value = initForm();
    }

    function submitForm() {
        if (editType.value === "add") {
            submitAddRole();
        } else if (editType.value === "edit") {
            submitUpdateRole();
        }
    }

    function submitAddRole() {
        roleFormRef.value.validate((valid) => {
          if (!valid) return;
          submitLoading.value = true;
          let roleSysList = []
            roleForm.value.authoritySettings.forEach((item,index)=>{
                let obj = {
                    // authorityIds: getCheckedKeys(index==0?treeRef0?.value[0]:index==1?treeRef1?.value[0]:index==2?treeRef2?.value[0]:index==3?treeRef3?.value[0]:treeRef4?.value[0]),
                    authorityIds: getCheckedKeys(index),
                    systemType:item.systemType
                }
                roleSysList.push(obj)
            })
            let num = roleSysList.filter(function (item) {return item.authorityIds.length==0})
            if(num.length==roleSysList.length){
                submitLoading.value = false;
                return  ElMessage.error(t("roleManagement.message.rolesEmptyTips"));
            }
          let params = {
              platformType:'supply',
              roleDesc:roleForm.value.roleDesc,
              roleName:roleForm.value.roleName,
              deptId:roleForm.value.deptId,
              deptName:roleForm.value.deptName,
              roleSysList:roleSysList
          };
          RoleAPI.addRole(params)
            .then((res) => {
              ElMessage.success(t("roleManagement.message.addSucess"));
              close();
              emit("onSubmit");
            })
            .finally(() => {
              submitLoading.value = false;
            });
        });
    }

    function submitUpdateRole() {
        roleFormRef.value.validate((valid) => {
          if (!valid) return;
          submitLoading.value = true;
            let roleSysList = []
            roleForm.value.authoritySettings.forEach((item,index)=>{
                let obj = {
                    // authorityIds: getCheckedKeys(index==0?treeRef0?.value[0]:index==1?treeRef1?.value[0]:index==2?treeRef2?.value[0]:index==3?treeRef3?.value[0]:treeRef4?.value[0]),
                    authorityIds: getCheckedKeys(index),
                    systemType:item.systemType
                }
                roleSysList.push(obj)
            })
            let num = roleSysList.filter(function (item) {return item.authorityIds.length==0})
            if(num.length==roleSysList.length){
                submitLoading.value = false;
                return  ElMessage.error(t("roleManagement.message.rolesEmptyTips"));
            }
            let params = {
                roleId:roleForm.value.roleId,
                platformType:'supply',
                roleDesc:roleForm.value.roleDesc,
                roleName:roleForm.value.roleName,
                deptId:roleForm.value.deptId,
                deptName:roleForm.value.deptName,
                roleSysList:roleSysList
            };
            RoleAPI.updateRole(params)
            .then((res) => {
              ElMessage.success(t("roleManagement.message.editSucess"));
              close();
              emit("onSubmit");
            })
            .finally(() => {
              submitLoading.value = false;
            });
        });
    }


    function queryRoleMenuList() {
        loading.value=true
        submitLoading.value=true
        let params = {
            roleId:roleForm.value.roleId?roleForm.value.roleId:'',
        }
        console.log('====queryRoleMenuList====')
        RoleAPI.authorityInfo(params).then(data => {
                if(editType.value == "add" && data && data.length>0){
                    data.forEach(item=>{
                        item.roleAuthorityInfo.authorityIds=[]
                    })
                }
                roleForm.value.authoritySettings=data
                activeName.value=data?data[0].systemType:''
        }).finally(()=>{
                loading.value=false
                submitLoading.value=false
        })
    }

    const getCheckedKeys = (index) => {
        console.log('====werr1===='+treeRef)
        // let checked = treeRef.value[index]?.getCheckedKeys(false)

        const fullCheckedKeys = treeRef.value[index].getCheckedKeys(false);
        const halfCheckedKeys = treeRef.value[index].getHalfCheckedKeys(false);
        const checked = [...fullCheckedKeys, ...halfCheckedKeys];

        console.log('====werr1-checked===='+checked)
        return checked
    }

    function setEditType(data) {
        editType.value = data;
    }

    function setFormData(data) {
        Object.assign(roleForm.value, data);
        console.log(roleForm.value)
    }

    defineExpose({
        getDeptList,
        setFormData,
        setEditType,
        queryRoleMenuList
    });
</script>

<style scoped lang="scss">
    .demo-tabs{
        .el-tabs{
            width: 100%;
        }
    }

</style>
<style lang="scss">
  .role-management-dept-cascader {
    .el-cascader-panel .el-radio__input {
      display: block !important;
    }
    .el-cascader-panel .el-radio {
      display: block !important;
    }
  }
</style>
