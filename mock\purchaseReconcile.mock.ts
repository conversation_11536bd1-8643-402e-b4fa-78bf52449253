import { defineMockPms } from "./base";

export default defineMockPms([

    // 采购单分页列表
    {
    url: "reconciliationBill/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
          {
            id: '1',
            reconciliationBillCode: "123456111",
            purchaseCode: "wwww111",
            orderTime: "2021-03-25 12:39:54",
            supplierName: "供应商1，供应商2",
            purchaser: '采员1',
            amount: "234.98",
            changeAmount: "234.00",
            reconciliationBillStatus:1,
            checkTime: "2021-03-25 12:39:54",
            generateStatus:1,
          },
             {
                 id: '2',
                 reconciliationBillCode: "123456222",
                 purchaseCode: "wwww222",
                 orderTime: "2021-03-22 12:39:54",
                 supplierName: "供应商22，供应商23",
                 purchaser: '采2',
                 amount: "234.98",
                 changeAmount: "234.00",
                 reconciliationBillStatus:2,
                 checkTime: "2021-03-23 12:39:54",
                 generateStatus:1,

             },
             {
                 id: '3',
                 reconciliationBillCode: "123456333",
                 purchaseCode: "wwww333",
                 orderTime: "2021-03-25 12:39:54",
                 supplierName: "供应商1，供应商2",
                 purchaser: '采购员3',
                 amount: "234.98",
                 changeAmount: "234.00",
                 reconciliationBillStatus:2,
                 checkTime: "2021-03-25 12:39:54",
                 generateStatus:2,
             },
        ],
        total: '4',
      },
      msg: "一切ok",
    },
  },

    // 生成账单
    {
        url: "reconciliationBill/generatePayableBill",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "生成账单成功",
        },
    },

    // 反对账
    {
        url: "reconciliationBill/objection",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "反对账成功",
        },
    },

    //  采购对账单详情查询
    {
        url: "reconciliationBill/detail",
        method: ["GET"],
        body: {
            code: 0,
            data:{
                id: '2',
                reconciliationBillCode: '12334444',
                purchaser: "采购员",
                planDeliveryTime: "2022-13-13 11:12:12",
                createTime: "2022-13-13 11:12:12",
                purchaseCode: '12334444',
                source: '手动',
                purchaseType: '采购类型',
                reconciliationBillStatus: '未对账',
                remark:'备注',
                receiveProduct: [
                        {
                            receiveTransportCode:'001',
                            totalReceivedCountThisTime: 5,
                            totalReceivedAmountThisTime: '100',
                            receiveTransportTime: '2022-13-13 11:12:12',
                            product:[
                                {
                                    id: '001',
                                    productImg: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                                    productName: '西瓜1',
                                    unitName: '斤',
                                    receivedCountThisTime: 5,
                                    receivedAmountThisTime: '100',
                                    remark:'233e3',
                                },
                                {
                                    id: '002',
                                    productImg: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                                    productName: '西瓜2',
                                    unitName: '斤',
                                    receivedCountThisTime: 5,
                                    receivedAmountThisTime: '100',
                                    remark:'233e3',
                                },
                            ]
                        },
                        {
                            receiveTransportCode:'002',
                            totalReceivedCountThisTime: 5,
                            totalReceivedAmountThisTime: '100',
                            receiveTransportTime: '2022-13-13 11:12:12',
                            product:[
                                {
                                    id: '003',
                                    productImg: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                                    productName: '西瓜3',
                                    unitName: '斤',
                                    receivedCountThisTime: 5,
                                    receivedAmountThisTime: '100',
                                    remark:'233e3',
                                },
                                {
                                    id: '004',
                                    productImg: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                                    productName: '西瓜4',
                                    unitName: '斤',
                                    receivedCountThisTime: 5,
                                    receivedAmountThisTime: '100',
                                    remark:'233e3',
                                },
                            ]
                        },
                ],
            },
            msg: "一切ok",
        },
    },

    // 对账
    {
        url: "reconciliationBill/check",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "对账成功",
        },
    },


]);
