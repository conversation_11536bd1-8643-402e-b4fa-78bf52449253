<template>
  <div
    class="image-viewer-wrapper"
    v-if="isVisible && urlList.length > 0"
  >
    <el-image-viewer
      :url-list="urlList"
      @close="close"
      :teleported="true"
    />
  </div>
</template>

<script lang="ts" setup>
import API from "@/core/api/file";

const { t } = useI18n();
const props = defineProps({
  /**
   *  显示隐藏
   */
  visible: {
    type: Boolean,
    default: false,
  },
  /**
   * 图片数组 支持对象数组和JSON字符串
   */
  imagesList: {
    type: [Array, String],
    default: () => "",
  },
});
const state = reactive({
  urlList: [],
}) as any;

const { urlList } = toRefs(state);

const emit = defineEmits(["update:visible"]);

const isVisible = computed({
  get: () => {
    return props.visible;
  },
  set: (val: any) => {
    close();
  },
});

/**
 * 关闭
 */
function close() {
  emit("update:visible", false);
  reset();
}

/**
 * 重置
 */
function reset() {
  urlList.value = [];
}

/**
 * 处理数据
 * @param urls
 */
function processData(urls: any) {
  if (Array.isArray(urls)) {
    // urls 已经是JSON对象数组不处理
    if (!isJsonObjectArray(urls)) {
      // 如果不是对象数组，需要转换成对象数组，兼容 ["http://1.jpg","http://2.jpg"] 数组格式,只支持 http:开头的公开链接
      urls = urlsToObjectArray(urls);
    }
  } else if (isJson(urls)) {
    // urls 是 JSON 字符串，需要转换成数组,
    urls = JSON.parse(urls);
  } else if (typeof urls === "string" && isHttp(urls)) {
    // 如果字符串链接，需要转换成对象数组，兼容 "http://1.jpg,http://2.jpg" 单个或多个格式,只支持 http:开头的公开链接
    urls = urls?.split(",");
    urls = urlsToObjectArray(urls);
  } else {
    close();
    return ElMessage.error(t("components.parameterError"));
  }
  getSignatureUrl(urls);
}

/**
 * 获取图片签名
 * @param urls
 */
function getSignatureUrl(urls: any) {
  const isPrivate = urls.some((item: any) => !isHttp(item.fileName)); //是否私有链接

  if (isPrivate) {
    API.previewFileNew(urls).then((res: any) => {
      urlList.value = res.urls?.map((item: any) => {
        return item.url;
      });
    });
  } else {
    urlList.value = urls?.map((item: any) => {
      return item.fileName;
    });
  }
}

/**
 * 转换为对象数组
 * @param urls
 * @returns {*[]}
 */
function urlsToObjectArray(urls: any) {
  return urls.map((url: string) => ({
    fileName: url,
  }));
}

/**
 * 判断是否是有效Json字符串
 * @param str
 */
function isJson(str: any) {
  try {
    const parsed = JSON.parse(str);
    return parsed && typeof parsed === "object";
  } catch (e) {
    return false;
  }
}

/**
 * 判断是否是http
 * @param val
 * @returns {boolean}
 */
function isHttp(val: string) {
  let reg = new RegExp(/^(https?:|http?:)/);
  return reg.test(val);
}

/**
 * 判断是否数组对象
 * @param val
 * @returns {boolean}
 */

function isJsonObjectArray(val: any) {
  return (
    Array.isArray(val) &&
    val.every(
      (item: any) =>
        typeof item === "object" && item !== null && !Array.isArray(item)
    )
  );
}

watch(
  () => [isVisible.value, props.imagesList],
  ([isVisible, imagesList]) => {
      if (isVisible){
          processData(imagesList);
      }

  },
  { deep: true, immediate: true }
);
</script>

<style lang="scss">
.el-image-viewer__wrapper {
  .el-image-viewer__canvas {
    width: 500px;
    margin: 0 auto;
  }
}
</style>
