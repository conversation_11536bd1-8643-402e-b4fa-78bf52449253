import request from "@/core/utils/request";


class UserAPI {
    static queryUserListPage(queryParams: UserPageQuery) {
        return request({
            url: "/supply-base/users/page",
            method: "get",
            data: queryParams,
        });
    }
}

export default UserAPI;

/** 登录用户信息 */
export interface UserInfo {
    /** 用户ID */
    userId?: number;

    /** 用户名 */
    username?: string;

    /** 昵称 */
    nickname?: string;

    /** 头像URL */
    avatar?: string;

    /** 角色 */
    roles: string[];

    /** 权限 */
    perms: string[];
}

/**
 * 用户分页查询对象
 */
export interface UserPageQuery extends PageQuery {
    /** 搜索关键字 */
    keywords?: string;

    /** 用户状态 */
    status?: number;

    /** 部门ID */
    deptId?: number;

    /** 开始时间 */
    createTimeRange?: [string, string];
}

/** 用户分页对象 */
export interface UserPageVO {
    /** 用户头像URL */
    avatar?: string;
    /** 创建时间 */
    createTime?: Date;
    /** 部门名称 */
    deptName?: string;
    /** 用户邮箱 */
    email?: string;
    /** 性别 */
    genderLabel?: string;
    /** 用户ID */
    id?: number;
    /** 手机号 */
    mobile?: string;
    /** 用户昵称 */
    nickname?: string;
    /** 角色名称，多个使用英文逗号(,)分割 */
    roleNames?: string;
    /** 用户状态(1:启用;0:禁用) */
    status?: number;
    /** 用户名 */
    username?: string;
}

/** 用户表单类型 */
export interface UserForm {
    /** 用户头像 */
    avatar?: string;
    /** 部门ID */
    deptId?: number;
    /** 邮箱 */
    email?: string;
    /** 性别 */
    gender?: number;
    /** 用户ID */
    id?: number;
    /** 手机号 */
    mobile?: string;
    /** 昵称 */
    nickname?: string;
    /** 角色ID集合 */
    roleIds?: number[];
    /** 用户状态(1:正常;0:禁用) */
    status?: number;
    /** 用户名 */
    username?: string;
}
