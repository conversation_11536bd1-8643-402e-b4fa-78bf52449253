export default {
  quickWarehousing: {
    label: {
      receiptNoticeCode: "Receipt Notice Code",
      themeDesc: "Theme Description", 
      receiptType: "Receipt Type",
      status: "Status",
      sourceOrderCode: "Source Order Code",
      queryType: "Query Type",
      plannedDeliveryTime: "Planned Delivery Time",
      orderSyncTime: "Order Sync Time",
      createTime: "Create Time",
      createUserName: "Creator",
      warehouseName: "Warehouse",
      entryOperator: "Entry Operator",
      entryTime: "Entry Time",
      to: "To",
      startTime: "Start Time",
      endTime: "End Time",
      today: "Today",
      yesterday: "Yesterday",
      weekday: "Last 7 Days",
      initial: "Initial",
      received: "Received",
      draft: "Draft",
      purchaseInventory: "Purchase Inventory",
      returnStorage: "Return Storage",
    },
    button: {
      addBtn: "Add",
      goWarehousing: "Go Warehousing",
      complete: "Complete",
      search: "Search",
      reset: "Reset",
      edit: "Edit",
      delete: "Delete",
      print: "Print",
      detail: "Detail",
    },
    title: {
      quickWarehousingManagement: "Quick Warehousing Management",
      addQuickWarehousing: "Add Quick Warehousing",
      editQuickWarehousing: "Edit Quick Warehousing",
      quickWarehousingDetail: "Quick Warehousing Detail",
    },
    message: {
      deleteTips: "Are you sure to delete this quick warehousing order?",
      deleteSuccess: "Delete successful!",
      deleteFail: "Delete failed!",
      deleteCancel: "Delete cancelled!",
      completeTips: "Are you sure to complete this quick warehousing order?",
      completeSuccess: "Operation successful",
      completeFail: "Operation failed",
      addSuccess: "Add successful",
      editSuccess: "Edit successful",
    },
    placeholder: {
      inputTips: "Please input",
      selectTips: "Please select",
    },
  },
}; 