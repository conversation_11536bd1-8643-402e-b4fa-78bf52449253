<template>
  <div class="app-container">
    <div class="page-title">
      <el-page-header @back="handleBack">
        <template #content>
          <span class="cursor-pointer mr8px">{{ pageTitle }}</span>
        </template>
      </el-page-header>
    </div>

    <el-form ref="formRef" :model="formData" label-width="140px" class="page-content">
      <!-- 基本信息 -->
      <div class="title-label">
        <div class="title-line"></div>
        <div class="title-content">{{ t('quickWarehousing.label.basicInformation') }}</div>
      </div>
      <el-row>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.receiptNoticeCode')+''" prop="receiptNoticeCode">
            {{ formData.receiptNoticeCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.receiptType')+''" prop="receiptType">
            {{ filterReceiptTypeLabel(formData.receiptType) }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.themeDesc')+''" prop="themeDesc">
            {{ formData.themeDesc }}
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.sourceOrderCode')+''" prop="sourceOrderCode">
            {{ formData.sourceOrderCode }}
          </el-form-item>
        </el-col>
        <!-- 供应商 supplierName，供应商地址，供应商联系人，供应商联系电话 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.supplier')+''" prop="supplierName">
            {{ formData.supplierName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.supplierAddress')+''" prop="supplierAddress">
            <span class="encryptBox">
              <span v-if="formData.supplierAddressShow">
                {{ formData.supplierAddressFormat }}
                <el-icon
                  v-if="formData.supplierFullAddress"
                  @click="formData.supplierAddressShow = false"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="[
                    'wms:storeManagement:quickWarehousing:eye',
                  ]"
                >
                  <component :is="formData.supplierAddressShow ? 'View' : ''" />
                </el-icon>
              </span>
              <span v-else>
                {{ formData.supplierFullAddress ? formData.supplierFullAddress : "-" }}
              </span>
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.supplierContact')+''" prop="contactPerson">
            {{ formData.supplierNameShow ? (formData.contactPerson ? formData.contactPerson : '-') : encryptName(formData.contactPerson) }}
            <el-icon
              v-if="formData.contactPerson"
              @click="formData.contactPerson ? getSupplierRealName() : ''"
              class="encryptBox-icon"
              color="#762ADB "
              size="16"
              v-hasPermEye="['wms:storeManagement:quickWarehousing:eye']"
            >
              <component :is="formData.supplierNameShow ? '' : 'View'" />
            </el-icon>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.supplierPhone')+''" prop="mobile">
            <span class="encryptBox">
              <span v-if="formData.countryAreaCode">
                {{ formData.countryAreaCode + " " }}
              </span>
              <span
                v-if="
                  formData.mobile && formData.mobile.length <= 4
                "
              >
                {{ formData.mobile }}
              </span>
              <span
                v-else-if="
                  formData.mobile && formData.mobile.length > 4
                "
              >
                {{ formData.mobile }}
                <el-icon
                  v-if="formData.mobile"
                  @click="formData.supplierMobilePhoneShow ? getSupplierRealPhone() : ''"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="[
                    'wms:storeManagement:quickWarehousing:eye',
                  ]"
                >
                  <component :is="formData.supplierMobilePhoneShow ? 'View' : ''" />
                </el-icon>
              </span>
              <span v-else>-</span>
            </span>
          </el-form-item>
        </el-col>
        <!-- 客户，客户地址，客户联系人，客户联系电话 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.customer')+''" prop="customerName">
            {{ formData.customerName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.customerAddress')+''" prop="customerAddress">
            <span class="encryptBox">
              <span v-if="formData.customerAddressShow">
                {{ formData.customerAddressFormat }}
                <el-icon
                  v-if="formData.customerFullAddress"
                  @click="formData.customerAddressShow = false"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="[
                    'wms:storeManagement:quickWarehousing:eye',
                  ]"
                >
                  <component :is="formData.customerAddressShow ? 'View' : ''" />
                </el-icon>
              </span>
              <span v-else>
                {{ formData.customerFullAddress ? formData.customerFullAddress : "-" }}
              </span>
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.customerContact')+''" prop="contactPerson">
            {{ formData.customerNameShow ? (formData.customerContactPerson ? formData.customerContactPerson : '-') : encryptName(formData.customerContactPerson) }}
            <el-icon
              v-if="formData.customerContactPerson"
              @click="formData.customerContactPerson ? getCustomerRealName() : ''"
              class="encryptBox-icon"
              color="#762ADB "
              size="16"
              v-hasPermEye="['wms:storeManagement:quickWarehousing:eye']"
            >
              <component :is="formData.customerNameShow ? '' : 'View'" />
            </el-icon>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.customerPhone')+''" prop="customerMobile">
            <span class="encryptBox">
              <span v-if="formData.customerCountryAreaCode">
                {{ formData.customerCountryAreaCode + " " }}
              </span>
              <span
                v-if="
                  formData.customerMobile && formData.customerMobile.length <= 4
                "
              >
                {{ formData.customerMobile }}
              </span>
              <span
                v-else-if="
                  formData.customerMobile && formData.customerMobile.length > 4
                "
              >
                {{ formData.customerMobile }}
                <el-icon
                  v-if="formData.customerMobile"
                  @click="formData.customerMobilePhoneShow ? getCustomerRealPhone() : ''"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="[
                    'wms:storeManagement:quickWarehousing:eye',
                  ]"
                >
                  <component :is="formData.customerMobilePhoneShow ? 'View' : ''" />
                </el-icon>
              </span>
              <span v-else>-</span>
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.plannedDeliveryTime')+''" prop="plannedDeliveryTime">
            {{ parseTime(formData.plannedDeliveryTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </el-form-item>
        </el-col>
        <!-- 合同名称，合同编号，合同分类 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.contractName')+''" prop="contractName">
            {{ formData.contractName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.contractCode')+''" prop="contractCode">
            {{ formData.contractCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.contractType')+''" prop="contractType">
            {{ receiptType === 4 ? '-' : filterContractTypeLabel(formData.contractType) }}
          </el-form-item>
        </el-col>
        <!-- 业务员，原单据号，商品计划总量，商品计划总转换量 -->
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.salesman')+''" prop="salesmanName">
            {{ formData.salesmanName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.originOrderCode')+''" prop="originOrderCode">
            {{ formData.originOrderCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.expectedQty')+''" prop="expectedQty">
            {{ formData.expectedQty }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('quickWarehousing.label.expectedWeight')+''" prop="expectedWeight">
            {{ formData.expectedWeight }}
          </el-form-item>
        </el-col>
        <!-- 来源单据备注-->
        <el-col :span="24">
          <el-form-item :label="t('quickWarehousing.label.sourceDocumentRemark')+''" prop="remark">
            {{ formData.remark }}
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 商品明细 -->
      <section class="flex align-center justify-between">
        <div class="title-label">
          <div class="title-line"></div>
          <div class="title-content">{{ t('quickWarehousing.label.detailList') }}</div>
        </div>
      </section>
      <el-tabs v-model="activeTab">
        <el-tab-pane :label="t('quickWarehousing.label.productDetails')" name="productList">
          <el-table :data="formData.productList" border style="width: 100%" class="mb-4">
            <el-table-column type="index" :label="t('quickWarehousing.label.serialNumber')" width="60" align="center" fixed="left" />
            <el-table-column :label="t('quickWarehousing.label.productInfo')" width="200" fixed="left">
              <template #default="scope">
                <div>
                  <div>{{ t('quickWarehousing.label.productCode') }}：{{ scope.row.productCode }}</div>
                  <div>{{ scope.row.productName }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="t('quickWarehousing.label.productCategory')" width="120">
              <template #default="scope">
                <span>{{ scope.row.fullCategoryName }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="t('quickWarehousing.label.productSpecs')" width="100">
              <template #default="scope">
                <span>{{ scope.row.productSpecs }}</span>
              </template>
            </el-table-column>
            <!-- 计划量 -->
            <el-table-column :label="t('quickWarehousing.label.planQty')" min-width="180" prop="productExpectQty">
              <template #default="scope">
                <span>{{ scope.row.productExpectQty }}{{ scope.row.productUnitName }}</span>
              </template>
            </el-table-column>
            <!-- 计划转换量 expectedWeight-->
            <el-table-column :label="t('quickWarehousing.label.expectedWeight2')" width="180" prop="expectedWeight">
              <template #default="scope">
                <span>{{ scope.row.expectedWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
            <!-- 成本单价 costUnitPrice-->
            <el-table-column :label="t('quickWarehousing.label.costUnitPrice')" width="180" prop="costUnitPrice">
              <template #default="scope">
                <span>{{ scope.row.costUnitPrice }}</span>
                <span v-if="!isNull(scope.row.costUnitPrice)">{{'元/'+scope.row.productUnitName }}</span>
              </template>
            </el-table-column>
            <!-- 成本金额 costAmount-->
            <el-table-column :label="t('quickWarehousing.label.costAmount')" width="180" prop="costAmount"></el-table-column>
            <!-- 入库量 inWarehouseQty-->
            <el-table-column :label="t('quickWarehousing.label.inWarehouseQty')" width="180" prop="inWarehouseQty"></el-table-column>
            <!-- 入库转换量 inWarehouseWeight-->
            <el-table-column :label="t('quickWarehousing.label.inWarehouseWeight')" width="180" prop="inWarehouseWeight"></el-table-column>

          </el-table>
        </el-tab-pane>
        <el-tab-pane :label="t('quickWarehousing.label.inWarehouseDetails')" name="qualityInspectionList">
          <section v-for="(item, index) in formData.entryOrderList" :key="index" class="inwarehouse-header">
            <el-form type="inline" :model="item">
              <el-row>
                <el-col :span="4">
                  <el-form-item :label="t('quickWarehousing.label.inWarehouseCode')+'：'" prop="entryOrderCode">
                    {{ item.entryOrderCode || '-' }}
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item :label="t('quickWarehousing.label.weighbridgeNo')+'：'" prop="weighbridgeNo">
                    {{ item.weighbridgeNo || '-' }}
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="" prop="weighbridgeNoAttachment">
                    <!-- {{ item.weighbridgeNoAttachment || '-'  }} -->
                    <el-badge :value="filterAttachmentFile(item.weighbridgeNoAttachment).length" :offset="[5, 10]">
                      <el-button type="primary" link @click="handleShowFile(item.weighbridgeNoAttachment)">
                        附件
                      </el-button>
                    </el-badge>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item :label="t('quickWarehousing.label.vehicleNo')+'：'" prop="vehicleNo">
                    {{ item.vehicleNo || '-' }}
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item :label="t('quickWarehousing.label.entryOrderRemark')+'：'" prop="remark">
                    {{ item.remark || '-' }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <el-table :data="item.productList" border style="width: 100%" class="mb-4" show-summary :span-method="(params) => getSpanMethod(params, index)">
              <el-table-column type="index" :label="t('quickWarehousing.label.serialNumber')" width="60" align="center" fixed="left" />
              <el-table-column :label="t('quickWarehousing.label.productInfo')" width="200" fixed="left">
                <template #default="scope">
                  <div v-if="!scope.row.parentId">
                    <div>{{t('quickWarehousing.label.productCode')}}：{{ scope.row.productCode }}</div>
                    <div>{{ scope.row.productName }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="t('quickWarehousing.label.productCategory')" width="120">
                <template #default="scope">
                  <span v-if="!scope.row.parentId">{{ scope.row.fullCategoryName }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="t('quickWarehousing.label.productSpecs')" width="100">
                <template #default="scope">
                  <span v-if="!scope.row.parentId">{{ scope.row.productSpecs }}</span>
                </template>
              </el-table-column>
              <!-- 成本单价(RMB) costUnitPrice-->
              <el-table-column :label="t('quickWarehousing.label.costUnitPrice')" width="180" prop="costUnitPrice">
                <template #default="scope">
                  <span>{{ scope.row.costUnitPrice }}</span>
                  <span v-if="!isNull(scope.row.costUnitPrice)">{{'元/'+scope.row.productUnitName }}</span>
                </template>
              </el-table-column>
              <!-- 成本金额 (RMB) costAmount-->
              <el-table-column :label="t('quickWarehousing.label.costAmount2')" width="180" prop="costAmount"></el-table-column>
              <!-- 入库单价(RMB)  unitPrice-->
              <el-table-column :label="t('quickWarehousing.label.unitPrice')" width="180" prop="unitPrice">
                <template #default="scope">
                  <span>{{ scope.row.unitPrice }}</span>
                  <span v-if="!isNull(scope.row.unitPrice)">{{ scope.row.pricingScheme === 0 ? '元/'+scope.row.productUnitName : '元/'+scope.row.conversionRelSecondUnitName }}</span>
                </template>
              </el-table-column>
              <!-- 入库量 productInventoryQty-->
              <el-table-column :label="t('quickWarehousing.label.inWarehouseQty')" width="180" prop="productActualQty">
                <template #default="scope">
                  <span>{{ scope.row.productActualQty }}</span>
                  <span v-if="!isNull(scope.row.productActualQty)">{{ scope.row.productUnitName }}</span>
                </template>
              </el-table-column>
              <!-- 入库转换量 productInventoryWeight-->
              <el-table-column :label="t('quickWarehousing.label.inWarehouseWeight')" width="180" prop="productActualWeight">
                <template #default="scope">
                  <span>{{ scope.row.productActualWeight }}</span>
                  <span v-if="!isNull(scope.row.productActualWeight)">{{ scope.row.conversionRelSecondUnitName }}</span>
                </template>
              </el-table-column>
              <!-- 入库金额(RMB)  amount-->
              <el-table-column :label="t('quickWarehousing.label.inWarehouseAmount')" width="180" prop="amount"></el-table-column>
              <!-- 入库库区 warehouseAreaName|warehouseName-->
              <el-table-column :label="t('quickWarehousing.label.inWarehouseArea')" width="180" prop="warehouseAreaName">
                <template #default="scope">
                  <span>{{ scope.row.warehouseAreaName }}|{{ scope.row.warehouseName }}</span>
                </template>
              </el-table-column>
              <!--运费-->
              <el-table-column :label="t('quickWarehousing.label.freight')" prop="shippingAmount">
              </el-table-column>
              <!-- 产地 -->
              <el-table-column :label="t('quickWarehousing.label.originPlace')" prop="originPlaceName"></el-table-column>
              <!-- 单据号 -->
              <el-table-column :label="t('quickWarehousing.label.billNo')" prop="billNo"></el-table-column>
              <!-- 商品包装 productPackaging-->
              <el-table-column :label="t('quickWarehousing.label.productPackaging')" width="180" prop="productPackaging"></el-table-column>
              <!--备注-->
              <el-table-column :label="t('quickWarehousing.label.productRemark')" prop="remark"></el-table-column>
              <el-table-column :label="t('quickWarehousing.label.qualityInspectionInfo')" width="100" align="center" fixed="right">
                <template #default="scope">
                  <el-button type="primary" link @click="handleQualityCheck(scope.row)">
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </section>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <!-- 底部按钮 -->
    <div class="fixed-footer">
      <el-button @click="handleCancel">{{ t('quickWarehousing.button.close') }}</el-button>
    </div>

    <!-- 质检商品弹窗 -->
    <QualityCheckDialog v-model:visible="qualityDialog.visible" :product-data="qualityDialog.productData"
      @confirm="onQualityConfirm" type="show" />
    <UploadDialog ref="uploadDialogRef" v-model:visible="uploadDialog.visible" :showUploadBtn="false" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import QuickWarehousingAPI, { QuickWarehousingFormData } from "@/modules/wms/api/quickWarehousing";
import QualityCheckDialog from "./components/QualityCheckDialog.vue";
import CommonAPI from "@/modules/wms/api/common";
import { parseTime } from "@/core/utils/index.js";
import UploadDialog from "./components/uploadDialog.vue";
import { useQuickWarehousing } from "./composables";
import WarehouseEntryNoticeAPI from "@/modules/wms/api/warehouseEntryNotice";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const { filterReceiptTypeLabel, filterContractTypeLabel, filterAddress } = useQuickWarehousing();
defineOptions({
  name: "QuickWarehousingAdd",
  inheritAttrs: false,
});
const route = useRoute();
const router = useRouter();
const receiptType = Number(route.query.receiptType);
const pageTitle = computed(() => {
  return "详情";
});
const activeTab = ref("productList"); // 当前选中的tab商品明细

// 表单数据
const formData = reactive<QuickWarehousingFormData>({
  // 基本信息
  receiptNoticeCode: "", // 入库通知单号
  receiptType: 4, // 入库类型:1:采购入库、2:退货入库、3:调拨入库、4:直接入库、5:地头入库
  themeDesc: "", // 主题描述
  sourceOrderCode: "", // 来源单号
  // purchaseSalesPerson: "", // 采购/销售员
  plannedDeliveryTime: "", // 计划交货时间
  // status: 0, // 状态:0:草稿、1:初始 2：完结 3:取消

  // 供应商信息
  supplierCode: "", // 供应商编码
  supplierName: "", // 供应商名称

  // 客户信息
  // customerCode: "", // 客户编码
  // customerName: "", // 客户名称

  // 地址信息
  countryId: "", // 国家id
  countryName: "", // 国家名称
  countryAreaCode: "", // 国家区域代码
  provinceId: "", // 省份id
  provinceName: "", // 省名称
  cityId: "", // 城市id
  cityName: "", // 市名称
  districtId: "", // 区县id
  districtName: "", // 区县名称
  address: "", // 详细地址

  // 联系信息
  contactPerson: "", // 联系人
  mobile: "", // 手机号

  // 合同信息
  contractCode: "", // 合同编码
  contractName: "", // 合同名称
  contractType: undefined, // 合同类型

  // 业务信息
  salesmanId: "", // 业务员ID
  salesmanName: "", // 业务员姓名
  weighbridgeNo: "", // 榜单编号
  weighbridgeNoAttachment: "", // 榜单编号附件
  vehicleNo: "", // 入库车号
  remark: "", // 备注
  // 商品明细
  productList: [
    /*  {
       id: undefined,
       productCode: "", // 商品编码
       productName: "", // 商品名称
       productSpecs: "", // 规格
       productPackaging: "", // 商品包装
       
       // 分类信息
       firstCategoryId: undefined, // 一级分类id
       firstCategoryName: "", // 一级分类名称
       secondCategoryId: undefined, // 二级分类id
       secondCategoryName: "", // 二级分类名称
       thirdCategoryId: undefined, // 三级分类id
       thirdCategoryName: "", // 三级分类名称
       
       // 单位信息
       productUnitId: undefined, // 商品采购单位id
       productUnitName: "", // 商品采购单位名称
       conversionRelSecondUnitId: undefined, // 商品换算关系第二个值的单位id
       conversionRelSecondUnitName: "", // 商品换算关系第二个值的单位名称
       
       // 数量信息
       productExpectQty: 0, // 商品数量(期望)
       actualInQty: 0, // 本次实际入库数量
       inWarehouseQty: 0, // 已入库数量
       
       // 重量信息
       expectedWeight: 0, // 计划重量
       actualInWeight: 0, // 本次实际入库重量
       inWarehouseWeight: 0, // 已入库重量
       receivedWeight: 0, // 实收总重量
       weight: 0, // 重量（KG）
       
       // 价格信息
       unitPrice: 0, // 入库单价
       amount: 0, // 入库金额
       costUnitPrice: 0, // 成本单价
       costAmount: 0, // 成本金额
       currency: "CNY", // 交易币种
       
       // 仓库信息
       warehouseCode: "", // 仓库编码
       warehouseName: "", // 仓库名称
       warehouseAreaCode: "", // 仓库库区编码
       warehouseAreaName: "", // 仓库库区名称
       
       // 其他信息
       receiptNoticeCode: "", // 入库通知单号
       receiptNoticeId: undefined, // 入库通知单id
       parentId: undefined, // 父id
       isSku: 0, // 是否sku->1:是;0:否
       isDiscreteUnit: 0, // 一级单位增减->1:开启；0:关闭
       pricingScheme: 0, // 计价模式->0:一级单位;1:二级单位
       remark: "", // 备注
     }, */
  ],
  // 入库明细
  entryOrderList: [
    /* {
      productList: [ // 主单商品列表
        
      ]
    } */
  ]
});

// 基础数据
const businessPersonList = ref<any[]>([]);
const supplierList = ref<any[]>([]);
const warehouseAreaList = ref<any[]>([]);

const uploadDialogRef = ref();
const uploadDialog = reactive({
  visible: false,
  fileList: [],
  type: 'show',
});

const isNull = (value: any) => {
  return value === null || value === undefined || value === '';
}
const handleShowFile = (fileList: any) => {
  if (!fileList) return;
  const filesList = JSON.parse(fileList);
  if (filesList.length > 0) {
    uploadDialog.visible = true;
    nextTick(() => {
      uploadDialogRef.value.setFormData(JSON.parse(fileList));
    });
  }
};
const qualityDialog = reactive({
  visible: false,
  productData: {},
});

// 计算属性
/* const totalQuantity = computed(() => {
  return formData.productList?.reduce((sum: number, item: any) => sum + (item.actualInQty || 0), 0) || 0;
});

const totalAmount = computed(() => {
  return formData.productList?.reduce(
    (sum: number, item: any) => sum + (item.unitPrice || 0) * (item.actualInQty || 0),
    0
  ) || 0;
});

const totalConvertedQuantity = computed(() => {
  return formData.productList?.reduce(
    (sum: number, item: any) => sum + (item.actualInWeight || 0),
    0
  ) || 0;
}); */

// 方法
const handleBack = () => {
  router.back();
};

// 计算入库转换量
/* const calculateInWeight = (row: any) => {
  CommonAPI.convertProductUnit({
    convertUnitTypeEnum: 'FIRST_TO_SECOND',
    originalValue: row.actualInQty,
    productCode: row.productCode,
  }).then((res) => {
    row.actualInWeight = res.convertedValue;
    calculateAmount(row);
  });
}; */

// 计算入库金额
/* const calculateAmount = (row: any) => {
  CommonAPI.calculateAmount({
    convertedQty: row.actualInWeight,
    productCode: row.productCode,
    qty: row.actualInQty,
    unitPrice: row.unitPrice,
  }).then((res) => {
    row.amount = res.amount;
  });
}; */

// 质检弹窗
const handleQualityCheck = (row: any) => {
  qualityDialog.visible = true;
  QuickWarehousingAPI.queryProductQualityInspectionList({
    receivingOrderCode: row.receivingOrderCode,
    productCode: row.productCode,
  }).then((res) => {
    qualityDialog.productData = {
      ...res,
      productCode: row.productCode,
      productName: row.productName,
      productSpecs: row.productSpecs,
      actualInQty: row.productActualQty,
      actualInWeight: row.productActualWeight,
      productUnitName: row.productUnitName,
      conversionRelSecondUnitName: row.conversionRelSecondUnitName,
    } || 
    {
      productCode: row.productCode,
      productName: row.productName,
      productSpecs: row.productSpecs,
      actualInQty: row.productActualQty,
      actualInWeight: row.productActualWeight,
      productUnitName: row.productUnitName,
      conversionRelSecondUnitName: row.conversionRelSecondUnitName,
      // deductionAmount: row.deductionAmount,
      // deductionDesc: row.deductionDesc,
    };
  });
};

const onQualityConfirm = (data: any) => {
  // 处理质检结果
  const { index, ...qualityData } = data;
  // 根据productCode判断是否存在，存在则更新，不存在则新增  
  const existingIndex = formData.qualityInspectionList?.findIndex((item: any) => item.productCode === qualityData.productCode);
  if (existingIndex !== undefined && existingIndex >= 0) {
    formData.qualityInspectionList[existingIndex] = qualityData;
  } else {
    formData.qualityInspectionList?.push(qualityData);
  }
};

// 表格行合并方法
const getSpanMethod = (params: any, orderIndex: number) => {
  const { row, column, rowIndex, columnIndex } = params;
  
  // 质检信息列的索引是最后一列，索引为 12（从0开始计数）
  // 序号(0) + 商品信息(1) + 商品分类(2) + 规格(3) + 成本单价(4) + 成本金额(5) + 入库单价(6) + 入库量(7) + 入库转换量(8) + 入库金额(9) + 入库库区(10) + 商品包装(11) + 质检信息(12)
  if (columnIndex === 12) {
    const productList = formData.entryOrderList[orderIndex].productList;
    const currentProductCode = row.productCode;
    
    // 找到相同productCode的第一行
    let firstIndex = -1;
    let count = 0;
    
    for (let i = 0; i < productList.length; i++) {
      if (productList[i].productCode === currentProductCode) {
        if (firstIndex === -1) {
          firstIndex = i;
        }
        count++;
      }
    }
    
    // 如果当前行是相同productCode的第一行，则合并
    if (rowIndex === firstIndex) {
      return {
        rowspan: count,
        colspan: 1
      };
    } else {
      // 其他同productCode的行不显示
      return {
        rowspan: 0,
        colspan: 0
      };
    }
  }
  
  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  };
};

const handleCancel = () => {
  router.back();
};

/* const handleSaveDraft = async () => {
  try {
    saving.value = true;
    formData.status = 0; // 草稿状态
    await QuickWarehousingAPI.addDraft(formData);
    ElMessage.success("保存成功");
    router.back();
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
    saving.value = false;
  }
}; */
const filterAttachmentFile = (fileList: any) => {
  if (!fileList) return [];
  try {
    const filesList = JSON.parse(fileList);
    return filesList.map((item: any) => item.url);
  } catch (error) {
    return [];
  }
};
// 获取基础数据
const getBaseData = async () => {
  try {
    // 获取仓库库区列表
    const areaData: any = await QuickWarehousingAPI.getWarehouseAreaList();
    warehouseAreaList.value = areaData;

    // 获取业务员列表
    const businessPersonData: any = await QuickWarehousingAPI.querySalesPersonUser();
    businessPersonList.value = businessPersonData || [];

    // 获取供应商列表
    const supplierData: any = await QuickWarehousingAPI.getSupplierListAll({});
    supplierList.value = supplierData || [];
  } catch (error) {
    console.error("获取基础数据失败:", error);
  }
};

/* 姓名加密 */
function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

// 获取真实供应商联系人
function getSupplierRealName() {
  formData.supplierNameShow = true;
}

// 获取真实客户联系人
function getCustomerRealName() {
  formData.customerNameShow = true;
}

// 获取真实供应商联系电话
function getSupplierRealPhone() {
  WarehouseEntryNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      formData.mobile = data.mobile;
      formData.supplierMobilePhoneShow = false;
    })
    .finally(() => {});
}

// 获取真实客户联系电话
function getCustomerRealPhone() {
  WarehouseEntryNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      formData.customerMobile = data.customerMobile;
      formData.customerMobilePhoneShow = false;
    })
    .finally(() => {});
}

function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}

function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

// 获取详情数据
const getDetailData = async () => {
  if (route.query.id) {
    try {
      const data: any = await QuickWarehousingAPI.queryDetailDisplay({ id: route.query.id });
      Object.assign(formData, data);
      formData.qualityInspectionList = data.qualityInspectionList || [];
      
      // 初始化加密相关状态
      formData.supplierMobilePhoneShow = true;
      formData.customerMobilePhoneShow = true;
      
      // 构建完整地址
      formData.supplierFullAddress = (formData.countryName && formData.provinceName && formData.cityName && formData.districtName && formData.address) 
        ? `${formData.countryName}${formData.provinceName}${formData.cityName}${formData.districtName}${formData.address}` 
        : '-';
      
      formData.customerFullAddress = (formData.customerCountryName && formData.customerProvinceName && formData.customerCityName && formData.customerDistrictName && formData.customerAddress) 
        ? `${formData.customerCountryName}${formData.customerProvinceName}${formData.customerCityName}${formData.customerDistrictName}${formData.customerAddress}` 
        : '-';
      
      // 联系人加密处理
      if (formData.contactPerson && formData.contactPerson.length > 1) {
        formData.supplierNameShow = false;
      } else {
        formData.supplierNameShow = true;
      }
      
      if (formData.customerContactPerson && formData.customerContactPerson.length > 1) {
        formData.customerNameShow = false;
      } else {
        formData.customerNameShow = true;
      }
      
      // 地址加密处理
      if (formData.supplierFullAddress && containsNumber(formData.supplierFullAddress)) {
        formData.supplierAddressShow = true;
        formData.supplierAddressFormat = replaceNumbersWithAsterisk(formData.supplierFullAddress);
      } else {
        formData.supplierAddressShow = false;
      }
      
      if (formData.customerFullAddress && containsNumber(formData.customerFullAddress)) {
        formData.customerAddressShow = true;
        formData.customerAddressFormat = replaceNumbersWithAsterisk(formData.customerFullAddress);
      } else {
        formData.customerAddressShow = false;
      }
      
    } catch (error) {
      console.error("获取详情失败:", error);
    }
  }
};

onMounted(() => {
  getBaseData();
  getDetailData();
});
</script>

<style scoped lang="scss">
:deep(.el-form-item){
  margin-bottom: 0px;
}
:deep(.el-form-item__label){
  background: #F4F6FA;
  box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
  font-weight: 400;
  font-size: 12px;
  color: #90979E;
}
:deep(.el-form-item__content){
  padding-left: 8px;
  background: #FFFFFF;
  box-shadow: inset -1px -1px 0px 0px #E5E7F3, inset 0px 1px 0px 0px #E5E7F3;
}
.align-center {
  align-items: center;
}

.inwarehouse-header {
  background: #F6F0FF;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #151719;
  line-height: 20px;
}

/* :deep(.el-form-item--default) {
  margin-bottom: 12px;
} */

:deep(.inwarehouse-header .el-form-item--default) {
  margin-bottom: 0;
}

.title-label {
  display: flex;
  align-items: center;
  margin: 32px 0 20px 0;

  .title-line {
    width: 4px;
    height: 16px;
    background: var(--el-color-primary);
    margin-right: 8px;
  }

  .title-content {
    font-size: 16px;
    font-weight: 500;
    color: #151719;
  }

  .flex-1 {
    flex: 1;
  }

  .add-product-btn {
    margin-left: auto;
  }
}

.page-title {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7f3;
  font-size: 18px;
  font-weight: 500;
  color: #151719;

  .mr8px {
    margin-right: 8px;
  }
}

.title-block {
  padding: 6px 20px;
  background: #F6F0FF;
  box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #151719;
  line-height: 20px;
}

.fixed-footer {
  /* position: fixed;
  bottom: 0;
  right: 0;
  left: 0; */
  width: 100%;
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 16px 24px;
  text-align: right;
  z-index: 100;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-container {
  background: #ffffff;
  border-radius: 4px;
}

.page-content {
  padding: 0px 24px 0 24px;
}

.encryptBox {
  display: flex;
  align-items: center;
  
  .encryptBox-icon {
    margin-left: 8px;
    cursor: pointer;
  }
}
</style>
