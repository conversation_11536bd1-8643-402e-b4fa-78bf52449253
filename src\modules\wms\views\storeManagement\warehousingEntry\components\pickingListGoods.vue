<template>
  <el-drawer :model-value="visible" :title="$t('warehouseEntry.title.addProduct')" :close-on-click-modal="false"
    :size="1050" @close="close">
    <section style="display: flex;flex-direction: column;height: 100%;">
      <el-form :model="goodsQueryParams" ref="goodsQueryFormRef">
        <el-row :gutter="20">
          <el-col :span="9">
            <!-- 分拣单号 -->
            <el-form-item :label="'分拣单号'" prop="sortingCode">
              <el-input v-model="goodsQueryParams.sortingCode"
                :placeholder="'请输入分拣单号'" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <!-- 商品名称 -->
            <el-form-item :label="$t('warehouseEntry.label.productNameCopy')" prop="productCode">
              <el-input v-model="goodsQueryParams.productCode"
                :placeholder="$t('warehouseEntry.placeholder.productInputTips')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <el-button type="primary" @click="queryGoodList">
              {{ $t("common.search") }}
            </el-button>
            <el-button @click="handleResetQuery">
              {{ $t("common.reset") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <div class="selected-info">
        已选
        <span class="selected-count">{{ multipleSelection.length }}</span>
        个商品
      </div>

      <el-table style="min-height: 0;flex-grow: 1;" ref="dataTableRef" v-loading="productLoading"
        highlight-current-row stripe :data="productList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- 商品信息 -->
        <el-table-column :label="$t('warehouseEntry.label.goodsInfor')" min-width="160">
          <template #default="scope">
            <div class="product-code">
              {{ $t("warehouseEntry.label.productCode") }}：
              {{ scope.row.productCode }}
            </div>
            <div class="product-name">
              {{ scope.row.productName }}
            </div>
          </template>
        </el-table-column>
        <!-- 规格 productSpec-->
        <el-table-column :label="$t('warehouseEntry.label.productSpecs')" prop="productSpec" min-width="80" />
        <!-- 单位 productUnit-->
        <el-table-column :label="$t('warehouseEntry.label.productUnit')" prop="productUnit" min-width="80" />
        <!-- 分拣单号 sortingCode-->
        <el-table-column :label="$t('warehouseEntry.label.sortingCode')" prop="sortingCode" min-width="80" />
        <!-- 分拣后数量 sortingAfterQty-->
        <el-table-column :label="$t('warehouseEntry.label.sortingAfterQty')" prop="sortingAfterQty" min-width="90" />
        <!-- 分拣后重量 sortingAfterWeight-->
        <el-table-column :label="$t('warehouseEntry.label.sortingAfterWeight')" prop="sortingAfterWeight" min-width="90" />
        <!-- 待入库数量 sortingAfterQty - inWarehouseQty  -->
        <el-table-column :label="$t('warehouseEntry.label.inWarehouseQty')" prop="inWarehouseQty" min-width="120">
          <template #default="scope">
            {{ scope.row.sortingAfterQty - scope.row.inWarehouseQty }}
          </template>
        </el-table-column>
        <!-- 待入库重量(Kg) sortingAfterWeight - inWarehouseWeight -->
        <el-table-column :label="$t('warehouseEntry.label.inWarehouseWeight')" prop="inWarehouseWeight" min-width="120">
          <template #default="scope">
            {{ (scope.row.sortingAfterWeight - scope.row.inWarehouseWeight) < 0 ? 0 :   (scope.row.sortingAfterWeight - scope.row.inWarehouseWeight)?.toFixed(3)}}
          </template>
        </el-table-column>
      </el-table>
      <pagination style="flex-grow: 0;height: 120px;" v-if="productListTotal > 0" v-model:total="productListTotal" v-model:page="goodsQueryParams.page"
        v-model:limit="goodsQueryParams.limit" @pagination="queryGoodList" />
    </section>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">
          {{ $t("warehouseEntry.button.cancel") }}
        </el-button>
        <el-button type="primary" :disabled="!multipleSelection.length" @click="submitForm">
          {{ $t("warehouseEntry.button.comfirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import warehouseEntryAPI from "@/modules/wms/api/warehouseEntry";
import warehouseEntryNoticeAPI from "@/modules/wms/api/warehouseEntryNotice";
import type { CascaderProps } from "element-plus";
import CommonAPI from "@/modules/wms/api/common";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();

const cascaderRef = ref();
const goodsQueryFormRef = ref(ElForm);
const productListTotal = ref(0);
const goodsQueryParams = reactive({
  page: 1,
  limit: 20,
  productCode: "",
  sortingCode: "",
});

const productLoading = ref(false);
const productList = ref([]);
const multipleSelection = ref([]);
const categoryList = ref([]);

const queryTypeList = ref([
  {
    key: 1,
    value: t("warehouseEntry.label.receivingOrderCode"),
  },
  {
    key: 2,
    value: t("warehouseEntry.label.sourceOrderCode"),
  },
  {
    key: 3,
    value: t("warehouseEntry.label.receiptNoticeCode"),
  },
]);

const propsCategory: CascaderProps = {
  multiple: true,
  checkStrictly: false,
  value: "id",
  label: "categoryName",
  children: "children",
};

// const propsCategory: CascaderProps = {
//   lazy: true,
//   checkStrictly: false,
//   multiple: true,
//   async lazyLoad(node, resolve) {
//     const { level, data } = node;
//     let arr: any = [];
//     if (level == 0) {
//       arr = await getCategoryList(null);
//     } else {
//       arr = await getCategoryList(data.value);
//     }
//     const nodes = arr.map((item: any) => ({
//       value: item.id,
//       label: item.categoryName,
//       parentId: item.parentId,
//       leaf: level >= 2,
//     }));
//     resolve(nodes);
//   },
// };

function getCategoryList() {
  CommonAPI.queryCategoryTreeList({}).then((data: any) => {
    categoryList.value = data;
  });
  // return new Promise((resolve, reject) => {
  //   let params: any = {};
  //   if (id) {
  //     params.id = id;
  //   }
  //   warehouseEntryNoticeAPI
  //     .queryCategoryTreeList(params)
  //     .then((data: any) => {
  //       resolve(data);
  //     })
  //     .catch((error) => {
  //       reject(error);
  //     });
  // });
}

function handleChange() {
  let valueArr = goodsQueryParams.category;
  let firstCategoryIds: any = [];
  let secondCategoryIds: any = [];
  let thirdCategoryIds: any = [];

  if (valueArr && valueArr.length > 0) {
    valueArr.forEach((item: any) => {
      if (item[0]) {
        firstCategoryIds.push(item[0]);
      }
      if (item[1]) {
        secondCategoryIds.push(item[1]);
      }
      if (item[2]) {
        thirdCategoryIds.push(item[2]);
      }
    });
  }

  goodsQueryParams.firstCategoryIds = Array.from(new Set(firstCategoryIds));
  goodsQueryParams.secondCategoryIds = Array.from(new Set(secondCategoryIds));
  goodsQueryParams.thirdCategoryIds = Array.from(new Set(thirdCategoryIds));
  // goodsQueryParams.thirdCategoryIds = [];
  // if (cascaderRef.value.getCheckedNodes()) {
  //   const checkedNodes = cascaderRef.value.getCheckedNodes();
  //   checkedNodes.forEach((node: any) => {
  //     if (node.level == 3) {
  //       goodsQueryParams.thirdCategoryIds.push(node.value);
  //     }
  //   });
  // }
}

function close() {
  emit("update:visible", false);
  reset();
}

function reset() {
  goodsQueryFormRef.value.clearValidate();
  goodsQueryFormRef.value.resetFields();
  goodsQueryParams.page = 1;
  goodsQueryParams.limit = 20;
  
}

function handleSelectionChange(val: any) {
  multipleSelection.value = val;
  let sortingCode = multipleSelection.value[0]?.sortingCode;
  let isSameSortingCode = multipleSelection.value.every((item: any) => item.sortingCode == sortingCode);
  if (!isSameSortingCode) {
    ElMessage.error("同时只能入库一个分拣单下的商品，请重新选择");
    return;
  }
}

function queryGoodList() {
  productLoading.value = true;
  let params: any = {
    ...goodsQueryParams,
  };
  delete params.category;
  warehouseEntryAPI
    .queryPickingResultProductPageList(params)
    .then((res: any) => {
      productList.value = res.records;
      productListTotal.value = parseInt(res.total) || 0;
    })
    .finally(() => {
      productLoading.value = false;
    });
}

function handleResetQuery() {
  goodsQueryParams.page = 1;
  goodsQueryParams.limit = 20;
  goodsQueryParams.productCode = "";
  goodsQueryParams.sortingCode = "";
  queryGoodList();
}

function submitForm() {
  for(let item of multipleSelection.value){
    item.productInventoryQty = item.inWarehouseQty;
    item.productInventoryWeight = item.inWarehouseWeight;
  }
  emit("onSubmit", multipleSelection.value);
  close();
}

// 初始化
// onMounted(() => {
//   getCategoryList();
// });

defineExpose({
  queryGoodList,
  getCategoryList,
});

// Add a default export
// export default { name: 'AddGoods' };
</script>

<style scoped lang="scss">
.input_style {
  width: calc(100% - 180px);
}

.product-code {
  color: #90979e;
}

.product-name {
  color: #151719;
}

.selected-info {
  margin: 16px 0;
  color: #606266;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #52585f;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  margin-bottom: 10px;

  .selected-count {
    color: #762adb;
  }
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.product-info {
  display: flex;
  align-items: center;

  img {
    width: 64px;
    height: 64px;
    border-radius: 4px;
  }

  .product-info-content {
    margin-left: 10px;

    .product-info-content-item {
      &.code {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #90979e;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      &.name {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #151719;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>
