import { defineMockWms } from "./base";

export default defineMockWms([
  {
    url: "qualityInspectionRecords/queryPageList",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
             {
                 id: '1',
                 inspectionCode: '质检单号',
                 inspectionType: 0,
                 receivingOrderCode: '收运单号2',
                 warehouseAreaCode: '1',
                 warehouseAreaName: '库区1',
                 inspectionResultType: 0,
                 inspectionTime: 1740473528000,
                 inspector: "质检人",
                 remark: "卑职呃呃",
                 createType: 1,
                 workOrderNum:'1111',
                 createUser:'1111',
                 createTime: 1740473528000,
                 status: 1,
             },
             {
                 id: '2',
                 inspectionCode: '出库通知单号',
                 inspectionType: 1,
                 receivingOrderCode: '收运单号2',
                 warehouseAreaCode: '2',
                 warehouseAreaName: '库区2',
                 inspectionResultType:1,
                 inspectionTime: 1740473528000,
                 inspector: "质检人",
                 remark: "卑职呃呃",
                 createType: 1,
                 workOrderNum:'',
                 createUser:'ccxc',
                 createTime: '1740473528000',
                 status:2,
             },
        ],
        total: '2',
      },
      msg: "一切ok",
    },
  },


  // 删除质检单
  {
      url: "qualityInspectionRecords/delete",
      method: ["POST"],
      body:{
          code: 0,
          data: null,
          msg: "删除成功",
      },
  },

    // 质检单详情
    {
        url: "qualityInspectionRecords/queryDetail",
        method: ["POST"],
        body: {
            code: 0,
            data: {
                    id: '1',
                    inspectionCode: '质检单号',
                    inspectionType: 2,
                    receivingOrderCode: '收运单号2',
                    warehouseAreaCode: '1',
                    warehouseAreaName: '库区1',
                    inspectionResultType: 0,
                    inspectionTime: 1740473528000,
                    inspector: "质检人",
                    remark: "卑职呃呃",
                    createType: 1,
                    workOrderNum:'1111',
                    createUser:'1111',
                    createTime: 1740473528000,
                    status: 1,
                    productList:[
                        {
                            id: '1',
                            productCode: '001',
                            productName: '商品名称',
                            productSpec: '商品规格',
                            productUnitName: '斤',
                            totalStockQty: 100,
                            productActualQty: 80,
                            abnormalQty: 20,
                            abnormalCode: '2,22',
                            abnormalCause: '异常原因名称2,异常原因名称22',
                            inspectionStatus: 1,
                            imagesUrls: '',
                        },
                    ]
            },
            msg: "一切ok",
        },
    },


    // 暂存，提交质检单(添加)
    {
        url: "qualityInspectionRecords/add",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "暂存成功",
        },
    },

    // 暂存，提交质检单(编辑)
    {
        url: "qualityInspectionRecords/edit",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "暂存成功",
        },
    },

]);
