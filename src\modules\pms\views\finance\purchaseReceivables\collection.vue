<script setup lang="ts">
import { usePurchaseReceivable } from "./composables/usePurchaseReceivable";
import { submitPayment } from "@pms/api/purchaseReceivables";
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
import { parseTime } from "@/core/utils";
import { useTagsViewStore } from "@/core/store";
import UploadMultiple from "@/core/components/Upload/UploadMultiple.vue";
import { useCommon } from "@/modules/pms/composables/common";
import { useI18n } from "vue-i18n";

// Form data
const formData = ref({
  currencyCode: "", // 交易币种
  exchangeRate: null, // 汇率
  id: null, // 主键id
  receivableMethod: "", // 收款方式
  receivableRemark: "", // 收款备注
  receivableTime: "", // 收款时间
  receivableVoucher: null, // 收款凭证
});

const { currencyFilter } = useCommon();
const parseTimeHandler = (time: string, format: string) => {
  return parseTime(time, format);
};

const { t } = useI18n();

// Add form rules
const rules = {
  receivableMethod: [
    {
      required: true,
      message: t("purchaseReceivables.validation.methodRequired"),
      trigger: "change",
    },
  ],
  receivableAmount: [
    {
      required: true,
      message: t("purchaseReceivables.validation.amountRequired"),
      trigger: "blur",
    },
  ],
  currencyCode: [
    {
      required: true,
      message: t("purchaseReceivables.validation.currencyRequired"),
      trigger: "change",
    },
  ],
  receivableTime: [
    {
      required: true,
      message: t("purchaseReceivables.validation.timeRequired"),
      trigger: "change",
    },
  ],
  receivableVoucher: [
    {
      required: true,
      message: t("purchaseReceivables.validation.voucherRequired"),
      trigger: "change",
    },
  ],
  exchangeRate: [
    {
      validator: (rule, value, callback) => {
        // 如果值为空或未定义，直接通过验证（非必填）
        if (value === undefined || value === null || value === "") {
          callback();
          return;
        }

        // 尝试转换为数字
        const numValue = parseFloat(value);

        // 检查是否为数字且大于0
        if (isNaN(numValue)) {
          callback(new Error(t("purchaseReceivables.validation.validNumber")));
          return;
        }

        if (numValue <= 0) {
          callback(
            new Error(t("purchaseReceivables.validation.rateGreaterThanZero"))
          );
          return;
        }

        // 检查小数位数是否不超过4位
        if (!/^\d+(\.\d{1,4})?$/.test(value.toString())) {
          callback(
            new Error(t("purchaseReceivables.validation.rateDecimalLimit"))
          );
          return;
        }

        // 通过验证
        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
};

const formRef = ref();

const route = useRoute();
const router = useRouter();

const { basicInfo, refundDetails, totalAmount, loadDetailData } =
  usePurchaseReceivable();

// Add payment method options
const paymentMethodOptions = [
  { value: 1, label: t("purchaseReceivables.paymentMethod.transfer") },
  { value: 2, label: t("purchaseReceivables.paymentMethod.cash") },
  { value: 3, label: t("purchaseReceivables.paymentMethod.alipay") },
  { value: 4, label: t("purchaseReceivables.paymentMethod.wechat") },
];

// Add currency options
const currencyOptions = [
  { value: "CNY", label: t("purchaseReceivables.currency.CNY") },
  { value: "USD", label: t("purchaseReceivables.currency.USD") },
];

const findCurrency = (code: string) => {
  const currency = currencyOptions.find((item) => item.value === code);
  return currency ? currency.label : "";
};
// Update handleSubmit
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const imgs = JSON.stringify(formData.value.receivableVoucher);
        await submitPayment({
          ...formData.value,
          receivableVoucher: imgs,
          id: route.query.id as string,
        });
        ElMessage.success(t("purchaseReceivables.message.paymentSuccess"));
        router.back();
      } catch (error) {
        ElMessage.error(t("purchaseReceivables.message.paymentFailed"));
        console.error("Failed to submit payment:", error);
      }
    }
  });
};

// Update handleFileUpload
const handleFileUpload = async (val) => {
  formData.value.receivableVoucher = val;

  /*try {
    const response = await commonUpload(file.raw, "image");
    console.log(response);
    formData.value.receivableVoucher = response.url;
  } catch (error) {
    ElMessage.error("上传凭证失败");
    console.error("Failed to upload voucher:", error);
  }*/
};

const currencyStr = ref();
// Load data when component mounts
onMounted(async () => {
  await loadDetailData(route.query.id as string);
  formData.value.receivableAmount = basicInfo.value?.receivableAmount;
  formData.value.currencyCode = basicInfo.value?.currencyCode;
  currencyStr.value = findCurrency(formData.value.currencyCode);
});

const tagsViewStore = useTagsViewStore();
const navigateAndCloseCurrentTab = (path: string) => {
  // 先进行路由跳转
  router.push(path);

  // 等待路由跳转完成后关闭当前标签页
  nextTick(() => {
    // 删除当前页面的标签
    tagsViewStore.delView(route);
  });
};
const handleCancel = () => {
  // Implementation for cancellation\
  navigateAndCloseCurrentTab("/pms/finance/purchaseReceivables");
  /*router.push({
    path: "/pms/finance/purchaseReceivables",
  });*/
};

const fileType = ["jpg", "jpeg", "png", "pdf"];
</script>

<template>
  <div class="collection-container">
    <el-card>
      <div class="card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="handleCancel"
        />
        <span class="code" @click="handleCancel">
          {{ $t("purchaseReceivables.detail.billNumber") }}：{{
            basicInfo.purchaseReceivableCode
          }}
        </span>
        <!-- <span class="status">{{ inqueryDetail.status }}</span> -->
      </div>
      <!-- Basic Information Section -->
      <div class="basic-info">
        <div class="card-title mb-20px">{{ t("purchaseReceivables.detail.basicInfo") }}</div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="form-label inline-block mr-8px">{{ t("purchaseReceivables.detail.billNumber") }}：</div>
            <div class="form-text inline-block">
              {{ basicInfo.purchaseReceivableCode }}
            </div>
          </el-col>
          <el-col :span="6">
            <div class="form-label inline-block mr-8px">{{ t("purchaseReceivables.detail.purchaseOrderNo") }}：</div>
            <div class="form-text inline-block">
              {{ basicInfo.purchaseBillCode }}
            </div>
          </el-col>
          <el-col :span="6" v-if="!!basicInfo.supplierName">
            <div class="form-label inline-block mr-8px">{{ t("purchaseReceivables.detail.supplier") }}：</div>
            <div class="form-text inline-block">
              {{ basicInfo.supplierName }}
            </div>
          </el-col>
          <el-col :span="6">
            <div class="form-label inline-block mr-8px">{{ t("purchaseReceivables.purchaseBuyer") }}：</div>
            <div class="form-text inline-block">
              {{ basicInfo.purchaseUser }}
            </div>
          </el-col>
          <el-col :span="6">
            <div class="form-label inline-block mr-8px">{{ t("purchaseReceivables.detail.generationTime") }}：</div>
            <div class="form-text inline-block">
              {{
                parseTimeHandler(
                  basicInfo.createTime,
                  "{y}-{m}-{d} {h}:{i}:{s}"
                )
              }}
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- Refund Details Table -->
      <div class="refund-details">
        <div class="card-title mb-20px">{{ t("purchaseReceivables.collection.refundDocuments") }}</div>
        <el-table :data="refundDetails" border>
          <el-table-column prop="sequence" :label="t('purchaseReceivables.collection.sequence')" />
          <el-table-column prop="refundOrder" :label="t('purchaseReceivables.detail.refundOrder')" />
          <el-table-column prop="amount" :label="t('purchaseReceivables.receivableAmount')">
            <template #default="{ row }">
              {{ currencyFilter(basicInfo.currencyCode)
              }}{{ row.amount }}
            </template>
          </el-table-column>
          <el-table-column prop="applicationDate" :label="t('purchaseReceivables.collection.applicationDate')">
            <template #default="{ row }">
              {{ parseTimeHandler(row.applicationDate, "{y}-{m}-{d}") }}
            </template>
          </el-table-column>
          <el-table-column prop="remarks" :label="t('purchaseReceivables.detail.remarks')" />
        </el-table>
        <div class="total-row">
          <span>{{ t("purchaseReceivables.collection.total") }}：</span>
          <span class="total-amount">
            {{ currencyFilter(basicInfo.currencyCode)
            }}{{ basicInfo.receivableAmount }}
          </span>
        </div>
      </div>

      <!-- Payment Form -->
      <div class="payment-form">
        <div class="card-title mb-20px">{{ t("purchaseReceivables.detail.settlement") }}</div>
        <el-form ref="formRef" :model="formData" :rules="rules">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="t('purchaseReceivables.collection.paymentMethod')" prop="receivableMethod">
                <!-- 收款方式：1->转账，2->现金，3->线下支付宝, 4->线下微信 -->
                <el-select
                  v-model="formData.receivableMethod"
                  :placeholder="t('purchaseReceivables.pleaseSelect')"
                >
                  <el-option
                    v-for="option in paymentMethodOptions"
                    :key="option.value"
                    :value="option.value"
                    :label="option.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('purchaseReceivables.collection.paymentAmount')" prop="receivableAmount">
                <el-input
                  v-model="formData.receivableAmount"
                  disabled
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 交易币种：CNY->人民币，USD->美元 -->
              <el-form-item :label="t('purchaseReceivables.collection.currency')">
                <!--                <el-select v-if="!formData.currencyCode" v-model="formData.currencyCode" placeholder="请选择">
                  <el-option
                    v-for="option in currencyOptions"
                    :key="option.value"
                    :value="option.value"
                    :label="option.label"
                  />
                </el-select>-->
                <el-input disabled v-model="currencyStr" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('purchaseReceivables.collection.exchangeRate')" prop="exchangeRate">
                <el-input
                  v-model="formData.exchangeRate"
                  type="number"
                  :placeholder="t('purchaseReceivables.pleaseSelect')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('purchaseReceivables.collection.paymentTime')" prop="receivableTime">
                <el-date-picker
                  v-model="formData.receivableTime"
                  type="datetime"
                  :placeholder="t('purchaseReceivables.pleaseSelect')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="t('purchaseReceivables.collection.paymentVoucher')" prop="receivableVoucher">
                <UploadMultiple
                  @update:model-value="handleFileUpload"
                  ref="detailPicsRef"
                  v-model="formData.attachment"
                  :limit="1"
                  :formRef="formRef"
                  :fileSize="10"
                  :fileType="fileType"
                  class="modify-multipleUpload"
                  name="detailPic"
                  listType="text"
                />
                <!--                <el-upload
                  class="upload-box"
                  action="#"
                  :auto-upload="false"
                  :on-change="handleFileUpload"
                  accept=".jpg,.jpeg,.png"
                  :limit="1"
                  :on-exceed="(files) => ElMessage.warning('只能上传一个文件')"
                >
                  <el-button type="primary">上传附件</el-button>
                  <template #tip>
                    <div class="upload-tip form-text">
                      只能上传 jpg/jpeg/png 文件，且不超过10M
                    </div>
                  </template>
                </el-upload>-->
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="t('purchaseReceivables.collection.remarks')" class="receivableRemark">
                <el-input
                  v-model="formData.receivableRemark"
                  type="textarea"
                  :rows="4"
                  :placeholder="t('purchaseReceivables.pleaseEnter')"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div class="form-actions">
                <el-button type="primary" @click="handleCancel">{{ t("purchaseReceivables.cancel") }}</el-button>
                <el-button type="primary" @click="handleSubmit">{{ t("purchaseReceivables.receipt") }}</el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.card-header {
  padding: 0px 10px 20px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #e5e7f3;
  border-radius: 4px 4px 0px 0px;
  cursor: pointer;
  .code {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
  .back-btn {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  .status {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
}
.collection-container {
  .refund-details {
    .total-row {
      margin-top: 20px;
      margin-bottom: 40px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585f;
      line-height: 20px;
      text-align: left;
      font-style: normal;

      .total-amount {
        color: #c00c1d;
      }
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    :deep(.el-form-item) {
      margin-bottom: 0;

      &.remarks {
        grid-column: span 3;
      }

      &.form-actions {
        grid-column: span 3;
        justify-content: flex-end;
        margin-top: 24px;
      }
    }
  }
}
.form-actions {
  text-align: right;
}
</style>
