import request from "@/core/utils/request";

const BASE_URL = '/supply-biz-common/paymentMethods'
class PaymentMethodAPI {

  /** 分页查询 */
  static getPageList(queryParams?: PaymentMethodPageQuery){
    return request<any, PageResult<PaymentMethodResp[]>>({
      url:`${BASE_URL}/queryPageList`,
      method:'post',
      data:queryParams
    })
  }
  /** 新增保存 */
  static addSave(data?: PaymentMethodForm){
    return request({
      url:`${BASE_URL}/add`,
      method:'post',
      data:data
    })
  }
  /** 修改保存 */
  static editSave(data?: PaymentMethodForm){
    return request({
      url:`${BASE_URL}/edit`,
      method:'post',
      data:data
    })
  }
  /** 删除 */
  static deleteConfirm(id: number){
    return request({
      url:`${BASE_URL}/delete`,
      method:'post',
      params: { id },
    })
  }
  /** 批量启用 */
  static enableBatch(ids: number[]){
    return request({
      url:`${BASE_URL}/enableStatusBatch`,
      method:'post',
      data:ids
    })
  }
  /** 批量禁用 */
  static disableBatch(ids: number[]){
    return request({
      url:`${BASE_URL}/disableStatusBatch`,
      method:'post',
      data:ids
    })
  }
}
export default PaymentMethodAPI

/** 分页请求参数 */
export interface PaymentMethodPageQuery extends PageQuery{
  /** 支付方式 */
  methodName?: string;
}

/** 分页响应参数 */
export interface PaymentMethodResp {
  /** ID */
  id?: number;
  /** 支付方式 */
  methodName?: string;
  /** 状态 */
  enableStatus?: number;
  /** 更新时间 */
  updateTime?: string;
}

/** 新增/修改参数 */
export interface PaymentMethodForm {
  /** 支付方式 */
  methodName?: string;
  /** 状态 */
  enableStatus?: number;
}

/** 启禁用状态枚举 */
export enum EnableStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
}
