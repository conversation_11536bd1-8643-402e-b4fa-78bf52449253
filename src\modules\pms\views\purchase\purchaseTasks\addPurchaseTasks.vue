<template>
  <div class="app-container">
    <div class="addPurchaseTask">
      <div class="page-title">
        <div @click="handleClose" class="display_block">
          <el-icon><Back /></el-icon>
          <span>{{ $t("purchaseTasks.button.addPurchaseTask") }}</span>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          ref="formRef"
          :rules="rules"
          label-width="110px"
          label-position="right"
        >
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("purchaseTasks.label.basicInformation") }}
            </div>
          </div>
          <div>
            <el-row>
              <el-col :span="8">
                <!-- 仓库 -->
                <el-form-item
                  :label="$t('purchaseTasks.label.warehouse')"
                  prop="warehouseId"
                >
                  <el-select
                    v-model="form.warehouseId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    @change="handleWarehouseChange"
                    :disabled="productAllList && productAllList.length > 0"
                  >
                    <el-option
                      v-for="item in warehouseList"
                      :key="item.warehouseId"
                      :label="item.warehouseName"
                      :value="item.warehouseId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 期望收货日期 -->
                <el-form-item
                  :label="$t('purchaseTasks.label.expectedDeliveryDate')"
                  prop="expectedDeliveryDate"
                >
                  <el-date-picker
                    v-model="form.expectedDeliveryDate"
                    type="date"
                    :disabled="productAllList && productAllList.length > 0"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleDateChange"
                    :disabled-date="disabledDate"
                    class="date_style"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" class="flex-style">
                <el-form-item>
                  <el-button
                    type="primary"
                    :disabled="productAllList && productAllList.length > 0"
                    @click="queryProductAll"
                  >
                    {{ $t("common.confirm") }}
                  </el-button>
                  <el-button @click="handleResetQueryProductAll">
                    {{ $t("common.reset") }}
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable" v-if="isShowProduct">
            <div class="title-line"></div>
            <div class="title-content flex-x-between w-full">
              <div>{{ $t("purchaseTasks.label.goodsInformation") }}</div>
              <div>
                <el-button
                  type="primary"
                  key="primary"
                  text
                  @click="addProduct()"
                >
                  {{ $t("purchaseTasks.button.addGood") }}
                </el-button>
              </div>
            </div>
          </div>
          <div v-if="isShowProduct">
            <el-form-item label-width="0">
              <el-table :data="form.purchaseTaskProductInfoDTOList" stripe>
                <el-table-column
                  :label="$t('purchaseTasks.label.goodsInformation')"
                  prop="contractName"
                  min-width="200"
                >
                  <template #default="scope">
                    <!-- <div class="product-div">
                      <div class="picture">
                        <img :src="scope.row.productImg" alt="" />
                      </div>
                      <div class="product">
                        <div class="product-code">
                          {{ t("purchaseTasks.label.productCode") }}：{{ scope.row.productCode }}
                        </div>
                        <div class="product-name">{{ scope.row.productName }}</div>
                      </div>
                    </div> -->
                    <div class="product-div">
                      <div class="picture">
                        <img :src="scope.row.productImg" alt="" />
                      </div>
                      <div class="product">
                        <div>
                          <span class="product-key">
                            {{ $t("purchaseTasks.label.productCode") }}：
                          </span>
                          <span class="product-value">
                            {{ scope.row.productCode }}
                          </span>
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('purchaseTasks.label.goodsClassification')"
                  prop="productCategoryFullName"
                />
                <el-table-column
                  :label="'*' + $t('purchaseTasks.label.quantity')"
                  prop="purchaseCount"
                  width="200"
                >
                  <template #default="scope">
                    <el-form-item
                      class="mt15px"
                      label-width="0px"
                      :prop="
                        'purchaseTaskProductInfoDTOList.' +
                        scope.$index +
                        '.purchaseCount'
                      "
                      :rules="
                        dialog.visible
                          ? []
                          : [
                              {
                                required: true,
                                message: t('purchaseTasks.rules.purchaseCount'),
                                trigger: 'blur',
                              },
                              {
                                pattern:
                                  /(^0(\.\d{1,2})$)|(^[1-9]\d{0,10}(\.\d{1,2})?$)/,
                                message: t(
                                  'purchaseTasks.rules.purchaseCountFomart'
                                ),
                                trigger: 'blur',
                              },
                            ]
                      "
                    >
                      <el-input
                        v-model="scope.row.purchaseCount"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('purchaseTasks.label.unit')"
                  prop="unitName"
                />
                <el-table-column
                  :label="'*' + $t('purchaseTasks.label.supplier')"
                  prop="defaultSupplierId"
                  width="200"
                >
                  <template #default="scope">
                    <el-form-item
                      class="unsetPadding"
                      label-width="0px"
                      :inline-message="true"
                      :prop="
                        'purchaseTaskProductInfoDTOList.' +
                        scope.$index +
                        '.defaultSupplierId'
                      "
                      :rules="
                        dialog.visible
                          ? []
                          : [
                              {
                                required: true,
                                message: $t('purchaseTasks.rules.supplier'),
                                trigger: 'change',
                              },
                            ]
                      "
                    >
                      <el-select
                        v-model="scope.row.defaultSupplierId"
                        :placeholder="$t('common.placeholder.selectTips')"
                        @change="
                          (e: any) =>
                            purchaseTasksChange(e, scope.$index, scope.row)
                        "
                      >
                        <el-option
                          v-for="item in scope.row
                            .completedPricingSupplierListVOList"
                          :key="item.supplierId"
                          :label="item.supplierName"
                          :value="item.supplierId"
                        />
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('purchaseTasks.label.purchaseUnitPrice')"
                  prop="purchasePrice"
                  align="right"
                >
                  <template #default="scope">
                    <span v-if="scope.row.purchasePrice">
                      <span v-if="scope.row.currency == 'CNY'">￥</span>
                      <span v-if="scope.row.currency == 'USD'">$</span>
                    </span>

                    {{ scope.row.purchasePrice }}
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('purchaseTasks.label.purchaser')"
                  prop="purchaserName"
                />
                <el-table-column :label="$t('common.handle')">
                  <template #default="scope">
                    <el-button
                      type="danger"
                      size="small"
                      link
                      @click="handleDelete(scope.$index)"
                    >
                      {{ $t("common.delete") }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div
        class="page-footer"
        v-if="productAllList && productAllList.length > 0"
      >
        <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ $t("common.confirm") }}
        </el-button>
      </div>
      <AddProduct
        ref="addProductRef"
        v-model:visible="dialog.visible"
        :title="dialog.title"
        @on-submit="onSubmit"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "AddPurchaseTasks",
  inheritAttrs: false,
});
import PurchaerTasksAPI, {
  PurchaseFrom,
} from "@/modules/pms/api/purchaseTasks";
import WarehouseAPI from "@/modules/pms/api/warehouse";
import PurchaseOrderAPI from "@/modules/pms/api/purchaseOrder";
import supplierAPI from "@/modules/pms/api/supplier";
import AddProduct from "./components/addProduct.vue";
import { convertToTimestamp, parseTime } from "@/core/utils/index.js";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import moment from "moment";
const tagsViewStore = useTagsViewStore();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const formRef = ref(ElForm);
const submitLoading = ref(false);
const loading = ref(false);
const isShowProduct = ref(false);
const addProductRef = ref();
const productTotal = ref(0);
const dialog = reactive({
  title: "",
  visible: false,
});
const form = reactive<PurchaseFrom>({
  expectedDeliveryDate: undefined,
  // id: '',
  purchaseTaskProductInfoDTOList: [],
  warehouseId: "",
  warehouseName: "",
});
const warehouseList = ref([]);
const productAllList = ref([]);
const supplierList = ref([]);
const rules = reactive({
  warehouseId: [
    {
      required: true,
      message: t("purchaseTasks.rules.warehouseTip"),
      trigger: "change",
    },
  ],
  expectedDeliveryDate: [
    {
      required: true,
      message: t("purchaseTasks.rules.expectedDeliveryDateTip"),
      trigger: "change",
    },
  ],
});

const disabledDate = (time: any) => {
  // 获取今天的日期
  const today = new Date();
  today.setHours(0, 0, 0, 0); // 将时间设置为0点，确保比较时不考虑具体时间
  // 比较传入的日期是否小于今天
  return time.getTime() < today.getTime();
};

// function handleVisibleChange(visible: boolean, row:any) {
//   if (visible) { // 当下拉框变为可见时触发数据获取
//     getSupplierList(row); // 调用fetchData方法来获取数据
//   }
// }

// 获取供应商列表
// async function getSupplierList(row:any) {
//   try{
//     let params: any = {
//       expectedDeliveryDate: form.expectedDeliveryDate,
//       warehouseId: form.warehouseId,
//       productId:row.productId
//     }
//     PurchaerTasksAPI
//       .getSupListByParam(params)
//       .then((data: any) => {
//         supplierList.value = data;
//       })
//       .finally(() => {});
//     } catch (error) {
//           console.error('获取数据失败:', error);
//         }
// }

function getWarehouseList() {
  WarehouseAPI.getStorehouseSelect()
    .then((data: any) => {
      warehouseList.value = data;
    })
    .finally(() => {});
}

function handleWarehouseChange(value: any) {
  const selectedOption: any = warehouseList.value.find(
    (option: any) => option.warehouseId === value
  );
  form.warehouseName = selectedOption.warehouseName
    ? selectedOption.warehouseName
    : "";
}

/** 查询所有可选的商品列表 */
function queryProductAll() {
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    // getSupplierList()
    loading.value = true;
    let params: any = {
      chooseType: 4,
      warehouseId: form.warehouseId,
      startExpectedDeliveryDate: form.expectedDeliveryDate.getTime(),
    };
    PurchaseOrderAPI.queryProductAll(params)
      .then((data: any) => {
        productAllList.value = data.records;
        isShowProduct.value = true;
        productTotal.value = parseInt(data.total);
      })
      .finally(() => {
        loading.value = false;
      });
  });
}
// function handleDateChange(value: any) {
//   if (value) {
//     form.expectedDeliveryDate = value.getTime(); // 获取时间戳
//   }
// }

function handleResetQueryProductAll() {
  ElMessageBox.confirm(
    t("purchaseTasks.message.resetQueryProduct"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      formRef.value.resetFields();
      productAllList.value = [];
      form.purchaseTaskProductInfoDTOList = [];
      supplierList.value = [];
      isShowProduct.value = false;
    },
    () => {
      ElMessage.info(t("purchaseTasks.message.resetConcel"));
    }
  );
}

/** 添加商品 */
async function addProduct() {
  await queryProductAll();
  dialog.title = t("purchaseTasks.title.productSpecificationsTitle");
  let data = {
    chooseType: 4,
    warehouseId: form.warehouseId,
    startExpectedDeliveryDate: form.expectedDeliveryDate,
  };
  addProductRef.value.setFormData({
    productAllList: productAllList.value,
    productTotal: productTotal.value,
    queryParams: data,
  });
  addProductRef.value.queryManagerCategoryList();
  dialog.visible = true;
}

const handleClose = async () => {
  await tagsViewStore.delView(route);
  router.go(-1);
};

function onSubmit(data: any) {
  let arr = data.concat(form.purchaseTaskProductInfoDTOList);
  let uniqueArr = [
    ...new Map(arr.map((item: any) => [item.productCode, item])).values(),
  ];
  form.purchaseTaskProductInfoDTOList = uniqueArr;
}

// function purchaseAmountChange(index,val) {
//    if(form.purchaseTaskProductInfoDTOList[index].purchaseCount!==undefined && form.purchaseTaskProductInfoDTOList[index].purchasePrice!==undefined){
//        if(val==1){
//            let planPurchaseAmount = (form.purchaseTaskProductInfoDTOList[index].purchaseCount * form.purchaseTaskProductInfoDTOList[index].purchasePrice).toFixed(2)
//            form.purchaseTaskProductInfoDTOList[index].planPurchaseAmount=planPurchaseAmount
//        }
//        let totalPurchaseCount= '0'
//        let totalPurchaseAmount= '0'
//        form.purchaseTaskProductInfoDTOList.forEach(item=>{
//            item.purchaseCount=item.purchaseCount?parseFloat(item.purchaseCount):0
//            item.planPurchaseAmount=item.planPurchaseAmount?parseFloat(item.planPurchaseAmount):0
//            totalPurchaseCount= (parseFloat(totalPurchaseCount) + item.purchaseCount).toFixed(2)
//            totalPurchaseAmount= (parseFloat(totalPurchaseAmount) + item.planPurchaseAmount).toFixed(2)
//        })
//        form.totalPurchaseCount=totalPurchaseCount
//        form.totalPurchaseAmount=totalPurchaseAmount
//    }
// }

// function supplierChange(index,val){
//   console.log('form.purchaseTaskProductInfoDTOList[index]',form.purchaseTaskProductInfoDTOList[index])
// }

function purchaseTasksChange(val: any, index: any, row: any) {
  getPurchaseUserList(val, index);
  row.completedPricingSupplierListVOList.forEach((item: any) => {
    if (item.supplierId == val) {
      form.purchaseTaskProductInfoDTOList[index].currency = item.currency
        ? item.currency
        : "";
      form.purchaseTaskProductInfoDTOList[index].purchasePrice =
        item.purchasePrice ? item.purchasePrice : 0;
      form.purchaseTaskProductInfoDTOList[index].supplierName =
        item.supplierName ? item.supplierName : "";
    }
  });
}

// 获取采购员
function getPurchaseUserList(val: any, index: any) {
  let params: any = {
    supplierId: val,
  };
  supplierAPI
    .queryPurchaseUserList(params)
    .then((data: any) => {
      form.purchaseTaskProductInfoDTOList[index].purchaserName =
        data.purchaserName ? data.purchaserName : "";
      form.purchaseTaskProductInfoDTOList[index].purchaserId = data.purchaserId
        ? data.purchaserId
        : "";
    })
    .finally(() => {});
}

function handleSubmit() {
  if (
    form.purchaseTaskProductInfoDTOList &&
    form.purchaseTaskProductInfoDTOList.length == 0
  ) {
    return ElMessage.error(t("purchaseTasks.message.addOrEditPurchaseTips"));
  }
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    submitLoading.value = true;
    let params: any = {
      taskSource: 1,
      expectedDeliveryDate: undefined,
      ...form,
    };
    params.expectedDeliveryDate = moment(
      params.expectedDeliveryDate,
      "YYYY-MM-DD"
    )
      .startOf("day")
      .valueOf();
    // params.expectedDeliveryDate = convertToTimestamp(
    //   params.expectedDeliveryDate + " 00:00:00"
    // );

    if (
      params.purchaseTaskProductInfoDTOList &&
      params.purchaseTaskProductInfoDTOList.length > 0
    ) {
      params.purchaseTaskProductInfoDTOList.forEach((item: any) => {
        item.currencyCode = item.currency;
        item.imagesUrls = item.productImg;
        item.purchaseUserId = item.purchaserId;
        item.purchaseUserName = item.purchaserName;
        item.supplierId = item.supplierId
          ? item.supplierId
          : item.defaultSupplierId;
        item.supplierName = item.supplierName
          ? item.supplierName
          : item.defaultSupplierName;
        item.currencyCode = item.currency;

        delete item.completedPricingSupplierListVOList;
      });
    }
    PurchaerTasksAPI.addPurchaseTask(params)
      .then((data) => {
        ElMessage.success(t("purchaseTasks.message.addSucess"));
        router.go(-1);
      })
      .finally(() => {
        submitLoading.value = false;
      });
  });
}
function handleDelete(index: any) {
  form.purchaseTaskProductInfoDTOList.splice(index, 1);
  ElMessage.success(t("purchaseTasks.message.deleteSucess"));
}

onMounted(() => {
  getWarehouseList();
  // getSupplierList();
});
</script>
<style scoped lang="scss">
.addPurchaseTask {
  background: #ffffff;
  border-radius: 4px;
  .flex-style {
    display: flex;
    flex-direction: row-reverse;
  }
  .page-title {
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
      cursor: pointer;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .title-lable {
      padding: 20px 0px 15px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .title-line {
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
      .title-content {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #52585f;
        font-style: normal;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
}
.product-div {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .picture {
    margin-right: 16px;
    img {
      width: 80px;
      height: 80px;
    }
  }
  .product {
    font-family:
      PingFangSC,
      PingFang SC;
    font-style: normal;
    .product-code {
      font-weight: 400;
      font-size: 14px;
      color: #90979e;
    }
    .product-name {
      font-weight: 500;
      font-size: 14px;
      color: #52585f;
    }
  }
  :deep(.unsetPadding .el-form-item__error) {
    padding-top: 0px !important;
  }
}
:deep(.date_style) {
  width: 100% !important;
}
.mt15px {
  margin-bottom: 15px !important;
}
</style>
