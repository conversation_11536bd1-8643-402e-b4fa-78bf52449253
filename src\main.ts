import { createApp } from "vue";
import App from "./App.vue";
import setupPlugins from "@/core/plugins";

// 本地SVG图标
import "virtual:svg-icons-register";

// 样式
import "element-plus/theme-chalk/dark/css-vars.css";
import "@/core/styles/index.scss";
import "@/modules/wms/style/index.scss";
import "uno.css";
import "animate.css";
import Print from 'vue3-print-nb'
import ElTableInfiniteScroll from "el-table-infinite-scroll";
const app = createApp(App);
// 注册插件
app.use(setupPlugins);
app.mount("#app");
app.use(Print)
app.use(ElTableInfiniteScroll);