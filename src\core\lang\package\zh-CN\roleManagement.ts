export default {
  roleManagement: {
    label: {
        roleName: "角色名称",
        status: "状态",
        userCount: "账号数",
        roleDesc: "角色描述",
        roleStatus: "角色状态",
        department: "部门",
        createTime: "创建时间",
        authoritySettings: "权限设置",
        affiliatedDepartment: "所属部门",
    },
    button: {
        addRole: "新建角色",
    },
    title: {
        addRoleTitle: "新建角色",
        editRoleTitle: "编辑角色",
    },
    message: {
        rolesEmptyTips: '角色不能为空',
        deleteNotTips: '此角色处于启用状态，不能删除！',
        deleteTips: "确定删除此角色？",
        deleteConcel: "已取消删除！",
        deleteSucess: "删除成功！",
        deleteFail: "删除失败！",
        disableTips: "确定要禁用此角色吗？",
        enableSucess: "启用成功！",
        enableFail: "启用失败！",
        disableSucess: "禁用成功！",
        disableFail: "禁用失败！",
        addSucess: "新建成功",
        editSucess: "编辑成功",
    },
    rules: {
        roleName: "请输入角色名称",
        roleNameFomart: "可输入1到20位中文、英文和数字",
        roleDescription: "请输入角色描述",
    },
  },
};
