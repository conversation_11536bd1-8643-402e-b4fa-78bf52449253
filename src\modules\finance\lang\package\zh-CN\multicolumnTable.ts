export default {
  multicolumnTable: {
    label: {
      accountingPeriod: "会计期间：",
      start: "开始日期",
      end: "结束日期",
      accountingSubject: "会计科目：",
      pleaseSelect: "请选择",
      paixu: "排序依据：",
      voucherNoSort: "显示最明细科目",
      voucherDateSort: "只显示下级科目",
      showauxiliaryAccounting: "显示明细栏余额"
    },
    table: {
      serialNo: "序号",
      date: "日期",
      voucherNo: "凭证字号",
      account: "科目",
      summary: "摘要",
      debit: "借方",
      credit: "贷方",
      direction: "方向",
      balance: "余额",
      cashonHand: "库存现金",
      creditBalance: "贷",
      debitBalance: "借"
    }
  }
}