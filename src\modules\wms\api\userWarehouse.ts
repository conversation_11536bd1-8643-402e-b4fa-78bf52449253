import request from "@/core/utils/request";

const USERWAREHOUSE_BASE_URL = "/supply-wms/userWarehouse";

class UserWarehouseAPI {
    /** 仓库分页用户列表 */
    static getUserWarehouse(queryParams?: PageQuery) {
        return request<any, PageResult<userWarehouseInfo[]>>({
            url: `${USERWAREHOUSE_BASE_URL}/list/page`,
            method: "post",
            data: queryParams,
        });
    }
    /** 根据用户查询选择仓库列表 */
    static checkedUserById(data: { userId?: string }) {
        return request({
            url: `${USERWAREHOUSE_BASE_URL}/findUserWarehouseContainChecked`,
            method: "get",
            params: data,
        });
    }
    /** 更新用户和仓库关系 */
    static editStoreArea(data: { userId?: string, warehouseCodos?: Array<string> }) {
        return request({
            url: `${USERWAREHOUSE_BASE_URL}/update`,
            method: "post",
            data: data,
        });
    }
    /** 勾选仓库 */
    static checkedwarehouse(data: Array<string>) {
        return request({
            url: '/supply-wms/current/warehouse/checked',
            method: "post",
            data: data,
        });
    }
  // 获取所有部门列表
  static allDeptList() {
    return request({
      url: "/supply-base/base/dept/tree",
      method: "get",
    });
  }
}

/** 角色分页对象 */
export interface userWarehouseInfo {
    /** 库区编码 */
    areaCode: string;
    /** 库区名称 */
    areaName: string;
}

export interface userWarehousePageQuery extends PageQuery {
    /** 手机号 */
    mobile: string;
    /** 姓名 */
    nickName: number;
}
/** 仓库列表对象 */
export interface warehouseInfo {
    /** 仓库编码 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 状态 */
    checked: boolean;
}

export default UserWarehouseAPI;
