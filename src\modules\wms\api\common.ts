import request from "@/core/utils/request";
import {
  ProductAllPageQuery,
  ProductAllPageVO,
} from "@/modules/pms/api/purchaseOrder";

const COMMON_BASE_URL = "/supply-base";

class CommonAPI {
  /** 获取地区列表 */
  static getAreaList(data: { pid?: string }) {
    return request({
      url: `${COMMON_BASE_URL}/area/queryAreaListByPid`,
      method: "get",
      params: data,
    });
  }
  /** 获取当前登录租户*/
  static getCurrentTenant() {
    return request({
      url: `${COMMON_BASE_URL}/current/user/currentTenant`,
      method: "get",
    });
  }

  /** 获取当前仓库的库区下拉数据源 */
  static getOutWarehouseAreaList(data?: { status?: number }) {
    return request({
      url: `/supply-wms/storeArea/queryListByCurrentWarehouse`,
      method: "get",
      params: data,
    });
  }

  /** 获取当前仓库的库区下拉数据源 （库存大于0）*/
  static getOutWarehouseAreaNumberList(data?: { status?: number }) {
    return request({
      url: `/supply-wms/storeArea/queryHasStockListByCurrentWarehouse`,
      method: "get",
      params: data,
    });
  }

  /** 查询所有可选的商品列表(分页展示库存转移选择的商品)*/
  static queryProductAll(data?: ProductAllPageQuery) {
    return request<any, PageResult<ProductAllPageVO>>({
      url: `/supply-wms/productStock/selectPage`,
      method: "post",
      data: data,
    });
  }

  /** 查询所有可选的商品列表(分页展示库存转移选择的商品) 附加商品加权平均单价*/
  static queryProductAllHasAveragePrice(data?: ProductAllPageQuery) {
    return request<any, PageResult<ProductAllPageVO>>({
      url: `/supply-wms/productStock/selectWeightedPricePage`,
      method: "post",
      data: data,
    });
  }

  /** 查询所有可选的商品列表(质检单库内巡检-包含库存为0的商品)*/
  static queryProductAllPage(data?: ProductAllPageQuery) {
    return request<any, PageResult<ProductAllPageVO>>({
      url: `/supply-wms/productStock/page`,
      method: "post",
      data: data,
    });
  }

  /** 查询所有可选的目标商品列表(分页展示拆装单选择的目标商品)*/
  static queryTargetProductAll(data?: ProductAllPageQuery) {
    return request<any, PageResult<ProductAllPageVO>>({
      url: `/supply-wms/product/product/page`,
      method: "post",
      data: data,
    });
  }

  /** 查询所有可选的目标商品列表(分页展示拆装单选择的目标商品) 附加商品加权平均单价*/
  static queryTargetProductAllHasAveragePrice(data?: ProductAllPageQuery) {
    return request<any, PageResult<ProductAllPageVO>>({
      url: `/supply-wms/product/product/selectWeightedPricePage`,
      method: "post",
      data: data,
    });
  }

  /** 查询字典表*/
  static findTreeListByDictKey(data: { dictKey?: string }) {
    return request({
      url: `${COMMON_BASE_URL}/dict/findTreeListByDictKey`,
      method: "get",
      params: data,
    });
  }

  /** 获取商品分类下拉数据源 */
  static queryCategoryTreeList(data?: { id?: any }) {
    return request<any, CategoryVO[]>({
      url: `/supply-biz-common/product/category/queryCategoryTreeList`,
      method: "post",
      data: data,
    });
  }
  /** 获取库区类型 */
  static queryWarehouseAreaStorageTypes(data:any) {
    return request({
      url: `/supply-biz-common/warehouseAreaStorageTypes/queryPageList`,
      method: "post",
      data: data,
    });
  }
  // 计算加权平均价接口（实时）
  static calculateWeightedAveragePrice(data: any) {
   /*  {
        "isDiscreteUnit": 0,  一级单位增减->1:开启；0:关闭
        "productCode": "", 商品编码
        "warehouseAreaCode": "", 仓库库区编码
        "warehouseLocationCode": "" 仓库库位编码
    } */
    return request({
      url: `/supply-wms/productStockBatch/calculateWeightedAveragePrice`,
      method: "post",
      data: data,
    });
  }
  //计算加权平均价接口（实时）加金额
  static calculateWeightedAveragePriceAndAmount(data: any) {
    /*  {
        "pricingScheme": 0,  计价模式->1:二级单位；0:一级单位
        "productCode": "", 商品编码
        "convertedQty": 0, 转换量
        "qty": 0, 量
        "warehouseAreaCode": "", 仓库库区编码
        "warehouseLocationCode": "" 仓库库位编码
    } */
    return request({
      url: `/supply-wms/productStockBatch/calculateWeightedAveragePriceAndAmount`,
      method: "post",
      data: data,
    });
  }
  // 商品一级单位和二级单位转换
  static convertProductUnit(data: any) {
    /* {
      "convertUnitTypeEnum": "", 转换单位类型枚举;FIRST_TO_SECOND:一级转二级;SECOND_TO_FIRST：二级转一级,可用值:FIRST_TO_SECOND,SECOND_TO_FIRST
      "originalValue": 0, 源值
      "productCode": 0 商品code
    } */
    return request({
      url: `/supply-biz-common/product/product/convertUnit`,
      method: "post",
      data: data,
    });
  }
  // 单价+量+转换量:计算金额
  static calculateAmount(data: any) {
   /*  {
      "convertedQty": 0, // 转换量
      "productCode": 0, // 商品productCode
      "qty": 0, // 量
      "unitPrice": 0 // 单价
    } */
    return request({
      url: `/supply-biz-common/product/product/convertUnitToAmount`,
      method: "post",
      data: data,
    });
  }
  // 获取当前商品总库存大于0的库区
  static queryHasStockListByReq(data: any){
    /* {
      "productCode":'',商品编码
      "isDiscreteUnit":0,商品是否一级单位增减
    } */
    return request({
      url: `/supply-wms/storeArea/queryHasStockListByReq`,
      method: "post",
      data: data
    })
  }
  static queryWarehouseAreaProductStockQtyList(data: any){
    /* {
      "productCode":'',商品编码
      "isDiscreteUnit":0,商品是否一级单位增减
    } */
    return request({
      url: `/supply-wms/common/business/queryWarehouseAreaProductStockQtyList`,
      method: "post",
      data: data
    })
  }
  //当前商品可用库存合集
  static queryAvailableStockInfoListByReq(data: any){
    /*
      productCode:'',商品编码
      isDiscreteUnit:0,商品是否一级单位增减
      productUnitName:'',一级单位名称
      conversionRelSecondUnitName:'',二级单位名称
    */
    return request({
      url: `/supply-wms/storeArea/queryAvailableStockInfoListByReq`,
      method: "post",
      data: data
    })
  }
  //根据商品列表查询库区并集
  static queryAllHasStockListByReq(data:any){
    /* {
          productQueryDTOList:[
            {
              "productCode":'',商品编码
             "isDiscreteUnit":0,商品是否一级单位增减
            }
          ]
     }*/
    return request({
      url: `/supply-wms/storeArea/queryAllHasStockListByReq`,
      method: "post",
      data: data
    })
  }
  //获取租户名称
  static queryTenantName(){
    return request({
      url: `/supply-wms/warehouseOutboundNotice/queryTenantName`,
      method: "get",
    })
  }

}
export default CommonAPI;

/** 地区 */
export interface AreaInfo {
  /** 主键 */
  id?: string;
  /** 父id */
  pid?: string;
  /** 区域编码 */
  areaCode?: number;
  /** 简称 */
  shortName?: string;
  /** 区域名称 */
  name?: number;
}
export interface AreaListQuery {
  pid?: string;
}

/** 可选商品列表弹窗分页查询参数 */
export interface ProductAllPageQuery extends PageQuery {
  /** 出库库区id */
  warehouseAreaCode?: string;
  warehouseAreaId?: string;
  warehouseAreaCodes?: string[];
  warehouseAreaIds?: string[];
  /** 商品分类 */
  productCategory?: string[];
  firstCategoryIds?: string[];
  secondCategoryIds?: string[];
  thirdCategoryIds?: string[];
  /** 商品 */
  productName?: string;
}

/** 可选商品列表弹窗分页对象 */
export interface ProductAllPageVO {
  /** ID */
  id?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 规格名称 */
  productSpec?: string;
  /** 单位编码 */
  productUnitId?: string;
  /** 单位名称 */
  productUnitName?: string;
  /** 商品分类 */
  fullCategoryName?: string;
  /** 总库存 */
  totalStockQty?: number;
  /** 总库存重量 */
  totalStockWeight?: number;
  /** 可用库存 */
  availableStockQty?: number;
}

/** 分类对象 */
export interface CategoryVO {
  /** 分类ID */
  id?: string;
  /** 分类名称 */
  categoryName?: string;
  /** 上级分类编号 */
  parentId?: string;
  /** 级别 */
  level?: number;
}
