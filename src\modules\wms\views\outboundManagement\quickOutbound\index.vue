<template>
  <div class="app-container">
    <div class="quickOutbound">
      <el-card class="mb-12px search-card" :class="{'custom-style': showSearchForm,'custom-style1': !showSearchForm}">
        <div v-if="showSearchForm" class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="96px">
            <!-- 出库通知单 -->
            <el-form-item prop="outboundNoticeCode" :label="$t('quickOutbound.label.outboundNoticeCode')">
              <el-input v-model="queryParams.outboundNoticeCode" :placeholder="$t('quickOutbound.placeholder.inputLimitTips')" clearable class="!w-[256px]"/>
            </el-form-item>
            <!-- 主题描述 -->
            <el-form-item prop="orderTheme" :label="$t('quickOutbound.label.themeDescription')">
              <el-input v-model="queryParams.orderTheme" :placeholder="$t('common.placeholder.inputTips')" clearable class="!w-[256px]"/>
            </el-form-item>
            <!-- 出库类型 -->
            <el-form-item prop="outboundType" :label="$t('quickOutbound.label.outboundType')">
              <el-select v-model="queryParams.outboundType" multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.placeholder.selectTips')" clearable class="!w-[256px]">
                <el-option
                  v-for="item in outboundTypeList"
                  :key="item.outboundTypeId"
                  :label="item.outboundTypeName"
                  :value="item.outboundTypeId"
                />
              </el-select>
            </el-form-item>
            <!-- 状态 -->
            <el-form-item :label="$t('quickOutbound.label.status')" prop="outboundNoticeStatus">
              <el-select v-model="queryParams.outboundNoticeStatus" multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.placeholder.selectTips')" clearable class="!w-[256px]">
                <el-option
                  v-for="item in outboundNoticeStatusList"
                  :key="item.statusId"
                  :label="item.statusName"
                  :value="item.statusId"
                />
              </el-select>
            </el-form-item>
            <!-- 来源单号 -->
            <el-form-item prop="sourceOrderCode" :label="$t('quickOutbound.label.sourceOrderCode')">
              <el-input v-model="queryParams.sourceOrderCode" :placeholder="$t('quickOutbound.placeholder.inputLimitTips')" clearable class="!w-[256px]"/>
            </el-form-item>
            <!-- 是否预售单 -->
            <el-form-item :label="$t('quickOutbound.label.isPresaleOrder')" prop="isPresale">
              <el-select v-model="queryParams.isPresale" :placeholder="$t('common.placeholder.selectTips')" clearable class="!w-[256px]">
                <el-option
                  v-for="item in isPresaleOrderList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
            <!-- 时间 -->
            <el-form-item prop="dateRange">
              <el-select v-model="queryParams.dateType" :placeholder="$t('common.placeholder.selectTips')" class="!w-[160px] ml5px multipleSelect">
                <el-option
                  v-for="item in dateTypeList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
              <el-date-picker
                :editable="false"
                class="!w-[360px]"
                v-model="dateRange"
                type="datetimerange"
                :range-separator="$t('quickOutbound.label.to')"
                :start-placeholder="$t('quickOutbound.label.startTime')"
                :end-placeholder="$t('quickOutbound.label.endTime')"
                :default-time="defaultTime"
              />
              <span class="ml16px mr14px cursor-pointer" style="color: var(--el-color-primary)" @click="handleChangeDateRange(2)">{{ $t("quickOutbound.label.yesterday") }}</span>
              <span class="mr14px cursor-pointer" style="color: var(--el-color-primary)" @click="handleChangeDateRange(1)">{{ $t("quickOutbound.label.today") }}</span>
              <span class="mr14px cursor-pointer" style="color: var(--el-color-primary)" @click="handleChangeDateRange(4)">{{ $t("quickOutbound.label.tomorrow") }}</span>
              <span class="mr16px cursor-pointer" style="color: var(--el-color-primary)" @click="handleChangeDateRange(3)">{{ $t("quickOutbound.label.weekday") }}</span>
            </el-form-item>
            <el-form-item>
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:search']" type="primary" @click="handleQuery">{{ $t("common.search") }}</el-button>
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:reset']" @click="handleResetQuery">{{ $t("common.reset") }}</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div style="display: flex;justify-content: center;align-items: center;">
          <div style="width: 78px;height: 16px;" @click="openOrCloseSearchForm"><img width="100%;height:100%" src="@/core/assets/images/open.png"></div>
        </div>
      </el-card>

      <el-card class="content-card">
        <div class="action-bar">
          <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:cancelShiftArrangement']" type="primary" @click="cancelShiftArrangement()">{{ $t("quickOutbound.button.cancelShiftArrangement") }}</el-button>
          <el-button type="primary" plain @click="batchShiftArrangement" v-hasPerm="['wms:outboundManagement:quickOutbound:batchShiftArrangement']">{{ $t("quickOutbound.button.batchShiftArrangement") }}</el-button>
          <el-button type="primary" plain @click="toAdd" v-hasPerm="['wms:outboundManagement:quickOutbound:add']">{{ $t("quickOutbound.button.addBtn") }}</el-button>
<!--          <el-button type="primary" plain @click="handleExport" v-hasPerm="['wms:outboundManagement:quickOutbound:export']">{{ $t("quickOutbound.button.export") }}</el-button>-->
        </div>
        <el-table v-loading="loading" border :data="quickOutboundList" @selection-change="handleSelectionChange" highlight-current-row stripe>
          <template #empty>
            <Empty />
          </template>
          <el-table-column fixed="left" type="selection" width="40" align="center" />
          <el-table-column fixed="left" type="index" :label="$t('common.sort')" width="50" align="center" />
          <!-- 主题描述 -->
          <el-table-column :label="$t('quickOutbound.label.themeDescription')" prop="orderTheme" width="178">
            <template #default="scope">
              <div v-if="scope.row.orderTheme" style="word-break: break-all">{{scope.row.orderTheme}}</div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <!-- 计划量 -->
          <el-table-column :label="$t('quickOutbound.label.plannedQuantity')" prop="totalPlanProductQty" show-overflow-tooltip width="100" />
          <!-- 出库量 -->
          <el-table-column :label="$t('quickOutbound.label.outboundQuantity')" prop="totalOutboundQty" show-overflow-tooltip width="100" />
          <!-- 出库人 -->
          <el-table-column :label="$t('quickOutbound.label.outboundUserName') + '(' + $t('quickOutbound.label.outboundTime') + ')'" show-overflow-tooltip min-width="200">
            <template #default="scope">
              <div v-for="item in scope.row.warehouseOutboundPickInfoVOList" v-if="scope.row.warehouseOutboundPickInfoVOList && scope.row.warehouseOutboundPickInfoVOList.length > 0">
                <span>{{item.pickUserName?item.pickUserName:'-'}}({{item.pickTime?parseDateTime(item.pickTime, "dateTime"):'-'}})</span>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <!-- 客户 -->
          <el-table-column :label="$t('quickOutbound.label.customerName')" prop="customerName" show-overflow-tooltip width="120"/>
          <!-- 是否预售单 -->
          <el-table-column :label="$t('quickOutbound.label.isPresaleOrder')" prop="isPresale" show-overflow-tooltip width="90">
            <template #default="scope">
              <span v-if="scope.row.isPresale == 0">{{$t('common.statusYesOrNo.no')}}</span>
              <span v-else-if="scope.row.isPresale == 1">{{$t('common.statusYesOrNo.yes')}}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 打印状态 -->
          <el-table-column width="50" prop="printStatus" :label="$t('quickOutbound.label.print')">
            <template #default="scope">
              <div class="flex-center-start">
                <div v-if="scope.row.printStatus == 1" class="solid-circle" style="background-color: #29B610"></div>
                <div v-if="scope.row.printStatus == 0" class="solid-circle" style="background-color: #CCCFD5"></div>
                <div>
                  <span v-if="scope.row.printStatus == 0">{{$t('common.statusYesOrNo.no')}}</span>
                  <span v-else-if="scope.row.printStatus == 1">{{$t('common.statusYesOrNo.yes')}}</span>
                  <span v-else>-</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 计划发货时间 -->
          <el-table-column :label="$t('quickOutbound.label.plannedDeliveryTime')" prop="plannedDeliveryTime" width="157">
            <template #default="scope">
              <span v-if="scope.row.plannedDeliveryTime">{{parseDateTime(scope.row.plannedDeliveryTime, "dateTime")}}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 要求出库时间 -->
          <el-table-column :label="$t('quickOutbound.label.plannedReceivedTime')" prop="plannedReceivedTime" width="157">
            <template #default="scope">
              <span v-if="scope.row.plannedReceivedTime">{{parseDateTime(scope.row.plannedReceivedTime,"dateTime")}}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 业务员 -->
          <el-table-column :label="$t('quickOutbound.label.salesperson')" prop="purchaseSalesPerson" show-overflow-tooltip width="130"/>
          <!-- 申请人 -->
          <el-table-column :label="$t('quickOutbound.label.createUserName')" prop="createUserName" show-overflow-tooltip width="120"/>
          <!-- 出库类型:1:销售出库、2:提货卡兑换、3：业务招待、4：补发、5：地头售卖、6:采购退货、7：调拨出库、8：直接出库、9：参展 -->
          <el-table-column :label="$t('quickOutbound.label.outboundType')" prop="outboundType" show-overflow-tooltip width="100">
            <template #default="scope">
              <span v-if="scope.row.outboundType == 1">{{ $t("quickOutbound.outboundTypeList.procurementOutbound") }}</span>
              <span v-else-if="scope.row.outboundType == 2">{{ $t("quickOutbound.outboundTypeList.pickupCardRedemption") }}</span>
              <span v-else-if="scope.row.outboundType == 3">{{ $t("quickOutbound.outboundTypeList.businessReception") }}</span>
              <span v-else-if="scope.row.outboundType == 4">{{ $t("quickOutbound.outboundTypeList.reissue") }}</span>
              <span v-else-if="scope.row.outboundType == 5">{{ $t("quickOutbound.outboundTypeList.sellAtTheFieldEdge") }}</span>
              <span v-else-if="scope.row.outboundType == 6">{{ $t("quickOutbound.outboundTypeList.returnOutbound") }}</span>
              <span v-else-if="scope.row.outboundType == 7">{{ $t("quickOutbound.outboundTypeList.allotOutbound") }}</span>
              <span v-else-if="scope.row.outboundType == 8">{{ $t("quickOutbound.outboundTypeList.directOutbound") }}</span>
              <span v-else-if="scope.row.outboundType == 9">{{ $t("quickOutbound.outboundTypeList.participateInTheExhibition") }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 计划转换量 -->
          <el-table-column :label="$t('quickOutbound.label.plannedConversionQuantity')" prop="totalPlanProductWeight" show-overflow-tooltip width="100" />
          <!-- 出库转换量 -->
          <el-table-column :label="$t('quickOutbound.label.outboundConversionQuantity')" prop="totalOutboundWeight" show-overflow-tooltip width="100" />
          <!-- 出库通知单 -->
          <el-table-column :label="$t('quickOutbound.label.outboundNoticeCode')" prop="outboundNoticeCode" show-overflow-tooltip width="160"/>
          <!-- 来源单号 -->
          <el-table-column :label="$t('quickOutbound.label.sourceOrderCode')" prop="sourceOrderCode" show-overflow-tooltip width="160"/>
          <!-- 计划配送方式 -->
          <!--<el-table-column :label="$t('quickOutbound.label.plannedDistributionMethod')" prop="deliveryName" show-overflow-tooltip width="160"/>-->
          <!-- 申请时间 -->
          <el-table-column :label="$t('quickOutbound.label.createTime')" prop="createTime" width="157">
            <template #default="scope">
              <span v-if="scope.row.createTime">{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 供应商  -->
          <el-table-column :label="$t('quickOutbound.label.supplierName')" prop="supplierName" show-overflow-tooltip width="120"/>
          <!-- 结算方式 -->
          <el-table-column :label="$t('quickOutbound.label.settlementMethod')" prop="paymentType" show-overflow-tooltip width="100">
            <template #default="scope">
              <span v-if="scope.row.paymentType == 1">{{$t('quickOutbound.label.presentSettlement')}}</span>
              <span v-else-if="scope.row.paymentType == 2">{{$t('quickOutbound.label.accountHanging')}}</span>
              <span v-else-if="scope.row.paymentType == 3">{{$t('quickOutbound.label.noSettlementRequired')}}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 合同编码 -->
          <el-table-column :label="$t('quickOutbound.label.contractCode')" prop="contractCode" show-overflow-tooltip width="160"/>
          <!-- 合同分类 -->
          <el-table-column :label="$t('quickOutbound.label.contractClassification')" prop="contractType" show-overflow-tooltip width="100">
            <template #default="scope">
              <span v-if="scope.row.contractType == 1">销售合同</span>
              <span v-else-if="scope.row.contractType == 2">采购合同</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column :label="$t('quickOutbound.label.remark')" prop="remark" show-overflow-tooltip width="160"/>
          <!-- 状态 -->
          <el-table-column :label="$t('quickOutbound.label.status')" prop="outboundNoticeStatus" width="90" fixed="right">
            <template #default="scope">
              <div class="purchase">
                <!-- 	状态:0:草稿、1:初始 2拣货中 3 完成 4 取消 5 已排班 6 部分出库-->
                <div v-if="scope.row.outboundNoticeStatus == 0" type="success" class="purchase-status purchase-status-color0">
                  {{ t("quickOutbound.statusList.draft") }}
                </div>
                <div v-else-if="scope.row.outboundNoticeStatus == 1" type="info" class="purchase-status purchase-status-color1">
                  {{ t("quickOutbound.statusList.initial") }}
                </div>
                <div v-else-if="scope.row.outboundNoticeStatus == 2" type="info" class="purchase-status purchase-status-color2">
                  {{ t("quickOutbound.statusList.picking") }}
                </div>
                <div v-else-if="scope.row.outboundNoticeStatus == 3" type="info" class="purchase-status purchase-status-color3">
                  {{ t("quickOutbound.statusList.finish") }}
                </div>
                <div v-else-if="scope.row.outboundNoticeStatus == 4" type="info" class="purchase-status purchase-status-color4">
                  {{ t("quickOutbound.statusList.cancel") }}
                </div>
                <div v-else-if="scope.row.outboundNoticeStatus == 5" type="info" class="purchase-status purchase-status-color2">
                  {{ t("quickOutbound.statusList.arranged") }}
                </div>
                <div v-else-if="scope.row.outboundNoticeStatus == 6" type="info" class="purchase-status purchase-status-color2">
                  {{ t("quickOutbound.statusList.portionOutbound") }}
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column fixed="right" :label="$t('common.handle')" width="240">
            <!-- 	状态:0:草稿、1:初始 2拣货中 3 完成 4 取消 5 已排班 6 部分出库-->
            <template #default="scope">
              <!-- 去排班(初始状态) -->
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:toShiftArrangement']" v-if="scope.row.outboundNoticeStatus == 1" type="primary" link @click="toShiftArrangement(scope.row)">
                {{ $t("quickOutbound.button.toShiftArrangement") }}
              </el-button>
              <!-- 确认出库(初始/已排班/部分出库状态) -->
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:confirmOutbound']" v-if="scope.row.outboundNoticeStatus == 1 || scope.row.outboundNoticeStatus == 5 || scope.row.outboundNoticeStatus == 6" type="primary" link @click="confirmOutbound(scope.row.id)">
                {{ $t("quickOutbound.button.confirmOutbound") }}
              </el-button>
              <!-- 完结(部分出库状态) -->
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:finished']" v-if="scope.row.outboundNoticeStatus == 6" type="primary" link @click="toFinished(scope.row.id)">
                {{ $t("quickOutbound.button.finished") }}
              </el-button>
              <!-- 打印(非草稿和取消状态) -->
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:print']" v-if="scope.row.outboundNoticeStatus != 0 && scope.row.outboundNoticeStatus != 4" type="primary" link @click="handlePrint(scope.row.id)">
                {{ $t("common.print") }}
              </el-button>
              <!-- 详情(部分出库/完结/取消状态) -->
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:detail']" v-if="scope.row.outboundNoticeStatus == 3 || scope.row.outboundNoticeStatus == 6 || scope.row.outboundNoticeStatus == 4" type="primary" link @click="toDetail(scope.row.id)">
                {{ $t("quickOutbound.button.detail") }}
              </el-button>
              <!-- 编辑(草稿状态) -->
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:edit']" v-if="scope.row.outboundNoticeStatus == 0" type="primary" link @click="toEdit(scope.row.id)">
                {{ $t("common.edit") }}
              </el-button>
              <!-- 删除(草稿状态) -->
              <el-button v-hasPerm="['wms:outboundManagement:quickOutbound:delete']" v-if="scope.row.outboundNoticeStatus == 0" type="danger" link @click="toDelete(scope.row.id)">
                {{ $t("common.delete") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <pagination
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.limit"
            @pagination="handleQuery"
          />
        </div>
      </el-card>
    </div>
    <!-- 去排班弹框 -->
    <el-dialog :title="$t('quickOutbound.title.shiftArrangement')" v-model="approveDialog" class="approve-popup" width="560" :close-on-click-modal="false" @close="close">
      <div style="padding: 0px 30px 16px 30px;">
        <div style="margin-bottom: 15px;" class="flex-center-start">
          <div class="!w-[120px]" style="text-align: right;margin-right: 6px;">{{$t('quickOutbound.label.customerName')}}：</div>
          <div>{{customerName}}</div>
        </div>
        <div style="margin-bottom: 15px;" class="flex-center-start">
          <div class="!w-[120px]" style="text-align: right;margin-right: 6px;">{{$t('quickOutbound.label.plannedReceivedTime')}}：</div>
          <div>{{ parseDateTime(plannedReceivedTime, "dateTime") }}</div>
        </div>
        <div class="flex-center-start">
          <div class="!w-[120px]" style="text-align: right">
            <span style="color: #f56c6c">*</span>
            <span style="margin:0px 6px">{{$t('quickOutbound.label.plannedDeliveryTime')}}：</span>
          </div>
          <div style="width: calc(100% - 120px)">
            <el-date-picker
              style="width: 100% !important;"
              v-model="plannedDeliveryDate"
              type="date"
              @change="setDate"
              :placeholder="$t('quickOutbound.placeholder.defaultNowDate')">
            </el-date-picker>
          </div>
        </div>
        <div class="time-button">
          <div style="width: calc(100% - 120px)">
            <el-time-picker
              style="width: 100% !important;"
              v-model="plannedDeliveryTime"
              format="HH:mm"
              value-format="HH:mm"
              :placeholder="$t('quickOutbound.placeholder.chooseTime')">
            </el-time-picker>
          </div>
        </div>
        <div class="time-button">
          <div style="width: calc(100% - 120px)">
            <el-button class="!w-[66px]" @click="setTime(9)">9:00</el-button>
            <el-button class="!w-[66px]" @click="setTime(12)">12:00</el-button>
            <el-button class="!w-[66px]" @click="setTime(14)">14:00</el-button>
            <el-button class="!w-[66px]" @click="setTime(18)">18:00</el-button>
            <el-button class="!w-[66px]" @click="setTime(21)">21:00</el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer" style="padding: 12px 0px;margin-right: 30px">
          <el-button @click="closeApproveDialog">
            {{ $t("outboundNotice.button.cancel") }}
          </el-button>
          <el-button type="primary" @click="submitApprove" :loading="loading">{{ $t("common.confirm") }}</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 批量排班弹框 -->
    <el-dialog :title="$t('quickOutbound.title.batchShiftArrangement')" v-model="batchApproveDialog" class="batch-approve-popup" width="560" :close-on-click-modal="false" @close="close">
      <div style="padding: 0px 30px 16px 30px;">
        <div style="margin-bottom: 40px;font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 16px;color: #151719;line-height: 22px;font-style: normal;">{{$t('quickOutbound.message.selected')}}<span style="color: #762ADB;">{{multipleSelection.length}}</span>{{$t('quickOutbound.message.batchShiftArrangementWarning')}}</div>
        <div>
          <span style="color: #f56c6c">*</span>
          <span style="margin:0px 6px">{{$t('quickOutbound.label.plannedDeliveryTime')}}</span>
          <el-date-picker
            v-model="plannedDeliveryDate"
            type="date"
            @change="setDate"
            :placeholder="$t('quickOutbound.placeholder.defaultNowDate')">
          </el-date-picker>
        </div>
        <div class="time-button">
          <el-time-picker
            v-model="plannedDeliveryTime"
            format="HH:mm"
            value-format="HH:mm"
            :placeholder="$t('quickOutbound.placeholder.chooseTime')">
          </el-time-picker>
        </div>
        <div class="time-button">
          <el-button style="width: 14%" @click="setTime(9)">9:00</el-button>
          <el-button style="width: 14%" @click="setTime(12)">12:00</el-button>
          <el-button style="width: 14%" @click="setTime(14)">14:00</el-button>
          <el-button style="width: 14%" @click="setTime(18)">18:00</el-button>
          <el-button style="width: 14%" @click="setTime(21)">21:00</el-button>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer" style="padding: 12px 0px;margin-right: 30px">
          <el-button @click="closeBatchApproveDialog">
            {{ $t("outboundNotice.button.cancel") }}
          </el-button>
          <el-button type="primary" @click="batchShiftArrangementSubmit" :loading="loading">{{ $t("common.confirm") }}</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 排班提示 -->
    <el-dialog v-model="dialogVisible" width="500">
      <div>{{ $t("quickOutbound.label.success") }}{{ approveResult.successQty }}{{ $t("quickOutbound.label.approveResultUnit") }}</div>
      <div>{{ $t("quickOutbound.label.fail") }}{{ approveResult.failQty }}{{ $t("quickOutbound.label.approveResultUnit") }}</div>
      <div v-if="approveResult.failQty">{{ $t("quickOutbound.label.failReason") }}：{{ approveResult.failReason.join(";") }}</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            {{ $t("common.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导出 -->
    <ExportSequence
      ref="exportSequenceRef"
      v-model:dialog-visible="exportDialogVisible"
      :path="`wms:quickOutbound:export`">
    </ExportSequence>
    <Print ref="printRef" class="display-none" />
  </div>
</template>

<script setup lang="ts">

  import QuickOutboundApi, {QuickOutboundQueryPage, QuickOutboundResponse,} from "@/modules/wms/api/quickOutbound";
  import { IObject } from "@/core/components/CURD/types";
  import { useRouter } from "vue-router";
  import {changeDateRange, parseDateTime} from "@/core/utils/index.js";
  import moment from "moment";
  import { emitter } from "@/core/utils/eventBus";
  import CommonAPI from "@/modules/wms/api/common";
  import Print from "./components/print.vue";
  import {useUserStore} from "@/core/store";
  import { useNavigation } from "@/core/composables/useNavigation";
  const { refreshAndNavigate } = useNavigation();
  defineOptions({
    name: "QuickOutbound",
    inheritAttrs: false,
  });

  const router = useRouter();
  const { t } = useI18n();
  const queryFormRef = ref(ElForm);
  const showSearchForm = ref(true);
  function openOrCloseSearchForm() {
    showSearchForm.value = !showSearchForm.value
  }
  const loading = ref(false);
  const total = ref(0);
  const dateRange = ref([
    /*moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
    moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),*/
  ]);
  const approveDialog = ref(false);
  const operationId = ref('');
  const customerName = ref('');
  const plannedReceivedTime = ref('');
  const plannedDeliveryDate = ref('');
  const plannedDeliveryTime = ref('');
  const multipleSelection = ref([]);
  const dialogVisible = ref(false);
  const approveResult = ref({
    failQty: 0,
    successQty: 0,
    failReason: [],
  });
  const exportDialogVisible = ref(false);
  const exportSequenceRef= ref();
  const batchApproveDialog = ref(false);
  const outboundTypeList = ref([
    {
      outboundTypeId: 1,
      outboundTypeName: t("quickOutbound.outboundTypeList.procurementOutbound"),
    },
    {
      outboundTypeId: 2,
      outboundTypeName: t("quickOutbound.outboundTypeList.pickupCardRedemption"),
    },
    {
      outboundTypeId: 3,
      outboundTypeName: t("quickOutbound.outboundTypeList.businessReception"),
    },
    {
      outboundTypeId: 4,
      outboundTypeName: t("quickOutbound.outboundTypeList.reissue"),
    },
    {
      outboundTypeId: 5,
      outboundTypeName: t("quickOutbound.outboundTypeList.sellAtTheFieldEdge"),
    },
    {
      outboundTypeId: 6,
      outboundTypeName: t("quickOutbound.outboundTypeList.returnOutbound"),
    },
    {
      outboundTypeId: 7,
      outboundTypeName: t("quickOutbound.outboundTypeList.allotOutbound"),
    },
    {
      outboundTypeId: 8,
      outboundTypeName: t("quickOutbound.outboundTypeList.directOutbound"),
    },
    {
      outboundTypeId: 9,
      outboundTypeName: t("quickOutbound.outboundTypeList.participateInTheExhibition"),
    },
  ]);
  const outboundNoticeStatusList = ref([
    {
      statusId: 0,
      statusName: t("quickOutbound.statusList.draft"),
    },
    {
      statusId: 1,
      statusName: t("quickOutbound.statusList.initial"),
    },
    /*{
      statusId: 2,
      statusName: t("quickOutbound.statusList.picking"),
    },*/
    {
      statusId: 3,
      statusName: t("quickOutbound.statusList.finish"),
    },
    {
      statusId: 4,
      statusName: t("quickOutbound.statusList.cancel"),
    },
    {
      statusId: 5,
      statusName: t("quickOutbound.statusList.arranged"),
    },
    {
      statusId: 6,
      statusName: t("quickOutbound.statusList.portionOutbound"),
    },
  ]);
  const isPresaleOrderList = ref([
    {
      key:0,
      value: t('common.statusYesOrNo.no')
    },
    {
      key:1,
      value: t('common.statusYesOrNo.yes')
    },
  ]);
  const defaultTime: [Date, Date] = [
    new Date(2000, 1, 1, 0, 0, 0),
    new Date(2000, 2, 1, 23, 59, 59),
  ];
  const dateTypeList = ref([
    {
      key: 1,
      value: t("quickOutbound.dateTypeList.createDate"),
    },
    {
      key: 2,
      value: t("quickOutbound.dateTypeList.plannedDate"),
    },
    {
      key: 3,
      value: t("quickOutbound.dateTypeList.outboundDate"),
    },
    {
      key: 4,
      value: t("quickOutbound.dateTypeList.receivedDate"),
    },
  ]);
  const queryParams = reactive<QuickOutboundQueryPage>({
    dateType: 2,
    outboundNoticeStatus:[0,1,5,6],
    page: 1,
    limit: 20,
  });
  const quickOutboundList = ref<QuickOutboundResponse[]>();

  /** 时间转换 */
  function handleChangeDateRange(val: any) {
    if(queryParams.dateType == 2 && val == 3){
      dateRange.value = [
        moment().startOf("days").format("YYYY-MM-DD HH:mm:ss"),
        moment().add(6, "days").endOf("days").format("YYYY-MM-DD HH:mm:ss")
      ]
    }else {
      dateRange.value = changeDateRange(val);
    }
  }
  /** 查询 */
  function handleQuery() {
    if (queryParams.outboundNoticeCode && queryParams.outboundNoticeCode.length < 4) {
      return ElMessage.error(t("quickOutbound.message.codeValidTips"));
    }
    if (queryParams.sourceOrderCode && queryParams.sourceOrderCode.length < 4) {
      return ElMessage.error(t("quickOutbound.message.codeValidTips"));
    }
    loading.value = true;
    let params = {
      ...queryParams,
    };
    //创建时间
    if (queryParams.dateType == 1 && dateRange.value && dateRange.value.length > 0) {
      params.startCreateTime = new Date(dateRange.value[0]).getTime();
      params.endCreateTime = new Date(dateRange.value[1]).getTime();
    }
    //计划发货时间
    if (queryParams.dateType == 2 && dateRange.value && dateRange.value.length > 0) {
      params.starPlannedDeliveryTime = new Date(dateRange.value[0]).getTime();
      params.endPlannedDeliveryTime = new Date(dateRange.value[1]).getTime();
    }
    //出库时间
    if (queryParams.dateType == 3 && dateRange.value && dateRange.value.length > 0) {
      params.starLastOutboundTime = new Date(dateRange.value[0]).getTime();
      params.endLastOutboundTime = new Date(dateRange.value[1]).getTime();
    }
    //要求到货时间
    if (queryParams.dateType == 4 && dateRange.value && dateRange.value.length > 0) {
      params.startPlannedReceivedTime = new Date(dateRange.value[0]).getTime();
      params.endPlannedReceivedTime = new Date(dateRange.value[1]).getTime();
    }
    delete params.dateType;
    QuickOutboundApi.queryPageList(params).then((data)=>{
      quickOutboundList.value = data.records ? data.records : []
      total.value = parseInt(data.total)
    }).finally(()=>{
      loading.value = false;
    })
  }

  /** 重置查询 */
  function handleResetQuery() {
    queryFormRef.value.resetFields();
    queryParams.page = 1;
    queryParams.limit = 20;
    queryParams.dateType = 2;
    queryParams.outboundNoticeStatus = [0,1,5,6];
    queryParams.outboundType = [];
    dateRange.value = [
      /*moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
      moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),*/
    ];
    handleQuery();
  }

  //确认出库
  function confirmOutbound(id) {
    /*router.push({
      path:'/wms/quickOutbound/confirmOutbound',
      query:{id:id}
    })*/
    refreshAndNavigate({
      path:'/wms/quickOutbound/confirmOutbound',
      query:{id:id}
    })
  }
  //完结
  function toFinished(id) {
    ElMessageBox.confirm(t("quickOutbound.message.finishedWarning"), t("common.tipTitle"), {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }).then(() => {
        loading.value = true
        let params = {
          id:id
        }
        QuickOutboundApi.completedOutbound(params).then((data)=>{
          handleResetQuery();
        }).finally(() => (loading.value = false));
      }, () => {
        ElMessage.info(t("quickOutbound.message.cancelTip"));
      }
    );
  }
  const fetchDetailData = async (id: string) => {
    try {
      const data = await QuickOutboundApi.queryFastDetail({id:id,isPrint:1})
      return data
    } catch (error) {
      console.error("Failed to fetch detail data:", error);
      return null;
    } finally {
    }
  }
  const queryTenantName = async() =>{
    try{
      const data = await CommonAPI.queryTenantName()
      return data
    } catch (e) {
      console.error("queryTenantName:", e);
      return null;
    }finally {
    }
  }
  //打印
  const printRef = ref();
  const userStore = useUserStore();
  async function handlePrint(id) {
    const detailData: any = await fetchDetailData(id);
    const name: any = await queryTenantName();
    const printData = {
      title: name + t("quickOutbound.label.outbound"),//租户名称+出库单
      customerName:detailData.customerName || "-",//客户
      contactPerson:detailData.contactPerson || "-",//收货人
      customerMobile:detailData.customerMobile ? detailData.customerAreaCode + " " + detailData.customerMobile :  "-",//电话
      addressFormat:`${detailData.countryName || ''}${detailData.provinceName || ''}${detailData.cityName || ''}${detailData.districtName || ''}${detailData.address || ''}`,//收货地址
      purchaseSalesPerson:detailData.purchaseSalesPerson || "-",//业务员-制单
      contractCode:detailData.contractCode || "-",//合同编号
      outboundNoticeCode:detailData.outboundNoticeCode || "-",//出库通知单号
      paymentType:detailData.paymentType == 1 ? t('quickOutbound.label.presentSettlement') : detailData.paymentType == 2 ? t('quickOutbound.label.accountHanging') : detailData.paymentType == 3 ? t('quickOutbound.label.noSettlementRequired') : "-",//结算方式
      contractType:detailData.contractType == 1 ? "销售合同" : detailData.contractType == 2 ? "采购合同" : "-",//合同分类
      warehouseOutboundDetailVOList:[],//出库商品明细
      sendPerson:'-',//发货人
      extractPerson:'-',//提货人
      /** 合计金额（优惠后金额） */
      totalSaleAmount: detailData.totalDiscountAmount || "-",
      /** 优惠（优惠金额 = 总金额 - 优惠后金额） */
      totalDiscountAmount: (detailData.outboundType != 6 && detailData.totalSaleAmount && detailData.totalDiscountAmount) ? (Number(detailData.totalSaleAmount) - Number(detailData.totalDiscountAmount)).toFixed(2) : '-',
      /** 总数量 */
      totalPlanProductQty: detailData.totalPlanProductQty || "-",
      /** 总出库数量 */
      totalOutboundQty: detailData.totalOutboundQty || "-",
      /** 出库人 */
      outboundUserName: userStore.user.nickName || "-",
    }
    printData.warehouseOutboundDetailVOList = detailData.warehouseOutboundDetailVOList.map((list) =>({
      /** 商品编码 */
      productCode: list.productCode || '-',
      /** 商品名称 */
      productName: list.productName || '-',
      /** 单位 */
      productUnitName: list.productUnitName || '-',
      /** 计划数量 */
      planProductQty: list.planProductQty || '-',
      /** 已出库数量 */
      alreadyOutboundQty: list.alreadyOutboundQty || '-',
      /** 单价 */
      salePrice: list.salePrice || '-',
      /** 金额 */
      saleAmount: list.saleAmount || '-',
      /** 备注 */
      remark: list.remark || '-',
    }))
    // 执行打印
    nextTick(() => {
      printRef.value.handlePrint(printData);
    });
    handleResetQuery();
  }
  //详情
  function toDetail(id) {
    router.push({
      path:'/wms/quickOutbound/detailQuickOutbound',
      query:{id:id}
    })
  }
  //新增
  function toAdd() {
    refreshAndNavigate({
      path:'/wms/quickOutbound/addOrEditOutbound',
      query:{title:'新增快速出库',type:'add',}
    })
  }
  //编辑
  function toEdit(id) {
    refreshAndNavigate({
      path:'/wms/quickOutbound/addOrEditOutbound',
      query:{title:'编辑快速出库',type:'edit',id:id}
    })
  }
  //删除
  function toDelete(id) {
    ElMessageBox.confirm(t("quickOutbound.message.deleteWarning"), t("common.tipTitle"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }).then(() => {
      loading.value = true;
      let params = {
        id:id
      }
      QuickOutboundApi.delFastOutbound(params).then((res)=>{
        ElMessage.success(t("quickOutbound.message.deleteSuccess"))
        handleResetQuery();
      }).finally(()=>{
        loading.value = false;
      })
    }, () => {
      ElMessage.info(t("quickOutbound.message.cancelTip"));
    })
  }
  //取消排班（已排班状态可批量取消）
  function cancelShiftArrangement() {
    if (multipleSelection.value.length == 0) {
      return ElMessage.error(t("quickOutbound.message.cancelSelectTip"));
    }
    let flag = multipleSelection.value.some(
      (item: any) => item.outboundNoticeStatus != 5
    );
    if (flag) {
      return ElMessage.error(t("quickOutbound.message.generateNotCancelShiftArrangementTips"));
    }
    ElMessageBox.confirm(t("quickOutbound.message.cancelShiftArrangementWarning"), t("common.tipTitle"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }).then(() => {
        loading.value = true;
        let params: any = {
          ids: [],
        };
        params.ids = multipleSelection.value.map((item: any) => item.id);
        QuickOutboundApi.cancelScheduling(params).then((data)=>{
          dialogVisible.value = true;
          approveResult.value = data;
          handleResetQuery();
        }).finally(() => (loading.value = false));
      }, () => {
        ElMessage.info(t("quickOutbound.message.cancelTip"));
      }
    );
  }
  //批量排班（初始状态可批量排班）
  function batchShiftArrangement() {
    if (multipleSelection.value.length == 0) {
      return ElMessage.error(t("quickOutbound.message.batchSelectTip"));
    }
    let flag = multipleSelection.value.some(
      (item: any) => item.outboundNoticeStatus != 1
    );
    if (flag) {
      return ElMessage.error(t("quickOutbound.message.generateNotBatchShiftArrangementTips"));
    }
    plannedDeliveryDate.value = parseDateTime(new Date().getTime(),'date')
    plannedDeliveryTime.value = ''
    batchApproveDialog.value = true
  }
  //批量排班提交
  function batchShiftArrangementSubmit() {
    if(plannedDeliveryDate.value == '' || plannedDeliveryDate.value == null || plannedDeliveryDate.value == undefined || plannedDeliveryTime.value == '' || plannedDeliveryTime.value == null || plannedDeliveryTime.value == undefined){
      return ElMessage.error(t("quickOutbound.rules.plannedDeliveryTimeRules"));
    }
    loading.value = true;
    let params: any = {
      ids: [],
      plannedDeliveryTime:new Date(plannedDeliveryDate.value + ' ' + plannedDeliveryTime.value + ':00').getTime()
    };
    params.ids = multipleSelection.value.map((item: any) => item.id);
    QuickOutboundApi.doScheduling(params).then((data)=>{
      batchApproveDialog.value = false
      dialogVisible.value = true;
      approveResult.value = data;
      handleResetQuery();
    }).finally(() => (loading.value = false));
  }
  //批量排班取消
  function closeBatchApproveDialog(){
    batchApproveDialog.value = false
    plannedDeliveryTime.value = ''
    ElMessage.info(t("quickOutbound.message.cancelTip"));
  }
  //导出
  function handleExport() {
    if (queryParams.outboundNoticeCode && queryParams.outboundNoticeCode.length < 4) {
      return ElMessage.error(t("quickOutbound.message.codeValidTips"));
    }
    if (queryParams.sourceOrderCode && queryParams.sourceOrderCode.length < 4) {
      return ElMessage.error(t("quickOutbound.message.codeValidTips"));
    }
    ElMessageBox.confirm(t('common.exportTips'), t('common.export'), {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: "warning",
    }).then(
      () => {
        loading.value = true;
        let params = {
          ...queryParams
        }
        //创建时间
        if (queryParams.dateType == 1 && dateRange && dateRange.value.length > 0) {
          params.startCreateTime = new Date(dateRange.value[0]).getTime();
          params.endCreateTime = new Date(dateRange.value[1]).getTime();
        }
        //计划发货时间
        if (queryParams.dateType == 2 && dateRange && dateRange.value.length > 0) {
          params.starPlannedDeliveryTime = new Date(dateRange.value[0]).getTime();
          params.endPlannedDeliveryTime = new Date(dateRange.value[1]).getTime();
        }
        //出库时间
        if (queryParams.dateType == 3 && dateRange && dateRange.value.length > 0) {
          params.starLastOutboundTime = new Date(dateRange.value[0]).getTime();
          params.endLastOutboundTime = new Date(dateRange.value[1]).getTime();
        }
        //要求到货时间
        if (queryParams.dateType == 4 && dateRange && dateRange.value.length > 0) {
          params.startPlannedReceivedTime = new Date(dateRange.value[0]).getTime();
          params.endPlannedReceivedTime = new Date(dateRange.value[1]).getTime();
        }
        delete params.dateType;
        delete params.page
        delete params.limit
        QuickOutboundApi.exportList(params).then((data)=>{
          exportSequenceQuickOutboundList()
        }).finally(()=>{
          loading.value = false;
        })
      },
      () => {
        ElMessage.info(t('common.exportConcel'));
      }
    );
  }
  /** 导出序列*/
  function exportSequenceQuickOutboundList(){
    exportSequenceRef.value.exportSequenceListPage()
    exportDialogVisible.value = true
  }
  //选择计划发货日期
  function setDate(val) {
    if(val){
      plannedDeliveryDate.value = parseDateTime(new Date(val).getTime(),'date')
    }else {
      plannedDeliveryDate.value = ''
    }
  }
  //快捷设置计划发货时间
  function setTime(num) {
    plannedDeliveryTime.value = num > 10 ? num.toString() + ':00' : '0' + num.toString() + ':00'
  }
  //去排班
  function toShiftArrangement(row) {
    operationId.value = row.id
    customerName.value = row.customerName
    plannedReceivedTime.value = row.plannedReceivedTime
    plannedDeliveryDate.value = parseDateTime(new Date().getTime(),'date')
    plannedDeliveryTime.value = ''
    approveDialog.value = true
  }
  //去排班提交
  function submitApprove() {
    if(plannedDeliveryDate.value == '' || plannedDeliveryDate.value == null || plannedDeliveryDate.value == undefined || plannedDeliveryTime.value == '' || plannedDeliveryTime.value == null || plannedDeliveryTime.value == undefined){
      return ElMessage.error(t("quickOutbound.rules.plannedDeliveryTimeRules"));
    }
    loading.value = true;
    let params: any = {
      ids: [operationId.value],
      plannedDeliveryTime:new Date(plannedDeliveryDate.value + ' ' + plannedDeliveryTime.value + ':00').getTime()
    };
    QuickOutboundApi.doScheduling(params).then((data)=>{
      approveDialog.value = false
      dialogVisible.value = true;
      approveResult.value = data;
      handleResetQuery();
    }).finally(() => (loading.value = false));
  }
  //去排班取消
  function closeApproveDialog() {
    approveDialog.value = false
    plannedDeliveryTime.value = ''
    ElMessage.info(t("quickOutbound.message.cancelTip"));
  }
  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(val: any[]) {
    multipleSelection.value = val;
  }
  onActivated(() => {
    handleQuery();
  });
  emitter.on("reloadListByWarehouseId", (e) => {
    nextTick(() => {
      handleQuery();
    });
  });
</script>

<style lang="scss" scoped>
  :deep(.el-button--primary.el-button--default.is-link) {
    color: #762adb;
  }
  :deep(.el-button--danger.el-button--default.is-link) {
    color: #c00c1d;
  }
  .quickOutbound {
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }
    :deep(.custom-style .el-card__body){
      padding: 20px 20px 0px 20px;
    }
    :deep(.custom-style1 .el-card__body){
      padding: 0px 20px;
    }

    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .action-bar {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
    .purchase{
      width: 100% !important;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px !important;
      font-style: normal;
      text-align: center;
      line-height: 16px !important;
      .purchase-status{
        display: block;
        padding: 4px 8px !important;
        border-radius: 2px;
      }
      .purchase-status-color1{
        background: rgba(255,156,0,0.08);
        border: 1px solid rgba(255,156,0,0.2);
        color: #FF9C00 ;
      }
      .purchase-status-color2{
        background: rgba(64,158,255,0.08);
        border: 1px solid rgba(64,158,255,0.2);
        color: #409EFF;
      }
      .purchase-status-color3{
        background: rgba(41,182,16,0.08);
        border: 1px solid rgba(41,182,16,0.2);
        color:#29B610;
      }
      .purchase-status-color4{
        background: rgba(97, 59, 236, 0.08);
        border: 1px solid rgba(97, 59, 236,0.2);
        color:#613BEC;
      }
      .purchase-status-color5{
        background: rgba(255, 77, 79, 0.08);
        border: 1px solid rgba(255, 77, 79, 0.20);
        color:#FF4D4F;
      }
      .purchase-status-color0{
        background: rgba(144,151,158,0.1);
        border: 1px solid rgba(200,201,204,0.2);
        color: #90979E;
      }
    }
    .solid-circle {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 4px;
    }
  }
  :deep(.multipleSelect .el-select__selected-item) {
    color: #151719 !important;
  }
</style>
<style lang="scss">
  .approve-popup{
    padding: 0px;
    .el-dialog__header {
      padding: 16px 30px;
      border-bottom: 1px solid #E5E7F3;
      margin-bottom: 15px;
    }
    .el-dialog__footer{
      border-top: 1px solid #E5E7F3;
    }
    .el-dialog__headerbtn{
      width: 58px;
      height: 58px;
      .el-icon{
        width: 22px;
        height: 22px;
      }
      .el-icon svg{
        width: 22px;
        height: 22px;
      }
    }
    .el-date-editor.el-input, .el-date-editor.el-input__wrapper{
      width: calc(100% - 120px) !important;
    }
    .time-button{
      margin-top: 9px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  .batch-approve-popup{
    padding: 0px;
    .el-dialog__header {
      padding: 16px 30px;
      border-bottom: 1px solid #E5E7F3;
      margin-bottom: 45px;
    }
    .el-dialog__footer{
      border-top: 1px solid #E5E7F3;
    }
    .el-dialog__headerbtn{
      width: 58px;
      height: 58px;
      .el-icon{
        width: 22px;
        height: 22px;
      }
      .el-icon svg{
        width: 22px;
        height: 22px;
      }
    }
    .el-date-editor.el-input, .el-date-editor.el-input__wrapper{
      width: calc(100% - 101.45px) !important;
    }
    .time-button{
      margin-top: 9px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
</style>
