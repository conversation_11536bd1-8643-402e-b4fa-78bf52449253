<template>
  <div class="app-container">
    <div class="addPurchase">
      <div class="page-title">
        <div @click="handleClose()" class="cursor-pointer mr8px">
          <el-icon><Back /></el-icon>
        </div>
        <div>
          {{ $t("purchase.title.goodsDetails") }}
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" label-width="82px" label-position="right">
          <div class="item_content">
            <div class="title">
              {{ $t("purchase.label.basicInformation") }}
            </div>
            <div class="grad-row">
              <el-row>
                <!-- 商品编码 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.productCode')"
                    prop="productCode"
                  >
                    {{ form.productCode || "-" }}
                  </el-form-item>
                </el-col>
                <!-- 商品名称 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.productName')"
                    prop="productName"
                  >
                    {{ form.productName || "-" }}
                  </el-form-item>
                </el-col>
                <!-- 商品分类 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.productCategory')"
                    prop="fullCategoryName"
                  >
                    {{ form.fullCategoryName }}
                  </el-form-item>
                </el-col>
                <!-- 商品描述  -->
                <el-col :span="24">
                  <el-form-item
                    :label="$t('purchase.label.productDesc')"
                    prop="productDesc"
                  >
                    {{ form.productDesc || "-" }}
                  </el-form-item>
                </el-col>
                <!-- 单位 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.unit')"
                    prop="productUnitId"
                  >
                    {{ form.productUnitName || "-" }}
                  </el-form-item>
                </el-col>
                <!-- 换算关系 -->
                <el-col :span="8" class="flex-center-start">
                  <el-form-item
                    :label="$t('purchase.label.changeRelationship')"
                    prop="conversionRelFirstNum"
                  >
                    {{ form.conversionRelFirstNum }}
                  </el-form-item>
                  <el-form-item label-width="0px" prop="productUnitId">
                    {{ form.productUnitName }}
                  </el-form-item>
                  <div class="equal">=</div>
                  <el-form-item label-width="0px" prop="conversionRelSecondNum">
                    {{ form.conversionRelSecondNum }}
                  </el-form-item>
                  <el-form-item
                    label-width="0px"
                    prop="conversionRelSecondUnitId"
                  >
                    {{ form.conversionRelSecondUnitName }}
                  </el-form-item>
                </el-col>
                <!-- 是否标品 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.isStandardCopy')"
                    prop="isStandard"
                  >
                    <span v-if="form.isStandard == 0">
                      {{ $t("common.statusYesOrNo.no") }}
                    </span>
                    <span v-else-if="form.isStandard == 1">
                      {{ $t("common.statusYesOrNo.yes") }}
                    </span>
                    <span v-else>-</span>
                  </el-form-item>
                </el-col>
                <!-- 商品属性 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.attributeType')"
                    prop="attributeType"
                  >
                      {{ form.attributeTypeName || "-" }}
                  </el-form-item>
                </el-col>
                <!-- 规格 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.productSpec')"
                    prop="productSpec"
                  >
                    {{ form.productSpec || "-" }}
                  </el-form-item>
                </el-col>
                <!-- 损耗比例 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.lossRatio')"
                    prop="lossRatio"
                  >
                    {{ form.lossRatio || "-" }}
                    <span v-if="form.lossRatio">%</span>
                  </el-form-item>
                </el-col>
                <!-- 商品长宽高 -->
                <el-col :span="8" class="flex-center-start">
                  <el-form-item
                    :label="$t('purchase.label.productlwhv')"
                    prop="length"
                  >
                    {{ $t("purchase.label.length") }}:
                    <span class="pr8">{{ form.length || "-" }}</span>
                  </el-form-item>
                  <el-form-item label-width="0px" prop="width">
                    {{ $t("purchase.label.width") }}:
                    <span class="pr8">{{ form.width || "-" }}</span>
                  </el-form-item>
                  <el-form-item label-width="0px" prop="height">
                    {{ $t("purchase.label.height") }}:
                    <span class="pr8">{{ form.height || "-" }}</span>
                  </el-form-item>
                  <el-form-item label-width="0px" prop="volume">
                    {{ $t("purchase.label.volume") }}:
                    <span class="pr8">{{ form.volume || "-" }}</span>
                  </el-form-item>
                </el-col>
                <!-- 重量 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.weight')"
                    prop="weight"
                  >
                    <span v-if="form.weight || form.weight == 0">{{ form.weight }} Kg</span>
                    <span v-else>-</span>
                  </el-form-item>
                </el-col>
                <!-- 商品品牌 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.brand')"
                    prop="productBrandId"
                  >
                    {{ form.productBrandName || "-" }}
                  </el-form-item>
                </el-col>
                <!-- 条码信息 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.barcode')"
                    prop="barcode"
                  >
                    {{ form.barcode || "-" }}
                  </el-form-item>
                </el-col>
                  <!-- 型号 -->
                  <el-col :span="8">
                      <el-form-item
                        :label="$t('purchase.label.modelNumber')"
                        prop="modelNumber"
                      >
                          {{ form.modelNumber || "-" }}
                      </el-form-item>
                  </el-col>
                  <!-- 外部编码 -->
                  <el-col :span="8">
                      <el-form-item
                        :label="$t('purchase.label.outerProductCode')"
                        prop="outerProductCode"
                      >
                          {{ form.outerProductCode || "-" }}
                      </el-form-item>
                  </el-col>
                  <!-- 颜色 -->
                  <el-col :span="8">
                      <el-form-item
                        :label="$t('purchase.label.color')"
                        prop="color"
                      >
                          {{form.colorName || "-"}}
                      </el-form-item>
                  </el-col>
                  <!-- 尺码 -->
                  <el-col :span="8">
                      <el-form-item
                        :label="$t('purchase.label.size')"
                        prop="size"
                      >
                          {{form.sizeName || "-"}}
                      </el-form-item>
                  </el-col>
                  <!-- 是否SKU -->
                  <el-col :span="8">
                    <el-form-item
                      :label="$t('purchase.label.isSku')"
                      prop="isSku"
                    >
                      <span v-if="form?.isSku == 0">{{ $t("purchase.isSKUOptionList.no") }}</span>
                      <span v-else-if="form?.isSku == 1">{{ $t("purchase.isSKUOptionList.yes") }}</span>
                      <span v-else>-</span>
                    </el-form-item>
                  </el-col>
                <!-- 备注 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.remark')"
                    prop="remark"
                  >
                    {{ form.remark || "-" }}
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="item_content">
            <div class="title">
              {{ $t("purchase.label.saleInformation") }}
            </div>
            <div class="grad-row">
              <el-row>
                <!-- 销售价格 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.saleAmount')"
                    prop="saleAmount"
                  >
                    <span v-if="form.saleAmount || form.saleAmount == 0">￥ {{form.saleAmount}}</span>
                    <span v-else>-</span>
                  </el-form-item>
                </el-col>
                <!-- 浮动区间 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.saleAmountRadio')"
                    prop="saleAmountRadio"
                  >
                    {{ form.saleAmountRadio || "-" }}
                    <span v-if="form.saleAmountRadio">%</span>
                  </el-form-item>
                </el-col>
                <!-- 是否可售卖 -->
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.isSalable')"
                    prop="isSalable"
                  >
                    <el-switch
                      :active-text="$t('common.statusYesOrNo.yes')"
                      :inactive-text="$t('common.statusYesOrNo.no')"
                      inline-prompt
                      style="
                        --el-switch-on-color: #762adb;
                        --el-switch-off-color: #cccfd5;
                      "
                      v-model="form.isSalable"
                      :active-value="1"
                      :inactive-value="0"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
            <div class="item_content">
                <div class="title">
                    {{ $t("purchase.label.repertoryInformation") }}
                </div>
                <div class="grad-row">
                <el-row :gutter="20" class="mb-20px">
<!--                    &lt;!&ndash; 允许负库存 &ndash;&gt;-->
<!--                    <el-col :span="8">-->
<!--                        <el-form-item-->
<!--                          :label="$t('purchase.label.enableNegStock')"-->
<!--                          prop="enableNegStock"-->
<!--                        >-->
<!--                            <el-switch-->
<!--                              :active-text="$t('common.statusYesOrNo.yes')"-->
<!--                              :inactive-text="$t('common.statusYesOrNo.no')"-->
<!--                              inline-prompt-->
<!--                              style="-->
<!--                      &#45;&#45;el-switch-on-color: #762adb;-->
<!--                      &#45;&#45;el-switch-off-color: #cccfd5;-->
<!--                    "-->
<!--                              v-model="form.enableNegStock"-->
<!--                              :active-value="1"-->
<!--                              :inactive-value="0"-->
<!--                              disabled-->

<!--                            />-->
<!--                        </el-form-item>-->
<!--                    </el-col>-->



                  <!-- 所属仓库 -->
                  <el-col :span="8" class="mb-18px">
                    <el-form-item
                      :label="$t('purchase.label.warehouseName')"
                    >
                      {{ warehouseName }}
                    </el-form-item>
                  </el-col>
                  <!-- 存储条件 -->
                  <el-col :span="8">
                    <el-form-item
                      :label="$t('purchase.label.storageCondition')"
                      prop="storageCondition"
                    >
                      {{ form.storageCondition || "-" }}
                    </el-form-item>
                  </el-col>
                  <!-- 保质期 -->
                  <el-col :span="8" class="flex-center-start">
                    <el-form-item
                      :label="$t('purchase.label.shelfLife')"
                      prop="shelfLife"
                    >
                      {{ form.shelfLife }}
                    </el-form-item>
                    <el-form-item label-width="0px" prop="shelfLifeUnit">
                      <span v-if="form.shelfLifeUnit == 1">
                        {{ $t("purchase.shelfLifeUnitList.day") }}
                      </span>
                      <span v-else-if="form.shelfLifeUnit == 2">
                        {{ $t("purchase.shelfLifeUnitList.month") }}
                      </span>
                      <span v-else-if="form.shelfLifeUnit == 3">
                        {{ $t("purchase.shelfLifeUnitList.year") }}
                      </span>
                      <span v-else>-</span>
                    </el-form-item>
                  </el-col>
                  <!--安全库存 -->
                  <el-col :span="8">
                    <el-form-item
                      :label="$t('purchase.label.safetyStock')"
                      prop="safetyStock"
                    >
                      <span v-if="!isEmpty(form.safetyStock)">{{form.safetyStock}} {{safetyStockUnit}}</span>
                      <span v-else>-</span>
                    </el-form-item>
                  </el-col>
                  <!--库存上限 -->
                  <el-col :span="8">
                      <el-form-item
                        :label="$t('purchase.label.stockUpperLimit')"
                        prop="stockUpperLimit"
                      >
                        <span v-if="!isEmpty(form.stockUpperLimit)">{{form.stockUpperLimit}} {{safetyStockUnit}}</span>
                        <span v-else>-</span>
                      </el-form-item>
                  </el-col>
                  <!--库存下限 -->
                  <el-col :span="8">
                      <el-form-item
                        :label="$t('purchase.label.stockLowerLimit')"
                        prop="stockLowerLimit"
                      >
                        <span v-if="!isEmpty(form.stockLowerLimit)">{{form.stockLowerLimit}} {{safetyStockUnit}}</span>
                        <span v-else>-</span>
                      </el-form-item>
                  </el-col>


                    <!-- 一级单位增减 -->
                    <el-col :span="8">
                        <el-form-item
                          :label="$t('purchase.label.isDiscreteUnit')"
                          prop="isDiscreteUnit"
                          label-width="120px"
                        >
                            <el-switch
                              :active-text="$t('common.statusYesOrNo.yes')"
                              :inactive-text="$t('common.statusYesOrNo.no')"
                              inline-prompt
                              style="
                      --el-switch-on-color: #762adb;
                      --el-switch-off-color: #cccfd5;
                    "
                              v-model="form.isDiscreteUnit"
                              :active-value="1"
                              :inactive-value="0"
                              disabled
                            />
                        </el-form-item>
                    </el-col>
                    <!-- 计价模式 -->
                    <el-col :span="8">
                      <el-form-item
                        :label="$t('purchase.label.pricingScheme')"
                        prop="pricingScheme"
                      >
                        <span v-if="form.pricingScheme == 0">{{ $t("purchase.pricingSchemeOptionList.firstLevelUnit") }}</span>
                        <span v-else-if="form.pricingScheme == 1">{{ $t("purchase.pricingSchemeOptionList.secondLevelUnit") }}</span>
                        <span v-else>-</span>
                      </el-form-item>
                    </el-col>
                </el-row>
                </div>
            </div>
          <div class="item_content">
            <div class="title">
              {{ $t("purchase.label.graphicInformation") }}
            </div>
            <div class="grad-row">
              <el-row>
                <!-- 商品图片 -->
                <el-col :span="24">
                  <el-form-item
                    :label="$t('purchase.label.imagesUrls')"
                    prop="imagesUrls"
                  >
                    <upload-multiple
                      :tips="$t('purchase.message.pictureTip')"
                      :fileSize="10"
                      :isPrivate="`public-read`"
                      :modelValue="form.imagesUrls"
                      @update:model-value="onChangeMultiple"
                      ref="detailPicsRef"
                      :limit="form.imagesUrls?.length"
                      :formRef="formUpdateRef"
                      class="modify-multipleUpload"
                      name="detailPic"
                      :isDelete="false"
                    >
                      <template #default="{ file }">点击上传</template>
                    </upload-multiple>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="item_content">
            <div class="title">
              <div class="flex_style">
                <div>{{ $t("purchase.label.supplyChainInformation") }}</div>
              </div>
            </div>

            <el-row>
              <el-table
                v-loading="loading"
                :data="form.supplierList"
                highlight-current-row
                stripe
              >
                <el-table-column
                  :label="$t('purchase.label.supplierName')"
                  min-width="150"
                >
                  <template #default="scope">
                    <el-tooltip
                      :content="scope.row.supplierName"
                      placement="top"
                      effect="dark"
                    >
                      <span>{{ scope.row.supplierName }}</span>
                    </el-tooltip>
                    <span
                      v-if="scope.row.isDefault == 1"
                      class="default-supplier"
                    >
                      {{ $t("purchase.label.defaults") }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('purchase.label.supplierCode')"
                  prop="supplierCode"
                  show-overflow-tooltip
                />
                <el-table-column
                  :label="$t('purchase.label.supplierWarehouseName')"
                  prop="supplierWarehouseName"
                  show-overflow-tooltip
                />
              </el-table>
            </el-row>
          </div>
          <div class="item_content" style="border-bottom: unset !important">
            <div class="title">
              {{ $t("purchase.label.productStatusInformation") }}
            </div>
            <div class="grad-row">
              <el-row>
                <el-col :span="8">
                  <el-form-item
                    :label="$t('purchase.label.statusCopy')"
                    prop="status"
                  >
                    <div class="purchase">
                      <div
                        class="purchase-status purchase-status-color3"
                        v-if="form.status == 1"
                        type="success"
                      >
                        {{ $t("purchase.statusList.haveBeenPutOnShelves") }}
                      </div>
                      <div
                        class="purchase-status purchase-status-color1"
                        v-else
                        type="info"
                      >
                        {{ $t("purchase.statusList.haveBeenGetOffShelves") }}
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">
          {{ $t("purchase.button.close") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { isEmpty } from "lodash-es";

defineOptions({
  name: "GoodsDetails",
  inheritAttrs: false,
});
import PurchaseAPI, { PurchaseFrom } from "@/modules/goods/api/purchase";
import { useRoute, useRouter } from "vue-router";
import {useTagsViewStore, useWarehouseStore} from "@/core/store";

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const formRef = ref(ElForm);
const loading = ref(false);
const id = route.query.id;

const formUpdateRef = ref(null);

// 角色表单
let form = reactive<PurchaseFrom>({
  imagesUrls: [],
  conversionRelFirstNum: 1,
  shelfLifeUnit: 2,
  pricingScheme: 0,
  isSku: undefined
});

//安全库存单位
const safetyStockUnit = computed(() => {
  return form.isDiscreteUnit ? form?.productUnitName : form?.conversionRelSecondUnitName
})
const warehouseStore = useWarehouseStore();
const warehouseName = computed(() => {
  return warehouseStore.getSelectedWarehouseName;
});

function onChangeMultiple(val) {
  form.imagesUrls = val ? val : "";
  form.mainImageUrl = val ? val[0].fileName : "";
  if (form.imagesUrls && form.imagesUrls.length > 0) {
    formRef.value.clearValidate("imagesUrls"); //清除图片校验文字
  }
  console.info("==form.imagesUrls==" + form);
}

async function handleClose() {
  await tagsViewStore.delView(route);
  router.push({
    path: "/wms/products",
  });
}

/** 查询商品详情列表 */
function queryPurchaseDetail() {
  loading.value = true;
  let params = {
    id: id,
  };
  PurchaseAPI.queryPurchaseDetail(params)
    .then((data) => {
      Object.assign(form, data);
      if (form.imagesUrls && typeof form.imagesUrls === "string") {
        form.imagesUrls = JSON.parse(form.imagesUrls);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

onMounted(() => {
  queryPurchaseDetail();
});
</script>
<style scoped lang="scss">
.addPurchase {
  background: #ffffff;
  border-radius: 4px;
  .pr8 {
    padding-right: 8px;
  }
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
    .flex_style {
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;
    }
    .equal {
      height: 32px;
      line-height: 32px;
      padding: 0px 8px;
      margin-bottom: 18px;
    }
    .button-add {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary);
    }
    .default-supplier {
      margin-left: 8px;
      padding: 2px 7px;
      background: #fe8200;
      border-radius: 4px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 16px;
      text-align: left;
      font-style: normal;
    }
  }
}
</style>
