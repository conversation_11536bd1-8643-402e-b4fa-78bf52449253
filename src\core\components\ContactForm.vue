<template>
  <div class="contact-form">
    <div class="form-subtitle">
      {{ getProductTitle() }}
    </div>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent="handleSubmit"
      label-position="top"
    >
      <el-form-item :label="$t('contactForm.fields.enterpriseName')" prop="enterpriseName">
        <el-input
          v-model="formData.enterpriseName"
          :placeholder="$t('contactForm.placeholders.enterpriseName')"
          clearable
          :aria-label="$t('contactForm.fields.enterpriseName')"
        />
      </el-form-item>

      <el-form-item :label="$t('contactForm.fields.contactName')" prop="contact">
        <el-input
          v-model="formData.contact"
          :placeholder="$t('contactForm.placeholders.contactName')"
          clearable
          :aria-label="$t('contactForm.fields.contactName')"
          :aria-required="true"
          maxlength="20"
        />
      </el-form-item>

      <el-form-item :label="$t('contactForm.fields.phoneNumber')" prop="contactNumber">
        <div class="phone-input w-100%">
          <el-select
            v-model="formData.contactAreaCode"
            class="country-code"
            placeholder="区号"
          >
            <el-option v-for="item in countryNumCodeList" :label="item.internationalCode" :value="item.internationalCode" />
          </el-select>
          <el-input
            v-model="formData.contactNumber"
            :placeholder="$t('contactForm.placeholders.phoneNumber')"
            clearable
            style="flex: 1"
            :aria-label="$t('contactForm.fields.phoneNumber')"
            :aria-required="true"
          />
        </div>
      </el-form-item>

      <el-form-item :label="$t('contactForm.fields.email')" prop="email">
        <el-input
          v-model="formData.email"
          :placeholder="$t('contactForm.placeholders.email')"
          clearable
          :aria-label="$t('contactForm.fields.email')"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item :label="$t('contactForm.fields.message')" prop="intentionDescription">
        <el-input
          v-model="formData.intentionDescription"
          type="textarea"
          :placeholder="$t('contactForm.placeholders.message')"
          :rows="4"
          class="message-textarea"
          :aria-label="$t('contactForm.fields.message')"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <div class="form-actions">
        <el-button @click="handleCancel">
          {{ $t("contactForm.buttons.cancel") }}
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="props.submitting">
          {{ $t("contactForm.buttons.submit") }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { ContactFormData } from '@/core/api/contact'
import UserAPI from "@/core/api/accountManagement";
defineOptions({
  name: 'ContactForm'
})

// Props
interface Props {
  submitting?: boolean
  productType?: string
}

const props = withDefaults(defineProps<Props>(), {
  submitting: false,
  productType: ''
})

// Emits
const emit = defineEmits<{
  submit: [data: ContactFormData]
  cancel: []
}>()

// Refs
const formRef = ref<FormInstance>()

// Form data
const formData = reactive<ContactFormData>({
  "enterpriseName": "",
  "appCode": "",
  "contact": "",
  "contactAreaCode": "+86",
  "contactNumber": "",
  "email": "",
  "intentionDescription": ""
})

// Validation rules
const formRules: FormRules = {
  contact: [
    {
      required: true,
      message: () => useI18n().t('contactForm.validation.contactNameRequired'),
      trigger: 'blur'
    },
    {
      max: 20,
      message: () => useI18n().t('contactForm.validation.contactNameLength'),
      trigger: 'blur'
    }
  ],
  contactNumber: [
    {
      required: true,
      message: () => useI18n().t('contactForm.validation.phoneNumberRequired'),
      trigger: 'blur'
    },
    {
      pattern: /^\d+$/,
      message: () => useI18n().t('contactForm.validation.phoneNumberInvalid'),
      trigger: 'blur'
    }
  ],
  email: [
    {
      type: 'email',
      message: () => useI18n().t('contactForm.validation.emailInvalid'),
      trigger: 'blur'
    },
    {
      max: 50,
      message: () => useI18n().t('contactForm.validation.emailLength'),
      trigger: 'blur'
    }
  ],
  intentionDescription: [
    {
      max: 200,
      message: () => useI18n().t('contactForm.validation.messageLength'),
      trigger: 'blur'
    }
  ]
}


const countryNumCodeList = ref([])
// 获取区号
function getAreaList() {
	UserAPI.getAllCountry()
		.then((data) => {
			countryNumCodeList.value = data;
		})
		.finally(() => { });
}
// Methods
const getProductTitle = () => {
  const { t } = useI18n()
  switch (props.productType) {
    case 'customerSteward':
      return t('applicationCenter.cardTitle.customerStewardTitle')
    case 'wms':
      return t('applicationCenter.cardTitle.WMSTitle')
    case 'jyPlatform':
      return t('applicationCenter.cardTitle.JYTitle')
    default:
      return t('contactForm.subtitle')
  }
}

const handleSubmit = async () => {
  if (!formRef.value || props.submitting) return
  
  try {
    await formRef.value.validate()
    
    // Set appCode based on productType
    const appCodeMap: Record<string, string> = {
      'customerSteward': '1',
      'wms': '2', 
      'jyPlatform': '3'
    }
    
    const submitData = {
      ...formData,
      appCode: appCodeMap[props.productType] || '1'
    }
    
    emit('submit', submitData)
  } catch (error) {
    console.log('Form validation failed:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
}

// Reset form
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    enterpriseName: '',
    appCode: '',
    contact: '',
    contactAreaCode: '+86',
    contactNumber: '',
    email: '',
    intentionDescription: ''
  })
}

onMounted(() => {
  getAreaList()
})
// Expose methods
defineExpose({
  resetForm
})
</script>

<style scoped lang="scss">
.contact-form {
  .form-subtitle {
    color: #762adb;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    text-align: center;
  }

  .el-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .phone-input {
      display: flex;
      gap: 8px;

      .country-code {
        width: 80px;
        flex-shrink: 0;
      }

      // Responsive design for phone input
      @media (max-width: 480px) {
        flex-direction: column;
        gap: 10px;

        .country-code {
          width: 100%;
        }
      }
    }

    .message-textarea {
      .el-textarea__inner {
        min-height: 100px;
        resize: vertical;
      }
    }
  }

  .form-actions {
    text-align: right;
    margin-top: 30px;

    .el-button {
      margin-left: 10px;
    }

    // Responsive design for form actions
    @media (max-width: 480px) {
      text-align: center;
      
      .el-button {
        margin: 5px;
        width: 100px;
      }
    }
  }

  // Responsive design for form labels
  @media (max-width: 768px) {
    .el-form {
      :deep(.el-form-item__label) {
        font-size: 14px;
      }
    }
  }

  @media (max-width: 480px) {
    .form-subtitle {
      font-size: 14px;
      margin-bottom: 15px;
    }

    .el-form {
      :deep(.el-form-item) {
        margin-bottom: 15px;
      }

      :deep(.el-form-item__label) {
        font-size: 13px;
        line-height: 1.4;
      }
    }

    .form-actions {
      margin-top: 20px;
    }
  }
}
</style>