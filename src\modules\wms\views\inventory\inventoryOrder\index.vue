<template>
  <div class="app-container">
    <div class="inventory-count">
      <div class="search-container">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
          <el-form-item prop="countNo" :label="$t('inventoryCount.label.countNo')">
            <el-input
              class="!w-[256px]"
              v-model="queryParams.checkCode"
              :placeholder="$t('inventoryCount.placeholder.countNo')"
              clearable
            />
          </el-form-item>
          <el-form-item prop="warehouseAreaCode" :label="'盘点库区'">
            <el-select
              v-model="queryParams.warehouseAreaCode"
              :placeholder="$t('inventoryCount.placeholder.warehouseArea')"
              clearable
              filterable
              class="!w-[256px]"
            >
              <el-option
                v-for="item in warehouseAreaList"
                :key="item.areaCode"
                :label="item.warehouseArea"
                :value="item.areaCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="checkStatus" :label="$t('inventoryCount.label.countStatus')">
            <el-select
              v-model="queryParams.checkStatus"
              :placeholder="$t('inventoryCount.placeholder.countStatus')"
              clearable
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              class="!w-[256px]"
            >
              <el-option
                v-for="(item, index) in countStatusList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- receiptStatus领用状态 0-未领用 1-已领用 -->
          <el-form-item prop="receiptStatus" :label="'是否领单'">
            <el-select
              v-model="queryParams.receiptStatus"
              :placeholder="'请选择'"
              clearable
              filterable
              class="!w-[256px]"
            >
              <el-option
                v-for="(item, index) in receiptStatusList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
         
          <el-form-item prop="createTimeRange">
            <el-select
              v-model="queryParams.queryTimeType"
              :placeholder="'请选择时间类型'"
              class="!w-[120px] mr-2"
            >
              <el-option v-for="item in queryTimeTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
            </el-select>
            <el-date-picker
              v-model="queryParams.dateRange"
              type="datetimerange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="defaultTime"
              class="!w-[350px]"
            />
            <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(1)">{{$t('profitAndLossManagement.label.today')}}</span>
            <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(2)">{{$t('profitAndLossManagement.label.yesterday')}}</span>
            <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(3)">{{$t('profitAndLossManagement.label.weekday')}}</span>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" v-hasPerm="['wms:inventory:inventoryOrder:list']">
              {{ $t('common.search') }}
            </el-button>
            <el-button @click="handleResetQuery" v-hasPerm="['wms:inventory:inventoryOrder:list']">
              {{ $t('common.reset') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-card shadow="never" class="table-container">
        <div class="mb-4 flex justify-between">
          <div>
            <!-- <el-button type="primary" @click="handleAdd">{{ $t('inventoryCount.button.add') }}</el-button> -->
            <!-- <el-button type="danger" :disabled="!selectedRows.length" @click="handleBatchDelete">{{ $t('common.delete') }}</el-button> -->
            <!-- <el-button type="success" @click="handleExport">{{ $t('inventoryCount.button.export') }}</el-button> -->
          </div>
        </div>
        <el-table
          v-loading="loading"
          :data="tableData"
          highlight-current-row
          stripe
          @selection-change="handleSelectionChange"
          max-height="600"
        >
          <template #empty>
            <Empty/>
          </template>
          
          <!-- 序号列 -->
          <el-table-column label="序号" type="index" width="60" :index="(index) => index + 1" fixed="left"/>
          
          <!-- 盘点单号 -->
          <el-table-column prop="checkCode" label="盘点单号" show-overflow-tooltip min-width="120" />
          
          <!-- 盘点库区 -->
          <el-table-column prop="warehouseAreaName" label="盘点库区" show-overflow-tooltip min-width="120" />
          
          <!-- 领用信息 -->
          <el-table-column label="领用信息" show-overflow-tooltip min-width="150">
            <template #default="scope">
                <div>
                  <div>
                    <span>领用人：{{ scope.row.handleUserName }}</span>
                  </div>
                  <div>
                    <span>领用时间：{{ parseDateTime(scope.row.handleTime, 'dateTime') || '-' }}</span>
                  </div>
                </div>
            </template>
          </el-table-column>
          
          <!-- 盘点前商品 -->
          <el-table-column label="盘点前商品" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <div v-if="scope.row.beforeCheckQty || scope.row.beforeCheckTotalQty">
                <div v-if="scope.row.beforeCheckQty">个数：{{ scope.row.beforeCheckQty }}</div>
                <div v-if="scope.row.beforeCheckTotalQty">数量：{{ scope.row.beforeCheckTotalQty }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 盘点后商品 -->
          <el-table-column label="盘点后商品" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <div v-if="scope.row.afterCheckQty || scope.row.afterCheckTotalQty">
                <div v-if="scope.row.afterCheckQty">个数：{{ scope.row.afterCheckQty }}</div>
                <div v-if="scope.row.afterCheckTotalQty">数量：{{ scope.row.afterCheckTotalQty }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 处理人 -->
          <el-table-column prop="operationUserName" label="处理人" show-overflow-tooltip min-width="100" />
          
          <!-- 处理时间 -->
          <el-table-column prop="operationTime" label="处理时间" show-overflow-tooltip min-width="150">
            <template #default="scope">
              <span v-if="scope.row.operationTime">{{ parseDateTime(scope.row.operationTime, 'dateTime') }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 处理方式 -->
          <el-table-column label="处理方式" show-overflow-tooltip min-width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.operationType === 1" type="warning">损益</el-tag>
              <el-tag v-else-if="scope.row.operationType === 2" type="primary">移库</el-tag>
              <el-tag v-else-if="scope.row.operationType === 3" type="info">关闭</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 处理意见 -->
          <el-table-column prop="operationRemark" label="处理意见" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <span v-if="scope.row.operationRemark">{{ scope.row.operationRemark }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 处理单号 -->
          <el-table-column prop="operationCode" label="处理单号" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <span v-if="scope.row.operationCode">{{ scope.row.operationCode }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 备注 -->
          <el-table-column prop="remark" label="备注" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 创建人 -->
          <el-table-column prop="createUserName" label="创建人" show-overflow-tooltip min-width="100">
            <template #default="scope">
              <span v-if="scope.row.createUserName">{{ scope.row.createUserName }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <!-- 创建时间 -->
          <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip min-width="150">
            <template #default="scope">
              <span v-if="scope.row.createTime">{{ parseDateTime(scope.row.createTime, 'dateTime') }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <el-table-column :label="$t('inventoryCount.label.countStatus')">
            <template #default="scope">
              <el-tag v-if="scope.row.checkStatus === 0" type="info">{{ $t('inventoryCount.countStatus.draft') }}</el-tag>
              <el-tag v-else-if="scope.row.checkStatus === 1" type="warning">{{ $t('inventoryCount.countStatus.counting') }}</el-tag>
              <el-tag v-else-if="scope.row.checkStatus === 3" type="success">{{ $t('inventoryCount.countStatus.completed') }}</el-tag>
              <el-tag v-else-if="scope.row.checkStatus === 2" type="success">{{ $t('inventoryCount.countStatus.checked') }}</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('inventoryCount.label.operation')" width="230" fixed="right">
            <template #default="scope">
              <el-button
                v-hasPerm="['wms:inventory:inventoryOrder:detail']"
                link
                type="primary"
                @click="handleDetail(scope.row)"
                v-if="detailBtnShow(scope.row)"
              >详情</el-button>
              <el-button
                v-hasPerm="['wms:inventory:inventoryOrder:complete']"
                link
                type="primary"
                @click="handleProcess(scope.row)"
                v-if="checkBtnShow(scope.row)"
              >处理</el-button>
              <el-button
                v-hasPerm="['wms:inventory:inventoryOrder:delete']"
                link
                type="primary"
                v-if="delBtnShow(scope.row)"
                @click="handleDelete(scope.row)"
              >{{ $t('common.delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
    </div>
    
    <!-- 盘点处理弹窗 -->
    <el-dialog
      v-model="processDialogVisible"
      title="盘点处理"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="process-dialog">
        <!-- 商品信息表格 -->
        <el-table
          :data="processTableData"
          border
          style="margin-bottom: 20px;"
          v-loading="processLoading"
          element-loading-text="加载中..."
          height="300px"
        >
          <el-table-column prop="productName" label="商品名称" show-overflow-tooltip min-width="120" />
          <!-- <el-table-column prop="productSpec" label="商品规格" show-overflow-tooltip min-width="100" /> -->
          <!-- <el-table-column prop="productUnit" label="单位" width="60" /> -->
          <el-table-column prop="beforeCheckTotalQty" label="库区总库存" align="right" min-width="100" />
          <el-table-column prop="afterCheckTotalQty" label="盘点后总库存" align="right" min-width="100" />
          <el-table-column label="数量差值" align="right" min-width="100">
            <template #default="scope">
              <span :style="{ color: getDiffColor(scope.row.beforeCheckTotalQty, scope.row.afterCheckTotalQty) }">
                {{ (scope.row.afterCheckTotalQty || 0) - (scope.row.beforeCheckTotalQty || 0) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 处理表单 -->
        <el-form
          ref="processFormRef"
          :model="processForm"
          :rules="processFormRules"
          label-width="100px"
          v-loading="processSubmitting"
          element-loading-text="处理中..."
        >
          <el-form-item label="处理方式：" prop="operationType">
            <el-select
              v-model="processForm.operationType"
              placeholder="请选择处理方式"
              style="width: 200px;"
              @change="onOperationTypeChange"
            >
              <el-option label="损益" :value="1" />
              <el-option label="移库" :value="2" />
              <el-option label="关闭" :value="3" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="处理意见：" prop="operationRemark">
            <el-input
              v-model="processForm.operationRemark"
              type="textarea"
              :rows="3"
              placeholder="输入内容如：已于X年X月X日损益"
              style="width: 400px;"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
          
          <!-- 损益单选择 -->
          <el-form-item
            v-if="processForm.operationType === 1"
            label="处理单号："
            prop="operationCode"
          >
            <el-select
              v-model="processForm.operationCode"
              placeholder="请选择已完结的损益单"
              style="width: 300px;"
              filterable
              clearable
            >
              <el-option
                v-for="item in profitLossOrderList"
                :key="item"
                :label="`${item}`"
                :value="item"
              />
            </el-select>
          </el-form-item>
          
          <!-- 移库单选择 -->
          <el-form-item
            v-if="processForm.operationType === 2"
            label="处理单号："
            prop="operationCode"
          >
            <el-select
              v-model="processForm.operationCode"
              placeholder="请选择已完结的移库单"
              style="width: 300px;"
              filterable
              clearable
            >
              <el-option
                v-for="item in transferOrderList"
                :key="item.transferOrderCode"
                :label="`${item.transferOrderCode}`"
                :value="item.transferOrderCode"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processDialogVisible = false" :disabled="processSubmitting">取消</el-button>
          <el-button type="primary" @click="submitProcess" :loading="processSubmitting">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onActivated, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { parseDateTime, changeDateRange, convertToTimestamp} from '@/core/utils';
import { emitter } from '@/core/utils/eventBus';
import InventoryCountApi, { InventoryCountPageQuery, InventoryCountVO } from '@/modules/wms/api/inventoryCount';
import CommonAPI from '@/modules/wms/api/common';
import moment from 'moment';

defineOptions({
  name: 'InventoryCount',
  inheritAttrs: false,
});

const router = useRouter();
const { t } = useI18n();
const queryFormRef = ref<FormInstance>();
const processFormRef = ref<FormInstance>();
const loading = ref(false);
const tableData = ref<InventoryCountVO[]>([]);
const total = ref(0);
const warehouseAreaList = ref<any[]>([]);
const selectedRows = ref<InventoryCountVO[]>([]);

// 处理弹窗相关状态
const processDialogVisible = ref(false);
const processTableData = ref<any[]>([]);
const processLoading = ref(false);
const processSubmitting = ref(false);
const processForm = reactive({
  checkCode: '',
  operationType: undefined as number | undefined,
  operationRemark: '',
  operationCode: ''
});

// 损益单列表
const profitLossOrderList = ref<any[]>([]);
// 移库单列表
const transferOrderList = ref<any[]>([]);

// 处理表单验证规则
const processFormRules = {
  operationType: [
    { required: true, message: '请选择处理方式', trigger: 'change' }
  ],
  operationRemark: [
    { required: true, message: '请输入处理意见', trigger: 'blur' }
  ],
  operationCode: [
    { required: false, message: '请选择处理单号', trigger: 'change' }
  ]
};

// 默认时间
const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];

// 查询时间类型列表：1:创建时间 2:盘点时间 3:领单时间
const queryTimeTypeList = [
  { key: 1, value: '创建时间' },
  { key: 2, value: '盘点时间' },
  { key: 3, value: '领单时间' }
];

// 盘点状态列表
const countStatusList = [
  { value: 0, label: t('inventoryCount.countStatus.draft') },
  { value: 1, label: t('inventoryCount.countStatus.counting') },
  { value: 2, label: t('inventoryCount.countStatus.checked') }, // 已盘点
  { value: 3, label: t('inventoryCount.countStatus.completed') }
];

const detailBtnShow = (row: InventoryCountVO) => {

  // 盘点中、已盘点、完结状态：要显示详情按钮
  return row.checkStatus != 0;
}

const delBtnShow = (row: InventoryCountVO) => {
  // 领用状态=否且状态=草稿
  return row.checkStatus == 0 && row.receiptStatus == 0;
}

const checkBtnShow = (row: InventoryCountVO) => {
  // 已盘点：要显示处理按钮
  return row.checkStatus == 2;
}

// 领用状态列表
const receiptStatusList = [
  { value: 0, label: '否' },
  { value: 1, label: '是' }
];

// 查询参数
const queryParams = reactive<Partial<InventoryCountPageQuery> & {
  page: number;
  limit: number;
  receiptStatus?: number;
  queryTimeType?: number;
  dateRange?: string[];
  startTime?: string;
  endTime?: string;
}>({
  page: 1,
  limit: 20,
  checkStatus: undefined,
  warehouseAreaCode: '',
  receiptStatus: undefined,
  queryTimeType: 1, // 默认创建时间
  dateRange: [
    moment().subtract('days', 29).format('YYYY-MM-DD') + ' 00:00:00',
    moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59'
  ],
});

/** 查询库区列表 */
function getWarehouseAreaList() {
  CommonAPI.getOutWarehouseAreaList().then((data: any) => {
    console.log(data);
    warehouseAreaList.value = data;
    if (warehouseAreaList.value && warehouseAreaList.value.length > 0) {
      warehouseAreaList.value.map((item) => {
        item.warehouseArea = item.areaName + '|' + item.areaCode;
        return item;
      });
    }
  });
}
 /** 时间转换 */
 function handleChangeDateRange(val: any) {
      queryParams.dateRange = changeDateRange(val);
      handleQuery();
  }
/** 查询盘点单列表 */
function handleQuery() {
  loading.value = true;
  
  // 处理查询参数
  const params = { ...queryParams };
  
  // 处理时间参数
  if (params.dateRange && params.dateRange.length === 2) {
    params.startTime = params.dateRange[0];
    params.endTime = params.dateRange[1];
  }
  delete params.dateRange;
  
  // 过滤掉 undefined 值
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([key, value]) => value !== undefined)
  ) as InventoryCountPageQuery;
  filteredParams.startTime = convertToTimestamp(filteredParams.startTime);
  filteredParams.endTime = convertToTimestamp(filteredParams.endTime);
  filteredParams.receiptStatus = params.receiptStatus != null ? [params.receiptStatus] : [];
  InventoryCountApi.getInventoryCountPage(filteredParams)
    .then((response) => {
      tableData.value = response.records || [];
      total.value = parseNumber(response.total) || 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 表格选择行变化 */
function handleSelectionChange(selection: InventoryCountVO[]) {
  selectedRows.value = selection;
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  queryParams.queryTimeType = 1; // 重置为默认创建时间
  queryParams.dateRange = [
    moment().subtract('days', 29).format('YYYY-MM-DD') + ' 00:00:00',
    moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59'
  ];
  handleQuery();
}

/** 添加盘点单 */
function handleAdd() {
  router.push({
    path: '/wms/inventory/inventoryOrder/add'
  });
}

/** 查看盘点单详情 */
function handleDetail(row: InventoryCountVO) {
  router.push({
    path: `/wms/inventory/inventoryOrder/detail`,
    query: {
      id: row.checkCode
    }
  });
}

/** 编辑盘点单 */
/* function handleUpdate(row: InventoryCountVO) {
  router.push({
    path: `/inventory/inventoryCount/edit/${row.id}`
  });
} */

/** 删除盘点单 */
function handleDelete(row: InventoryCountVO) {
  ElMessageBox.confirm(
    '是否确认删除已创建的盘点单，删除后不可恢复',
   '提示',
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning',
    }
  )
    .then(() => {
      InventoryCountApi.deleteInventoryCount(row.checkCode).then(() => {
        ElMessage.success(t('inventoryCount.message.deleteSuccess'));
        handleQuery();
      });
    })
    .catch(() => {});
}

/** 批量删除盘点单 */
function handleBatchDelete() {
  const ids = selectedRows.value.map(item => item.id);
  if (!ids.length) {
    return;
  }
  
  ElMessageBox.confirm(
    t('inventoryCount.message.deleteConfirm'),
    t('common.warning'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning',
    }
  )
    .then(() => {
      InventoryCountApi.deleteInventoryCount(ids).then(() => {
        ElMessage.success(t('inventoryCount.message.deleteSuccess'));
        handleQuery();
      });
    })
    .catch(() => {});
}

/** 处理方式变化 */
function onOperationTypeChange(operationType: number) {
  // 清空处理单号
  processForm.operationCode = '';
  
  // 动态更新验证规则
  if (operationType === 1 || operationType === 2) {
    // 损益或移库时，处理单号必填
    processFormRules.operationCode = [
      { required: true, message: '请选择处理单号', trigger: 'change' }
    ];
  } else {
    // 关闭时，处理单号不必填
    processFormRules.operationCode = [
      { required: false, message: '请选择处理单号', trigger: 'change' }
    ];
  }
  
  // 根据类型获取对应的单据列表
  if (operationType === 1) {
    getProfitLossOrderList();
  } else if (operationType === 2) {
    getTransferOrderList();
  }
}


/** 获取损益单列表 */
function getProfitLossOrderList() {
  InventoryCountApi.getLossCodeList().then((data) => {
    profitLossOrderList.value = data;
  });
}

/** 获取移库单列表 */
function getTransferOrderList() {
  InventoryCountApi.getTransferCodeList().then((data) => {
    transferOrderList.value = data;
  });
}

/** 处理盘点单 */
function handleProcess(row: InventoryCountVO) {
  processForm.checkCode = row.checkCode || '';
  processForm.operationType = undefined;
  processForm.operationRemark = '';
  processForm.operationCode = '';
  
  // 重置验证规则
  processFormRules.operationCode = [
    { required: false, message: '请选择处理单号', trigger: 'change' }
  ];
  
  // 清空表格数据并打开弹窗
  processTableData.value = [];
  processDialogVisible.value = true;
  
  // 调用详情接口获取商品数据
  if (row.checkCode) {
    processLoading.value = true;
    InventoryCountApi.getInventoryCountDetail(row.checkCode)
      .then((response) => {
        // 从返回数据中获取detailList
        const detailList = response?.detailList || [];

        processTableData.value = detailList.map((item: any) => ({
          id: item.id,
          productCode: item.productCode,
          productName: item.productName,
          productSpec: item.productSpec,
          productUnit: item.productUnit,
          beforeCheckTotalQty: item.beforeCheckTotalQty || 0,
          afterCheckTotalQty: item.afterCheckTotalQty || 0,
          // 其他字段
          warehouseCode: item.warehouseCode,
          checkId: item.checkId,
          checkCode: item.checkCode,
          productUnitId: item.productUnitId,
          firstCategoryId: item.firstCategoryId,
          secondCategoryId: item.secondCategoryId,
          thirdCategoryId: item.thirdCategoryId,
          firstCategoryName: item.firstCategoryName,
          secondCategoryName: item.secondCategoryName,
          thirdCategoryName: item.thirdCategoryName
        }));
      })
      .catch((error) => {
        console.error('获取盘点详情失败:', error);
        ElMessage.error('获取盘点详情失败');
      })
      .finally(() => {
        processLoading.value = false;
      });
  } else {
    ElMessage.error('盘点单号不能为空');
  }
}

/** 获取差值颜色 */
function getDiffColor(before: number, after: number): string {
  const diff = (after || 0) - (before || 0);
  if (diff > 0) return '#67C23A'; // 绿色 - 盘盈
  if (diff < 0) return '#F56C6C'; // 红色 - 盘亏
  return '#606266'; // 灰色 - 无差异
}

/** 提交处理 */
function submitProcess() {
  processFormRef.value?.validate((valid) => {
    if (valid) {
      processSubmitting.value = true;
      const params = {
        checkCode: processForm.checkCode,
        operationType: processForm.operationType,
        operationRemark: processForm.operationRemark,
        operationCode: processForm.operationCode
      };
      
      // 调用处理API
      InventoryCountApi.completeInventoryCount(params )
        .then(() => {
          ElMessage.success('处理成功');
          processDialogVisible.value = false;
          handleQuery();
        })
        .catch(() => {
          ElMessage.error('处理失败');
        })
        .finally(() => {
          processSubmitting.value = false;
        });
    }
  });
}

/** 完成盘点单 */
function handleComplete(row: InventoryCountVO) {
  ElMessageBox.confirm(
    t('inventoryCount.message.completeConfirm'),
    t('common.warning'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning',
    }
  )
    .then(() => {
      InventoryCountApi.completeInventoryCount(row.checkCode || '').then(() => {
        ElMessage.success(t('inventoryCount.message.completeSuccess'));
        handleQuery();
      });
    })
    .catch(() => { });
}

/** 导出盘点单 */
function handleExport() {
  // 处理查询参数
  const params = { ...queryParams };

  // 处理时间范围参数
  if (params.createTimeRange && params.createTimeRange.length === 2) {
    params.createTimeBegin = params.createTimeRange[0];
    params.createTimeEnd = params.createTimeRange[1];
    delete params.createTimeRange;
  }
  
  InventoryCountApi.exportInventoryCount(params);
}

// 初始化
onActivated(() => {
  getWarehouseAreaList();
  handleQuery();
});

onMounted(() => {
  getWarehouseAreaList();
  handleQuery();
});

// 监听仓库变更事件
emitter.on('reloadListByWarehouseId', () => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.inventory-count {
  height: 100%;

  .search-container {
    margin-bottom: 16px;
  }

  .table-container {
    .operation-btn {
      margin-right: 8px;
    }
  }
}

.process-dialog {
  .el-table {
    th {
      background-color: #f5f7fa;
    }
  }

  .el-form-item {
    margin-bottom: 20px;

    &__label {
      font-weight: 500;
    }
  }

  .dialog-footer {
    text-align: center;

    .el-button {
      min-width: 80px;
    }
  }
}

// 快捷时间选择样式
.ml16px {
  margin-left: 16px;
}

.mr14px {
  margin-right: 14px;
}

.mr16px {
  margin-right: 16px;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
