<template>
  <div>
    <el-drawer :model-value="drawerVisible" :title="$t('common.uploadFiLe')" :close-on-click-modal="false" @close="close" size="520px">
        <upload-multiple
                :listType="`text`"
                :ifDrag="true"
                :isShowTip="true"
                :customTips="$t('purchase.message.customTips')"
                :tips="''"
                :fileSize="2"
                :fileType="['xls', 'xlsx']"
                :isPrivate="`public-read`"
                :modelValue="imagesUrls"
                :limit="1"
                :formRef="fileRef"
                ref="detailPicsRef"
                class="modify-multipleUpload"
                name="detailPic"
                @update:model-value="onChangeMultiple"
                @file="onChangeMultipleFile"
        />
      <div class="download-template cursor-pointer" @click="downLoadOrder">{{ $t("purchase.message.templateTips") }}</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close">{{ $t("purchase.button.close") }}</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">{{ $t("purchase.button.save") }}</el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import FileAPI from "@/core/api/file";
import ProductApi from "@/modules/goods/api/purchase"

const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const fileRef = ref();
const imagesUrls = ref([]);
const file = ref();

const submitLoading: Ref<boolean> = ref(false);
const drawerVisible: Ref<boolean> = ref(false);

watch(() => props.visible, (val) => {
  drawerVisible.value = true;
},{ immediate: true });

function close() {
    emit("update:visible", false);
}

function onChangeMultiple(val: any) {
    imagesUrls.value = val;
}
function onChangeMultipleFile(val: any) {
   file.value = val;
}

/*确认提交*/
function handleSubmit() {
    if(imagesUrls.value?.length == 0){
       return ElMessage.error(t('purchase.message.uploadTips'));
    }
    submitLoading.value=true
    let params = {
      file:file.value?.[0],
    }
    ProductApi.importProduct(params).then(res => {
      emit("onSubmit",res || [])
      emit("update:visible", false);
    }).finally(() => {
      submitLoading.value = false;
    });
}

/*模板下载*/
function downLoadOrder() {
  ProductApi.getProductImportTemplateUrl()
    .then((url: any) => {
      if(!url) return;
      FileAPI.downloadFile(url, t('purchase.message.templateName'));
    })
}

</script>

<style lang="scss" scoped>
    .download-template{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        font-style: normal;
        color: var(--el-color-primary) ;
    }
</style>
