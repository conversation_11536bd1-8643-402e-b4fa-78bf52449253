<template>
  <div id="printPurchaseOrder" style="display:none;">
    <div style="margin-right: 120px;">
      <div class="box-title">记账凭证</div>
      <div>
        <div v-for="(balanceFlows, i) in form.dataList" :key="i" :class="{tableContent: i!=0}">
          <div class="grad-row-print">
            <div class="el-form-item--div">
              <span style="margin-left: 10px" class="el-form-item__label">核算单位：</span>
              <span class="el-form-item__content" style="color: #90979E">灵宝市高山天然果品有限责任公司</span>
            </div>
            <div class="el-form-item--div">
          <span class="el-form-item__content" style="color: #90979E">
            <span>{{form.voucherWord}} {{form.voucherNumber}}号</span>
            <span style="margin-left: 10px;margin-right: 10px">{{i+1}}/{{splitNum}}</span>
          </span>
            </div>
          </div>
          <table class="tabs" style="margin-bottom: 10px">
            <!--      <table class="tabs">-->
            <thead>
            <tr>
              <th>摘要</th>
              <th>会计科目</th>
              <th>借方金额</th>
              <th>贷方金额</th>
            </tr>
            </thead>
            <tbody>
            <div v-if="balanceFlows.length === 5">
              <tr v-for="(item,index) in balanceFlows" :key="index">
                <!--        <tr v-for="(item,index) in form.balanceFlows" :key="index">-->
                <td>{{ item.summary }}</td>
                <td>
                  <div class="subject-name">{{ item.subjectCode ? `${item.subjectCode}: ${item.subjectName}` : '' }}</div>
                </td>
                <td>
                  <span v-if="item.debitAmount">{{numToMoney(item.debitAmount)}}</span>
                  <span v-else>-</span>
                </td>
                <td>
                  <span v-if="item.creditAmount">{{numToMoney(item.creditAmount)}}</span>
                  <span v-else>-</span>
                </td>
              </tr>
            </div>
            <div v-else>
              <tr v-for="(item,index) in balanceFlows" :key="index">
                <!--        <tr v-for="(item,index) in form.balanceFlows" :key="index">-->
                <td>{{ item.summary }}</td>
                <td>
                  <div class="subject-name">{{ item.subjectCode ? `${item.subjectCode}: ${item.subjectName}` : '' }}</div>
                </td>
                <td>
                  <span v-if="item.debitAmount">{{numToMoney(item.debitAmount)}}</span>
                  <span v-else>-</span>
                </td>
                <td>
                  <span v-if="item.creditAmount">{{numToMoney(item.creditAmount)}}</span>
                  <span v-else>-</span>
                </td>
              </tr>
              <tr v-for="(i) in 5 - balanceFlows.length" :key="i">
                <!--        <tr v-for="(item,index) in form.balanceFlows" :key="index">-->
                <td>
                  <div></div>
                </td>
                <td>
                  <div></div>
                </td>
                <td>
                  <div></div>
                </td>
                <td>
                  <div></div>
                </td>
              </tr>
            </div>
            <tr>
              <td>
                <div>附单据数 {{form.attachment ? JSON.parse(form.attachment).length : 0}} 张</div>
              </td>
              <td>
                <span>合计{{convertCurrency(form.totalDebitAmount)}} </span>
              </td>
              <td>{{numToMoney(form.totalDebitAmount)}}</td>
              <td>{{numToMoney(form.totalCreditAmount)}}</td>
            </tr>
            </tbody>
          </table>
          <div class="grad-row-print">
            <div class="el-form-item--div">
              <span style="margin-left: 10px" class="el-form-item__label">审核人：</span>
              <span class="el-form-item__content">{{form.auditUser}}</span>
            </div>
            <div class="el-form-item--div">
              <span class="el-form-item__label">复核：</span>
            </div>
            <div class="el-form-item--div">
          <span class="el-form-item__content" style="color: #90979E">
            <span>制单人：</span>
            <span style="margin-left: 10px;margin-right: 10px">{{form.createUser}}</span>
          </span>
            </div>
          </div>
          <div v-if="i != form.dataList.length - 1" style="page-break-before: always; page-break-inside: avoid;"></div>
        </div>

      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {voucher} from "@/modules/finance/api";
import {useCommon} from "@/modules/finance/composables/common";

const { numToMoney, convertCurrency } = useCommon();
const  type= ref(1)
const  id= ref('')
const  orderCode= ref('')
const form = reactive({
  // balanceFlows: [],
  dataList: [],
  totalDebitAmount: 0,
  totalCreditAmount: 0,
  voucherWord: '',
  voucherNumber: '',
  createUser: '',
  auditUser: '',
  attachment: ''
});
const splitNum = ref(0);

function setFormData(data) {
  const params = { id: data.id };
  form.dataList = [];
  voucher.queryVoucherDetail(params).then(res => {
    // form.balanceFlows = res.balanceFlows;
    form.attachment = res.attachment;
    form.createUser = res.operateLogs.filter(item => item.operateType === 0)[0].createUserName;
    const auditList = res.operateLogs.filter(item => item.operateType === 1);
    if(auditList.length>0) {
      form.auditUser = auditList[auditList.length - 1].createUserName;
    }
    res.balanceFlows.map(item => {
      form.totalDebitAmount += Number(item.debitAmount);
      form.totalCreditAmount += Number(item.creditAmount);
    })
    form.voucherWord = res.voucherWord;
    form.voucherNumber = res.voucherNumber;
    splitNum.value = Math.ceil(res.balanceFlows.length / 5);
    for(let i=0;i<splitNum.value;i++) {
      let splitArr = res.balanceFlows.splice(0,5)
      form.dataList.push(splitArr);
    }
    setDatas()
  });
}


function setDatas(){
  if (nextTick) {
    nextTick(() => {
      setTimeout(function () {
        const printContent1 = document.getElementById('printPurchaseOrder').innerHTML;
        const printContent = `
                        <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <style lang="scss">
                                @media print {
                                    @page {
                                      @bottom-center {
                                        content: counter(pages) "/" counter(page);
                                        font-size: 10px;
                                      }
                                    }
                                    body {
                                        zoom: 0.8;
                                        text-align: center;
                                        .tableContent{
                                            margin-top: 80px;
                                        }
                                        .tabs{
                                           border-top: 1px solid #E5E7F3 !important;
                                           border-left: 1px solid #E5E7F3 !important;
                                           border-right: 1px solid #E5E7F3 !important;
                                        }
                                        .mr10px{
                                            margin-right: 10px;
                                        }
                                        .mr5px{
                                            margin-right:5px;
                                        }
                                         .box-title {
                                              text-align: center!important;
                                              margin-top: 40px;
                                              margin-bottom: 24px;
                                              font-family: PingFangSC, PingFang SC;
                                              font-weight: 600 !important;
                                              font-size: 18px!important;
                                              color: #90979E;
                                              font-style: normal;
                                          }
                                         .grad-row-print {
                                              display: flex;
                                              justify-content: space-between;
                                              margin-bottom: 10px;
                                              .el-form-item--div100{
                                                width: 100%;
                                              }
                                              .el-form-item__label {
                                                  font-family: PingFangSC, PingFang SC;
                                                  font-weight: 400;
                                                  font-size: 12px;
                                                  color: #90979E;
                                                  /*font-style: normal;*/
                                              }
                                              .el-form-item__content {
                                                  font-family: PingFangSC, PingFang SC;
                                                  /*font-weight: 500;*/
                                                  font-size: 12px;
                                                  /*font-style: normal;*/
                                             }
                                         }
                                         .grad-row-print-money {
                                             display: flex;
                                             justify-content: flex-end;
                                             align-items: center;
                                             font-family: PingFangSC, PingFang SC;
                                             font-weight: 500;
                                             font-size: 12px;
                                             color: #52585F;
                                             font-style: normal;
                                         }
                                         table {
                                            border-collapse: collapse;
                                            width: 100%;
                                            table-layout: fixed;
                                            -webkit-print-color-adjust: exact;
                                            margin-bottom: 40px;
                                         }
                                         table, th, td {
                                            border: 1px solid #E5E7F3;
                                         }
                                         th,td{
                                            width: cacl((100% - 50px)/4);
                                            font-family: PingFangSC, PingFang SC;
                                            font-style: normal;
                                            text-align: left;
                                            height: 10px;
                                            padding: 14px 12px;
                                            color: #52585F;
                                            word-break:break-word;
                                            .index{
                                               width: 50px!important;
                                            }
                                         }
                                         th {
                                             background: #F4F6FA;
                                             font-weight: 600;
                                             font-size: 12px;
                                             background: #F4F6FA;
                                         }
                                         td {
                                            font-weight: 400;
                                            font-size: 12px;
                                         }
                                    }
                               }
                            </style>
                        </head>
                        <body>
                          ${printContent1}
                        </body>
                        </html>
                    `;
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);
        const win = iframe.contentWindow;
        // 将需要打印的HTML内容写入iframe的document中
        win.document.write(printContent);
        win.print();
        document.body.removeChild(iframe);
      }, 500);
    })
  }
}

defineExpose({
  setFormData,
});
</script>

<style scoped lang="scss">
</style>
<style lang="scss">
</style>
