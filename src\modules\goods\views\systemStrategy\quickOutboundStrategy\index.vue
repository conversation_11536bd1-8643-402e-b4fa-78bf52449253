<template>
  <div class="app-container">
      <div class="system-strategy">
        <el-card class="mb-12px search-card">
          <div class="search-form">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="80px">
              <el-row>
                <el-form-item :label="$t('systemStrategy.label.pageName')" prop="modulePageCode">
                  <el-select
                    style="width: 240px"
                    v-model="queryParams.modulePageCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in pageOptionData"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('systemStrategy.label.fieldsName')" prop="modulePageFieldName">
                  <el-input v-model="queryParams.modulePageFieldName" :placeholder="$t('common.placeholder.inputTips')" clearable style="width: 240px" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item :label="$t('systemStrategy.label.ifShow')" prop="isShow">
                  <el-select v-model="queryParams.isShow" :placeholder="$t('common.placeholder.selectTips')" clearable style="width: 240px">
                    <el-option :label="$t('systemStrategy.label.yes')" :value="1" />
                    <el-option :label="$t('systemStrategy.label.no')" :value="0" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery" v-hasPerm="['goods:system_strategy_quick_outbound:search']">
                    {{ $t("common.search") }}
                  </el-button>
                  <el-button @click="handleResetQuery" v-hasPerm="['goods:system_strategy_quick_outbound:reset']">
                    {{ $t("common.reset") }}
                  </el-button>
                </el-form-item>
              </el-row>
            </el-form>
          </div>
        </el-card>
        <!-- 数据表格 -->
        <el-card class="content-card">
          <el-table ref="dataTableRef" v-loading="loading" :data="fieldsConfigList" stripe highlight-current-row>
            <template #empty>
              <Empty />
            </template>
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column :label="$t('systemStrategy.label.pageName')" prop="modulePageName" show-overflow-tooltip/>
            <el-table-column :label="$t('systemStrategy.label.fieldsName')" prop="modulePageFieldName" show-overflow-tooltip/>
            <el-table-column :label="$t('systemStrategy.label.ifShow')" align="center" width="200">
              <template #default="scope">
                <span v-if="hasAuth(['goods:system_strategy_quick_outbound:show'])">
                  <el-switch v-model="scope.row.isShow" :active-value="1" :inactive-value="0"
                             :active-text="$t('systemStrategy.status.enabled')" :inactive-text="$t('systemStrategy.status.disabled')"
                             inline-prompt style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                             @change="handleStatusChange(scope.row)" />
                </span>
                <span v-else>-</span>

              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
</template>

<script setup lang="ts">
import { hasAuth } from "@/core/plugins/permission";

defineOptions({
  name: "quickOutboundStrategy",
  inheritAttrs: false,
});
// 查询参数
import SystemStrategyAPI, { SystemStrategyPageQuery, SystemStrategyVO, EnableStatus, StrategyModuleEnum } from "@/modules/goods/api/systemStrategy";
import type { FormInstance, TableInstance } from "element-plus";

const queryFormRef = ref<FormInstance>();
const dataTableRef = ref<TableInstance>();
const loading = ref(false);
const { t } = useI18n();

const pageModeCode = StrategyModuleEnum.QUICK_OUTBOUND
const queryParams = reactive<SystemStrategyPageQuery>({
  page: 1,
  limit: 1000,
  moduleCode: pageModeCode,
  modulePageCode: undefined,
  modulePageFieldName: '',
  isShow: undefined,
});

// 自定义字段列表
const fieldsConfigList = ref<SystemStrategyVO[]>([]);

/*是否显示切换*/
const handleStatusChange = async (row) => {
  loading.value = true;
    let params = {
      moduleCode: pageModeCode,
      modulePageCode: row?.modulePageCode,
      modulePageFieldCode: row?.modulePageFieldCode,
      modulePageFieldName: row?.modulePageFieldName,
      id: row.id
    }
    if (row.isShow === EnableStatus.ENABLED) {
      params.isShow = EnableStatus.ENABLED
      SystemStrategyAPI.save(params).then(() => {
        ElMessage.success(t("systemStrategy.message.enableSuccess"));
        handleQuery();
      }).catch(() => {
        // 失败时恢复状态
        row.isShow = EnableStatus.DISABLED;
        loading.value = false;
      });
    }
    else if (row.isShow === EnableStatus.DISABLED) {
      ElMessageBox.confirm(
        t("systemStrategy.message.closeConfirm"),
        t("common.tipTitle"),
        {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: "warning",
        }
      ).then(() => {
        params.isShow = EnableStatus.DISABLED
        SystemStrategyAPI.save(params).then(() => {
          ElMessage.success(t("systemStrategy.message.disableSuccess"));
          handleQuery();
        }).catch(() => {
          // 失败时恢复状态
          row.isShow = EnableStatus.ENABLED;
          loading.value = false;
        });
      }).catch(() => {
        // 用户取消时恢复状态
        row.isShow = EnableStatus.ENABLED;
        loading.value = false;
      });
    }
};

/** 查询自定义字段配置列表 */
function handleQuery() {
  loading.value = true;
  SystemStrategyAPI.getPageList(queryParams).then((data) => {
    fieldsConfigList.value = data?.records || [];
  }).finally(() => {
    loading.value = false;
  });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value?.resetFields();
  // queryParams.enableStatus = '';
  handleQuery();
}

const pageOptionData = ref([]);
function queryPageName() {
  loading.value = true;
  SystemStrategyAPI.getPageOption({strategyModuleEnumCode: pageModeCode}).then((data) => {
    pageOptionData.value = data || [];
  }).finally(() => {
    loading.value = false;
  });
}

// 页面加载时查询数据
onMounted(() => {
  queryPageName();
  handleQuery();
});
</script>

<style scoped lang="scss">
.system-strategy {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-card {
    flex-shrink: 0;
  }

  .content-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .action-bar {
      margin-bottom: 12px;
      flex-shrink: 0;
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
  .el-message-box__message {
    p {
      word-break: break-all !important;
    }
  }
}
:deep(.multipleSelect .el-select__selected-item) {
  color: #151719 !important;
}
// 开关组件样式调整
:deep(.el-switch__label) {
  font-size: 12px;
}

:deep(.el-switch__label.is-active) {
  color: #13ce66;
}

:deep(.el-switch__label:not(.is-active)) {
  color: #ff4949;
}
</style>
