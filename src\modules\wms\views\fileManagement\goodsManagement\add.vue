<script setup lang="ts">
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import ProductMgAPI from "@wms/api/productManagement";
import { parseTime } from "@/core/utils/index";
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
import { useI18n } from "vue-i18n"; // 导入国际化
import {useForm} from "./composables/form";
import { watch } from 'vue';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const isEdit = ref(route.query.type === "edit");
const {formData, formRules} = useForm();
const formRef = ref(null);
const selectedProductCategory = ref([]); // 商品分类选择
const classificationList = ref([]); // 商品分类列表
const attributeTypeList = ref([ // 商品属性列表 1->库存商品，2->辅材，3->包材
  {
    value: 1,
    label: "库存商品",
  },
  {
    value: 2,
    label: "辅材",
  },
  {
    value: 3,
    label: "包材",
  },
]); // 商品属性列表
const getCategoryList = () => {
  ProductMgAPI
    .queryCategoryTreeList({})
    .then((data) => {
      classificationList.value = data;
    })
    .finally(() => { });
};

const shelfLifeList = ref([
  {
    value: 1,
    label: "天",
  },
  {
    value: 2,
    label: "月",
  },
  {
    value: 3,
    label: "年",
  },
])

const productUnitList = ref([]); // 商品单位列表
const getProductUnitList = () => {
  ProductMgAPI
    .queryProductUnitList({})
    .then((data) => {
      productUnitList.value = data;
    })
    .finally(() => { });
};

const brandList = ref([]); // 商品品牌列表
const getBrandList = () => {
  ProductMgAPI
   .queryProductBrandList({})
   .then((data) => {
      brandList.value = data;
    })
   .finally(() => { });
};

// 根据转换关系自动生成规格
const generateProductSpec = () => {
  if (
    formData.value.conversionRelFirstNum &&
    formData.value.conversionRelSecondNum &&
    formData.value.productUnitId &&
    formData.value.conversionRelSecondUnitId
  ) {
    const firstUnit = productUnitList.value.find(unit => unit.id === formData.value.productUnitId);
    const secondUnit = productUnitList.value.find(unit => unit.id === formData.value.conversionRelSecondUnitId);
    
    if (firstUnit && secondUnit) {
      formData.value.productSpec = `${formData.value.conversionRelSecondNum}${secondUnit.name}/${formData.value.conversionRelFirstNum}${firstUnit.name}`;
    }
  }
};

// 监听转换关系相关字段的变化
watch(() => [
  formData.value.conversionRelFirstNum,
  formData.value.conversionRelSecondNum,
  formData.value.productUnitId,
  formData.value.conversionRelSecondUnitId
], () => {
  generateProductSpec();
}, { deep: true });

const handleSave = () => {
  if(isEdit.value) {
    const dataCopy = JSON.parse(JSON.stringify(formData.value));
    dataCopy.imagesUrls = JSON.stringify(dataCopy.imagesUrls);
    dataCopy.mainImageUrl = JSON.stringify(dataCopy.mainImageUrl);
    ProductMgAPI.updateProduct(dataCopy).then(() => {
      ElMessage.success("商品编辑成功");
      handleClose();
    });
  }
  else{
    const dataCopy = JSON.parse(JSON.stringify(formData.value));
    dataCopy.imagesUrls = JSON.stringify(dataCopy.imagesUrls);
    dataCopy.mainImageUrl = JSON.stringify(dataCopy.mainImageUrl);
    ProductMgAPI.saveProduct(dataCopy).then(() => {
      ElMessage.success("商品创建成功");
      handleClose();
    });
  }
}

const handleSaveProduct = () => {
  formRef.value.validate((valid) => {
    if (!valid) {
      return false;
    }
    handleSave();
  });
};

const handleCascader = (val: []) => {
  formData.value.firstCategoryId = val[0];
  formData.value.secondCategoryId = val[1];
  formData.value.thirdCategoryId = val[2];
};

const previewFile = async (
  bucket: string,
  fileName: string,
  originalFileName: string
) => {
  try {
    const res = await previewSingle(bucket, fileName, originalFileName);
  } catch (error) {
    ElMessage.error("预览文件失败");
  }
};

const fileType = ["jpg", "jpeg", "png"];
const onChangeFileUpload = (val: []) => {
  formData.value.imagesUrls = val;
  formData.value.mainImageUrl = val[0] || '';
};

const tagsViewStore = useTagsViewStore();
const handleClose = async () => {
  await tagsViewStore.delView(route);
  router.push("/wms/product");
};

const standardOptions = [
  { value: 1, label: "是" },
  { value: 0, label: "否" },
];

const fetchDetail = (data={}) => {
  ProductMgAPI.getProductDetailById(data).then((res) => {
    formData.value = res;
    formData.value.selectedProductCategory = [
      res?.firstCategoryId,
      res?.secondCategoryId,
      res?.thirdCategoryId,
    ]
    formData.value.imagesUrls = JSON.parse(res.imagesUrls);
    formData.value.mainImageUrl = JSON.parse(res.mainImageUrl);
    
  });
}

onMounted(() => {
  if (isEdit.value) {
    fetchDetail({ id: route.query.id });
  }
  getCategoryList();
  getProductUnitList();
  getBrandList();
});
</script>

<template>
  <div class="product-add">
    <div class="card-header mb-24px">
      <img @click="handleClose" src="@/core/assets/images/arrow-left.png" alt="" class="back-btn" />
      <span @click="handleClose" class="code">
        {{
          isEdit
            ? $t("WMSProductMangement.label.editProduct")
            : $t("WMSProductMangement.label.addProduct")
        }}
      </span>
    </div>
    <div class="card-title mb-24px">基础信息</div>

    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-row :gutter="10" class="mb-8px">
        <el-col :span="8">
          <el-form-item label="商品编码" required>
            <el-input v-model="formData.productCode" maxlength="50" placeholder="自动生成商品编码" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品名称" prop="productName">
            <el-input v-model="formData.productName" maxlength="50" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品分类" prop="selectedProductCategory">
            <el-cascader @change="handleCascader" v-model="formData.selectedProductCategory" :options="classificationList"
              clearable :props="{
                value: 'id',
                label: 'categoryName',
                children: 'children',
              }" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mb-8px">
        <el-col :span="8">
          <el-form-item label="是否标品" prop="isStandard">
            <el-select v-model="formData.isStandard" placeholder="请选择" clearable>
              <el-option v-for="item in standardOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单位" prop="productUnitId">
            <el-select v-model="formData.productUnitId" placeholder="请选择" clearable>
              <el-option v-for="item in productUnitList" :key="item.id" :value="item.id" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="转换关系" required>
            <div class="conversion-relation">
              <el-form-item prop="conversionRelFirstNum">
                <el-input v-model="formData.conversionRelFirstNum" placeholder="请输入" class="w-100px"></el-input>
              </el-form-item>
              <el-select v-model="formData.productUnitId" placeholder="采购单位" class="w-100px" disabled>
                <el-option v-for="item in productUnitList" :key="item.id" :value="item.id" :label="item.name" />
              </el-select>
              <span class="equal-sign">=</span>
              <el-form-item prop="conversionRelSecondNum">
                <el-input v-model="formData.conversionRelSecondNum" placeholder="请输入" class="w-100px" />
              </el-form-item>
              <el-form-item prop="conversionRelSecondUnitId">
                <el-select v-model="formData.conversionRelSecondUnitId" placeholder="最小单位" class="w-100px">
                  <el-option v-for="item in productUnitList" :key="item.id" :value="item.id" :label="item.name" />
                </el-select>
              </el-form-item>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mb-8px">
        <el-col :span="8">
          <el-form-item label="商品属性" prop="attributeType">
            <el-select v-model="formData.attributeType" placeholder="请选择" clearable>
              <el-option v-for="item in attributeTypeList" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="规格" prop="productSpec">
            <el-input v-model="formData.productSpec" maxlength="50" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="损耗比例" prop="lossRatio">
            <el-input v-model="formData.lossRatio" maxlength="50" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mb-8px">
        <el-col :span="8">
          <el-form-item label="商品长宽高" prop="length">
            <div class="dimensions-container">
              <el-input v-model="formData.length" maxlength="50" placeholder="长CM" class="dimension-input">
              </el-input>
              <el-input v-model="formData.width" maxlength="50" placeholder="宽CM" class="dimension-input">
              </el-input>
              <el-input v-model="formData.height" maxlength="50" placeholder="高CM" class="dimension-input">
              </el-input>
              <el-input v-model="formData.volume" maxlength="50" placeholder="体积M3" class="volume-input">
              </el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品重量" prop="weight">
            <el-input v-model="formData.weight" type="number" maxlength="50" placeholder="请输入">
              <template #append>
                <div>Kg</div>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品品牌" prop="productBrandId">
            <el-select v-model="formData.productBrandId" placeholder="请选择" clearable>
              <el-option v-for="item in brandList" :key="item.id" :value="item.id" :label="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mb-8px">
        <el-col :span="8">
          <el-form-item label="条码信息" prop="barcode">
            <el-input v-model="formData.barcode" maxlength="50" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="存储条件" prop="storageCondition">
            <el-input v-model="formData.storageCondition" maxlength="50" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保质期" prop="shelfLife">
            <el-input v-model="formData.shelfLife" maxlength="50" placeholder="请输入">
              <template #append>
                <el-select v-model="formData.shelfLifeUnit" class="w-70px">
                  <el-option v-for="item in shelfLifeList" :key="item.value" :value="item.value" :label="item.label" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mb-8px">
        <el-col :span="8">
          <el-form-item label="型号" prop="modelNumber">
            <el-input v-model="formData.modelNumber" maxlength="50" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="外部编码" prop="outerProductCode">
            <el-input v-model="formData.outerProductCode" maxlength="50" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" maxlength="50" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <div class="card-title mb-24px">图文信息</div>
      <el-row>
        <el-col :span="24">
          <el-form-item label="商品图片 " prop="imagesUrls">
            <UploadMultiple @update:model-value="onChangeFileUpload" ref="detailPicsRef" v-model="formData.imagesUrls"
              :limit="6" :fileSize="10" :fileType="fileType" class="modify-multipleUpload"
              name="detailPic" listType="picture-card" />
          </el-form-item>
        </el-col>
      </el-row>

      <div class="form-actions">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSaveProduct">确认</el-button>
      </div>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.product-add {
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;

  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;

    .code {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .status {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }

  /* .upload-area {
    padding: 24px;
    text-align: center;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;

    .text {
      margin: 10px 0;
      em {
        color: #409eff;
        font-style: normal;
        cursor: pointer;
      }
    }
  } */
  .upload-container {
    display: flex;
    flex-direction: column;
  }

  .tip {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #90979e;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }

  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
    text-align: right;
    border-top: 1px solid #ebeef5;
  }

  .conversion-relation {
    display: flex;
    align-items: center;
    gap: 8px;

    .equal-sign {
      margin: 0 4px;
    }
  }

  .dimensions-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .dimension-input {
      width: 80px;
    }

    .volume-input {
      width: 100px;
    }
  }
}
</style>
