<template>
  <div class="special-coast-container">
    <div class="header-content">
      <div class="header-content-item">
        <div class="header-content-item-img">
          <img :src="SpecialCoasIcon1" alt="" />
        </div>
        <div class="header-content-item-main">
          <div class="header-content-item-header">
            <div class="header-content-item-title">总订单数量</div>
            <div class="header-content-item-value">{{ headerData?.totalOrderCount }}</div>
          </div>
          <div class="header-content-item-bottom">
            <div class="header-content-item-bottom-content">
              <div class="header-content-item-bottom-content-title">补发</div>
              <div class="header-content-item-bottom-content-value">{{ headerData?.orderTotalCount }}</div>
            </div>
            <div class="header-content-item-bottom-content">
              <div class="header-content-item-bottom-content-title">业务招待</div>
              <div style="margin-left: calc(50% - 22px);text-align: left" class="header-content-item-bottom-content-value">{{ headerData?.businessOrderCount }}</div>
            </div>
            <div class="header-content-item-bottom-content">
              <div class="header-content-item-bottom-content-title">参展</div>
              <div style="margin-left: calc(50% - 10px);text-align: left" class="header-content-item-bottom-content-value">{{ headerData?.exhibitOrderCount }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="header-content-item">
        <div class="header-content-item-img">
          <img :src="SpecialCoasIcon2" alt="" />
        </div>
        <div class="header-content-item-main">
          <div class="header-content-item-header">
            <div class="header-content-item-title">总成本金额</div>
            <div class="header-content-item-value">{{ formatPrice(headerData?.totalCostAmount) }}</div>
          </div>
          <div class="header-content-item-bottom">
            <div class="header-content-item-bottom-content">
              <div class="header-content-item-bottom-content-title">补发成本</div>
              <div class="header-content-item-bottom-content-value">{{ formatPrice(headerData?.reissueCostAmount) }}</div>
            </div>
            <div class="header-content-item-bottom-content">
              <div class="header-content-item-bottom-content-title">业务招待成本</div>
              <div style="margin-left: calc(50% - 34px);text-align: left" class="header-content-item-bottom-content-value">{{ formatPrice(headerData?.businessCostAmount) }}</div>
            </div>
            <div class="header-content-item-bottom-content">
              <div class="header-content-item-bottom-content-title">参展成本</div>
              <div style="margin-left: calc(50% - 22px);text-align: left" class="header-content-item-bottom-content-value">{{ formatPrice(headerData?.exhibitCostAmount) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="header-content-item">
        <div class="header-content-item-img">
          <img :src="SpecialCoasIcon3" alt="" />
        </div>
        <div class="header-content-item-main">
          <div class="header-content-item-header">
            <div class="header-content-item-title">待核算订单量</div>
            <div class="header-content-item-value">{{ headerData?.unaccountedOrderCount }}</div>
          </div>
          <div class="header-content-item-bottom">
            <div class="header-content-item-bottom-content">
              <div class="header-content-item-bottom-content-title">订单总数</div>
              <div class="header-content-item-bottom-content-value">{{ headerData?.orderTotalCount }}</div>
            </div>
            <div class="header-content-item-bottom-content">
              <div class="header-content-item-bottom-content-title">已核算</div>
              <div style="margin-left: calc(50% - 16px);text-align: left" class="header-content-item-bottom-content-value">{{ headerData?.accountedOrderCount }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-card shadow="never" class="handle-btn-container">
        <div class="search-container">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
            <el-form-item prop="dateRange" :label="$t('omsOrder.label.dateType')">
              <el-select
                v-model="queryParams.dateType"
                :placeholder="$t('common.placeholder.selectTips')"
                class="!w-[160px]"
              >
                <el-option v-for="item in dateTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <el-date-picker
                :editable="false"
                class="!w-[330px]"
                v-model="queryParams.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                :placeholder="$t('common.placeholder.selectTips')"
              />
            </el-form-item>
            <el-form-item label="订单号" prop="orderCode">
              <el-input
                v-model="queryParams.orderCode"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                maxlength="30"
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item label="制单人" prop="submitter">
              <el-input
                v-model="queryParams.submitter"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                maxlength="30"
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item label="单据类型" prop="orderType">
              <el-select
                v-model="queryParams.orderType"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[256px]"
              >
                <el-option v-for="item in orderTypeOptionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item  label="状态" prop="accountingStatus">
              <el-select
                v-model="queryParams.accountingStatus"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[256px]"
              >
                <el-option v-for="item in accountingStatusOptionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-hasPerm="['oms:finance:specialOrderCost:search']" @click="handleQuery(true)">{{$t('common.search')}}</el-button>
              <el-button v-hasPerm="['oms:finance:specialOrderCost:reset']" @click="handleResetQuery">{{$t('common.reset')}}</el-button>
            </el-form-item>
          </el-form>
        </div>
    </el-card>
      <el-card shadow="never" class="table-container">
       <div class="mb10px">
         <el-button type="primary" :disabled="!multipleSelection.length" v-hasPerm="['oms:finance:specialOrderCost:batchTag']" @click="handleBatchTag(true)">批量标记</el-button>
         <el-button v-hasPerm="['oms:finance:specialOrderCost:exportIdx']" @click="exportSpecialOrderCostIdx">导出序列</el-button>
         <el-button v-hasPerm="['oms:finance:specialOrderCost:export']" @click="exportPurchaseReconcile">导出</el-button>
       </div>
        <el-table ref="tableRef" v-loading="loading" :data="pageDataList" show-summary
                  @selection-change="handleSelectionChange"
                  :summary-method="getSummaries" @sort-change="handleSortChange" highlight-current-row stripe>
          <template #empty><Empty/></template>
          <el-table-column fixed="left" type="selection" width="60" align="center" />
          <!--单号-->
          <el-table-column fixed="left" label="订单号" prop="orderCode" show-overflow-tooltip min-width="170"></el-table-column>
          <!--单据类型-->
          <el-table-column label="单据类型" prop="orderType" show-overflow-tooltip min-width="170">
            <template #default="scope">
              {{ getOrderTypeLabel(scope.row.orderType) }}
            </template>
          </el-table-column>
          <!--成本金额-->
          <el-table-column label="成本金额" prop="totalCostAmount" show-overflow-tooltip :sortable="'custom'" min-width="170">
            <template #default="scope">
              <span class="price-text-color"> {{ currencyCodeLabel }}{{ formatPrice(scope.row.totalCostAmount) }}</span>
            </template>
          </el-table-column>
          <!--制单人-->
          <el-table-column label="制单人" prop="submitter" show-overflow-tooltip min-width="170"></el-table-column>
          <!--订单时间-->
          <el-table-column label="订单时间" prop="submitterTime" show-overflow-tooltip min-width="170">
            <template #default="scope">
              <span v-if="scope.row.submitterTime">{{ parseDateTime(scope.row.submitterTime, "dateTime") }}</span>
            </template>
          </el-table-column>
          <!--核算时间  -->
          <el-table-column label="核算时间" show-overflow-tooltip prop="accountingTime" min-width="170">
            <template #default="scope">
              <span v-if="scope.row.accountingTime">{{ parseDateTime(scope.row.accountingTime, "dateTime") }}</span>
            </template>
          </el-table-column>
          <!--状态-->
          <el-table-column label="状态" prop="accountingStatus" show-overflow-tooltip min-width="170">
            <template #default="scope">
              <div class="purchase">
                <span class="purchase-status purchase-status-color3" v-if="scope.row.accountingStatus === 1">已核算</span>
                <span class="purchase-status purchase-status-color1" v-else>未核算</span>
              </div>
            </template>
          </el-table-column>
          <!--备注  -->
          <el-table-column label="备注" prop="accountingRemark" show-overflow-tooltip min-width="170"></el-table-column>
          <el-table-column fixed="right" :label="$t('common.handle')" width="150">
            <template #default="scope">
              <el-button v-hasPerm="['oms:finance:specialOrderCost:detail']" type="primary" link @click="detailHandler(scope.row.id,scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
  </div>

  <template v-if="showBatchTagVisible">
    <BatchTag v-model:visible="showBatchTagVisible" ref="batchTagRef"  @confirm="handleBatchTagConfirm" />
  </template>

  <ExportSequence
    ref="exportSequenceRef"
    v-model:dialog-visible="dialogVisible"
    :path="`oms:specialOrderCostManagement:export `">
  </ExportSequence>
</template>

<script setup lang="ts">
import type { TabsPaneContext, TableColumnCtx, TableInstance, FormInstance } from 'element-plus';
import SpecialOrderCostAPI, {
  querySpecialOrderStatistics,
  queryPageList,
  queryPageTotal,
} from "@/modules/oms/api/specialOrderCost";
import { convertToTimestamp, formatPrice, parseDateTime } from "@/core/utils";
import {useRouter} from "vue-router";
import SpecialCoasIcon1 from "@/core/assets/images/specialCoast_icon1.png";
import SpecialCoasIcon2 from "@/core/assets/images/specialCoast_icon2.png";
import SpecialCoasIcon3 from "@/core/assets/images/specialCoast_icon3.png";

import BatchTag from "@/modules/oms/views/finance/specialOrderCostManagement/components/batchTagDialog.vue"

defineOptions({
  name: "SpecialOrderCoast",
  inheritAttrs: false,
})
const router = useRouter();
const { t } = useI18n();
const dateTypeList = ref([
  {
    label: '下单日期',
    value: 1
  },
  {
    label: '核算日期',
    value: 2
  },
])

const orderTypeOptionList = ref([
  {
    label: '业务招待',
    value: 3
  },
  {
    label: '补发',
    value: 4
  },
  {
    label: '参展',
    value: 9
  },
])
const accountingStatusOptionList = ref([
  {
    label: '已核算',
    value: 1
  },
  {
    label: '未核算',
    value: 0
  },
])

function getOrderTypeLabel(val: number) {
  return orderTypeOptionList.value?.find(item => item.value === val)?.label || '-'
}

const total = ref(0);
const tableRef = ref<TableInstance>();
const loading = ref(false);
const pageDataList = ref([]);
const summaryListData = reactive({
  totalCostAmount: 0,
})
const headerData = ref(null);
const queryFormRef = ref<FormInstance>();
const sortInfo = reactive({
  sortColumn: '',
  sortType: '',
});

const queryParams = reactive({
  dateType: '',
  dateRange: [],
  orderCode: '',
  accountingStatus: '',
  orderType: '',
  page: 1,
  limit: 20,
});

const multipleSelection = ref([]);
function handleSelectionChange(val: any,) {
  multipleSelection.value = val;
}

/*获取金额单位*/
const currencyCodeLabel = computed(() => {
  let defaultCurrencyCode = '(￥)';
  if(pageDataList.value?.length){
    defaultCurrencyCode = pageDataList.value?.[0]?.currencyCode === 'CNY' ? '￥' : '$'
  }
  return defaultCurrencyCode
})


interface SummaryMethodProps {
  columns: []
  data: []
}

/*汇总处理*/
const getSummaries = (param: SummaryMethodProps) => {
  const { columns } = param
  const sums: string[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold">${t('omsFinance.label.totalSum')}:</span>`
      })
      return
    }else if(column?.property === 'totalCostAmount'){
      sums[index] = h('span', {
        innerHTML: `<span style="font-weight: bold;color: #C00C1D">${currencyCodeLabel.value }${formatPrice(summaryListData?.totalCostAmount)}</span>`
      })
      return
    }else {
      sums[index] = h('span', { innerHTML: '' })
      return
    }
  })
  return sums
}

/*排序查询*/
const handleSortChange = (sortData) => {
  if(sortData.prop && sortData.order){
    sortInfo.sortColumn = sortData.prop;
    sortInfo.sortType = sortData.order === 'ascending';
    handleQuery()
  }
}

/*fetch 列表*/
const queryListData = async () => {
  let params = {
    ...queryParams,
    ...sortInfo,
  }
  if(queryParams.dateType==1 && queryParams.dateRange && queryParams.dateRange.length>0){
    params.orderCreateTimeStar=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
    params.orderCreateTimeEnd=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
  }
  if(queryParams.dateType==2 && queryParams.dateRange && queryParams.dateRange.length>0){
    params.accountingTimeStar=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
    params.accountingTimeEnd=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
  }
  return await SpecialOrderCostAPI.queryPageList(params)
};

/*fetch 汇总*/
const querySummary = async () => {
  let params = {
    ...queryParams,
    ...sortInfo,
  }
  return await SpecialOrderCostAPI.queryPageTotal(params)
};

/*查询头部*/
const queryHeaderDetail = async () => {
  return await SpecialOrderCostAPI.querySpecialOrderStatistics()
};

/*查询列表*/
const handleQuery = async (clearSortFlag = false) => {
  loading.value = true;
  if (clearSortFlag && tableRef.value) {
    tableRef.value.clearSort();
    clearSortConfig()
  }
  try {
    const [listData,summaryData, headerDataDetail] = await Promise.all([queryListData(),querySummary(),queryHeaderDetail()])
    pageDataList.value = listData?.records || [];
    total.value = parseInt(listData?.total);
    summaryListData.totalCostAmount = summaryData?.totalCostAmount;
    headerData.value = headerDataDetail || {};
  } catch (error){
    ElMessage.error(t("omsFinance.message.loadFail"));
  }finally {
    loading.value = false;
  }
};

/*重置*/
const handleResetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery(true);
}

/*清除排序字段*/
const clearSortConfig = () => {
  sortInfo.sortColumn = '';
  sortInfo.sortType = '';
}

/*详情*/
const detailHandler = (id: number, rowData: any) => {
  router.push({
    path: "/oms/finance/specialDetail",
    query: {
      id: id,
    }
  });
}
const showBatchTagVisible = ref(false);

/*批量标记*/
const handleBatchTag = async (tag: boolean) => {
  showBatchTagVisible.value = true;
}
const handleBatchTagConfirm = async (data) => {
  try {
    await SpecialOrderCostAPI.markBatch({
      ids: multipleSelection.value.map((item: any) => item.id),
      remark: data,
    })
    ElMessage.success('操作成功');
    showBatchTagVisible.value = false;
    multipleSelection.value = [];
    tableRef.value?.clearSelection();
    handleQuery();
  } catch (error) {
    showBatchTagVisible.value = false;
    // multipleSelection.value = [];
    // tableRef.value?.clearSelection();
  }
}

const dialogVisible = ref(false);
const exportSequenceRef= ref()
/** 导出*/
function exportPurchaseReconcile() {
  ElMessageBox.confirm(t('common.exportTips'), t('common.export'), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      let params = {
        ...queryParams,
        ...sortInfo,
      }
      if(queryParams.dateType==1 && queryParams.dateRange && queryParams.dateRange.length>0){
        params.orderCreateTimeStar=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
        params.orderCreateTimeEnd=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
      }
      if(queryParams.dateType==2 && queryParams.dateRange && queryParams.dateRange.length>0){
        params.accountingTimeStar=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
        params.accountingTimeEnd=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
      }
      delete params.page
      delete params.limit
      SpecialOrderCostAPI.exportSpecialOrderCost(params)
        .then((data) => {
          exportSpecialOrderCostIdx()
        })
        .finally(() => {
          loading.value = false;
        });
    },
    () => {
      ElMessage.info(t('common.exportConcel'));
    }
  );
}

/** 导出序列*/
function exportSpecialOrderCostIdx(){
  exportSequenceRef.value.exportSequenceListPage()
  dialogVisible.value = true;
}

onActivated(() => {
  handleQuery();
})
</script>

<style lang="scss" scoped>
.handle-btn-container {
  margin-bottom: 10px;
}
.header-content {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 10px;

  .header-content-item {
    display: flex;
    background: #fff;
    padding: 20px;

    &.header-content-item:nth-child(1) {
      flex: 1;
    }

    &.header-content-item:nth-child(2) {
      flex: 1.5;
    }

    &.header-content-item:nth-child(3) {
      flex: 1;
    }

    .header-content-item-img {
      width: 38px;
      margin-right: 20px;
    }
    .header-content-item-main {
      display: flex;
      flex-direction: column;
      flex: 1;

      .header-content-item-header {
        margin-bottom: 18px;

        .header-content-item-title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 11px;
          color: #90979E;
          line-height: 14px;
          text-align: left;
          font-style: normal;
          margin-bottom: 4px;
        }
        .header-content-item-value {
          font-family: DINPro, DINPro;
          font-weight: bold;
          font-size: 20px;
          color: #252829;
          line-height: 22px;
          text-align: left;
          font-style: normal;
        }
      }
      .header-content-item-bottom {
        display: flex;
        flex-direction: row;
        flex: 1;
        //justify-content: flex-start;

        .header-content-item-bottom-content {
          justify-content: space-between;
          flex: 1;
          text-align: center;
          gap: 50%;

          .header-content-item-bottom-content-title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 11px;
            color: #90979E;
            line-height: 14px;
            font-style: normal;
            margin-bottom: 4px;
          }
          .header-content-item-bottom-content-value {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 13px;
            color: #464B54;
            line-height: 20px;
            font-style: normal;
          }
        }
        .header-content-item-bottom-content:not(:last-child){
          border-right: 1px solid #ccc;
        }
        .header-content-item-bottom-content:nth-child(1) {
          text-align: left;
        }
        //.header-content-item-bottom-content-value:nth-child(2) {
        //    margin-left: calc(50% - 30px);
        //}


      }

    }
  }
}
.price-text-color {
  color: #C00C1D;
}
.accounting-status-done {
  background: rgba(41,182,16,0.08);
  border-radius: 2px;
  border: 1px solid rgba(41,182,16,0.2);
  font-size: 12px;
  color: #29B610;
  padding: 8px;
}
</style>
