import request from "@/core/utils/request";

const BASE_URL = "/supply-wms/sorting";

class API {
  /**
   * 分拣单列表
   */
  static getPageList(data: any) {
    return request({
      url: `${BASE_URL}/page`,
      method: "post",
      data: data,
    });
  }

  /**
   * 领单
   */
  static pickOrder(sortingCode: string) {
    return request<any>({
      url: `${BASE_URL}/receipt/${sortingCode}`,
      method: "get",
    });
  }

  /**
   *  取消领单
   */
  static releaseOrder(sortingCode: string) {
    return request<any>({
      url: `${BASE_URL}/cancel/receipt/${sortingCode}`,
      method: "get",
    });
  }

  /**
   * 保存分拣信息
   * @param data
   */
  static saveOrder(data: any) {
    return request<any>({
      url: `${BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }

  /**
   * 获取分拣详情
   * @param sortingCode
   */
  static queryDetail(sortingCode: string) {
    return request<any>({
      url: `${BASE_URL}/detail/${sortingCode}`,
      method: "get",
    });
  }

  /**
   * ysn列表
   */
  static queryYsnList(data: any) {
    return request<any>({
      url: `${BASE_URL}/ysnCode/page`,
      method: "post",
      data: data,
    });
  }
}

export default API;
