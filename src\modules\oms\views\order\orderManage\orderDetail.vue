<template>
    <div class="app-container">
        <div class="orderDetail" v-loading="loading">
            <div >
                <div class="page-title">
                    <div class="purchase-title">
                        <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                        <div> {{t('omsOrder.label.orderCode')}}：{{form.orderCode}}</div>
                        <div><CopyButton :text="form.orderCode" :showCopyText="true"></CopyButton></div>
                    </div>
                    <div class="purchase">
                        <span class="purchase-status purchase-status-color0" v-if="form.orderStatus==0">{{t('omsOrder.orderStatusList.draft')}}</span>
                        <span class="purchase-status purchase-status-color1" v-if="form.orderStatus==1">{{t('omsOrder.orderStatusList.check')}}</span>
                        <span class="purchase-status purchase-status-color2" v-if="form.orderStatus==2">{{t('omsOrder.orderStatusList.ready')}}</span>
                        <span class="purchase-status purchase-status-color3" v-if="form.orderStatus==3">{{t('omsOrder.orderStatusList.send')}}</span>
                        <span class="purchase-status purchase-status-color0" v-if="form.orderStatus==4">{{t('omsOrder.orderStatusList.finash')}}</span>
                        <span class="purchase-status purchase-status-color0" v-if="form.orderStatus==5">{{t('omsOrder.orderStatusList.close')}}</span>
                    </div>
                    <div v-if="form?.orderStatus == 1 && form?.approveStatus == 1" class="header-approve-status" style="width: 70%;margin-left: 60px">
                      <el-descriptions :column="2">
                        <el-descriptions-item width="200px"  :label="$t('omsOrder.button.approveStatus')">
                          <span v-if="form?.approveStatus == 0">{{ t('omsOrder.label.approveIng') }}</span>
                          <span v-else-if="form?.approveStatus == 1">{{ t('omsOrder.label.approveUnpass') }}</span>
                          <span v-else-if="form?.approveStatus == 2">{{ t('omsOrder.label.approvePass') }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item :label="$t('omsOrder.label.approveUnpassReason')">
                          {{form?.approveRejectReason}}
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                </div>
            </div>
            <div class="page-content">
                <el-form :model="form" ref="fromRef" label-width="96px" label-position="right">
                    <div class="title-lable">
                      <div class="title-line"></div>
                      <div class="title-content">
                        {{ $t("omsOrder.label.basicInformation") }}
                      </div>
                    </div>
                    <div class="grad-row">
                      <el-row>
                        <!--销售类型-->
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.orderType')">
                            {{ form.orderType ? t(`omsOrder.orderTypeList[${form.orderType}]`) : '-' }}
                          </el-form-item>
                        </el-col>
                        <!--兑换卡号-->
                        <el-col :span="6" v-if="form.orderType == 2">
                          <el-form-item :label="$t('omsOrder.label.exchangeCode')">
                            {{ form.exchangeCode || '-' }}
                          </el-form-item>
                        </el-col>
                        <!--原订单号-->
                        <el-col :span="6" v-if="form.orderType == 4">
                          <el-form-item :label="$t('omsOrder.label.originalOrderCode')">
                            {{ form.originalOrderCode || '-' }}
                          </el-form-item>
                        </el-col>
                        <!--销售主题-->
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.orderTheme')">{{form.orderTheme}}</el-form-item>
                        </el-col>
                        <!--销售人员-->
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.salesName')">
                            {{form.salesName}}
                          </el-form-item>
                        </el-col>
                        <!--审核人员-->
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.approveUserName')">{{form.approveUserName}}</el-form-item>
                        </el-col>
                        <!--经办人-->
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.operatorUser')">{{form.handler}}</el-form-item>
                        </el-col>
                        <!--结算方式-->
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.paymentType')">
                            {{ form.paymentType ? t(`omsOrder.paymentTypeList[${form.paymentType}]`) : '-' }}
                          </el-form-item>
                        </el-col>

                        <!--预售-->
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.presale')">
                            <span v-if="form.isPresale == 1">{{$t("omsOrder.presaleList[1]")}}</span>
                            <span v-else-if="form.isPresale == 0">{{$t("omsOrder.presaleList[0]")}}</span>
                            <span v-else>-</span>
                          </el-form-item>
                        </el-col>
                        <!--期望出库时间-->
                        <el-col :span="6" >
                          <el-form-item :label="$t('omsOrder.label.planDeliveryDateCopy')">{{form.planDeliveryDate}}&nbsp;{{form.planDeliveryTime ? form.planDeliveryTime : ''}}</el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                    <div class="title-lable" style="justify-content:space-between;">
                        <div class="flex-center-start">
                            <div class="title-line"></div>
                            <div class="title-content">
                                {{ $t("omsOrder.label.productListInformation") }}
                            </div>
                        </div>
                        <div v-if="type=='check' && form.orderStatus==1">
                            <el-button color="#008E7C" @click="handleDeliveryPlan" v-if="!form.deliveryScheduleVOList || form.deliveryScheduleVOList.length==0">
                                {{$t('omsOrder.button.deliveryPlan')}}
                            </el-button>
                        </div>
                    </div>
                    <div>
                        <el-table :data="form.orderDetailList" highlight-current-row stripe>
                            <el-table-column type="index" :label="$t('common.sort')" width="60" />
                            <el-table-column :label="$t('omsOrder.label.product')" min-width="150" show-overflow-tooltip>
                                <template #default="scope">
                                    <div class="product-div">
                                        <div class="product">
                                            <div>
                                                <span class="product-key">{{$t('omsOrder.label.productCode')}}：</span>
                                                <span class="product-value">{{scope.row.productCode}}</span>
                                            </div>
                                            <div class="product-name">{{scope.row.productName}}</div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('omsOrder.label.productSpecName')" prop="productSpecs" show-overflow-tooltip/>
                            <el-table-column :label="'*'+$t('omsOrder.label.saleCount')" prop="saleCount" show-overflow-tooltip align="right"/>
                            <el-table-column :label="'*'+$t('omsOrder.label.salePrice')" prop="salePrice" show-overflow-tooltip align="right">
                                <template #default="scope">
                                    <span v-if="scope.row?.salePrice || scope.row?.salePrice == 0">
                                         <span v-if="form.currencyCode == 'CNY'">￥</span>
                                         <span v-else>$</span>
                                         <span>{{scope.row.salePrice}}</span>
                                    </span>
                                    <span v-else>-</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="'*'+$t('omsOrder.label.total')" show-overflow-tooltip align="right">
                                <template #default="scope">
                                   <span v-if="scope.row?.saleMoney || scope.row?.saleMoney == 0">
                                       <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                                       <span v-else>$</span>
                                       {{scope.row.saleMoney}}
                                   </span>
                                   <span v-else>-</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('omsOrder.label.remark')" prop="remark" show-overflow-tooltip/>
                        </el-table>
                        <div  class="table-title">
                            <div>
                                <span>{{t('omsOrder.label.total')}}：</span>
                            </div>
                            <div>
                                <span class="mr16px">{{t('omsOrder.label.productCount')}} <span  style="font-size: 18px; color: #C00C1D ">{{form.orderDetailList.length}}</span></span>
                                <span>
                                    {{t('omsOrder.label.totalMoney')}}
                                    <span  style="font-size: 18px; color: #C00C1D ">
                                        <template v-if="form.orderDetailList && form.orderDetailList.length>0">
                                            <span v-if="form.currencyCode == 'CNY'">￥</span>
                                            <span v-else>$</span>
                                        </template>
                                        {{form.originalTotalAmount}}
                                    </span>
                                </span>
                            </div>
                        </div>
                      <div class="grad-row mt25px">
                        <el-row>
                          <el-col :span="6">
                            <el-form-item :label="$t('omsOrder.label.productTotalAmount')">
                              <span style="font-size: 18px; color: #C00C1D ">￥ {{form.totalAmount}}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item :label="$t('omsOrder.label.discountType')">
                              <span v-if="form.discountType" style="font-size: 18px; color: #C00C1D ">{{ t(`omsOrder.discountTypeList[${form.discountType}]`) }}</span>
                              <span v-if="form.discountType == 2" class="ml20px" style="font-size: 18px; color: #C00C1D ">￥{{form.discountValue}}</span>
                              <span v-if="form.discountType == 3" class="ml20px" style="font-size: 18px; color: #C00C1D ">{{form.discountValue}}%</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item :label="$t('omsOrder.label.totalDiscountedAmount')">
                              <span v-if="form.totalDiscountedAmount || form.totalDiscountedAmount == 0" style="font-size: 18px; color: #C00C1D ">￥ {{form.totalDiscountedAmount}}</span>
                              <span v-else>-</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("omsOrder.label.customerInformation") }}
                        </div>
                        <div class="ml10px cursor-pointer" v-hasPermEye="['oms:order:orderManage:eye']" v-if="form.contactPerson || form.contactMobile || form.address">
                            <el-icon
                                    v-if="!form.isNameDecrypted || !form.isMobileDecrypted || form.deliveryAddress?.includes('*') &&!form.isAddressDecrypted"
                                    @click="handleViewInfo()"
                                    v-hasPermEye="['pms:requirements:eye']"
                                    color="#762ADB "
                                    size="16"
                            >
                                <View />
                            </el-icon>
                        </div>
                    </div>
                    <div class="grad-row">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item :label="$t('omsOrder.label.customerNameCopy')">
                                  {{form.customerName || '-'}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('omsOrder.label.receiveName')">
                                    {{form.contactPerson || '-'}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('omsOrder.label.receiveMobileCopy')">
                                    <span v-if="form.contactMobile">{{form.contactAreaCode }}-{{form.contactMobile}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('omsOrder.label.deliveryAddressCopy')">
                                    <span  style="word-break:break-all;">{{form.deliveryAddress || '-'}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6">
                                <el-form-item :label="$t('omsOrder.label.remarkCopy')">
                                    <span  v-if="form.remark" style="word-break:break-all;">{{form.remark}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="18" class="file-div">
                              <el-form-item :label="$t('omsOrder.label.orderAttachmentFilesInformation')">
                                <upload-multiple
                                  :isDelete="false"
                                  :showUploadBtn="false"
                                  :isShowTip="false"
                                  :listType="`text`"
                                  :tips="''"
                                  :fileSize="20"
                                  :fileType="['jpg','jpeg','png','pdf','zip','rar']"
                                  :isPrivate="`public-read`"
                                  :modelValue="form.orderAttachmentFiles"
                                  @update:model-value="onChangeMultiple"
                                  ref="detailPicsRef"
                                  :limit="1"
                                  :formRef="formUpdateRef"
                                  class="modify-multipleUpload"
                                  name="detailPic">
                                  <template #default="{ file }">
                                    点击上传
                                  </template>
                                </upload-multiple>
                              </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                          <!--客户属性-->
                          <el-col :span="6">
                            <el-form-item :label="$t('omsOrder.label.customerAttributes')">
                              <span v-if="form.customerAttributes == 1">{{$t("omsOrder.label.bigCustomer")}}</span>
                              <span v-else-if="form.customerAttributes == 2">{{ $t("omsOrder.label.smallCustomer") }}</span>
                              <span v-else>-</span>
                            </el-form-item>
                          </el-col>
                          <!--客户合同-->
                          <el-col :span="6">
                            <el-form-item :label="$t('omsOrder.label.contract')">
                              <span style="color:var(--el-color-primary);cursor: pointer" v-if="form?.contractName" @click="turnToContractDetail">{{ form?.contractName }}</span>
                              <span v-else>-</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                    </div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("omsOrder.label.orderInformation") }}
                        </div>
                    </div>
                    <div class="grad-row">
                        <el-row v-if="form.orderStatus==0">
                            <el-col :span="6">
                                <el-form-item :label="$t('omsOrder.label.orderDate')">
                                    <span>-</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row v-else>
                            <el-col :span="6" v-if="form.orderStatus!==5">
                                <el-form-item :label="$t('omsOrder.label.externalOrderCode')">
                                    <span v-if="form.sourceOrderCode">{{form.sourceOrderCode}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('omsOrder.label.orderDate')">
                                    <span v-if="form.submitterTime">{{ parseDateTime(form.submitterTime, "dateTime") }}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="form.orderStatus==4">
                                <el-form-item :label="$t('omsOrder.label.deliverDate')">
                                    <span v-if="form.deliveryTime">{{ parseDateTime(form.deliveryTime, "dateTime") }}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="form.orderStatus==4">
                                <el-form-item :label="$t('omsOrder.label.finashDate')">
                                    <span v-if="form.completionTime">{{ parseDateTime(form.completionTime, "dateTime") }}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="form.orderStatus==5">
                                <el-form-item :label="$t('omsOrder.label.closeDate')">
                                    <span v-if="form.shutdownTime">{{ parseDateTime(form.shutdownTime, "dateTime") }}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="form.orderStatus==5">
                                <el-form-item :label="$t('omsOrder.label.shutdownReason')">
                                    <span style="color: red;"> {{ form.shutdownReason }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="title-lable" v-if="form.orderStatus!==0 && form.orderStatus!==5">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("omsOrder.label.deliveryWarehouseInformation") }}
                        </div>
                    </div>
                    <div v-if="form.orderStatus!==0" class="grad-row">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item :label="$t('omsOrder.label.warehouseName')" prop="warehouseId">
                                    <span> {{form.warehouseName || '-'}}</span>
                                </el-form-item>
                            </el-col>
                          <el-col :span="6">
                            <el-form-item :label="$t('omsOrder.label.deliveryMethod')" prop="deliveryName"><span>{{form.deliveryName || '-'}}</span></el-form-item>
                          </el-col>
                        </el-row>
                    </div>
                    <div v-if="(form.orderStatus==1 && form.deliveryScheduleVOList && form.deliveryScheduleVOList.length>0) || form.orderStatus==2 || form.orderStatus==3 || form.orderStatus==4">
                        <div class="title-lable" style="justify-content:space-between;">
                            <div class="flex-center-start">
                                <div class="title-line"></div>
                                <div class="title-content">
                                    {{ $t("omsOrder.label.deliveryPlanInformation") }}
                                </div>
                            </div>
                            <div>
                                <!-- v-if="(form.orderStatus==1 && form.deliveryScheduleVOList && form.deliveryScheduleVOList.length>0) || form.orderStatus==2 || form.orderStatus==3" -->
                                <!-- 如果所有批次都已经配送了，则不显示调整计划入口 -->
<!--                                <el-button type="primary" v-if="form.deliveryScheduleVOList.some(item => item.batchStatus < 3)" @click="handleAdjustOrder('update')">{{$t('omsOrder.button.adjustDeliveryPlan')}}</el-button>-->
                            </div>
                        </div>
                        <div>
                            <div v-for="(item,index) in form.deliveryScheduleVOList">
                                <div v-if="item.deliveryScheduleDetailVOS && item.deliveryScheduleDetailVOS.length>0">
                                    <div  class="table-title">
                                        <div class="flex-center-start">
                                            <!--** 如果是（item.batchStatus==1）未审核状态，不显示状态和更改状态操作;如果是（item.batchStatus==4）已完成状态，不显示更改状态操作 *-->
                                            <div class="purchase" v-if="item.batchStatus!==1">
                                                <span class="purchase-status purchase-status-color0" v-if="item.batchStatus==0">{{t('omsOrder.orderStatusList.draft')}}</span>
                                              <!--  <span class="purchase-status purchase-status-color1" v-if="item.batchStatus==1">{{t('omsOrder.orderStatusList.check')}}</span>-->
                                                <span class="purchase-status purchase-status-color2" v-if="item.batchStatus==2">{{t('omsOrder.orderStatusList.ready')}}</span>
                                                <span class="purchase-status purchase-status-color3" v-if="item.batchStatus==3">{{t('omsOrder.orderStatusList.send')}}</span>
                                                <span class="purchase-status purchase-status-color0" v-if="item.batchStatus==4">{{t('omsOrder.orderStatusList.finash')}}</span>
                                                <span class="purchase-status purchase-status-color0" v-if="item.batchStatus==5">{{t('omsOrder.orderStatusList.close')}}</span>
                                            </div>
                                            <div style="color:var(--el-color-primary);" class="cursor-pointer ml6px" v-if="form?.canUpdateStatusForBatch" @click="handleUpdateStatus(item)">{{t('omsOrder.button.updateStatus')}}</div>
                                            <div style="color:#151719;" class="ml20px">{{parseDateTime(item.planDate, "date")}}</div>
                                            <div class="grad-row el-form-item__label ml20px">{{t('omsOrder.label.deliveryBatch')}}：<span style="color: #52585F">{{item.batchCode}}</span></div>
                                        </div>
                                        <div class="flex-center-start grad-row">
                                            <div class="el-form-item__label">
                                                {{t('omsOrder.label.estimatedDeliveryTime')}}：
                                                <span v-if="item.planDate" style="color: #52585F">
                                                  {{parseDateTime(item.planDate, "date")}}
                                                  <span v-if="item.planDateStart && item.planDateEnd" style="color: #52585F">
                                                    {{parseDateTime(item.planDateStart, "dateTime").split(' ')[1]}}~{{parseDateTime(item.planDateEnd, "dateTime").split(' ')[1]}}
                                                  </span>
                                                </span>
                                            </div>
                                            <div class="el-form-item__label">
                                                {{t('omsOrder.label.generationTime')}}：
                                                <span v-if="item.createTime" style="color: #52585F">{{ parseDateTime(item.createTime, "dateTime")}}</span>
                                            </div>
                                            <div class="el-form-item__label" v-if="item.batchStatus==3 || item.batchStatus==4">
                                                {{t('omsOrder.label.deliverDate')}}：
                                                <span v-if="item.deliveryTime" style="color: #52585F;">{{ parseDateTime(item.deliveryTime, "dateTime")}}</span>
                                            </div>
                                            <div class="el-form-item__label" v-if="item.batchStatus==4">
                                                {{t('omsOrder.label.finashDate')}}：
                                                <span v-if="item.completionTime" style="color: #52585F;">{{ parseDateTime(item.completionTime, "dateTime")}}</span>
                                            </div>
                                        </div>
                                        <div>
                                            <el-icon @click="changeShow(item)" v-if="item.show"><ArrowUp /></el-icon>
                                            <el-icon @click="changeShow(item)" v-else><ArrowDown /></el-icon>
                                        </div>
                                    </div>
                                    <el-table :data="item.deliveryScheduleDetailVOS" :show-header="false" highlight-current-row stripe v-show="item.show">
                                        <el-table-column type="index" :label="$t('common.sort')" width="60"  align="center"/>
                                        <el-table-column :label="$t('omsOrder.label.productName')" show-overflow-tooltip>
                                            <template #default="scope">
                                                <span style="font-weight: 600;">{{scope.row.productName}}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column :label="$t('omsOrder.label.productSpecName')" show-overflow-tooltip>
                                            <template #default="scope">
                                                <span>{{$t('omsOrder.label.productSpecName')}}：{{scope.row.productSpecs}}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column :label="$t('omsOrder.label.saleCountCopy')" show-overflow-tooltip align="right">
                                            <template #default="scope">
                                                <span>{{$t('omsOrder.label.saleCountCopy')}}：{{scope.row.productQty}}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column :label="$t('omsOrder.label.salePrice')" show-overflow-tooltip align="right">
                                            <template #default="scope">
                                                {{$t('omsOrder.label.salePrice')}}：
                                                <span v-if="scope.row.saleAmount">
                                                     <span v-if="form.currencyCode == 'CNY'">￥</span>
                                                     <span v-else>$</span>
                                                     <span>{{scope.row.saleAmount}}</span>
                                                </span>
                                                <span v-else>-</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column :label="$t('omsOrder.label.total')" show-overflow-tooltip align="right">
                                            <template #default="scope">
                                                {{$t('omsOrder.label.total')}}：
                                                <span v-if="scope.row.totalSaleAmount">
                                                     <span v-if="form.currencyCode == 'CNY'">￥</span>
                                                     <span v-else>$</span>
                                                     <span>{{scope.row.totalSaleAmount}}</span>
                                                </span>
                                                <span v-else>-</span>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                  <div v-if="outboundNoticeShow">
                    <div class="title-lable">
                      <div class="title-line"></div>
                      <div class="title-content">{{$t('omsOrder.label.saleOutBounceOrder')}}</div>
                    </div>
                    <div class="grad-row">
                      <el-row>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.outboundNoticeCode')">
                            <span style="color:var(--el-color-primary);cursor: pointer;text-decoration-line: underline" @click="turnToSaleOutboundNotice">{{form?.warehouseOutboundNoticeVO?.outboundNoticeCode}}</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.outboundNoticeStatus')">
                            <span v-if="form?.warehouseOutboundNoticeVO?.outboundNoticeStatus == 1">{{$t('omsOrder.outboundNoticeStatusList[1]')}}</span>
                            <span v-else-if="form?.warehouseOutboundNoticeVO?.outboundNoticeStatus == 2">{{$t('omsOrder.outboundNoticeStatusList[2]')}}</span>
                            <span v-else-if="form?.warehouseOutboundNoticeVO?.outboundNoticeStatus == 3">{{$t('omsOrder.outboundNoticeStatusList[3]')}}</span>
                            <span v-else-if="form?.warehouseOutboundNoticeVO?.outboundNoticeStatus == 4">{{$t('omsOrder.outboundNoticeStatusList[4]')}}</span>
                            <span v-else-if="form?.warehouseOutboundNoticeVO?.outboundNoticeStatus == 5">{{$t('omsOrder.outboundNoticeStatusList[5]')}}</span>
                            <span v-else-if="form?.warehouseOutboundNoticeVO?.outboundNoticeStatus == 6">{{$t('omsOrder.outboundNoticeStatusList[6]')}}</span>
                            <span v-else>-</span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                  <div v-if="orderReturnShow">
                    <div class="title-lable">
                      <div class="title-line"></div>
                      <div class="title-content">{{$t('omsOrder.label.orderReturn')}}</div>
                    </div>
                    <div class="grad-row">
                      <el-row>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.orderReturnCode')">
                            <span style="color:var(--el-color-primary);cursor: pointer;text-decoration-line: underline" @click="turnToAfterSalePage"> {{form?.orderReturnVO?.orderReturnCode}}</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.orderReturnTime')">
                            <span> {{ parseDateTime(form?.orderReturnVO?.createTime, "dateTime")}}</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.orderReturnApproveTime')">
                            <span>{{ parseDateTime(form?.orderReturnVO?.auditTime, "dateTime")   }}</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.returnType')">
                            <span v-if="form?.orderReturnVO?.returnType == 1">{{ $t('omsOrder.returnTypeList[1]')}}</span>
                            <span v-else-if="form?.orderReturnVO?.returnType == 2">{{ $t('omsOrder.returnTypeList[2]')}}</span>
                            <span v-else-if="form?.orderReturnVO?.returnType == 3">{{ $t('omsOrder.returnTypeList[3]')}}</span>
                            <span v-else>-</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.returnAmount')">
                            <span style="font-size: 16px; color: #C00C1D" v-if="form?.orderReturnVO?.returnAmount || form?.orderReturnVO?.returnAmount == 0">￥ {{form?.orderReturnVO?.returnAmount}}</span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                  <div v-if="warehouseReceiptNoticeShow">
                    <div class="title-lable">
                      <div class="title-line"></div>
                      <div class="title-content">{{ $t('omsOrder.label.afterSaleEntryOrder')}}</div>
                    </div>
                    <div class="grad-row">
                      <el-row>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.receiptNoticeCode')">
                            <span style="color:var(--el-color-primary);cursor: pointer;text-decoration-line: underline" @click="turnToWarehouseEntryNotice">{{form?.warehouseReceiptNoticeVO?.receiptNoticeCode}}</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="6">
                          <el-form-item :label="$t('omsOrder.label.outboundNoticeStatus')">
                            <span v-if="form?.warehouseReceiptNoticeVO?.status == 0">{{ $t('omsOrder.receiptNoticeStatusList[0]') }}</span>
                            <span v-else-if="form?.warehouseReceiptNoticeVO?.status == 1">{{ $t('omsOrder.receiptNoticeStatusList[1]') }}</span>
                            <span v-else-if="form?.warehouseReceiptNoticeVO?.status == 2">{{ $t('omsOrder.receiptNoticeStatusList[2]') }}</span>
                            <span v-else-if="form?.warehouseReceiptNoticeVO?.status == 3">{{ $t('omsOrder.receiptNoticeStatusList[3]') }}</span>
                            <span v-else-if="form?.warehouseReceiptNoticeVO?.status == 4">{{ $t('omsOrder.receiptNoticeStatusList[4]') }}</span>
                            <span v-else>-</span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                  <div>
                        <div class="title-lable">
                            <div class="title-line"></div>
                            <div class="title-content">{{ $t("omsOrder.label.operationLogInformation") }}</div>
                        </div>
                        <div>
                            <el-table :data="form.logList" highlight-current-row stripe class="log">
                                <el-table-column type="index" :label="$t('common.sort')" width="60"  align="center"/>
                                <el-table-column :label="$t('omsOrder.label.operationTime')" show-overflow-tooltip>
                                    <template #default="scope">
                                        <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column :label="$t('omsOrder.label.operationType')" prop="operationName" show-overflow-tooltip/>
                                <el-table-column :label="$t('omsOrder.label.operationUserName')" prop="createUserName" show-overflow-tooltip/>
                            </el-table>
                        </div>
                    </div>
                </el-form>
            </div>
            <div class="page-footer">
                 <!--草稿： 0 待审核： 1 备货中：2 配送中：3 已完成:4 已关闭：5-->
                <el-button @click="handleClose">{{ $t("common.reback") }}</el-button>
                <!--删除-->
                <el-button v-if="form?.canDeleteOrEdit" type="primary" plain @click="deleteOrder">{{ $t("common.delete") }}</el-button>
               <!--编辑-->
                <el-button v-if="form?.canDeleteOrEdit" type="primary" @click="handleEdit">{{ $t("common.edit") }}</el-button>
              <!--驳回-->
              <el-button type="danger" v-if="form?.canApprove" @click="rejectHandler">{{ $t("omsOrder.button.reject") }}</el-button>
              <!--审核完成-->
                <el-button v-if="form?.canApprove" type="primary" @click="handlecCheckFinash">{{ $t("omsOrder.button.checkFinash") }}</el-button>
              <!--撤回-->
              <el-button v-if="form?.canRecall" type="primary" @click="recallHandler">{{$t('omsOrder.button.recall')}}</el-button>
              <!--售后  -->
                <el-button type="primary" v-if="form?.showReturnButton" @click="turnToAfterSale">{{ $t("omsOrder.button.afterSale") }}</el-button>
              <!--补发-->
              <el-button type="primary" v-if="form?.showReissueButton" @click="reissueHandler">{{ $t("omsOrder.button.reissue") }}</el-button>
              <!--异常关闭-->
              <el-button type="primary" v-if="form?.canCancel"  @click="errorCloseHandle">{{ $t("omsOrder.button.errorClose") }}</el-button>
            </div>

            <Close
                    ref="closeRef"
                    v-model:visible="closeDialog.visible"
                    :title="closeDialog.title"
                    @onSubmit="onConcle"
            />
            <DeliveryPlan
                    ref="deliveryPlanRef"
                    v-model:visible="dialog.visible"
                    :title="dialog.title"
                    @onSubmit="onDeliveryPlan"
            />
        </div>

       <template v-if="errorCloseDialogVisible">
         <ErrorCloseDialog v-model:visible="errorCloseDialogVisible" :dialog-type="handleType" @confirm="errorCloseConfirm" @close="closeHandler" />
       </template>
    </div>
</template>

<script setup lang="ts">
    import { ElMessageBox } from "element-plus";

    defineOptions({
        name: "OrderDetail",
        inheritAttrs: false,
    });

    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import OrderAPI, { ErrorOrderFrom, OrderFrom } from "@/modules/oms/api/order";
    import { parseDateTime } from "@/core/utils/index.js";
    import FileAPI from "@/core/api/file";
    import Close from "./components/close.vue";
    import DeliveryPlan from "./components/deliveryPlan.vue";
    import { useDevilery } from "./composables/devilery";
    import { useUserStore } from "@/core/store";
    import { useNavigation } from "@/core/composables";

    const userStore = useUserStore();

    import ErrorCloseDialog from "@/modules/oms/views/order/orderManage/components/errorCloseDialog.vue";

    const { refreshAndNavigate } = useNavigation();
    const route = useRoute();
    const router = useRouter();
    const type = route.query.type;
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const fromRef = ref()
    const loading = ref(false);
    const id = route.query.id;
    const orderCode = route.query.orderCode;
    const form = reactive<OrderFrom>({
        orderDetailList:[],
        deliveryScheduleVOList:[],
        logList:[],
    });
    const  closeRef= ref()
    const closeDialog = reactive({
        title: "",
        visible: false,
    });
    const  deliveryPlanRef= ref()
    const dialog = reactive({
        title: "",
        visible: false,
    });

    const errorCloseDialogVisible = ref(false)

    const warehouseList = ref([])

    async function handleClose(){
      router.push({ path: "/oms/order/orderManage" })
    }

    /*售后入库单信息模块展示*/
    const warehouseReceiptNoticeShow = computed(() => {//orderType： 5地头售卖
      return form?.warehouseReceiptNoticeVO?.receiptNoticeCode && form?.orderType != 5
    })

    /*售后情况模块展示*/
    const orderReturnShow = computed(() => {
      return form?.orderReturnVO?.orderReturnCode
     })

    /*销售出库单信息*/

    const outboundNoticeShow = computed(() => {
      return form?.warehouseOutboundNoticeVO?.outboundNoticeCode
    })

    const handleViewInfo = async () => {
        try {
            const data = await OrderAPI.querySrcInfo({ id: id });
            form.contactPerson = data.name;
            form.contactMobile = data.mobile;
            form.deliveryAddress = data.address;
            form.isNameDecrypted = true;
            form.isMobileDecrypted = true;
            form.isAddressDecrypted = true;
        } catch (e) {}
    };

    function handleEdit(){
        router.push({
            path: "/oms/order/addOrder",
            query: {id:id,type:'edit', title:t("omsOrder.button.editOrder")}
        });
    }
    function changeShow(row){
        form.deliveryScheduleVOList.forEach((item)=>{
            if(row.orderBatchId==item.orderBatchId){
                item.show=!item.show
            }
        })
    }

    function turnToContractDetail() {
      router.push({
        path: "/oms/omsContract/contractDetail",
        query: { contractId: form.contractId },
      });
    }
    const handleType = ref('');
    function errorCloseHandle() {
      handleType.value = 'errorClose';
      errorCloseDialogVisible.value = true;
    }

    function rejectHandler() {
      handleType.value = 'reject';
      errorCloseDialogVisible.value = true;
    }

    function turnToAfterSale() {
      let orderBatchCode = form.deliveryScheduleVOList[0]?.batchCode
      refreshAndNavigate({
        path:"/oms/afterSale/addAfterSales",
        query:{
          fromPage:2,
          orderCode: form.orderCode,
          orderBatchCode: orderBatchCode,
        }
      })
    }

    function errorCloseConfirm(data) {
      if(data?.type === 'errorClose'){
        OrderAPI.cancelOrderApi({
          orderId: form.id,
          remark: data?.remark,
        })
          .then(() => {
            ElMessage.success(t("omsOrder.message.errorCloseOrderSucess"));
            errorCloseDialogVisible.value = false;
            handleClose();
          })

      }
      else if(data?.type == 'reject'){
        OrderAPI.rejectOrderApi({
          orderId: form.id,
          approveRejectReason: data?.remark,
        })
          .then(() => {
            ElMessage.success(t("omsOrder.message.rejectOrderSucess"));
            errorCloseDialogVisible.value = false;
            handleClose();
          })
      }
    }
    function closeHandler() {
      errorCloseDialogVisible.value = false;
    }

    function reissueHandler() {
      refreshAndNavigate({
        path: "/oms/order/addOrder",
        query: {
          type: 'add',
          fromPage: 'orderDetailReissue',
          orderCode: form.orderCode,
        },
      });
    }

    function approvePassHandler() {
      let msg = `订单[${form.orderCode}]客户期望送货日期时间为[${form.planDeliveryDate}&nbsp;${form.planDeliveryTime}]，确认按此安排?`
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(function () {
        OrderAPI.orderApprovePassApi({
          orderId: form.id,
        })
          .then(() => {
            ElMessage.success(t("omsOrder.message.rejectOrderSucess"));
            errorCloseDialogVisible.value = false;
            handleClose();
          })
      });
    }

    /*/!** 复制 *!/
    async function copyText() {
        try {
            await navigator.clipboard.writeText(form.orderCode);
            ElMessage.success(t('omsOrder.message.copySuccess'))
        } catch (error) {
            console.error('Failed to copy text:', error);
            ElMessage.error(t('omsOrder.message.copyFail'));
        }
    }*/

    function turnToSaleOutboundNotice() {
      router.push({
        path:'/wms/quickOutbound/detailQuickOutbound',
        query:{id: form?.warehouseOutboundNoticeVO?.id }
      })
    }

    function turnToWarehouseEntryNotice() {
      router.push({
        path: "/wms/storeManagement/warehouseEntryNoticeDetail",
        query: { id: form?.warehouseReceiptNoticeVO?.id, receiptNoticeCode: form?.warehouseReceiptNoticeVO?.receiptNoticeCode },
      });
    }

    function turnToAfterSalePage() {
      router.push({
        path:"/oms/afterSale/afterSaleDetail",
        query:{orderReturnCode:form?.orderReturnVO?.orderReturnCode,type:'detail',title:t('afterSaleManagement.button.afterSaleDetail')}
      })
    }




    /** 查询订单详情 */
    function queryOrderDetail(){
        loading.value = true;
        let params = {
            id:id,
            orderCode:orderCode,
        }
        OrderAPI.queryOrderDetail(params)
            .then((data) => {
                Object.assign(form,data)
                form.expectedReceivedTimeBase=form.expectedReceivedTimeBase?parseDateTime(form.expectedReceivedTimeBase, "date"):''
                form.expectedReceivedTimeStar=form.expectedReceivedTimeStar?parseDateTime(form.expectedReceivedTimeStar, "dateTime").split(' ')[1]:''
                form.expectedReceivedTimeEnd=form.expectedReceivedTimeEnd?parseDateTime(form.expectedReceivedTimeEnd, "dateTime").split(' ')[1]:''
                form.planDeliveryDate=form.expectedReceivedTimeBase
                if(form.expectedReceivedTimeStar && form.expectedReceivedTimeEnd){
                  form.planDeliveryTime=form.expectedReceivedTimeStar+'~'+form.expectedReceivedTimeEnd
                }
                let countryName=form.countryName?form.countryName:''
                let provinceName=form.provinceName?form.provinceName:''
                let cityName=form.cityName?form.cityName:''
                let districtName=form.districtName?form.districtName:''
                let address=form.address?form.address:''
                form.deliveryAddress=countryName+provinceName+cityName+districtName+address
                if(form.orderAttachmentFiles && typeof form.orderAttachmentFiles ==='string'){
                    form.orderAttachmentFiles =JSON.parse(form.orderAttachmentFiles);
                }
                let orderDetailList = []
                if(form.orderDetailList && form.orderDetailList.length>0){
                    form.orderDetailList.forEach(item=>{
                        let obj = {
                            ...item,
                            orderId:item.orderId,
                            orderCode:item.orderCode,
                            imagesUrls:item.imagesUrls,
                            // imagesUrls:item.imagesUrls && typeof item.imagesUrls ==='string'?JSON.parse(item.imagesUrls)[0].fileName:'',
                            productCode:item.productCode,
                            productName:item.productName,
                            productSpecs:item.productSpecs,
                            currencyCode:item.currencyCode,
                            currencyName:item.currencyName,
                            saleCount:item.productQty,
                            salePrice:item.saleAmount,
                            floatRange:item.saleAmountRadio,
                            saleMoney:item.totalSaleAmount,
                            firstCategoryId:item.firstCategoryId,
                            secondCategoryId:item.secondCategoryId,
                            thirdCategoryId:item.thirdCategoryId,
                            firstCategoryName:item.firstCategoryName,
                            secondCategoryName:item.secondCategoryName,
                            thirdCategoryName:item.thirdCategoryName,
                            productUnitId:item.productUnitId,
                            productUnitName:item.productUnitName,
                        }
                        orderDetailList.push(obj)
                    })
                }
                form.orderDetailList=orderDetailList
                if(form.deliveryScheduleVOList && form.deliveryScheduleVOList.length>0){
                    form.deliveryScheduleVOList[0].show=true
                }
                if(type=='check' && form.orderStatus==1){
                    getStorehouseSelect();
                }
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 仓库列表 */
    function getStorehouseSelect(){
        loading.value = true;
        OrderAPI.getStorehouseSelect()
            .then((data) => {
                warehouseList.value = data
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 删除订单 */
    function deleteOrder() {
        ElMessageBox.confirm(t('omsOrder.message.deleteTips'), t('omsOrder.title.draftDdelete'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                let data = {
                    id:id,
                    orderCode:orderCode,
                }
                OrderAPI.deleteOrder(data).then(res => {
                    ElMessage.success(t('omsOrder.message.deleteSucess'))
                    handleClose()
                })
            },
            () => {
                ElMessage.info(t('omsOrder.message.deleteConcel'));
            }
        );
    }

    function download(item){
        FileAPI.downloadFile(item.url, item.name);
    }

    function adjustOrder(){
        if(form.deliveryScheduleVOList && form.deliveryScheduleVOList.length>0){
            ElMessageBox.confirm(t('omsOrder.message.adjustOrderTips'), t('common.tips'), {
                confirmButtonText: t('common.confirm'),
                cancelButtonText: t('common.cancel'),
                type: "warning",
            }).then(
                () => {
                    router.push({
                        path: "/oms/order/addOrder",
                        query: {id:id,orderCode:orderCode,type:'check',deliveryScheduleDelete:'yes',title:t("omsOrder.button.adjustOrder")}
                    });
                },
                () => {
                    ElMessage.info(t('omsOrder.message.adjustOrderConcel'));
                }
            );
        }else{
             router.push({
                path: "/oms/order/addOrder",
                query: {id:id,orderCode:orderCode,type:'check',deliveryScheduleDelete:'no',title:t("omsOrder.button.adjustOrder")}
            });
        }
    }

    /** 取消订单 */
    function handleConcleOrder() {
        closeRef.value.setFormData({orderId:id});
        closeDialog.title = t('omsOrder.title.concleOrder');
        closeDialog.visible = true;
    }

    function onConcle() {
        localStorage.setItem('tabs',5)
        handleClose()
    }

    function handleAdjustOrder(type:string){
        router.push({
        path: "/oms/order/deliveryPlan",
        query: {id:id,orderCode:orderCode,type}
        });
    }
    /** 交货计划 */
    function handleDeliveryPlan() {
        dialog.title = t('omsOrder.title.deliveryPlan');
        dialog.visible = true;
    }
    const { createDeliveryPlan } = useDevilery();
    async function onDeliveryPlan(val: Number) {
        const deliveryScheduleDetailKey = [
          'productCode',
          'productName',
          'productSpecs',
          'currencyCode',
          'currencyName',
          {'saleAmount': 'salePrice'},
          'productUnitName',
          'productUnitId',
          {'productQty': 'saleCount'}, // 统一配送totalProductQty = productQty
          {'totalProductQty': 'saleCount'},
          {'productImg': 'imagesUrls'}
        ] // 创建交货计划createDeliveryScheduleDateDTOS的key订单详情key对应关系
        let data = {
            orderId:id,
            orderCode:orderCode,
            createDeliveryScheduleDateDTOS:[
              {
                planDate: new Date(`${form.expectedReceivedTimeBase} 00:00:00`).getTime(), // 计划交货日期 eg:2025-03-30 00:00:00的时间戳
                planDateStart: new Date(`${form.expectedReceivedTimeBase} ${form.expectedReceivedTimeStar}`).getTime(), // 计划开始时间
                planDateEnd: new Date(`${form.expectedReceivedTimeBase} ${form.expectedReceivedTimeEnd}`).getTime(), // 计划结束时间
                createDeliveryScheduleDateDetailDTOS: form.orderDetailList?.map(item => {
                  const obj = {}
                  deliveryScheduleDetailKey.forEach(key => {
                    // key could be a string or an object like {productQty: 'productQty'}
                    if (typeof key === 'object') {
                      // Get the first key and its value from the object
                      const objectKey = Object.keys(key)[0];
                      const objectValue = key[objectKey];
                      obj[objectKey] = item[objectValue];
                    } else {
                      obj[key] = item[key];
                    }
                  })
                  return obj
                }) // 交货计划商品明细
              }
            ],
        }
        //前端自己根据所选类型传入交货计划列表
        if(val === 1){ // 统一送达
          const flag = await createDeliveryPlan(data)
          if(flag){
            queryOrderDetail();
          }
        }else{ // 分批送达
            handleAdjustOrder('create')
            /* router.push({
                path: "/oms/order/deliveryPlan",
                query: {id:id,orderCode:orderCode,type}
            }); */
        }

    }

    /**审核完成 */
    function handlecCheckFinash() {
         fromRef.value.validate((valid) => {
             if (!valid) return;
             ElMessageBox.confirm(t('omsOrder.message.checkFinashTips1') +orderCode +t('omsOrder.message.checkFinashTips2')+form.planDeliveryDate+' '+ (form.planDeliveryTime ? form.planDeliveryTime : '') + t('omsOrder.message.checkFinashTips3'), t('omsOrder.title.checkFinash'), {
                 confirmButtonText: t('common.confirm'),
                 cancelButtonText: t('common.cancel'),
                 type: "warning",
             }).then(
                 () => {
                     const warehouse =  warehouseList.value.filter(item =>form.warehouseId==item.warehouseId)
                     let data = {
                         orderId:id,
                         warehouseId:form.warehouseId,
                         warehouseCode:warehouse[0]?.warehouseCode,
                         warehouseName:warehouse[0]?.warehouseName,
                     }
                     OrderAPI.auditCompleted(data).then(res => {
                         localStorage.setItem('tabs',2)
                         ElMessage.success(t('omsOrder.message.checkFinashSucess'))
                         handleClose()
                     })
                 },
                 () => {
                     ElMessage.info(t('omsOrder.message.checkFinashConcel'));
                 }
             );
         })
    }

    /*撤回*/
    function recallHandler() {
      ElMessageBox.confirm(t('omsOrder.message.recallOrderMsg'), t('omsOrder.message.recallOrderTitle'), {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: "warning",
      }).then(
        () => {
          OrderAPI.recallOrder({orderId: id}).then(res => {
            ElMessage.success(t('omsOrder.message.recallOrderSuccess'))
            queryOrderDetail()
          })
        },
        () => {
          ElMessage.info(t('omsOrder.message.recallOrderFail'));
        }
      );
    }

    /**修改状态 */
    function handleUpdateStatus(item) {
         fromRef.value.validate((valid) => {
             if (!valid) return;
             ElMessageBox.confirm(t('omsOrder.message.updateStatusTips') + (item.batchStatus==2 ? t('omsOrder.message.updateStatusTips1'):t('omsOrder.message.updateStatusTips2') ), t('omsOrder.title.updateStatus'), {
                 confirmButtonText: t('common.confirm'),
                 cancelButtonText: t('common.cancel'),
                 type: "warning",
             }).then(
                 () => {
                     let data = {
                         id: item.orderBatchId,
                         batchStatus: item.batchStatus,
                         orderId:  item.orderId,
                         orderCode: item.orderCode,
                     }
                     OrderAPI.updateStatus(data).then(res => {
                         ElMessage.success(t('omsOrder.message.updateStatusSucess'))
                         queryOrderDetail()
                     })
                 },
                 () => {
                     ElMessage.info(t('omsOrder.message.updateStatusConcel'));
                 }
             );
         })
    }

    onMounted(() => {
        queryOrderDetail();
    });
</script>
<style scoped lang="scss">
    .orderDetail {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .button-add{
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                color: var(--el-color-primary)
            }
            .table-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 50px;
                background: #F4F6FA;
                box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                padding: 15px 12px;

            }
        }
    }
    .header-approve-status{
      :deep(.el-descriptions__label){
        color: #90979E;
      }
      :deep(.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell){
        padding-bottom: 0px;
      }
    }
</style>
<style lang="scss">
    .orderDetail {
        .file-div {
            .el-form-item__content {
                display: block;
                margin-top: -38px;
            }
            .el-upload-list {
                margin: 5px;
            }
            .el-upload-list__item .is-success:hover{
                .el-upload-list__item-status-label {
                    width: 0px !important;
                    height: 0px !important;
                    display: none !important;
                }
                .el-icon {
                    width: 0px !important;
                    height: 0px !important;
                    display: none !important;
                }
                .el-icon--close-tip{
                    width: 0px !important;
                    height: 0px !important;
                    display: none !important;
                }
            }
            .el-upload-list__item-file-name{
                color:var(--el-color-primary)
            }
            .el-upload-list__item-status-label {
                width: 0px !important;
                height: 0px !important;
                display: none !important;
            }
            .el-icon {
                width: 0px !important;
                height: 0px !important;
                display: none !important;
            }
            .el-icon--close-tip{
                width: 0px !important;
                height: 0px !important;
                display: none !important;
            }
        }
        .log{
            .el-table__body-wrapper {
                max-height: 300px;
                overflow-y: auto;
            }
        }
      .el-form-item--default{
        margin-bottom: 0px !important;
      }
    }
</style>
