import { defineMockWms } from "./base";
import {ProductList} from "@/modules/wms/api/productDisassemblyAssembleOrder";

export default defineMockWms([

    // 获取拆装单号下拉数据
    {
        url: "disassemblyOrder/select",
        method: ["POST"],
        body: {
            code: 0,
            data: [
                    {
                        id: '1',
                        name: '拆装单号1',
                    },
                    {
                        id: '2',
                        name: '拆装单号2',
                    },
            ],
            msg: "一切ok",
        },
    },

    //根据拆装单号查询报损商品列表(报损单创建页面)
    {
        url: "disassemblyOrder/queryLossProductListByOrderId",
        method: ["POST"],
        body: {
            code: 0,
            data: [
                    {
                        id: '1',
                        productCode: '001',
                        productName: 'productName',
                        productSpec: '商品规格',
                        productUnitName: '斤',
                        sourceWarehouseAreaId: '001',
                        sourceWarehouseAreaName: 'warehouseAreaName',
                        disassemblyInQty: 5,
                    },
                ],
            msg: "一切ok",
        },
    },

    //查询所有可选的目标商品列表(分页展示拆装单选择的目标商品)
    {
        url: "product/product/page",
        method: ["POST"],
        body: {
            code: 0,
            data:{
                records: [
                    {
                        id: '11',
                        productCode: '0011',
                        productName: 'productName',
                        productSpec: '商品规格',
                        productUnitName: '斤',
                    },
                ],
            },
            msg: "一切ok",
        },
    },

    //获取拆装单分页数据
    {
        url: "disassemblyOrder/page",
        method: ["POST"],
        body: {
            code: 0,
            data: {
                records: [
                    {
                        id: '1',
                        disassemblyOrderCode: '拆装单号',
                        orderType: 1,
                        targetProductQty: 1,
                        sourceProductQty: 1,
                        lossOrderCode: '报损单号',
                        remark: "卑职呃呃",
                        disassemblerName: '拆装人',
                        disassemblyTime: 1740473528000,
                        createUser:'ccxc',
                        createTime: 1740473528000,
                        orderStatus: 0,
                    },
                    {
                        id: '2',
                        disassemblyOrderCode: '拆装单号',
                        orderType: 2,
                        targetProductQty: 2,
                        sourceProductQty: 2,
                        lossOrderCode: '报损单号',
                        remark: "卑职呃呃",
                        disassemblerName: '拆装人',
                        disassemblyTime: 1740473528000,
                        createUser:'ccxc',
                        createTime: 1740473528000,
                        orderStatus: 1,
                    },
                ],
                total: '2',
            },
            msg: "一切ok",
        },
    },

    // 删除拆装单
    {
        url: "disassemblyOrder/delete",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "删除成功",
        },
    },


    // 拆装单详情
    {
        url: "disassemblyOrder/detail",
        method: ["POST"],
        body: {
            code: 0,
            data: {
                id: '1',
                disassemblyOrderCode: '拆装单号',
                orderType: 1,
                receiptNoticeCode: '2222333333455',
                remark: "卑职呃呃",
                sourceList:[
                    {
                        id: '1',
                        productCode: '001',
                        productName: 'productName',
                        productSpec: '商品规格4',
                        productUnitName: '斤',
                        lossStatus: 1,
                        totalStockQty:90,
                        availableStockQty:80,
                        sourceWarehouseAreaId:'1',
                        sourceWarehouseAreaName:'11',
                        disassemblyOutQty:5,
                    },
                ],
                targetList:[
                    {
                        id: '1',
                        productCode: '001',
                        productName: 'productName',
                        productSpec: '商品规格3',
                        productUnitName: '斤',
                        lossStatus: 1,
                        targetWarehouseAreaId: '2',
                        targetWarehouseAreaName: '222',
                        disassemblyInQty: 4,
                    },
                ],
                disassemblerName: '拆装人',
                disassemblyTime: 1740473528000,
                createUser: 'ccxc',
                createTime: 1740473528000,

                orderStatus: 0,
            },
            msg: "一切ok",
        },
    },


    // 暂存拆装单
    {
        url: "disassemblyOrder/save",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "暂存成功",
        },
    },

    // 提交拆装单
    {
        url: "disassemblyOrder/submit",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "提交成功",
        },
    },

]);
