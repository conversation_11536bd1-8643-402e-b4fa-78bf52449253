import request from "@/core/utils/request";
const PROFITANDLOSS_BASE_URL = "/supply-wms/inventoryLossInfo";
const PROFITLOSS_BASE_URL = "/supply-wms/profitLoss";
const PRODUCTSTOCK_BASE_URL = "/supply-wms/productStock";
class profitAndLossManagementApi {
  /** 获取损益管理分页数据 */
  static getProfitAndLossPage(queryParams?: ProfitAddLossPageQuery){
    return request<any, PageResult<ProfitAddLossPageV0[]>>({
      // url: `${PROFITANDLOSS_BASE_URL}/list`,
      url: `${PROFITLOSS_BASE_URL}/pageList`,
      method: "post",
      data: queryParams,
    });
  }
  /** 新增/修改损益管理 */
  static saveInventoryLoss(data: InventoryLossFrom){
    return request({
      // url: `${PROFITANDLOSS_BASE_URL}/save`,
      url: `${PROFITLOSS_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }

    /** 获取盘点单号下拉列表 */
    static queryCheckCodeList(){
        return request({
            url: `/supply-wms/inventoryCheck/notHandle/list`,
            method: "post",
        });
    }

    /** 获取盘点单详情 */
    static queryCheckCodeDetail(data: { checkCode?:string }){
        return request({
            url: `/supply-wms/inventoryCheck/detail/${data.checkCode}`,
            method: "get",
        });
    }

  /** 删除损益管理 */
  // static deleteInventoryLoss(data: { id?:string }) {
  static deleteInventoryLoss(data: { profitLossId?:string }) {
    return request({
      // url: `${PROFITANDLOSS_BASE_URL}/del/${data.id}`,
      url: `${PROFITLOSS_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  /** 损益管理详情 */
  // static queryInventoryLossDetail(data: { id?:string }){
  static queryInventoryLossDetail(data: { profitLossId?:string }){
    return request<any, InventoryLossFrom>({
      // url: `${PROFITANDLOSS_BASE_URL}/detail/${data.id}`,
      url: `${PROFITLOSS_BASE_URL}/detail/${data.profitLossId}`,
      method: "get"
    });
  }
  /** 获取库区商品库存数量 */
  static queryAreaProductStockQty(data: {productCode?: string , warehouseAreaCode?: string}){
    return request({
      url: `${PRODUCTSTOCK_BASE_URL}/queryAreaProductStockQty`,
      method: 'post',
      data: data
    })
  }
  /** 获取商品库存库区 */
  static queryWarehouseAreaListByProductCode(data: {productCode?: string}){
    return request({
      url: `${PRODUCTSTOCK_BASE_URL}/selectList`,
      method: 'post',
      data: data
    })
  }

  /** 获取损益明细分页列表 */
  static getLossInfoPage(queryParams?: LossInfoPageQuery){
      return request<any, PageResult<LossInfoPageVO[]>>({
          url: `/supply-wms/inventoryCheck/ysnCode/list`,
          method: "post",
          data: queryParams,
      });
  }

    /**领用损益单 */
    static receiveProfitAndLoss(data: { profitLossId?:string}) {
        return request({
            url: `${PROFITLOSS_BASE_URL}/use`,
            method: "post",
            data: data,
        });
    }
    /**取消领用损益单 */
    static cancelProfitAndLoss(data: { profitLossId?:string}) {
        return request({
            url: `${PROFITLOSS_BASE_URL}/cancelUse`,
            method: "post",
            data: data,
        });
    }
    // 查询商品的库存库存数量
    static queryProductStockQty(data: {productCode?: string , warehouseAreaCode?: string}){
      return request({
        url: `${PRODUCTSTOCK_BASE_URL}/queryAreaProductStockQty`,
        method: 'post',
        data: data
      })
    }
}
export default profitAndLossManagementApi;

/** 损益管理分页查询参数 */
export interface ProfitAddLossPageQuery extends PageQuery{
  /** 损益单号 */
  profitLossCode?: string
  /** 损益类型 */
  profitLossTypeList?: any
  /** 状态 */
  statusList?: any
  /** 是否领单 */
  receiptStatus?: number
}
/** 损益管理分页对象 */
export interface ProfitAddLossPageV0{
  /** ID */
  id?: string;
  /** 损益单号 */
  profitLossCode?: string
  /** 损益类型 */
  profitLossType?: number
  /** 损益个数 */
  afterProfitLossQty?: number
  /** 损益数量 */
  profitLossTotalQty?: number
  /** 损益总重量 */
  profitLossTotalWeight?: number
  /** 损益库区 */
  warehouseAreaName?: string
  /** 领用人id */
  handleUser?: string
  /** 领用人 */
  handleUserName?: string
  /** 领用时间 */
  handleTime?: string
  /** 备注 */
  remark?: string
  /** 创建人 */
  createUserName?: string
  /** 创建时间 */
  createTime?: string
  /** 处理人 */
  handlerUserName?: string
  /** 处理时间 */
  handlerTime?: string
  /** 状态 */
  status?: number
}
/** 损益对象 */
export interface InventoryLossFrom{
  /** ID */
  id?: number
  /** 损益单号 */
  profitLossCode?: string
  /** 损益类型 */
  profitLossType?: number
  /** 损益增加数量 */
  increaseProfitLossQty?: number
  /** 损益减少数量 */
  decreaseProfitLossQty?: number
  /** 是否领单( 0-未领用 1-已领用) */
  receiptStatus?: string
  /** 领单人ID */
  handleUser?: string
  /** 领单人 */
  handleUserName?: string
  /** 领用时间 */
  handleTime?: string
  /** 状态( 0 草稿 1处理中 2完成) */
  status?: string
  /** 创建人 */
  createUserName?: string
  /** 商品编码 */
  productCode?: string
  /** 商品名称 */
  productName?: string
  /** 仓库库区编码 */
  warehouseAreaCode?: string
  /** 仓库库区名称 */
  warehouseAreaName?: string
  /** 损益前商品个数 */
  beforeProfitLossQty?: number
  /** 损益后商品个数 */
  afterProfitLossQty?: number
  /** 损益前商品数量 */
  beforeProfitLossTotalQty?: number
  /** 损益后商品数量 */
  afterProfitLossTotalQty?: number

  /** 创建时间 */
  createTime?: string
  /** 损益详情列表*/
  profitLossDetailList?: ProfitLossDetailList[],
  /** 损益增加列表*/
  profitLossDetailYsnAddVOList?: ProfitLossDetailYsnVO[],
  /** 损益移除列表*/
  profitLossDetailYsnSubVOList?: ProfitLossDetailYsnVO[],
}
/** 损益详情列表*/
export interface ProfitLossDetailList{
  /** ID */
  id?: number
  /** 损益单号 */
  profitLossCode?: string
  /** 损益类型 */
  profitLossType?: number
  /** 损益增加数量 */
  increaseProfitLossQty?: number
  /** 损益减少数量 */
  decreaseProfitLossQty?: number
  /** 是否领单( 0-未领用 1-已领用) */
  receiptStatus?: string
  /** 领单人ID */
  handleUser?: string
  /** 领单人 */
  handleUserName?: string
  /** 领用时间 */
  handleTime?: string
  /** 状态( 0 草稿 1处理中 2完成) */
  status?: string
  /** 创建人 */
  createUserName?: string
  /** 创建时间 */
  createTime?: string
  /** YSN码 */
  ysnCode?: string
  /** 商品源重量(kg) */
  beforeProfitLossWeight?: number
  /** 损益后商品重量(kg) */
  afterProfitLossWeight?: number
}
/** 损益增加、移除列表*/
export interface ProfitLossDetailYsnVO{
    /** ID */
    id?: number
    /** 重量 */
    weight?: number
    /** YSN码 */
    ysnCode?: string
    /** 商品编码 */
    productCode?: string;
    /** 商品名称 */
    productName?: string;
}
/* 损益明细参数*/
export interface LossInfoPageQuery extends PageQuery {
    /** 盘点单号 */
    checkCode?: string;
    /** 盘点单明细id */
    checkDetailId?: string;
    /** 商品编码 */
    productCode?: string;
}
/* 损益明细返回对象*/
export interface LossInfoPageVO{
    /** ID */
    id?: string
    /** 重量 */
    weight?: number
    /** YSN码 */
    ysnCode?: string
    /** 盘点状态 0待盘 1盘亏 2正常 3 盘盈 */
    ysnCheckStatus?: number
}
