<!--
 * @Author: ch<PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-08 10:24:01
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-02-13 17:29:48
 * @FilePath: \supply-manager-web\src\core\views\system\dept\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item
          :label="$t('deptManagement.label.deptName')"
          prop="deptName"
        >
          <el-input
            v-model="queryParams.deptName"
            :placeholder="$t('common.placeholder.inputTips')"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            class="filter-item"
            type="primary"
            @click="handleQuery"
            v-hasPerm="['pms:dept:search']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button @click="handleResetQuery" v-hasPerm="['pms:dept:reset']">
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button type="primary" @click="addDept" v-hasPerm="['pms:dept:add']">
          {{ $t("deptManagement.button.addDept") }}
        </el-button>
      </template>

      <el-table
        v-loading="loading"
        :data="deptList"
        row-key="id"
        stripe
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <template #empty>
          <Empty />
        </template>
        <!-- 部门名称 -->
        <el-table-column
          prop="deptName"
          :label="$t('deptManagement.label.deptName')"
        />
        <!-- 部门人数 -->
        <el-table-column
          prop="deptUserCount"
          :label="$t('deptManagement.label.employeesNum')"
        />
        <!-- 部门类型 层级 -->
        <el-table-column
          prop="deptLevel"
          :label="$t('deptManagement.label.deptLevel')"
        >
          <template #default="scope">
            <span>{{ filterLevel(scope.row.deptLevel) }}</span>
          </template>
        </el-table-column>
        <!-- 更新时间 -->
        <el-table-column
          prop="updateTime"
          :label="$t('deptManagement.label.updateTime')"
        >
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.updateTime, "dateTime") }}</span>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column
          :label="$t('common.handle')"
          fixed="right"
          align="left"
          width="200"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click.stop="addDept(scope.row)"
              v-hasPerm="['pms:dept:add']"
              v-if="scope.row.deptLevel != 3"
            >
              {{ $t("deptManagement.button.addBtn") }}
            </el-button>
            <el-button
              type="primary"
              link
              size="small"
              @click.stop="editDept(scope.row)"
              v-hasPerm="['pms:dept:edit']"
            >
              {{ $t("common.edit") }}
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click.stop="handleDelete(scope.row.id)"
              v-hasPerm="['pms:dept:del']"
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 新增编辑部门弹框 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      @closed="handleCloseDialog"
    >
      <el-form
        ref="deptFormRef"
        :model="formData"
        :rules="rules"
        label-position="top"
      >
        <!-- 部门名称 -->
        <el-form-item
          :label="$t('deptManagement.label.deptName')"
          prop="deptName"
        >
          <el-input
            v-model="formData.deptName"
            :placeholder="$t('common.placeholder.inputTips')"
            :maxlength="20"
          />
        </el-form-item>
        <!-- 部门层级 -->
        <el-form-item
          :label="$t('deptManagement.label.deptLevel')"
          prop="deptLevel"
        >
          <el-radio-group v-model="formData.deptLevel" disabled>
            <el-radio :label="1">
              {{ $t("deptManagement.label.firstLevelDept") }}
            </el-radio>
            <el-radio :label="2">
              {{ $t("deptManagement.label.secondaryLevelDept") }}
            </el-radio>
            <el-radio :label="3">
              {{ $t("deptManagement.label.thirdLevelDept") }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 上级部门 -->
        <el-form-item
          :label="$t('deptManagement.label.superiorDept')"
          prop="parentId"
          v-if="formData.deptLevel != 1"
        >
          <el-tree-select
            v-model="formData.parentId"
            :placeholder="$t('common.placeholder.selectTips')"
            :data="deptList"
            filterable
            check-strictly
            :render-after-expand="false"
            :props="{ value: 'id', label: 'deptName', children: 'children' }"
            node-key="id"
            disabled
          />
        </el-form-item>
        <!-- 部门描述 -->
        <el-form-item
          :label="$t('deptManagement.label.deptDesc')"
          prop="deptDesc"
        >
          <el-input
            v-model="formData.deptDesc"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            :placeholder="$t('common.placeholder.inputTips')"
            :maxlength="50"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">
            {{ $t("common.confirm") }}
          </el-button>
          <el-button @click="handleCloseDialog">
            {{ $t("common.cancel") }}
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Dept",
  inheritAttrs: false,
});

import DeptAPI, { DeptVO, DeptForm, DeptQuery } from "@/core/api/dept";
import { parseDateTime } from "@/core/utils/index.js";
const { t } = useI18n();

const queryFormRef = ref(ElForm);
const deptFormRef = ref(ElForm);

const loading = ref(false);
const dialog = reactive({
  title: "",
  visible: false,
});

const queryParams = reactive<DeptQuery>({});
const deptList = ref<DeptVO[]>();

// const deptOptions = ref<OptionType[]>();

const formData = reactive<DeptForm>({
  id: "",
  deptName: "",
  deptLevel: undefined,
  parentId: "",
  deptDesc: "",
  tenantId: "",
  setEditType: "",
});

const rules = reactive({
  deptName: [
    {
      required: true,
      message: t("deptManagement.rules.deptName"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("deptManagement.rules.nameFomart"),
      trigger: ["change", "blur"],
    },
  ],
  deptLevel: [
    {
      required: true,
      message: t("deptManagement.rules.deptLevel"),
      trigger: "change",
    },
  ],
  parentId: [
    {
      required: true,
      message: t("deptManagement.rules.superiorDept"),
      trigger: "change",
    },
  ],
});

/** 查询部门 */
function handleQuery() {
  loading.value = true;
  DeptAPI.getList(queryParams)
    .then((data) => {
      deptList.value = data;
    })
    .finally(() => (loading.value = false));
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  handleQuery();
}

/**
 * 新增
 *
 * @param parentId 父部门ID
 * @param deptId 部门ID
 */
async function addDept(row?: any) {
  dialog.visible = true;
  dialog.title = t("deptManagement.title.addDeptTitle");
  formData.setEditType = "add";
  if (row && row.id) {
    // Object.assign(formData, row);
    formData.parentId = row.id;
    formData.id = row.id;
    if (row.deptLevel == 1) {
      formData.deptLevel = 2;
    } else if (row.deptLevel == 2) {
      formData.deptLevel = 3;
    }
  } else {
    formData.deptLevel = 1;
  }
}

/**
 * 编辑
 *
 * @param parentId 父部门ID
 * @param deptId 部门ID
 */
function editDept(row?: any) {
  dialog.visible = true;
  dialog.title = t("deptManagement.title.editDeptTitle");
  Object.assign(formData, row);
  formData.setEditType = "edit";
}

/** 提交部门表单 */
function handleSubmit() {
  deptFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      if (formData.id && formData.setEditType == "edit") {
        DeptAPI.update(formData)
          .then(() => {
            ElMessage.success(t("deptManagement.message.editSucess"));
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        DeptAPI.add(formData)
          .then(() => {
            ElMessage.success(t("deptManagement.message.addSucess"));
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 删除部门 */
function handleDelete(ids: string) {
  ElMessageBox.confirm(
    t("deptManagement.message.deleteTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      DeptAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success(t("deptManagement.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("deptManagement.message.deleteCancel"));
    }
  );
}

/** 关闭弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  deptFormRef.value.resetFields();
  deptFormRef.value.clearValidate();

  formData.id = "";
  formData.parentId = "";
  formData.deptName = "";
  formData.deptDesc = "";
  formData.deptLevel = undefined;
  formData.setEditType = "";
}

/** 部门级别 */
function filterLevel(val: any) {
  if (!val) {
    return "-";
  }
  const typeObj: any = {
    1: t("deptManagement.label.firstLevelDept"),
    2: t("deptManagement.label.secondaryLevelDept"),
    3: t("deptManagement.label.thirdLevelDept"),
  };
  return typeObj[val];
}

onMounted(() => {
  handleQuery();
});
</script>
