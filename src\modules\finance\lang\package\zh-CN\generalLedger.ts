export default {
  setAccounts: {
    button: {
      addAccounts: '添加账套',
      editAccounts: '编辑账套'
    },
    setting: {
      set: '设置',
      edit: '编辑',
      copy: '复制',
      remove: '删除',
      add: '添加用户'
    },
    info: {
      guideline: '会计准则',
      type: '纳税类型',
      time: '启用时间',
      currentAccounts: '当前账套',

      basicInfo: '基本信息'
    },
    dialogInfo: {
      dialogTitle: '添加用户',
      index: '序号',
      name: '姓名',
      username: '用户名',
      role: '角色',
      submit: '提 交',
      cancel: '取 消'
    },
    messageInfo: {
      remove1: '确定要删除',
      remove2: '吗？删除后不可恢复！如需保存账套数据，请先备份下载到本地保存。',
    },
    formLabel: {
      accountsNumber: '账套编号',
      accountsName: '账套名称',
      companyName: '企业名称',
      startTime: '启用年月',
      accountSystem: '会计准则',
      type: '增值税种类',
      industry: '行业',
      address: '经营地址',
      connectName: '联系人',
      connectMobile: '联系电话',
      memo: '备注'
    },
    rules: {
      accountsNumber: '请输入账套编号',
      accountsName: '请输入账套名称',
      companyName: '请输入企业名称',
      startTime: '请选择启用年月',
      accountSystem: '请选择会计制度',
      type: '请选择增值税种类',
    },
    radioInfo: {
      generalTaxpayer: '一般纳税人',
      smallscaleTaxpayer: '小规模纳税人'
    }
  },
  accounting: {
    tabLabel: {
      property: '资产',
      liabilities: '负债',
      equity: '所有者权益',
      cost: '成本',
      profitLoss: '损益',
    },
    tableBtn:{
      add: '新增',
      addTitle: '新增科目',
      remove: '删除',
      import: '导入',
      export: '导出',
      edit: '编辑',
      editTitle: '编辑科目',
      addNextLevel: '新增下一级'
    },
    searchPlaceholder: {
      label: '请输入科目编号或科目名称'
    },
    tableLabel: {
      index: '序号',
      code: '科目编码',
      name: '科目名称',
      type: '类型',
      balance: '余额方向',
      assistance: '核算辅助',
      status: '状态',
      operate: '操作'
    },
    formLabel: {
      parentSubjectCode: '上级科目编码',
      parentSubjectName: '上级科目名称',
      subjectCode: '科目编码',
      subjectName: '科目名称',
      subjectType: '科目类别',
      subjectBalanceDirection: '余额方向',
      subjectAttribute: '核算属性',
      quantityAccountingCode: '数量核算',
    },
    radioInfo: {
      borrow: '借',
      loan: '贷',
      cashAccount: '现金科目',
      backAccount: '银行科目',
      noHave: '无',
      journal: '日记账',
      cashEquivalents: '现金等价物'
    },
    rules: {
      subjectCode: '请输入完整的科目编码',
      subjectName: '请输入科目名称',
    },
    messageInfo: {
      remove: '确定删除此科目吗？',
      disableTips: '确定要禁用此科目吗？',
      disableSuccess: '禁用成功！',
      enableSuccess: '启用成功！',
      checkQuantityAccounting: '请选择数量核算单位！',
      checkSubjectCode: '请输入正确位数的科目编码！',
      checkNextLevel: '请先删除子节点!',
      checkUserFlag: '已使用的科目无法删除!',
      removeSuccess: '删除成功！'
    }
  },
  voucher: {
    tableBtn: {
      add: '添加凭证字',
      remove: '删除',
      edit: '编辑'
    },
    tableLabel: {
      index: '序号',
      voucher: '凭证字',
      title: '显示标题',
      isDefault: '是否默认',
      operate: '操作'
    },
    formLabel: {
      voucherWord: '凭证字',
      displayTitle: '显示标题'
    },
    rules: {
      voucherWord: '请输入凭证字',
      displayTitle: '请输入显示标题',
    },
    messageInfo: {
      remove: '确定删除此凭证字吗？',
      batchRemove: '确定批量删除凭证字吗？',
      batchRemoveSuccess: '批量删除成功！'
    },
    dialogTitle: {
      add: '新增凭证字',
      edit: '编辑凭证字'
    }
  },
  abstract: {
    tableBtn: {
      add: '添加摘要',
      addTitle: '添加摘要',
      editTitle: '编辑摘要',
      edit: '编辑',
      remove: '删除',
    },
    tableLabel: {
      index: '序号',
      abstractContent: '摘要内容',
      operate: '操作'
    },
    searchPlaceholder: {
      label: '请输入摘要内容'
    },
    formLabel: {
      content: '摘要内容'
    },
    rules: {
      summaryContent: '请输入摘要内容'
    },
    messageInfo: {
      remove: '确定删除此摘要内容吗？'
    }
  },
  beginningPeriod: {
    tabLabel: {
      property: '资产',
      liabilities: '负债',
      equity: '所有者权益',
      cost: '成本',
      profitLoss: '损益',
    },
    tableBtn:{
      balance: '试算平衡',
      import: '导入',
      export: '导出',
    },
    searchPlaceholder: {
      label: '请输入科目编号或科目名称'
    },
    tableLabel: {
      index: '序号',
      code: '科目编码',
      name: '科目名称',
      type: '类型',
      balance: '余额方向',
      openingBalance: '期初余额',
      accumulatedDebit: '借方累计',
      accumulatedCredit: '贷方累计',
      beginningBalance: '年初余额',
    }
  },
}
