<template>
    <div class="app-container">
        <div class="productDisassemblyAssembleOrder">
            <div class="search-container">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="98px">
                    <el-form-item prop="disassemblyOrderCode" :label="$t('productDisassemblyAssembleOrder.label.disassemblyOrderCode')">
                        <el-input
                                v-model="queryParams.disassemblyOrderCode"
                                :placeholder="$t('productDisassemblyAssembleOrder.placeholder.disassemblyOrderCode')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="orderTypeList" :label="$t('productDisassemblyAssembleOrder.label.orderType')">
                        <el-select
                                v-model="queryParams.orderTypeList"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in orderTypeList" :key="item.orderTypeId" :label="item.orderTypeName" :value="item.orderTypeId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('productDisassemblyAssembleOrder.label.orderStatus')" prop="orderStatusList">
                        <el-select
                                v-model="queryParams.orderStatusList"
                                :placeholder="$t('common.placeholder.selectTips')"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in orderStatusList" :key="item.orderStatusId" :label="item.orderStatusName" :value="item.orderStatusId"></el-option>
                        </el-select>
                    </el-form-item>
<!--                    <el-form-item :label="$t('productDisassemblyAssembleOrder.label.receiveStatus')" prop="receivingStatus">-->
<!--                        <el-select-->
<!--                          v-model="queryParams.receivingStatus"-->
<!--                          :placeholder="$t('common.placeholder.selectTips')"-->
<!--                          clearable-->
<!--                          class="!w-[256px]"-->
<!--                        >-->
<!--                            <el-option v-for="(item,index) in receiveOption" :key="index" :label="item.label" :value="item.value"></el-option>-->
<!--                        </el-select>-->
<!--                    </el-form-item>-->
<!--                    <el-form-item :label="$t('productDisassemblyAssembleOrder.label.printStatus')" prop="printStatus">-->
<!--                        <el-select-->
<!--                          v-model="queryParams.printStatus"-->
<!--                          :placeholder="$t('common.placeholder.selectTips')"-->
<!--                          multiple-->
<!--                          clearable-->
<!--                          collapse-tags-->
<!--                          collapse-tags-tooltip-->
<!--                          class="!w-[256px]"-->
<!--                        >-->
<!--                            <el-option v-for="(item,index) in printOption" :key="index" :label="item.label" :value="item.value"></el-option>-->
<!--                        </el-select>-->
<!--                    </el-form-item>-->
                    <el-form-item prop="dateRange">
                        <el-select
                                v-model="queryParams.dateType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[200px] ml5px"
                        >
                            <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                        <el-date-picker
                                :editable="false"
                                class="!w-[370px]"
                                v-model="queryParams.dateRange"
                                type="datetimerange"
                                range-separator="~"
                                start-placeholder="开始时间"
                                end-placeholder="截止时间"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                :default-time="defaultTime"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                        <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(1)">{{$t('productDisassemblyAssembleOrder.label.today')}}</span>
                        <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(2)">{{$t('productDisassemblyAssembleOrder.label.yesterday')}}</span>
                        <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(3)">{{$t('productDisassemblyAssembleOrder.label.weekday')}}</span>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:reset']" @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-card shadow="never" class="table-container">
                <template #header>
                    <el-button  v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:add']" type="primary" @click="addProductDisassemblyAssembleOrder(null,'add')">
                        {{$t('productDisassemblyAssembleOrder.button.addProductDisassemblyAssembleOrder')}}
                    </el-button>
                </template>

                <el-table
                        v-loading="loading"
                        :data="productDisassemblyAssembleOrderList"
                        highlight-current-row
                        stripe
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column type="index" :label="$t('common.sort')" width="60" />
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.disassemblyOrderCode')" prop="disassemblyOrderCode" show-overflow-tooltip min-width="150px"></el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.orderType')" prop="orderType" show-overflow-tooltip min-width="100px">
                        <template #default="scope">
                            <span v-if="scope.row.orderType==1">{{$t('productDisassemblyAssembleOrder.orderTypeList.productPortfolio')}}</span>
                            <span v-if="scope.row.orderType==2">{{$t('productDisassemblyAssembleOrder.orderTypeList.productSplit')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.targetProductQty')" prop="targetProductQty" show-overflow-tooltip  min-width="150px">
                        <template #default="scope">
                            <div class="item">{{$t('productDisassemblyAssembleOrder.label.totality')}}:{{!isEmpty(scope.row.targetProductQty)?scope.row.targetProductQty:'-'}}</div>
                            <div class="item"> {{$t('productDisassemblyAssembleOrder.label.quantity')}}:{{!isEmpty(scope.row.targetProductTotalQty)?scope.row.targetProductTotalQty:'-'}}</div>
                            <div class="item">{{$t('productDisassemblyAssembleOrder.label.totalWeight')}}:{{!isEmpty(scope.row.targetProductTotalWeight)?scope.row.targetProductTotalWeight:'-'}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.sourceProductQty')" prop="sourceProductQty" show-overflow-tooltip  min-width="150px">
                        <template #default="scope">
                            <div class="item">{{$t('productDisassemblyAssembleOrder.label.totality')}}:{{!isEmpty(scope.row.sourceProductQty)?scope.row.sourceProductQty:'-'}}</div>
                            <div class="item"> {{$t('productDisassemblyAssembleOrder.label.quantity')}}:{{!isEmpty(scope.row.sourceProductTotalQty)?scope.row.sourceProductTotalQty:'-'}}</div>
                            <div class="item">{{$t('productDisassemblyAssembleOrder.label.weight')}}:{{!isEmpty(scope.row.sourceProductTotalWeight)?scope.row.sourceProductTotalWeight:'-'}}</div>
                        </template>
                    </el-table-column>
<!--                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.receiveInfo')" prop="receivingUserName" show-overflow-tooltip min-width="180px">-->
<!--                        <template #default="scope">-->
<!--                            <div class="item">{{$t('productDisassemblyAssembleOrder.label.receiveName')}}:{{scope.row.receivingUserName?scope.row.receivingUserName:'-'}}</div>-->
<!--                            <div class="item"> {{$t('productDisassemblyAssembleOrder.label.receiveTime')}}:{{ scope.row.receivingTime?parseDateTime(scope.row.receivingTime, "dateTime"):'-' }}</div>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.lossOrderCode')" prop="lossOrderCode" show-overflow-tooltip min-width="150px">
                        <template #default="scope">
                            <span class="cursor-pointer" style="color:var(--el-color-primary)" @click="jumpPurchaseOrder(scope.row)">{{scope.row.lossOrderCode}}</span>
                        </template>
                    </el-table-column>
                  <el-table-column :label="$t('productDisassemblyAssembleOrder.label.template')" prop="template" show-overflow-tooltip  min-width="150px">
                    <template #default="scope">
                      <div class="item">{{$t('productDisassemblyAssembleOrder.label.templateName')}}: {{scope.row?.disassemblyTemplateName || '-'}}</div>
                      <div class="item"> {{$t('productDisassemblyAssembleOrder.label.templateCode')}}: {{scope.row?.disassemblyTemplateCode || '-'}}</div>
                    </template>
                  </el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.remark')" prop="remark" show-overflow-tooltip min-width="150px"></el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.disassemblerName')" prop="disassemblerName" show-overflow-tooltip min-width="100px"></el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.disassemblyTime')" prop="disassemblyTime" show-overflow-tooltip min-width="180px">
                        <template #default="scope">
                            <span v-if="scope.row.disassemblyTime">{{ parseDateTime(scope.row.disassemblyTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.createUserName')" prop="createUserName" show-overflow-tooltip min-width="100px"></el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.createTime')" prop="createTime" show-overflow-tooltip min-width="180px">
                        <template #default="scope">
                            <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('productDisassemblyAssembleOrder.label.orderStatus')" prop="orderStatus" show-overflow-tooltip min-width="100px">
                        <template #default="scope">
                            <div class="purchase">
                                <span class="purchase-status purchase-status-color0" v-if="scope.row.orderStatus==0">{{$t('productDisassemblyAssembleOrder.orderStatusList.draft')}}</span>
                                <span class="purchase-status purchase-status-color3" v-if="scope.row.orderStatus==1">{{$t('productDisassemblyAssembleOrder.orderStatusList.disassemblyAssembleFinish')}}</span>
                                <span class="purchase-status purchase-status-color2" v-if="scope.row.orderStatus==2">{{$t('productDisassemblyAssembleOrder.orderStatusList.disassemblyAssemble')}}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                        <template #default="scope">
<!--                            <template v-if="scope.row.orderStatus==0">-->
<!--                                <template v-if="scope.row.receivingStatus==0">-->
<!--                                    <el-button-->
<!--                                      v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:receive']"-->
<!--                                      type="primary"-->
<!--                                      link-->
<!--                                      @click="handleReceive(scope.row)"-->
<!--                                    >-->
<!--                                        {{$t('productDisassemblyAssembleOrder.button.actionReceive')}}-->
<!--                                    </el-button>-->
<!--                                    <el-button-->
<!--                                      v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:delete']"-->
<!--                                      type="danger"-->
<!--                                      link-->
<!--                                      @click="handleDelete(scope.row.id)"-->
<!--                                    >-->
<!--                                        {{$t('common.delete')}}-->
<!--                                    </el-button>-->
<!--                                </template>-->
<!--                                <template v-else>-->
<!--                                    <el-button-->
<!--                                      v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:release']"-->
<!--                                      type="primary"-->
<!--                                      link-->
<!--                                      @click="handleCancel(scope.row)"-->
<!--                                    >-->
<!--                                        {{$t('productDisassemblyAssembleOrder.button.cancelTransfer')}}-->
<!--                                    </el-button>-->
<!--                                    <el-button-->
<!--                                      v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:edit']"-->
<!--                                      v-if="userStore.user.userId==scope.row.receivingUserId"-->
<!--                                      type="primary"-->
<!--                                      link-->
<!--                                      @click="addProductDisassemblyAssembleOrder(scope.row.id,'edit')"-->
<!--                                    >-->
<!--                                        {{$t('common.edit')}}-->
<!--                                    </el-button>-->
<!--                                </template>-->
<!--                            </template>-->
                          <el-button
                            v-hasPerm="['wms:insideWarehouseManagement:productDisassemblyAssembleOrder:detail']"
                            v-if="scope.row.orderStatus != 0"
                            type="primary"
                            link
                            @click="turnToDetailPage(scope.row.id,'detail')"
                          >
                            {{$t('common.detailBtn')}}
                          </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                        v-if="total > 0"
                        v-model:total="total"
                        v-model:page="queryParams.page"
                        v-model:limit="queryParams.limit"
                        @pagination="handleQuery"
                />
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">

    defineOptions({
        name: "ProductDisassemblyAssembleOrder",
        inheritAttrs: false,
    });

    import {changeDateRange, convertToTimestamp, parseDateTime,isEmpty} from "@/core/utils/index.js";
    import ProductDisassemblyAssembleOrderAPI, { ProductDisassemblyAssembleOrderPageVO, ProductDisassemblyAssembleOrderPageQuery} from "@/modules/wms/api/productDisassemblyAssembleOrder";
    import {useRouter} from "vue-router";
    import moment from "moment";
    import { emitter } from "@/core/utils/eventBus";
    import { useUserStore } from "@/core/store";
    import { useNavigation } from "@/core/composables/useNavigation";

    const { refreshAndNavigate } = useNavigation();
    const userStore = useUserStore();
    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const loading = ref(false);
    const total = ref(0);
    const orderTypeList = ref([
        {
            orderTypeId: 1,
            orderTypeName: t('productDisassemblyAssembleOrder.orderTypeList.productPortfolio')
        },
        {
            orderTypeId: 2,
            orderTypeName: t('productDisassemblyAssembleOrder.orderTypeList.productSplit')
        },
    ])
    const orderStatusList = ref([
        {
            orderStatusId: 0,
            orderStatusName: t('productDisassemblyAssembleOrder.orderStatusList.draft')
        },
        {
            orderStatusId: 2,
            orderStatusName: t('productDisassemblyAssembleOrder.orderStatusList.disassemblyAssemble')
        },
        {
            orderStatusId: 1,
            orderStatusName:t('productDisassemblyAssembleOrder.orderStatusList.disassemblyAssembleFinish')
        },
    ])
    const dateTypeList = ref([
        {
            key: 1,
            value: t('productDisassemblyAssembleOrder.dateTypeList.createDate')
        },
        {
            key: 2,
            value:t('productDisassemblyAssembleOrder.dateTypeList.disassemblyAssembleDate')
        },
    ])

    const receiveOption=ref([
        {
            value: 0,
            label: t('productDisassemblyAssembleOrder.whetherOption.no')
        },
        {
            value: 1,
            label: t('productDisassemblyAssembleOrder.whetherOption.yes')
        },

    ])
    const printOption=ref([
        {
            value: 0,
            label: t('productDisassemblyAssembleOrder.printStatus[0]')
        },
        {
            value: 1,
            label: t('productDisassemblyAssembleOrder.printStatus[1]')
        },
        {
            value: 2,
            label: t('productDisassemblyAssembleOrder.printStatus[2]')
        },
    ])
    const queryParams = reactive<ProductDisassemblyAssembleOrderPageQuery>({
        dateType:1,
        dateRange: [moment().subtract('days', 29).startOf("days").format('YYYY-MM-DD HH:mm:ss'), moment().endOf("days").format("YYYY-MM-DD HH:mm:ss")],
        page: 1,
        limit: 20,
    });
    const defaultTime: [Date, Date] = [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
    ]
    const productDisassemblyAssembleOrderList = ref<ProductDisassemblyAssembleOrderPageVO[]>();

    /** 时间转换 */
    function handleChangeDateRange(val: any) {
        queryParams.dateRange = changeDateRange(val);
    }

    /** 查询 */
    function handleQuery() {
        if (queryParams.disassemblyOrderCode && queryParams.disassemblyOrderCode.length < 4) {
            return ElMessage.error(t("productDisassemblyAssembleOrder.message.codeValideTips"));
        }
        loading.value = true;
        let params = {
            ...queryParams,
        }
        if(queryParams.dateType==1 && queryParams.dateRange && queryParams.dateRange.length>0){
            params.createStartTime=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.createEndTime=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        if(queryParams.dateType==2 && queryParams.dateRange && queryParams.dateRange.length>0){
            params.disassemblyStartTime=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.disassemblyEndTime=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        delete params.dateType
        delete params.dateRange
        ProductDisassemblyAssembleOrderAPI.getProductDisassemblyAssembleOrderPage(params)
            .then((data) => {
                productDisassemblyAssembleOrderList.value = data.records;
                total.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 重置查询 */
    function handleResetQuery() {
        queryFormRef.value.resetFields();
        queryParams.dateType=1
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    /** 删除 */
    function handleDelete(id?: string) {
        ElMessageBox.confirm(t('productDisassemblyAssembleOrder.message.deleteTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                loading.value = true;
                let data = {
                    id:id
                }
                ProductDisassemblyAssembleOrderAPI.deleteProductDisassemblyAssembleOrder(data)
                    .then(() => {
                        ElMessage.success(t('productDisassemblyAssembleOrder.message.deleteSucess'));
                        handleResetQuery();
                    })
                    .finally(() => (loading.value = false));
            },
            () => {
                ElMessage.info(t('productDisassemblyAssembleOrder.message.deleteConcel'));
            }
        );
    }

    /**报损单号详情跳转*/
    function jumpPurchaseOrder(item:any){
        router.push({
            path: "/wms/insideWarehouseManagement/detailReportLossOrder",
            query: {
                id:item.lossOrderId,
                type:'detail'
            }
        });
    }

    /** 新增/编辑*/
    function addProductDisassemblyAssembleOrder(id?:string,type?:string){
      refreshAndNavigate({
        path: "/wms/insideWarehouseManagement/addProductDisassemblyAssembleOrder",
        query: {id:id,type:type,title:type=='add'?t('productDisassemblyAssembleOrder.button.addProductDisassemblyAssembleOrder'):type=='edit'?t('productDisassemblyAssembleOrder.button.editProductDisassemblyAssembleOrder'):t('common.detailBtn')}
      })
    }

    /*跳转详情*/
    function turnToDetailPage(id?:string,type?:string) {
      router.push({
        path: "/wms/insideWarehouseManagement/detailProductDisassemblyAssemble",
        query: {id:id,type:type,title: t('common.detailBtn')}
      });
    }

    /**
     * 领单
     * @param item
     */
    function handleReceive(item:any){

        let params = {
            id:item.id
        }
        ProductDisassemblyAssembleOrderAPI.pickOrder(params).then(() => {
            ElMessage.success(t('productDisassemblyAssembleOrder.message.actionSucess'));
            handleQuery();
        })
    }

    /**
     * 取消领单
     * @param item
     */
    function handleCancel(item:any) {

            ElMessageBox.confirm(t('productDisassemblyAssembleOrder.message.receiveTips'), t('common.tipTitle'), {
                confirmButtonText: t('common.confirm'),
                cancelButtonText: t('common.cancel'),
                type: "warning",
            }).then(
              () => {
                  let params = {
                      id:item.id
                  }
                  ProductDisassemblyAssembleOrderAPI.releaseOrder(params).then(() => {
                      ElMessage.success(t('productDisassemblyAssembleOrder.message.actionSucess'));
                      handleQuery();
                  })

              }
            )


    }


    onActivated(() => {
        handleQuery();
    });

    emitter.on("reloadListByWarehouseId", (e) => {
        nextTick(() => {
            handleQuery();
        });
    });

</script>

<style lang="scss" scoped>
    .productDisassemblyAssembleOrder{}
</style>
