<template>
    <div class="app-container">
        <div class="addProductDisassemblyAssembleOrder"  v-loading="loading">
            <div >
                <div class="page-title">
                    <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                    <div>
                        <span> {{t('productDisassemblyAssembleOrder.label.disassemblyOrderCode')}}：{{form.disassemblyOrderCode}}</span>
                    </div>
                </div>
            </div>
            <div class="page-content">
                <el-form
                        :model="form"
                        :rules="rules"
                        ref="fromRef"
                        label-width="120px"
                        label-position="right"
                >
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("productDisassemblyAssembleOrder.label.basicInformation") }}
                        </div>
                    </div>
                    <div v-if="type=='detail'">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.createUserName')">
                                    {{form.createUserName}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.createTime')">
                                    {{parseDateTime(form.createTime, "dateTime")}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.disassemblerName')">
                                    <span>{{form.disassemblerName}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.disassemblyTime')">
                                    {{parseDateTime(form.disassemblyTime, "dateTime")}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.orderType')">
                                    <span>{{ getOrderTypeLabel }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                              <el-form-item :label="$t('productDisassemblyAssembleOrder.label.template')">
                                <span>{{form?.disassemblyTemplateName || '-'}}</span>
                              </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.disassemblyOrderStatus')">
                                    <span  v-if="!isEmpty(form.orderStatus)" style="word-break:break-all;">{{filterStatus(form.orderStatus)}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.remark')">
                                    <span v-if="form.remark" style="word-break:break-all;">{{form.remark}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line"></div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("productDisassemblyAssembleOrder.label.productDisassemblyAssembleOrderInformation") }}
                        </div>
                    </div>
                       <div class="mb-20">
                           <div class="flex-center-but">
                               <div class="trapezoid trapezoid-color1">
                                   {{$t('productDisassemblyAssembleOrder.label.sourceProduct')}}
                                   <span class="fw-400">（{{$t('productDisassemblyAssembleOrder.label.totalCountProduct')}}: {{sourceTotalNum}}</span>
                                   <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalWeight')}}: {{sourceTotalWeight}}</span>
                                   <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalRowCount')}}: {{ sourceSkuProductNum }}）</span>
                               </div>
                           </div>
                           <el-table class="product-list-table" v-loading="loadSource" :data="form.sourceList" max-height="530" highlight-current-row stripe>
                               <el-table-column type="index" :label="$t('common.sort')" width="60" />
                               <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productInformation')" width="200px">
                                   <template #default="scope">
                                     <div style="word-break: break-all"><span style="color: #90979E">{{scope.row.productCode}} | </span>{{scope.row.productName}}</div>
                                     <!--<div class="product-name">{{scope.row.productName}}</div>
                                     <div>
                                       <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productCode')}}：</span>
                                       <span class="product-value">{{scope.row.productCode || '-'}}</span>
                                     </div>-->
                                   </template>
                               </el-table-column>
                               <!--规格-->
                               <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productSpec')" prop="productSpec" show-overflow-tooltip min-width="100px"></el-table-column>
                               <!--出库库区-->
                               <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.sourceWarehouseArea')" show-overflow-tooltip min-width="260px">
                                   <template #default="scope">
                                       <el-form-item label-width="0px" :prop="'sourceList.'+scope.$index+'.sourceWarehouseAreaId'">
                                         <span>{{ scope.row.sourceWarehouseAreaId || '-' }}</span>
                                       </el-form-item>
                                   </template>
                               </el-table-column>
                              <!--单价-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.price')" min-width="260px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" :prop="'sourceList.'+scope.$index+'.unitPrice'">
                                   <span>{{ scope.row.unitPrice }}</span>
                                   <span>元/{{ scope.row?.pricingScheme == 0 ? scope.row?.productUnitName : scope.row?.conversionRelSecondUnitName }}</span>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--出库量-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.outWarehouseNum')" min-width="260px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" :prop="'sourceList.'+scope.$index+ '.disassemblyOutQty'">
                                   <div>
                                     <span v-if="scope.row.disassemblyOutQty || scope.row.disassemblyOutQty ==0">{{ scope.row.disassemblyOutQty }} {{scope.row?.productUnitName}}</span>
                                     <span v-else>-</span>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--一级单位增减-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" show-overflow-tooltip min-width="120px">
                               <template #default="scope">
                                 {{scope.row.isDiscreteUnit === 1 ? $t('productDisassemblyAssembleOrder.whetherOption.yes') : $t('productDisassemblyAssembleOrder.whetherOption.no')}}
                               </template>
                             </el-table-column>
                             <!--出库转换量-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.outWarehouseTransferNum')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" :prop="'sourceList.'+scope.$index+ '.disassemblyOutWeight'">
                                   <div>
                                     <span v-if="scope.row.disassemblyOutWeight || scope.row.disassemblyOutWeight ==0">{{ scope.row.disassemblyOutWeight }} {{scope.row?.conversionRelSecondUnitName}}</span>
                                     <span v-else>-</span>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--金额-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.amount')" prop="amount" show-overflow-tooltip min-width="260px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" :prop="'sourceList.'+scope.$index+ '.amount'">
                                   <div>
                                     <span>{{scope.row.amount}}</span>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--包装-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.packaging')" prop="productPackaging" show-overflow-tooltip min-width="160px">
                               <template #default="scope">
                                 <el-form-item label-width="0px" :prop="'sourceList.'+scope.$index+ '.productPackaging'">
                                   <div>
                                     <span>{{scope.row.productPackaging}}</span>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--可用库存-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.availableWarehouse')" prop="isDiscreteUnit" show-overflow-tooltip min-width="200px">
                               <template #default="scope">
                                 <div>
                                   <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.availableWarehouseNum')}}：</span>
                                   <span class="product-value">{{scope.row.availableStockQty || '-'}}</span>
                                 </div>
                                 <div>
                                   <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.availableWarehouseTransferNum')}}：</span>
                                   <span class="product-value">{{scope.row.availableStockWeight || '-'}}</span>
                                 </div>
                               </template>
                             </el-table-column>
                           </el-table>
                       </div>
                        <div class="mb-20">
                            <div class="flex-center-but">
                                <div class="trapezoid trapezoid-color2">
                                    {{$t('productDisassemblyAssembleOrder.label.targetProduct')}}
                                    <span class="fw-400">（{{$t('productDisassemblyAssembleOrder.label.totalCountProduct')}}: {{targetTotalNum}}</span>
                                    <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalWeight')}}: {{targetTotalWeight}}</span>
                                    <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalRowCount')}}: {{ targetSkuProductNum }}）</span>
                                </div>
                            </div>
                            <el-table class="product-list-table" :data="form.targetList" max-height="530" highlight-current-row stripe>
                                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                                <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productInformation')" width="200px">
                                    <template #default="scope">
                                      <div style="word-break: break-all"><span style="color: #90979E">{{scope.row.productCode}} | </span>{{scope.row.productName}}</div>
                                      <!--<div class="product-name">{{scope.row.productName}}</div>
                                      <div>
                                        <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productCode')}}：</span>
                                        <span class="product-value">{{scope.row.productCode || '-'}}</span>
                                      </div>-->
                                    </template>
                                </el-table-column>
                              <!--规格-->
                              <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productSpec')" prop="productSpec" show-overflow-tooltip min-width="100px"></el-table-column>
                              <!--入库库区-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inSourceWarehouseArea')" show-overflow-tooltip min-width="200px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" :prop="'targetList.'+scope.$index+'.targetWarehouseAreaId'">
                                    <span>{{ scope.row.targetWarehouseAreaId || '-'}}</span>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--单价-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.price')" min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" :prop="'targetList.'+scope.$index+'.unitPrice'">
                                    <span>{{scope.row.unitPrice}}</span>
                                    <span>元/{{ scope.row?.pricingScheme == 0 ? scope.row?.productUnitName : scope.row?.conversionRelSecondUnitName }}</span>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--入库量-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inWarehouseNum')" min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" :prop="'targetList.'+scope.$index+ '.disassemblyInQty'">
                                    <div>
                                      <span v-if="scope.row.disassemblyInQty || scope.row.disassemblyInQty ==0">{{ scope.row.disassemblyInQty }} {{scope.row?.productUnitName}}</span>
                                      <span v-else>-</span>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--一级单位增减-->
                              <el-table-column :label="$t('productDisassemblyAssembleOrder.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" show-overflow-tooltip min-width="120px">
                                <template #default="scope">
                                  {{scope.row.isDiscreteUnit === 1 ? $t('productDisassemblyAssembleOrder.whetherOption.yes') : $t('productDisassemblyAssembleOrder.whetherOption.no')}}
                                </template>
                              </el-table-column>
                              <!--入库转换量-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inWarehouseTransferNum')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" :prop="'targetList.'+scope.$index+ '.disassemblyInWeight'">
                                    <div>
                                      <span v-if="scope.row.disassemblyInWeight || scope.row.disassemblyInWeight ==0">{{ scope.row.disassemblyInWeight }} {{scope.row?.conversionRelSecondUnitName}}</span>
                                      <span v-else>-</span>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--金额-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.amount')" prop="amount" show-overflow-tooltip min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" :prop="'targetList.'+scope.$index+ '.amount'">
                                    <div>
                                      <span>{{ scope.row.amount }}</span>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--包装-->
                              <el-table-column :label="$t('productDisassemblyAssembleOrder.label.packaging')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                                <template #default="scope">
                                  <el-form-item label-width="0px" :prop="'targetList.'+scope.$index+ '.productPackaging'">
                                    <div>
                                      <span>{{ scope.row.productPackaging }}</span>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                            </el-table>
                        </div>
                </el-form>
            </div>
            <div class="page-footer" >
                <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

    defineOptions({
        name: "AddProductDisassemblyAssembleOrder",
        inheritAttrs: false,
    });
    import { parseDateTime,isEmpty } from "@/core/utils/index.js";
    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import ProductDisassemblyAssembleOrderAPI,{ProductDisassemblyAssembleOrderFrom} from "@/modules/wms/api/productDisassemblyAssembleOrder";

    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const fromRef = ref()
    const loadSource = ref(false);
    const loading = ref(false);
    const id = route.query.id;
    const type = route.query.type;

    const orderTypeList = ref([
        {
            orderTypeId: 1,
            orderTypeName: t('productDisassemblyAssembleOrder.orderTypeList.productPortfolio')
        },
        {
            orderTypeId: 2,
            orderTypeName: t('productDisassemblyAssembleOrder.orderTypeList.productSplit')
        },
    ])
    const orderStatusList = ref([
        {
            orderStatusId: 0,
            orderStatusName: t('productDisassemblyAssembleOrder.orderStatusList.draft')
        },
        {
            orderStatusId: 2,
            orderStatusName: t('productDisassemblyAssembleOrder.orderStatusList.disassemblyAssemble')
        },
        {
            orderStatusId: 1,
            orderStatusName:t('productDisassemblyAssembleOrder.orderStatusList.disassemblyAssembleFinish')
        },
    ])

    const form = reactive<ProductDisassemblyAssembleOrderFrom>({
        orderType: undefined,
        sourceList: [],
        targetList: [],
    });

    const rules = reactive({});

    //计算源 总量: 出库量求和
    const sourceTotalNum = computed(() => {
        let calc = form.sourceList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyOutQty || 0);
        }, 0)
        return calc
    })

    //计算源 总重量 出库转换量求和
    const sourceTotalWeight = computed(() => {
        let calc = form.sourceList?.reduce((total, item) => {
            return Number(total) +  Number(item.disassemblyOutWeight || 0);
        }, 0).toFixed(3)
        return calc
    })

    //计算源 商品个数 SKU的商品个数
    const sourceSkuProductNum = computed(() => {
      return form.sourceList?.filter(item => item.isSku)?.length || '0'
    })

    //计算目标 总量: 入库量求和
    const targetTotalNum = computed(() => {
        let calc = form.targetList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyInQty || 0);
        }, 0)
        return calc
    })

    //计算目标 总重量:入库转换量求和
    const targetTotalWeight = computed(() => {
        let calc = form.targetList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyInWeight || 0);
        }, 0).toFixed(3)
        return calc
    })

    //计算目标 商品个数 SKU的商品个数
    const targetSkuProductNum = computed(() => {
      return form.targetList?.filter(item => item.isSku)?.length || '0'
    })


    async function handleClose() {
      router.push({
        path:'/wms/insideWarehouseManagement/productDisassemblyAssembleOrder',
      })
    }

    /** 查询库存转移详情 */
    function getProductDisassemblyAssembleOrderDetail(){
        loading.value = true;
        let params = { id:id }
        ProductDisassemblyAssembleOrderAPI.getProductDisassemblyAssembleOrderDetail(params)
            .then((data) => {
                Object.assign(form,data)
                if(form.sourceList && form.sourceList.length>0){
                    form.sourceList.forEach(item=>{
                      item.sourceWarehouseAreaId=item.sourceWarehouseAreaName + ' | ' + item.sourceWarehouseAreaCode
                    })
                }
                if(form.targetList && form.targetList.length>0){
                    form.targetList.forEach(item=>{
                      item.targetWarehouseAreaId=item.targetWarehouseAreaName + ' | ' + item.targetWarehouseAreaCode
                    })
                }
            })
            .finally(() => {
                loading.value = false;
            });
    }

    function filterStatus(val:any) {
        if (!isEmpty(val)) {
            return orderStatusList.value.find(item => item.orderStatusId === val)?.orderStatusName || '';
        }
    }

    const getOrderTypeLabel = computed(() => {
      return orderTypeList.value?.find(out =>form.orderType==out.orderTypeId)?.orderTypeName || '-'
    })

    onMounted(async () => {
      getProductDisassemblyAssembleOrderDetail();
    });
</script>
<style scoped lang="scss">
    .addProductDisassemblyAssembleOrder {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .trapezoid {
                max-width:80%;
                //width:250px;
                padding: 9px 50px 9px 26px;
                clip-path: polygon(0 0, 90% 0, 100% 100%, 0% 100%);
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 16px;
                color: #FFFFFF;
                text-align: left;
                font-style: normal;
            }
            right{
            }
            .fw-400{
                font-weight: 400;
                margin-right: 20px;
            }
            .trapezoid-color1 {
                background-color: var(--el-color-primary);
            }
            .trapezoid-color2 {
                background-color: #008E7C;
            }
            .button-add{
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                color: var(--el-color-primary)
            }
            .table-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 50px;
                background: #F4F6FA;
                box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                padding: 15px 12px;
            }
        }
        .link{
            color: var(--el-color-primary);
            cursor: pointer;
        }
        .product-list-table {
          :deep(.el-form-item--default){
            margin-bottom: 0px;
          }
        }
        .product-name {
          font-weight: 500;
          font-size: 14px;
          color: #151719;
        }
    }
</style>
