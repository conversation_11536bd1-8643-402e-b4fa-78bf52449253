<template>
  <el-drawer
    :model-value="isVisible"
    :title="$t('outboundNotice.title.addProduct')"
    :close-on-click-modal="false"
    :size="850"
    @close="close"
  >
    <el-form :model="goodsQueryParams" ref="goodsQueryFormRef">
      <el-row :gutter="20">
          <el-col :span="10">
              <!-- 库区 -->
              <el-form-item
                prop="warehouseAreaCode"
                :label="$t('pickOrder.label.warehouseAreaName')"
              >
                  <el-select
                    v-model="goodsQueryParams.warehouseAreaCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                      <el-option
                        v-for="(item, index) in warehouseAreaOption"
                        :key="index"
                        :label="`${item.areaName} | ${item.areaCode}`"
                        :value="item.areaCode"
                      />
                  </el-select>
              </el-form-item>
          </el-col>
        <el-col :span="10">
          <!-- 商品分类 -->
          <el-form-item
            :label="$t('outboundNotice.label.goodsCategory')"
            prop="category"
          >
            <el-cascader
              ref="cascaderRef"
              :props="propsCategory"
              :options="categoryList"
              :placeholder="$t('common.placeholder.selectTips')"
              v-model="goodsQueryParams.category"
              @change="handleChange"
              clearable
              filterable
              collapse-tags
              collapse-tags-tooltip
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <!-- 商品名称 -->
          <el-form-item :label="$t('pickOrder.label.goods')" prop="productName">
            <el-input
              v-model="goodsQueryParams.productName"
              :placeholder="$t('outboundNotice.placeholder.productInputTips')"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="6" style="text-align: right">
          <el-button type="primary" @click="queryGoodList">
            {{ $t("common.search") }}
          </el-button>
          <el-button @click="handleResetQuery">
            {{ $t("common.reset") }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <div class="selected-info">
      {{ $t("pickOrder.label.selected") }}
      <span class="selected-count">{{ multipleSelection.length }}</span>
      {{ $t("pickOrder.label.selectedQuantity") }}
    </div>

    <el-table
      ref="dataTableRef"
      v-loading="productLoading"
      :data="productList"
      highlight-current-row
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
        <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productInformation')" min-width="160" show-overflow-tooltip>
            <template #default="scope">
                <div class="product-div">
                    <div class="product">
                        <div class="product-name">{{scope.row.productName}}</div>
                        <div class="product-code">
                            <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productCode')}}：</span>
                            <span class="product-value">{{scope.row.productCode}}</span>
                        </div>
                        <div class="product-code">
                            <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productSpec')}}：</span>
                            <span class="product-value">{{scope.row.productSpec}}</span>
                        </div>
                    </div>
                </div>
            </template>
        </el-table-column>
        <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productCategory')" prop="fullCategoryName" min-width="120" show-overflow-tooltip/>
        <el-table-column :label="$t('productDisassemblyAssembleOrder.label.outWarehouseArea')" prop="warehouseAreaName" min-width="120" show-overflow-tooltip>
            <template #default="scope">
                {{scope.row.warehouseAreaName}} | {{scope.row.warehouseAreaCode}}
            </template>
        </el-table-column>
        <el-table-column :label="$t('productDisassemblyAssembleOrder.label.totalStockQty')" prop="totalStockQty" show-overflow-tooltip align="right"/>
        <el-table-column :label="$t('productDisassemblyAssembleOrder.label.availableStockQty')" prop="availableStockQty" show-overflow-tooltip align="right"/>
    </el-table>
    <pagination
      class="pagination"
      v-if="productListTotal > 0"
      v-model:total="productListTotal"
      v-model:page="goodsQueryParams.page"
      v-model:limit="goodsQueryParams.limit"
      @pagination="queryGoodList"
    />

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">
          {{ $t("common.cancel") }}
        </el-button>
        <el-button
          type="primary"
          :disabled="!multipleSelection.length"
          @click="submitForm"
        >
          {{ $t("common.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import type { CascaderProps } from "element-plus";
import CommonAPI from "@/modules/wms/api/common";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();

const cascaderRef = ref();
const goodsQueryFormRef = ref(ElForm);
const productListTotal = ref(0);
const goodsQueryParams = reactive({
    hasTotalStockQty:true,
  page: 1,
  limit: 20,
  productName: "",
  category: [], // 商品分类
  firstCategoryIds: [],
  secondCategoryIds: [],
  thirdCategoryIds: [],
    warehouseAreaCode: "",
});

const categoryList = ref([]);

const productLoading = ref(false);
const productList = ref([]);
const multipleSelection = ref([]);
const propsCategory: CascaderProps = {
  multiple: true,
  checkStrictly: false,
  value: "id",
  label: "categoryName",
  children: "children",
};
const warehouseAreaOption= ref([])

const isVisible = computed({
  get: () => {
    queryManagerCategoryList();
      getWarehouseAreaList()
    return props.visible;
  },
  set: (val: any) => {},
});

function handleChange() {
  let valueArr = goodsQueryParams.category;
  let firstCategoryIds: any = [];
  let secondCategoryIds: any = [];
  let thirdCategoryIds: any = [];

  if (valueArr && valueArr.length > 0) {
    valueArr.forEach((item: any) => {
      if (item[0]) {
        firstCategoryIds.push(item[0]);
      }
      if (item[1]) {
        secondCategoryIds.push(item[1]);
      }
      if (item[2]) {
        thirdCategoryIds.push(item[2]);
      }
    });
  }

  goodsQueryParams.firstCategoryIds = Array.from(new Set(firstCategoryIds));
  goodsQueryParams.secondCategoryIds = Array.from(new Set(secondCategoryIds));
  goodsQueryParams.thirdCategoryIds = Array.from(new Set(thirdCategoryIds));
}

function queryManagerCategoryList(id?: any) {
  CommonAPI.queryCategoryTreeList({}).then((data: any) => {
    categoryList.value = data;
  });
}

function close() {
  emit("update:visible", false);
  reset();
}

function reset() {
    nextTick(()=>{
        goodsQueryFormRef.value?.clearValidate();
        goodsQueryFormRef.value?.resetFields();
    })
  goodsQueryParams.page = 1;
  goodsQueryParams.limit = 20;
  goodsQueryParams.productName = "";
  goodsQueryParams.category = [];
  goodsQueryParams.thirdCategoryIds = [];
  goodsQueryParams.firstCategoryIds = [];
  goodsQueryParams.secondCategoryIds = [];
  goodsQueryParams.warehouseAreaCode = '';

}

function handleSelectionChange(val: any) {
  multipleSelection.value = val;
}

function queryGoodList() {
  productLoading.value = true;
  let params: any = {
    ...goodsQueryParams,
  };
  delete params.category;
  CommonAPI.queryProductAll(params)
    .then((res: any) => {
      productList.value = res.records.map((item: any) => {
        item.productSpecs = item.productSpec;
        return { ...item };
      });
      productListTotal.value = parseInt(res.total) || 0;
    })
    .finally(() => {
      productLoading.value = false;
    });
}

function handleResetQuery() {
  reset()
  queryGoodList();
}

function submitForm() {
  emit("onSubmit", multipleSelection.value);
  close();
}


/**
 * 获取库区列表
 */
function getWarehouseAreaList() {
    let params: any = {
        status: 1,
    };

    CommonAPI.getOutWarehouseAreaList(params)
      .then((data) => {
          warehouseAreaOption.value=data || [];
      })
      .catch(() => {})
      .finally(() => {});
}

// 初始化
onMounted(() => {});

defineExpose({
  handleResetQuery,
  queryManagerCategoryList,
  queryGoodList,
});
</script>

<style scoped lang="scss">
.input_style {
  width: calc(100% - 180px);
}

.product-code {
  color: #90979e;
}

.product-name {
  color: #151719;
}

.selected-info {
  margin: 16px 0;
  font-weight: 400;
  font-size: 14px;
  color: #52585f;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  margin-bottom: 10px;

  .selected-count {
    color: #762adb;
  }
}

.pagination {
  height: 80px;
  overflow: hidden;
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.product-info {
  display: flex;
  align-items: center;

  img {
    width: 64px;
    height: 64px;
    border-radius: 4px;
  }

  .product-info-content {
    margin-left: 10px;

    .product-info-content-item {
      &.code {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #90979e;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      &.name {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #151719;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>
