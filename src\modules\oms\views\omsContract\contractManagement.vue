<template>
  <div class="app-container">
    <div class="contract-container">
      <el-card class="mb-12px search-card">
        <!-- Search Form -->
        <div class="search-form">
          <el-form :model="searchForm" :inline="true" label-width="84px">
            <el-form-item :label="t('omsContract.label.contractName')">
              <el-input
                v-model="searchForm.contractName"
                :placeholder="t('omsContract.label.contractName')"
                maxlength="50"
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item :label="t('omsContract.label.contractPartner')">
              <el-input
                v-model="searchForm.contractPartner"
                :placeholder="t('omsContract.label.contractPartner')"
                maxlength="50"
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item :label="t('omsContract.label.contractStatus')">
              <el-select
                v-model="searchForm.contractStatus"
                :placeholder="t('contract.button.search')"
                clearable
                class="!w-[256px]"
              >
                <el-option
                  v-for="option in contractStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="handleSearch"
                v-hasPerm="['oms:contract:page']"
              >
                {{ t("omsContract.button.search") }}
              </el-button>
              <el-button
                @click="handleReset"
                v-hasPerm="['oms:contract:reset']"
              >
                {{ t("omsContract.button.reset") }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="content-card">
        <!-- Action Button -->
        <div class="action-bar">
          <el-button
            type="primary"
            @click="handleAddContract"
            v-hasPerm="['oms:contract:add']"
          >
            {{ t("omsContract.button.add") }}
          </el-button>
        </div>

        <!-- Contract Table -->
        <el-table
          :data="contracts"
          border
          v-loading="loading"
        >
          <template #empty>
            <Empty />
          </template>
          <el-table-column
            prop="contractName"
            :label="t('contract.label.contractName')"
            min-width="160"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span
                v-if="hasAuth(['pms:contract:detail'])"
                @click="handleViewContract(row)"
                style="color:#762adb;cursor:pointer"
              >
                {{ row.contractName }}
              </span>
              <span v-else>
                {{ row.contractName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="contractPartner"
            :label="t('contract.label.contractPartner')"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            prop="contractCode"
            :label="t('contract.label.contractCode')"
            min-width="160"
            show-overflow-tooltip
          />
          <el-table-column
            prop="contractStatusName"
            :label="t('contract.label.contractStatus')"
            show-overflow-tooltip width="120"
          >
            <template #default="{ row }">
              <div
                class="contract status"
                :class="getStatusClass[row.contractStatus]"
                v-if="row.contractStatusName"
              >
                {{ row.contractStatusName }}
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="signType"
            :label="t('omsContract.label.signType')"
          >
            <template #default="{ row }">
              {{ getContractTypeLabel(row.signType) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="t('omsContract.label.contractPeriod')"
            width="185"
          >
            <template #default="{ row }">
              {{ parseTime(row.startDate, "{y}-{m}-{d}") }}
              {{ t("omsContract.label.rangeSeparator") }}
              {{ parseTime(row.endDate, "{y}-{m}-{d}") }}
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            :label="t('omsContract.label.createTime')"
            width="180"
          >
            <template #default="{ row }">
              {{ parseTime(row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
          <el-table-column prop="approveStatus" :label="t('omsContract.label.auditStatus')">
            <template #default="{ row }">
              <div v-if="row.approveStatus">
                <div>
                  <span style="color: #ff9c00" v-if="row.approveStatus === 1">{{ t('contract.label.pendingReview') }}</span>
                  <span style="color: #29b610" v-if="row.approveStatus === 2">{{ t('contract.label.agreed') }}</span>
                  <span style="color: #C00C1D" v-if="row.approveStatus === 3">{{ t('contract.label.rejected') }}</span>
                </div>
                <div style="color: #C00C1D;word-break: break-all" v-if="row.approveStatus === 3">{{row.approveRemark}}</div>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column :label="t('omsContract.button.operation')" fixed="right" width="120">
            <template #default="{ row }">
              <el-button
                v-if="showEditButton(row)"
                type="primary"
                link
                @click="handleEdit(row)"
                v-hasPerm="['oms:contract:edit']"
              >
                {{ t("omsContract.button.edit") }}
              </el-button>
              <el-button
                v-if="showVoidButton(row)"
                type="danger"
                link
                @click="handleVoid(row)"
                v-hasPerm="['oms:contract:cancel']"
              >
                {{ t("omsContract.button.void") }}
              </el-button>

              <el-button
                v-if="showDeleteButton(row)"
                type="danger"
                link
                @click="handleDelete(row)"
                v-hasPerm="['oms:contract:delete']"
              >
                {{ t("omsContract.button.delete") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[20, 50, 100, 200]"
            layout="total, sizes , prev, pager, next, jumper"
            @size-change="fetchContracts"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  getContractList,
  deleteContract,
  voidContract,
} from "@oms/api/contract";

import { parseTime } from "@/core/utils/index";
import { useI18n } from "vue-i18n"; // 导入国际化
import { title } from "process";
import { hasAuth } from "@/core/plugins/permission";

import { useNavigation } from "@/core/composables";

const { refreshAndNavigate } = useNavigation();

defineOptions({
  name: "ContractManagement",
  inheritAttrs: false,
});

// 使用国际化
const { t } = useI18n();

interface Contract {
  contractId: number;
  contractName: string;
  contractPartner: string;
  contractCode: string;
  contractStatus: number;
  contractType: number;
  signType: number;
  startDate: string;
  endDate: string;
  createTime: string;
  customerId: string;
  approveStatus: number;
  approveRemark: string;
}

const router = useRouter();

const contracts = ref<Contract[]>([]);
const searchForm = ref({
  contractName: "",
  contractPartner: "",
  contractStatus: "",
});

const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const loading = ref(false);

// 合同状态选项
const contractStatusOptions = [
  { label: t("omsContract.label.statusUnworking"), value: 0 },
  { label: t("omsContract.label.statusExecuting"), value: 1 },
  { label: t("omsContract.label.statusExpired"), value: 2 },
  { label: t("omsContract.label.statusCancelled"), value: 3 },
] as const;

const getStatusClass = {
  0: "unwokring",
  1: "executing",
  2: "dateexpired",
  3: "cancelled",
};
const getAuditStatusClass = {
  1: "pendingReview",//color: #ff9c00;
  2: "agreed",//color: #29b610;
  3: "rejected",//color: #C00C1D
};
// 合同签订类型选项
const contractTypeOptions = [
  { label: t("omsContract.label.typeSign"), value: 0 },
  { label: t("omsContract.label.typeRenew"), value: 1 },
] as const;

// 获取状态文本
const getStatusLabel = computed(() => (status: number) => {
  return (
    contractStatusOptions.find((option) => option.value === status)?.label ?? ""
  );
});

// 获取签订类型文本
const getContractTypeLabel = computed(() => (type: number) => {
  return (
    contractTypeOptions.find((option) => option.value === type)?.label ?? ""
  );
});

const fetchContracts = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm.value,
    };
    const data = await getContractList(params);
    contracts.value = data.records;
    total.value = parseInt(data.total);
  } catch (error) {
    console.error("获取合同列表失败:", error);
    ElMessage.error("获取合同列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchContracts();
};

const handleReset = () => {
  currentPage.value = 1;
  searchForm.value = {
    contractName: "",
    contractPartner: "",
    contractStatus: "",
  };
  fetchContracts();
};

const handleAddContract = () => {
  refreshAndNavigate({
    path: "/oms/omsContract/addContract",
    query: {
      title: t("omsContract.title.add"),
      type: "add",
    },
  });
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchContracts();
};

const handleViewContract = (row: Contract) => {
  router.push({
    path: "/oms/omsContract/contractDetail",
    query: { contractId: row.contractId },
  });
};

/* { label: "未生效", value: 0 },
{ label: "执行中", value: 1 },
{ label: "已到期", value: 2 },
{ label: "已作废", value: 3 }, */

// 按钮显示控制
const showEditButton = computed(() => (row: Contract) => {
  // 编辑条件：审核状态为已驳回
  return row.approveStatus === 3;
});

const showDeleteButton = computed(() => (row: Contract) => {
  // 删除条件：审核状态为已同意且合同状态为已到期/已作废
  return row.approveStatus === 2 && (row.contractStatus === 2 || row.contractStatus === 3);
});

const showVoidButton = computed(() => (row: Contract) => {
  // 作废条件：审核状态为已同意且合同状态为未生效/执行中
  return row.approveStatus === 2 && (row.contractStatus === 0 || row.contractStatus === 1);
});

const handleDelete = async (row: Contract) => {
  if (row.editAndDeleteFlag === 1) {
    ElMessage.warning(t("omsContract.message.supplierBindWarning"));
  } else {
    try {
      await ElMessageBox.confirm(
        t("omsContract.message.deleteConfirm", { name: row.contractName }),
        t("omsContract.message.tip"),
        {
          type: "warning",
        }
      );
      await deleteContract({ contractId: row.contractId });
      ElMessage.success(t("omsContract.message.deleteSuccess"));
      fetchContracts();
    } catch (error) {
      if (error !== "cancel") {
        console.error("删除合同失败:", error);
        ElMessage.error(t("omsContract.message.deleteFailed"));
      }
    }
  }
};

const handleVoid = async (row: Contract) => {
  try {
    await ElMessageBox.confirm(
      `<span style="word-break: break-all;"> ${t("omsContract.message.voidConfirm", { name: row.contractName })}</span>`,

      t("omsContract.message.tip"),
      {
        type: "warning",
        dangerouslyUseHTMLString: true, // 允许使用 HTML
      }
    );
    await voidContract({ contractId: row.contractId });
    ElMessage.success(t("omsContract.message.voidSuccess"));
    fetchContracts();
  } catch (error) {
    if (error !== "cancel") {
      console.error("作废合同失败:", error);
      ElMessage.error(t("omsContract.message.voidFailed"));
    }
  }
};

const handleEdit = (row: Contract) => {
  if (row.editAndDeleteFlag === 1) {
    ElMessage.warning(t("omsContract.message.supplierBindWarning"));
  } else {
    router.push({
      path: "/oms/omsContract/addContract",
      query: {
        title: t("omsContract.title.edit"),
        contractId: row.contractId,
        type: "edit",
      },
    });
  }
};

onActivated(() => {
  fetchContracts();
});
</script>

<style lang="scss" scoped>
:deep(.el-button--primary.el-button--default.is-link) {
  color: #762adb;
}
:deep(.el-button--danger.el-button--default.is-link) {
  color: #c00c1d;
}
.contract-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-card {
    flex-shrink: 0;
  }

  .content-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .action-bar {
      margin-bottom: 12px;
      flex-shrink: 0;
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.el-form-item--default) {
  // 表单项样式设置
  margin-bottom: 0;
}
</style>
