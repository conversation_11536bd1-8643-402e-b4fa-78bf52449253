<template>
    <div class="app-container">
        <div class="addPurchase">
            <div class="page-title">
                <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                <div>
                    <span v-if="type=='add'">{{ $t("purchase.button.addPurchaseMaterial") }}</span>
                    <span v-else>{{ $t("purchase.button.editPurchaseMaterial") }}</span>
                </div>
            </div>
            <div class="page-content">
                <el-form
                        :model="form"
                        :rules="rules"
                        ref="formRef"
                        label-width="82px"
                        label-position="right"
                >
                        <div class="title-lable">
                            <div class="title-content">
                                {{ $t("purchase.label.basicInformation") }}
                            </div>
                        </div>
                        <div>
                            <el-row class="flex-center-start">
                                <el-col :span="8">
                                    <el-form-item :label="$t('purchase.label.productCategory')" prop="productCategory">
<!--                                        <el-cascader v-model="form.productCategory"-->
<!--                                                     :props="props"-->
<!--                                                     @change="handleChange"-->
<!--                                                     ref="cascaderRef"-->
<!--                                                     filterable-->
<!--                                                     :placeholder="$t('purchase.placeholder.productCategory')"-->
<!--                                                     clearable />-->
                                        <el-cascader v-model="form.productCategory"
                                                     :options="categoryList"
                                                     :props="propsCategory"
                                                     @change="handleChange"
                                                     ref="cascaderRef"
                                                     filterable
                                                     :placeholder="$t('purchase.placeholder.productCategory')"
                                                     clearable />
                                    </el-form-item>
                                </el-col>
                                <!--<div class="mb18px ml20px">
                                    <el-button
                                            class="select-suppliers"
                                            type="primary"
                                            size="small"
                                            link
                                            @click="addCategory()"
                                    >
                                        {{$t('purchase.button.addCategory')}}
                                    </el-button>
                                </div>-->
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item
                                            :label="$t('purchase.label.productName')"
                                            prop="productName"
                                    >
                                        <el-input
                                                v-model="form.productName"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                clearable
                                                maxlength="120"
                                                :readonly="isEdit"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item
                                            :label="$t('purchase.label.isStandardCopy')"
                                            prop="isStandard"
                                    >
                                        <el-select
                                                v-model="form.isStandard"
                                                :placeholder="$t('common.placeholder.selectTips')"
                                                clearable
                                        >
                                            <el-option v-for="item in standardList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item
                                            :label="$t('purchase.label.conversionRelSecondUnitNameCopy')"
                                            prop="productUnitId"
                                    >
                                        <el-select
                                                v-model="form.productUnitId"
                                                :placeholder="$t('common.placeholder.selectTips')"
                                                clearable
                                        >
                                            <el-option v-for="item in unitList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8" class="flex-center-start">
                                    <el-form-item
                                            :label="$t('purchase.label.changeRelationship')"
                                            prop="conversionRelFirstNum"
                                    >
                                        <el-input
                                                v-model="form.conversionRelFirstNum"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                clearable
                                                class="!w-[62px]"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                            label-width="0px"
                                            prop="productUnitId"
                                    >
                                        <el-select
                                                v-model="form.productUnitId"
                                                :placeholder="$t('purchase.label.conversionRelSecondUnitNameCopy')"
                                                clearable
                                                disabled
                                                class="!w-[100px]"
                                        >
                                            <el-option v-for="item in unitList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <div class="equal">=</div>
                                    <el-form-item
                                            label-width="0px"
                                            prop="conversionRelSecondNum"
                                    >
                                        <el-input
                                                v-model="form.conversionRelSecondNum"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                clearable
                                                class="!w-[62px]"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                            label-width="0px"
                                            prop="conversionRelSecondUnitId"
                                    >
                                        <el-select
                                                v-model="form.conversionRelSecondUnitId"
                                                :placeholder="$t('purchase.label.conversionRelSecondUnitName')"
                                                clearable
                                                class="!w-[100px]"
                                        >
                                            <el-option v-for="item in unitList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8" class="flex-center-start">
                                    <el-form-item
                                            :label="$t('purchase.label.productlwhv')"
                                            prop="length"
                                    >
                                        <el-input
                                                v-model="form.length"
                                                :placeholder="$t('purchase.label.length')"
                                                clearable
                                                class="!w-[78px]"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                            label-width="0px"
                                            prop="width"
                                    >
                                        <el-input
                                                v-model="form.width"
                                                :placeholder="$t('purchase.label.width')"
                                                clearable
                                                class="!w-[78px]"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                            label-width="0px"
                                            prop="height"
                                    >
                                        <el-input
                                                v-model="form.height"
                                                :placeholder="$t('purchase.label.height')"
                                                clearable
                                                class="!w-[78px]"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                            label-width="0px"
                                            prop="volume"
                                    >
                                        <el-input
                                                v-model="form.volume"
                                                :placeholder="$t('purchase.label.volume')"
                                                clearable
                                                class="!w-[78px]"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item
                                            :label="$t('purchase.label.weight')"
                                            prop="weight"
                                    >
                                        <el-input
                                                v-model="form.weight"
                                                :placeholder="$t('purchase.placeholder.weight')"
                                                clearable
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item
                                            :label="$t('purchase.label.brand')"
                                            prop="productBrandId"
                                    >
                                        <el-select
                                                v-model="form.productBrandId"
                                                :placeholder="$t('common.placeholder.selectTips')"
                                                clearable
                                        >
                                            <el-option v-for="item in brandList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8" class="flex-center-start">
                                    <el-form-item
                                            :label="$t('purchase.label.shelfLife')"
                                            prop="shelfLife"
                                    >
                                        <el-input
                                                v-model="form.shelfLife"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                clearable
                                                maxLength="50"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                            label-width="0px"
                                            prop="shelfLifeUnit"
                                    >
                                        <el-select
                                                v-model="form.shelfLifeUnit"
                                                :placeholder="$t('common.placeholder.selectTips')"
                                                class="!w-[100px]"
                                        >
                                            <el-option v-for="item in shelfLifeUnitList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item
                                            :label="$t('purchase.label.lossRatio')"
                                            prop="lossRatio"
                                    >
                                        <el-input
                                                v-model="form.lossRatio"
                                                :placeholder="$t('purchase.placeholder.lossRatio')"
                                                maxLength="50"
                                                clearable
                                        >
                                            <template #append>%</template>
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item
                                            :label="$t('purchase.label.storageCondition')"
                                            prop="storageCondition"
                                    >
                                        <el-input
                                                v-model="form.storageCondition"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                maxlength="50"
                                                clearable
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item
                                            :label="$t('purchase.label.remark')"
                                            prop="remark"
                                    >
                                        <el-input
                                                :rows="4"
                                                type="textarea"
                                                show-word-limit
                                                v-model="form.remark"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                maxlength="100"
                                                clearable
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                        <div class="title-lable">
                            <div class="title-content">
                                {{ $t("purchase.label.graphicInformation") }}
                            </div>
                        </div>
                        <div>
                            <el-form-item
                                    :label="$t('purchase.label.imagesUrls')"
                                    prop="imagesUrls"
                            >
                                <upload-multiple
                                        :tips="`图片比例1:1`"
                                        :isPrivate="`public-read`"
                                        :modelValue="form.imagesUrls"
                                        @update:model-value="onChangeMultiple"
                                        ref="detailPicsRef"
                                        :limit="6"
                                        :formRef="formUpdateRef"
                                        class="modify-multipleUpload"
                                        name="detailPic">
                                    <template #default="{ file }">
                                        点击上传
                                    </template>
                                </upload-multiple>
                            </el-form-item>
                        </div>
                        <div class="title-lable"  style="justify-content:space-between;">
                            <div class="title-content">
                                {{ $t("purchase.label.supplyChainInformation") }}
                            </div>
                            <div class="button-add cursor-pointer" @click="setSuppliers()">
                                {{$t('purchase.button.selectSuppliers')}}
                            </div>
                        </div>
                        <div>
                            <el-table
                                    v-loading="loading"
                                    :data="form.supplierList"
                                    highlight-current-row
                                    stripe
                            >
                                <el-table-column :label="$t('purchase.label.supplierName')" min-width="150">
                                    <template #default="scope">
                                        <el-tooltip
                                                :content="scope.row.supplierName"
                                                placement="top"
                                                effect="dark"
                                        >
                                            <span>{{ scope.row.supplierName }}</span>
                                        </el-tooltip>
                                        <span v-if="scope.row.isDefault==1" class="default-supplier">{{$t('purchase.label.defaults')}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column :label="$t('purchase.label.supplierCode')" prop="supplierCode" show-overflow-tooltip/>
                                <el-table-column :label="$t('purchase.label.supplierWarehouseName')" prop="supplierWarehouseName" show-overflow-tooltip/>
                                <el-table-column fixed="right" :label="$t('common.handle')" width="160">
                                    <template #default="scope">
                                        <el-button
                                                v-if="scope.row.isDefault==0"
                                                type="primary"
                                                size="small"
                                                link
                                                @click="defaultSet(scope.$index)"
                                        >
                                            {{$t('purchase.button.setDefaultSuppliers')}}
                                        </el-button>
                                        <el-button
                                                type="danger"
                                                size="small"
                                                link
                                                @click="handleDelete(scope.$index)"
                                        >
                                            {{$t('common.delete')}}
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <div class="title-lable">
                            <div class="title-content">
                                {{ $t("purchase.label.productStatusInformation") }}
                            </div>
                        </div>
                        <div>
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item :label="$t('purchase.label.statusCopy')" prop="status">
                                        <el-select
                                                v-model="form.status"
                                                :placeholder="$t('common.placeholder.selectTips')"
                                                clearable
                                                class="!w-[256px]"
                                        >
                                            <el-option v-for="item in statusList" :key="item.statusId" :label="item.statusName" :value="item.statusId"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                </el-form>
            </div>
            <div class="page-footer">
                <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
                <el-button type="primary" :loading="submitLoading" @click="handleSubmit">{{ $t("common.confirm") }}</el-button>
            </div>
            <SetSupplier
                    ref="setSuppliersRef"
                    v-model:visible="dialog.visible"
                    :title="dialog.title"
                    @onSubmit="onSubmit"
            />
          <!--  <AddCategory
                    ref="addCategoryRef"
                    v-model:visible="dialog.visible"
                    :title="dialog.title"
                    @onSubmit="onSubmitCategory"
            />-->
        </div>
    </div>
</template>

<script setup lang="ts">

    defineOptions({
        name: "AddPurchase",
        inheritAttrs: false,
    });

    import type { CascaderProps } from 'element-plus';
    import productCategoryAPI from "@/modules/pms/api/productCategory";
    // import CategoryAPI from "@/modules/pms/api/category";
    import productBrandAPI from "@/modules/pms/api/productBrand";
    import unitAPI from "@/modules/pms/api/unit";
    import PurchaseAPI,{PurchaseFrom} from "@/modules/pms/api/purchase";
    import SetSupplier from "./components/setSupplier.vue";
    // import AddCategory from "./components/addCategory.vue";
    import supplierAPI from "@/modules/pms/api/supplier";
    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";

    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const submitLoading = ref(false)
    const formRef = ref(ElForm);
    const loading = ref(false);
    const id = route.query.id;
    const type = route.query.type;
    const setSuppliersRef = ref();
    // const addCategoryRef = ref();
    const dialog = reactive({
        title: "",
        visible: false,
    });
    const formUpdateRef = ref(null);
    const cascaderRef = ref();
  /*  const props: CascaderProps = {
        lazy: true,
        async lazyLoad(node, resolve) {
            const {level,data} = node
            let arr: any = []
            if (level == 0) {
                arr = await queryManagerCategoryList(null)
            } else{
                arr =  await queryManagerCategoryList(data.value)
            }
            const nodes = arr.map((item) => ({
                value: item.id,
                label: item.categoryName,
                parentId: item.parentId,
                leaf: level >= 2,
            }))
            console.log(nodes)
            resolve(nodes)
        },
    }*/
    const categoryList = ref([])
    const propsCategory: CascaderProps = {
        checkStrictly : false,
        value: 'id',
        label: 'categoryName',
        children: 'children',
    }
    const standardList = ref([
        {
            key: 1,
            value: t('common.statusYesOrNo.yes')
        },
        {
            key: 0,
            value:t('common.statusYesOrNo.no')
        }
    ])
    const unitList = ref([])
    const brandList = ref([])
    const shelfLifeUnitList = ref([
        {
            key: 1,
            value: t('purchase.shelfLifeUnitList.day')
        },
        {
            key: 2,
            value:t('purchase.shelfLifeUnitList.month')
        },
        {
            key: 3,
            value:t('purchase.shelfLifeUnitList.year')
        }
    ])
    const statusList = ref([
        {
            statusId: 1,
            statusName: t('purchase.statusList.haveBeenPutOnShelves')
        },
        {
            statusId: 2,
            statusName:t('purchase.statusList.haveBeenGetOffShelves')
        }
    ])
    // 角色表单
    let form = reactive<PurchaseFrom>({
        imagesUrls:[],
        conversionRelFirstNum:1,
        shelfLifeUnit:2
    });

    const rules = reactive({
        productCategory: [{ required: true, message: t("purchase.rules.productCategory"), trigger:  ["change", "blur"] }],
        productName: [
            { required: true, message: t("purchase.rules.productName"), trigger: "blur" },
            { min: 2, max: 120, message: t("purchase.rules.productNameFormat"), trigger: "blur" }
        ],
        isStandard: [{ required: true, message: t("purchase.rules.isStandard"), trigger: ["change", "blur"] }],
        productUnitId: [{ required: true, message: t("purchase.rules.productUnitId"), trigger: ["change", "blur"] }],
        conversionRelFirstNum: [
            { required: true, message: t("purchase.rules.conversionRelFirstNum"), trigger:  "blur" },
            {pattern: /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/, message: t("purchase.rules.conversionRelFirstNumFormat"), trigger: "blur"}
        ],
        conversionRelSecondNum: [
            { required: true, message: t("purchase.rules.conversionRelSecondNum"), trigger: "blur"},
            {pattern: /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/, message: t("purchase.rules.conversionRelSecondNumFormat"), trigger: "blur"},
        ],
        conversionRelSecondUnitId: [{ required: true, message: t("purchase.rules.conversionRelSecondUnitId"), trigger: ["change", "blur"] }],
        length: [{pattern: /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/, message:  t("purchase.rules.lengthFormat"), trigger: "blur"}],
        width: [{pattern: /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/, message:  t("purchase.rules.widthFormat"), trigger: "blur"}],
        height: [{pattern: /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/, message:  t("purchase.rules.heightFormat"), trigger: "blur"}],
        volume: [{pattern: /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/, message:  t("purchase.rules.volumeFormat"), trigger: "blur"}],
        weight: [{pattern: /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/, message:  t("purchase.rules.weightFormat"), trigger: "blur"}],
        // weight: [{pattern: /(^0(\.\d{1,2})?$)|(^[1-9]\d{0,13}(\.\d{1,2})?$)/, message:  t("purchase.rules.weightFormat"), trigger: "blur"}],
        lossRatio: [
            { required: true, message: t("purchase.rules.lossRatio"), trigger: ["change", "blur"] },
            {pattern: /^(([1-9]?\d{0,1}(\.\d{1,2})?)|100|100\.(0){0,2})$/, message: t("purchase.rules.lossRatioFormat"), trigger: "blur"}
            // {pattern: /^(0|100|100.0|100.00)$|^(0\.[1-9]|0\.[0-9][1-9]|0\.[1-9][0]|[1-9]|[1-9][0-9])(\.\d{1,2})?$/, message: t("roleManagement.rules.lossRatioFormat"), trigger: "blur" }
        ],
        shelfLife: [
            {pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{0,50}$/, message: t("purchase.rules.shelfLifeFormat"), trigger: "blur"}
        ],
        storageCondition: [
            {pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{0,50}$/, message: t("purchase.rules.storageConditionFormat"), trigger: "blur"}
        ],
        status: [{ required: true, message: t("purchase.rules.status"), trigger:  ["change", "blur"] }],
        imagesUrls: [{ required: true, message: t("purchase.rules.imagesUrls"), trigger:  ["change", "blur"] }],
    });

    function onChangeMultiple(val) {
        form.imagesUrls=val?val:''
        form.mainImageUrl=val ?val[0].fileName:''
        if(form.imagesUrls && form.imagesUrls.length>0){
            formRef.value.clearValidate('imagesUrls') //清除图片校验文字
        }
        console.info('==form.imagesUrls=='+form)
    }

    /** 查询商品分类列表 */
    function queryManagerCategoryList(id?:any) {
       /* return new Promise((resolve, reject) => {
            let params={}
            if(id){
                params.id=id
            }
            CategoryAPI.queryManagerCategoryList(params).then((data) => {
                resolve(data);
            }).catch((error) => {
                reject(error);
            })
        });*/
        productCategoryAPI.queryCategoryTreeList({}).then((data: any) => {
            categoryList.value = data;
        })
    }

    /** 查询单位列表 */
    function getUnitListList() {
        unitAPI.getUnitList()
            .then((data) => {
                unitList.value = data;
            })
    }

    /** 查询品牌列表 */
    function queryBrandList() {
        productBrandAPI.queryBrandList()
            .then((data) => {
                brandList.value = data
            })
    }

    /** 添加分类 */
    function addCategory() {
        dialog.title = t('product.button.addCategory');
        dialog.visible = true;
    }

    /** 假选择供应商弹窗 */
    function setSuppliers() {
        dialog.title = t('purchase.title.selectSuppliers');
        // setSuppliersRef.value.querySupplierAll();
        setSuppliersRef.value.setFormData({supplierType:4,productIds:[],supplierList:[]});
        dialog.visible = true;
    }

    function handleChange(){
        if(cascaderRef.value.getCheckedNodes()){
            let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues
            form.firstCategoryId = valueArr[0];
            form.secondCategoryId = valueArr[1];
            form.thirdCategoryId = valueArr[2];
            form.productCategory = valueArr;
        }
    }

  /*  function onSubmitCategory() {
      //刷新分类下拉列表
    }
*/
    function onSubmit(data) {
        if(data.length>0){
            data.forEach((item)=>{
                item.isDefault=0
            })
        }
        if(form.supplierList && form.supplierList.length>0){
            let arr = data.concat(form.supplierList);
            let uniqueArr =[...new Map(arr.map(item => [item.supplierCode, item])).values()];
            form.supplierList=uniqueArr;
        }else{
            form.supplierList = data
        }
        console.log("===supplierTableSelect==="+form.supplierList);
    }

    /** 假删除供应商 */
    function handleDelete(index?: number) {
        // ElMessageBox.confirm(t('purchase.message.deleteSupplierTips'), t('common.tipTitle'), {
        //     confirmButtonText: t('common.confirm'),
        //     cancelButtonText: t('common.cancel'),
        //     type: "warning",
        // }).then(
        //     () => {
                loading.value = true;
                form.supplierList.splice(index, 1);
                ElMessage.success(t('purchase.message.deleteSucess'));
                loading.value = false
          /*  },
            () => {
                ElMessage.info(t('purchase.message.deleteConcel'));
            }
        );*/
    }

    /** 假设置默认供应商 */
    function defaultSet(index?: number) {
        // ElMessageBox.confirm(t('purchase.message.deleteSupplierTips'), t('common.tipTitle'), {
        //     confirmButtonText: t('common.confirm'),
        //     cancelButtonText: t('common.cancel'),
        //     type: "warning",
        // }).then(
        //     () => {
                loading.value = true;
                const i = form.supplierList.findIndex(item => item.isDefault == 1);
                if(i>=0){
                    form.supplierList[i].isDefault=0
                }
                form.supplierList[index].isDefault=1
                ElMessage.success(t('purchase.message.setDefaultSuppliersSucess'));
                loading.value = false
                console.log("===form.supplierList==="+form.supplierList);
          /*  },
            () => {
                ElMessage.info(t('purchase.message.deleteConcel'));
            }
        );*/
    }

    async function handleClose(){
        await tagsViewStore.delView(route);
        router.go(-1);
    }

    function handleSubmit(){
        formRef.value.validate((valid) => {
            if (!valid) return;
            submitLoading.value=true
            form.imagesUrls = JSON.stringify(form.imagesUrls)
            let params = {
                ...form,
            }
            delete params.productCategory
            console.info(params)
            if(type=='add'){
                delete params.id
                PurchaseAPI.addPurchase(params)
                    .then((data) => {
                        ElMessage.success(t('purchase.message.addSucess'));
                        handleClose()
                    }).finally(() => {
                        submitLoading.value=false
                    })
            }else{
                PurchaseAPI.updatePurchase(params)
                    .then((data) => {
                        ElMessage.success(t('purchase.message.editSucess'));
                        handleClose()
                    }).finally(() => {
                        submitLoading.value=false
                    })
            }
        });
    }

    /** 查询商品详情列表 */
    function queryPurchaseDetail(){
        loading.value = true;
        let params = {
            id:id
        }
        PurchaseAPI.queryPurchaseDetail(params)
            .then((data) => {
                Object.assign(form, data);
                form.productCategory = [form.firstCategoryId,form.secondCategoryId,form.thirdCategoryId];
                if(form.imagesUrls && typeof form.imagesUrls ==='string'){
                    form.imagesUrls =JSON.parse(form.imagesUrls);
                }
            })
            .finally(() => {
                loading.value = false;
            });
    }

    onMounted(() => {
        getUnitListList();
        queryBrandList();
        queryManagerCategoryList();
        if(type=='edit'){
            queryPurchaseDetail();
        }
    });
</script>
<style scoped lang="scss">
    .addPurchase {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .equal{
                height: 32px;
                line-height: 32px;
                padding: 0px 8px;
                margin-bottom: 18px;
            }
            .button-add{
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                color: var(--el-color-primary)
            }
            .default-supplier{
                margin-left: 8px;
                padding: 2px 7px;
                background: #FE8200;
                border-radius: 4px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #FFFFFF;
                line-height: 16px;
                text-align: left;
                font-style: normal;
            }
        }
    }
</style>
