<template>
  <div class="app-container">
    <div class="addPage">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon>
            <Back />
          </el-icon>
          {{ $t("warehouseEntry.title.addTitle") }}
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" ref="formRef" label-width="112px" label-position="right">
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("warehouseEntry.label.basicInformation") }}
            </div>
          </div>
          <div class="tradingAndLogistics">
            <el-row>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntry.label.orderType')" prop="orderType">
                  <el-select v-model="form.orderType" :placeholder="$t('common.placeholder.selectTips')"
                    style="width: 220px">
                    <el-option :label="$t('warehouseEntry.label.receiptNoticeEntry')" :value="3" />
                    <el-option :label="$t('warehouseEntry.label.sortingOrderEntry')" :value="1" />
                  </el-select>
                </el-form-item></el-col>
              <el-col :span="6">
                <el-form-item :label="'状态'">
                  <span v-if="form.status == 0">初始</span>
                  <span v-else-if="form.status == 1">部分入库</span>
                  <span v-else-if="form.status == 2">全部入库</span>
                  <span v-else-if="form.status == 3">入库中</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="'是否领单'">
                  <span v-if="form.receivingStatus == 0">未领用</span>
                  <span v-else-if="form.receivingStatus == 1">已领用</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="'领单人'">
                  {{ form.receivingPerson ? form.receivingPerson : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="'领单时间'">
                  <span v-if="form.receivingTime">
                    {{ parseTime(form.receivingTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" align="right">
                <el-button type="primary" key="primary" text @click="handleGoodsSelect">
                  {{ $t("warehouseEntry.button.goodsAdd") }}
                </el-button>
              </el-col>
            </el-row>
          
            <div class="title">
              <div class="flex_style">
                <div>
                  {{ $t("warehouseEntry.label.orderDetails") }}
                </div>
              </div>
            </div>

            <!-- 按收运单入库 -->
            <el-row v-if="form.orderType == 3">
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0" prop="warehouseIds">
                  <el-table :data="form.productList" v-loading="formLoading" stripe highlight-current-row
                    v-if="form.productList && form.productList.length > 0">
                    <el-table-column type="index" :label="$t('common.sort')" width="60" align="center" fixed="left" />
                    <!-- 商品信息 -->
                    <el-table-column :label="$t('warehouseEntry.label.goodsInfor')" min-width="180"
                      show-overflow-tooltip fixed="left">
                      <template #default="scope">
                        <div class="product-code">
                          {{ $t("warehouseEntry.label.productCode") }}：
                          {{ scope.row.productCode }}
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 入库通知单 -->
                    <el-table-column :label="$t('warehouseEntry.label.receiptNoticeCode')" prop="receiptNoticeCode"
                      show-overflow-tooltip width="150" />
                    <!-- 收运单 -->
                    <el-table-column :label="$t('warehouseEntry.label.receivingOrderCode')" prop="receivingOrderCode"
                      show-overflow-tooltip width="160" />
                    <!-- 规格 -->
                    <el-table-column :label="$t('warehouseEntry.label.productSpecs')" prop="productSpecs"
                      show-overflow-tooltip />
                    <!-- 收运数量 -->
                    <el-table-column :label="$t('warehouseEntry.label.actualQuantityCopy')" prop="productActualQty"
                      show-overflow-tooltip align="right" />
                    <!-- 收运重量 -->
                    <el-table-column :label="$t('warehouseEntry.label.actualWeightCopy')" prop="productActualWeight"
                      show-overflow-tooltip align="right" />
                    <!-- 已入库数量 -->
                    <el-table-column :label="$t('warehouseEntry.label.productInventoryQty')" prop="productInventoryQty"
                      show-overflow-tooltip width="110" align="right" />
                    <!-- 库区 -->
                    <el-table-column :label="'*' + $t('warehouseEntry.label.warehouseAreaName')
                      " show-overflow-tooltip width="230">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.warehouseAreaCode'
                          " :rules="goodsDialog.visible
                            ? []
                            : [
                              {
                                required: true,
                                message: t(
                                  'warehouseEntry.rules.warehouseAreaCode'
                                ),
                                trigger: 'change',
                              },
                            ]
                            ">
                          <el-select v-model="scope.row.warehouseAreaCode"
                            :placeholder="$t('common.placeholder.selectTips')"
                            @change="handleWarehouseArea($event, scope.$index)" clearable>
                            <el-option v-for="item in warehouseAreaList" :key="item.areaCode"
                              :label="`${item.areaName} | ${item.areaCode}`" :value="item.areaCode" />
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 本次入库数量 提交时覆盖    productInventoryQty后删除 【productActualQty】-->
                    <el-table-column width="300" :label="'*' + $t('warehouseEntry.label.productInventoryQtyCopy')
                      " show-overflow-tooltip>
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'productList.' +
                          scope.$index +
                          '.productInventoryQtyCopy'
                          " :rules="goodsDialog.visible
                            ? []
                            : [
                              {
                                required: true,
                                message: t(
                                  'warehouseEntry.rules.productInventoryQtyCopy'
                                ),
                                trigger: 'blur',
                              },
                              {
                                pattern:
                                  /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                message: t(
                                  'warehouseEntry.rules.productInventoryQtyCopyFomart'
                                ),
                                trigger: 'blur',
                              },
                              {
                                required: true,
                                validator: validateQty,
                                trigger: 'blur',
                              },
                            ]
                            ">
                          <el-input v-model="scope.row.productInventoryQtyCopy"
                            :placeholder="$t('common.placeholder.inputTips')" clearable>
                            <template #append>
                              {{ scope.row.productUnitName }}
                            </template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 入库重量 productActualWeight-->
                    <el-table-column width="300" :label="'*' + '入库重量(Kg)'
                      " show-overflow-tooltip>
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'productList.' +
                          scope.$index +
                          '.productInventoryQtyCopy'
                          " :rules="goodsDialog.visible
                            ? []
                            : [
                              {
                                required: true,
                                message: t(
                                  'warehouseEntry.rules.productInventoryQtyCopy'
                                ),
                                trigger: 'blur',
                              },
                              {
                                pattern:
                                  /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                message: t(
                                  'warehouseEntry.rules.productInventoryQtyCopyFomart'
                                ),
                                trigger: 'blur',
                              },
                              {
                                required: true,
                                validator: validateQty,
                                trigger: 'blur',
                              },
                            ]
                            ">
                          <el-input v-model="scope.row.productActualWeight"
                            :placeholder="$t('common.placeholder.inputTips')" clearable>
                            <!--  <template #append>
                              {{ scope.row.productUnitName }}
                            </template> -->
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 入库类型 -->
                    <el-table-column :label="$t('warehouseEntry.label.receiptType')" width="130" show-overflow-tooltip>
                      <template #default="scope">
                        <!-- 入库类型:1:采购入库、2:退货入库 -->
                        <span v-if="scope.row.receiptType == 1">
                          {{ $t("warehouseEntry.label.purchaseInventory") }}
                        </span>
                        <span v-else-if="scope.row.receiptType == 2">
                          {{ $t("warehouseEntry.label.returnStorage") }}
                        </span>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')" fixed="right">
                      <template #default="scope">
                        <el-button type="danger" size="small" link @click="handleDelete(scope.$index)">
                          {{ $t("common.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 按分拣单入库 -->
            <el-row v-if="form.orderType == 1">
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0" prop="warehouseIds">
                  <el-table :data="form.productList" v-loading="formLoading" stripe highlight-current-row
                    v-if="form.productList && form.productList.length > 0">
                    <el-table-column type="index" :label="$t('common.sort')" width="60" align="center" fixed="left" />
                    <!-- 分拣单号 -->
                    <!-- 商品信息 -->
                    <el-table-column :label="$t('warehouseEntry.label.goodsInfor')" min-width="180"
                      show-overflow-tooltip fixed="left">
                      <template #default="scope">
                        <div class="product-code">
                          {{ $t("warehouseEntry.label.productCode") }}：
                          {{ scope.row.productCode }}
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('warehouseEntry.label.sortingOrderCode')" prop="sortingCode"
                      show-overflow-tooltip width="160" />
                    <!-- 规格 -->
                    <el-table-column :label="$t('warehouseEntry.label.productSpecs')" prop="productSpecs"
                      show-overflow-tooltip width="100" />
                    <el-table-column label="单位" prop="productUnit" show-overflow-tooltip width="100" />
                    <el-table-column label="商品数量" prop="inWarehouseQty" show-overflow-tooltip width="100" />
                    <el-table-column label="商品重量(Kg)" prop="productWeight" show-overflow-tooltip width="150" />
                    <!-- 库区 -->
                    <el-table-column :label="'*' + '入库库区'
                      " show-overflow-tooltip width="230">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.warehouseAreaCode'
                          " :rules="goodsDialog.visible
                            ? []
                            : [
                              {
                                required: true,
                                message: t(
                                  'warehouseEntry.rules.warehouseAreaCode'
                                ),
                                trigger: 'change',
                              },
                            ]
                            ">
                          <el-select v-model="scope.row.warehouseAreaCode"
                            :placeholder="$t('common.placeholder.selectTips')"
                            @change="handleWarehouseArea($event, scope.$index)" clearable>
                            <el-option v-for="item in warehouseAreaList" :key="item.areaCode"
                              :label="`${item.areaName} | ${item.areaCode}`" :value="item.areaCode" />
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 本次入库数量 提交时覆盖productInventoryQty后删除-->
                    <el-table-column width="300" :label="'*' + '入库数量'
                      " show-overflow-tooltip>
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" 
                        :prop="'productList.' +scope.$index +'.productActualQty'" :rules="goodsDialog.visible
                            ? []
                            : [
                              {
                                required: true,
                                message: t(
                                  'warehouseEntry.rules.productInventoryQtyCopy'
                                ),
                                trigger: 'blur',
                              },
                              {
                                pattern:
                                  /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                message: t(
                                  'warehouseEntry.rules.productInventoryQtyCopyFomart'
                                ),
                                trigger: 'blur',
                              }
                            ]
                            ">
                          <el-input v-model="scope.row.productActualQty"
                            :placeholder="$t('common.placeholder.inputTips')" clearable>
                            <template #append>
                              {{ scope.row.productUnit }}
                            </template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column width="300" :label="'*' + '入库重量(Kg)'
                      " show-overflow-tooltip>
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'productList.' +
                          scope.$index +
                          '.productActualWeight'
                          " :rules="goodsDialog.visible
                            ? []
                            : [
                              {
                                required: true,
                                message: t(
                                  'warehouseEntry.rules.productInventoryQtyCopy'
                                ),
                                trigger: 'blur',
                              },
                              {
                                pattern:
                                  /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                message: t(
                                  'warehouseEntry.rules.productInventoryQtyCopyFomart'
                                ),
                                trigger: 'blur',
                              },
                            ]
                            ">
                          <el-input v-model="scope.row.productActualWeight"
                            :placeholder="$t('common.placeholder.inputTips')" clearable>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>

                    <el-table-column :label="$t('common.handle')" fixed="right">
                      <template #default="scope">
                        <el-button type="danger" size="small" link @click="handleDelete(scope.$index)">
                          {{ $t("common.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("warehouseEntryNotice.button.cancel") }}
          </el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{ $t("warehouseEntry.button.confirmsubmit") }}
          </el-button>
        </div>
      </div>
    </div>
    <!-- 收运单商品选择 -->
    <AddGoods v-if="form.orderType == 3" ref="addGoodsRef" v-model:visible="goodsDialog.visible" @on-submit="onSubmitGoods" />
    <!-- 分拣单商品选择 -->
    <pickingListGoods v-if="form.orderType == 1" ref="addGoodsRef" v-model:visible="goodsDialog.visible" @on-submit="onSubmitGoods" />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "AddWarehouseEntryNotice",
  inheritAttrs: false,
});
import AddGoods from "./components/goods.vue";
import pickingListGoods from "./components/pickingListGoods.vue";
import warehouseEntryAPI, {
  addFormData,
} from "@/modules/wms/api/warehouseEntry";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const type = route.query.type;

const isEdit = route.query.isEdit;
const addGoodsRef = ref();
const formRef = ref(ElForm);

const loading = ref(false);
const formLoading = ref(false);

const warehouseAreaList = ref([]);

const goodsDialog = reactive({
  visible: false,
});

// 角色表单
const form = reactive<addFormData>({
  productList: [],
  orderType: null, // 默认选择按分拣单入库
});

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.push({
    path: "/wms/storeManagement/warehousingEntry",
  });
};

function handleGoodsSelect() {
  goodsDialog.visible = true;
  addGoodsRef.value.queryGoodList();

  
  addGoodsRef.value.getCategoryList();
}
function handleDelete(index: any) {
  form.productList.splice(index, 1);
}

function onSubmitGoods(data: any) {
  let arr = data.concat(form.productList);
  let uniqueArr = [
    ...new Map(
      arr.map((item: any) => [
        `${item.productCode}-${item.receivingOrderCode}`,
        item,
      ])
    ).values(),
  ];
  form.productList = uniqueArr;
  console.log(form.productList);
}

function getListByCurrentWarehouse() {
  warehouseEntryAPI
    .queryListByCurrentWarehouse({ status: 1 })
    .then((data: any) => {
      warehouseAreaList.value = data;
    })
    .finally(() => { });
}

function handleWarehouseArea(event: any, index: number) {
  warehouseAreaList.value.forEach((item: any) => {
    if (item.areaCode == event) {
      form.productList[index].warehouseAreaName = item.areaName;
    }
  });
}

function validateQty(rule: any, value: any, callback: any) {
  console.log(rule);
  let index = rule.field?.split(".")[1];
  console.log(index);
  let totalProductActualQty = form.productList[index].productActualQty
    ? form.productList[index].productActualQty
    : 0; //收运数量
  let totalProductInventoryQty = form.productList[index].productInventoryQty
    ? form.productList[index].productInventoryQty
    : 0; //已入库数量
  let total =
    parseFloat(totalProductActualQty) - parseFloat(totalProductInventoryQty);
  if (value && total) {
    if (value > total) {
      callback(new Error(t("warehouseEntry.rules.productInventoryQtyNum")));
    } else {
      callback();
    }
  }
}

// 提交
function handleSubmit() {
  if (form.productList && form.productList.length == 0) {
    return ElMessage.error(t("warehouseEntry.message.addOrEditGoodsTips"));
  }
  formRef.value.validate((valid: any) => {
    if (!valid) return;

    ElMessageBox.confirm(
      `${t("warehouseEntry.message.warehousingEntryTips")}`,
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }
    ).then(
      () => {
        loading.value = true;
        let params: any = {
          id: route.query.id,
          productList: [],
          orderType: form.orderType
        };
        if (form.productList && form.productList.length > 0) {
          if(form.orderType == 1){
            params.productList = form.productList.map((item: any) => {
            return {
              ...item,
              sourceOrderProductId: item.id
            };
          });
          }

          else if(form.orderType == 3){
            params.productList = form.productList.map((item: any) => {
            return {
              ...item,
              productInventoryQty: item.productInventoryQtyCopy,
              sourceOrderProductId: item.id
            };
          });
          }
         
        }
        warehouseEntryAPI
          .addWarehouseOrder(params)
          .then((data: any) => {
            ElMessage.success(t("warehouseEntry.message.warehouseEntrySucess"));
            loading.value = false;
            handleCancel();
          })
          .finally(() => {
            loading.value = false;
          });
      },
      () => {
        ElMessage.info(t("warehouseEntry.message.cancelTip"));
      }
    );
  });
}

function queryDetail() {
  formLoading.value = true;
  let params = {
    id: route.query.id,
  };
  warehouseEntryAPI
    .queryDetail(params)
    .then((data: any) => {
      Object.assign(form, data);
    })
    .finally(() => {
      formLoading.value = false;
    });
}

onMounted(() => {
  getListByCurrentWarehouse();
  queryDetail();
});
</script>
<style scoped lang="scss">
.addPage {
  background: #ffffff;
  border-radius: 4px;

  .page-title {
    cursor: pointer;
    padding: 20px 30px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;

    .el-icon {
      margin-right: 8px;
    }

    .display_block {
      display: inline-block;
    }
  }

  .page-content {
    padding: 0px 30px 24px 30px;

    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }

    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }

  .content {
    margin-top: 20px;
  }

  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }

  .mt20 {
    margin-top: 20px;
  }

  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }

  .product-code {
    color: #90979e;
  }

  .product-name {
    color: #151719;
  }

  .mt15px {
    margin-bottom: 15px !important;
  }

  .mb15px {
    margin-bottom: 15px !important;
  }
}
</style>
<style scoped>
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
}
</style>
