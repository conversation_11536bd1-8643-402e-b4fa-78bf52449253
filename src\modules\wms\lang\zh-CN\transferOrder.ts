export default {
  transferOrder: {
    label: {
      // 基本信息
      basicInformation: "基本信息",
      transferOrderCode: "调拨单号",
      transferOrderName: "主题描述",
      applicantUser: "申请人",
      applicantTime: "申请时间",
      planTransferOutTime: "计划调出时间",
      planTransferInTime: "计划调入时间",
      sourceWarehouse: "调出方",
      targetWarehouse: "调入方",
      address: "地址",
      detailAddress: "详细地址",
      planDeliveryType: "计划配送方式",
      remark: "备注",
      transferOrderName2: "调拨主题",
      sourceWarehouseName2: "调出仓库",
      targetWarehouseName2: "调入仓库",
      transferOrderStatus: "调拨状态",
      transferOrderName3: "主题",
    //   planTotalQty: "计划量",
      // 商品明细
      productDetails: "商品明细",
      serialNumber: "序号",
      productInfo: "商品信息",
      productCode: "商品编码",
      productName: "商品名称",
      productCategory: "商品分类",
      productSpec: "规格",
      productAttribute: "商品属性",
      planTransferQty: "计划量",
      planTransferConverted: "计划转换量",
      operation: "操作",
      total: "合计",
      
      // 列表页面
      transferOrderList: "调拨单列表",
      theme: "主题",
      sourceWarehouseName: "调出方",
      targetWarehouseName: "调入方",
      planTotalQty: "计划量",
      outboundQty: "出库量",
      inboundQty: "入库量",
      applicantUserName: "申请人",
      createUserName: "创建人",
      createTime: "创建时间",
      outboundConfirm: "出库确认",
      inboundConfirm: "入库确认",
      outboundStatus: "出库状态",
      inboundStatus: "入库状态",
      status: "状态",
      outboundConvertedQty: "出库转换量",
      inboundConvertedQty: "入库转换量",
      // 详情页面
      transferInfo: "调拨信息",
      transferDetails: "调拨明细",
      outboundInfo: "出库信息",
      inboundInfo: "入库信息",
      outboundNoticeCode: "出库通知单号",
      pickingListCode: "拣货单",
      actualDeliveryMethod: "实际配送方式",
      actualOutboundTime: "实际出库时间",
      carrier: "承运商",
      carNumber: "车号",
      poundCode: "磅单编号",
      attachment: "附件",
      inboundNoticeCode: "入库通知单号",
      inboundOrderCode: "入库单号",
      inboundCarNumber: "入库车号",
      inboundRemark: "入库备注",
      outboundTotalQty: "出库总量",
      // 状态
      draft: "草稿",
      initial: "初始",
      transferring: "调拨中",
      completed: "完结",
      pendingOutbound: "待出库",
      partialOutbound: "部分出库",
      outboundCompleted: "已出库",
      pendingInbound: "待入库",
      partialInbound: "部分入库",
      inboundCompleted: "已入库",
      
      // 时间类型
      applicantTimeType: "申请时间",
      lastOutboundConfirmTime: "最后出库确认时间",
      lastInboundConfirmTime: "最后入库确认时间",
      createTimeType: "创建时间",
      lastModifyTime: "最后修改时间",
      
      // 数量相关
      quantity: "数量",
      convertedQuantity: "转换量",
      totalQuantity: "总量",
      outboundQuantity: "出库量",
      inboundQuantity: "入库量",
      actualOutboundQty: "出库总量",
      actualInboundQty: "入库总量",
      
      // 其他
      confirmer: "确认人",
      confirmTime: "确认时间",
      view: "查看",
      qualityInspection: "质检信息",
      productPackage: "商品包装",
      warehouseArea: "库区",
      outboundWarehouseArea: "出库库区",
      inboundWarehouseArea: "入库库区",
      salePrice: "销售单价(RMB)",
      saleAmount: "销售金额(RMB)",
      costPrice: "成本单价(RMB)",
      costAmount: "成本金额(RMB)",
      inboundPrice: "入库单价(RMB)",
      inboundAmount: "入库金额(RMB)",
      weighbridgeNo: "磅单编号",
      vehicleNo: "车号",
      availableStockQty: "可用库存"
    },
    
    placeholder: {
      enterTransferOrderCode: "请输入调拨单号",
      enterTransferOrderName: "请输入调拨主题",
      selectSourceWarehouse: "请选择调出方仓库",
      selectTargetWarehouse: "请选择调入方仓库",
      selectTransferOrderStatus: "请选择调拨状态",
      selectTimeType: "请选择时间类型",
      enterInput: "请输入",
      pleaseSelect: "请选择",
      systemGenerated: "系统生成",
      defaultCurrentUser: "默认当前用户",
      automaticTime: "自动时间",
      selectDeliveryMethod: "请选择配送方式",
      selectSourceWarehouseCode: "请选择调出方仓库",
      selectTargetWarehouseCode: "请选择调入方仓库",
      enterRemark: "请输入备注",
      startTime: "开始时间",
      endTime: "结束时间"
    },
    
    button: {
      // 操作按钮
      create: "新建",
      add: "添加",
      edit: "编辑",
      delete: "删除",
      complete: "完结",
      detail: "详情",
      cancel: "取消",
      saveDraft: "保存草稿",
      submit: "提交",
      back: "返回",
      view: "查看",
      search: "查询",
      reset: "重置",
      
      // 时间快捷按钮
      today: "今天",
      yesterday: "昨天",
      nearSevenDays: "近七天",
      
      // 商品操作
      addProduct: "添加商品"
    },
    
    title: {
      // 页面标题
      editTransferOrder: "编辑调拨单",
      addTransferOrder: "新增调拨单",
      transferOrderDetail: "调拨单详情",
      transferOrderList: "调拨单列表",
      addProductTitle: "添加商品",
      selectProduct: "选择商品"
    },
    
    message: {
      // 成功提示
      saveDraftSuccess: "保存草稿成功",
      addSuccess: "新增成功",
      editSuccess: "修改成功",
      deleteSuccess: "删除成功",
      completeSuccess: "完结成功",
      
      // 错误提示
      saveFailed: "保存失败",
      addFailed: "新增失败",
      editFailed: "修改失败",
      deleteFailed: "删除失败",
      completeFailed: "完结失败",
      getDetailFailed: "获取调拨单详情失败",
      getWarehouseListFailed: "获取仓库列表失败",
      getBaseDataFailed: "获取基础数据失败",
      
      // 警告提示
      pleaseAddProductDetails: "请添加商品明细",
      transferOrderCodeRequired: "调拨单号不能为空",
      
      // 确认提示
      confirmDelete: "是否确认删除已创建的调拨单",
      confirmComplete: "调拨单手动完结后，请线下通知出/入库人员完结出/入库单据",
      deleteCancel: "已取消删除",
      completeCancel: "已取消完结",
      
      // 其他提示
      warning: "警告",
      tip: "提示",
      confirm: "确定",
      cancel: "取消"
    },
    
    rules: {
      // 表单验证规则
      applicantUserRequired: "请选择申请人",
      applicantTimeRequired: "请选择申请时间",
      sourceWarehouseRequired: "请选择调出仓库",
      targetWarehouseRequired: "请选择调入仓库",
      planTransferOutTimeRequired: "请选择计划调出时间",
      planTransferInTimeRequired: "请选择计划调入时间",
      planDeliveryTypeRequired: "请选择计划配送方式",
      planTransferQtyRequired: "计划量不能为空",
      planTransferConvertedRequired: "计划转换量不能为空",
      numberFormatError: "整数位限长8位，小数后3位",
      maxLength100: "最多输入100个字符",
      maxLength200: "最多输入200个字符"
    }
  }
}; 
