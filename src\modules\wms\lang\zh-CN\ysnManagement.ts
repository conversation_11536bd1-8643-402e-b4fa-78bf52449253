export default {
  ysnManagement:{
    label:{
      product:'商品',
      optType:'操作类型',
      printStatus:'打印状态',
      newYsnCode:'新YSN码',
      oldYsnCode:'原YSN码',
      productMessage:'商品信息',
      productWeight:'商品重量',
      quantity:'生成数量',
    },
    tableColumnLabel:{
      productCode:'商品编码',
      productName:'商品名称',
      optType:'操作类型',
      quantity:'生成数量',
      oldYsnCode:'原YSN码',
      newYsnCode:'新YSN码',
      weight:'重量',
      updateUser:'生成/更换人',
      updateTime:'生成/更换时间',
      printUser:'打印人',
      printTime:'打印时间',
      ysnCode:'YSN码',
      detailWeight:'重量(kg)',
    },
    placeholder:{
      productPlaceholder:'请输入商品编码或商品名称',
      newYsnCodePlaceholder:'请输入新YSN码',
      oldYsnCodePlaceholder:'请输入原YSN码',
      productMessage:'系统根据YSN查询带出商品编码/名称',
      productWeight:'系统根据YSN查询带出商品实际重量',
      quantityPlaceholder:'请输入不大于50的数字',
    },
    optTypeList:{
      new:'新生成',
      edit:'更换',
    },
    printStatusList:{
      unprinted:'未打印',
      printed:'已打印',
    },
    message:{
      addSuccess:'生成成功',
      changeSuccess:'更换成功',
    },
    rule:{
      optType:'请选择操作类型',
      oldYsnCode:'请输入原YSN',
      productMessage:'请选择商品信息',
      productWeight:'请输入商品重量',
      productWeightFormat:'商品重量为大于0的数字，支持小数点前8位，小数点后3位',
      quantity:'请输入生成数量',
      quantityFormat:'生成数量支持大于0小于等于50的整数',
    },
    title:{
      createYSN:'YSN生成',
      newYsnCode:'新生成YSN码',
    },
    button:{
      add:'新增',
      closeBtn:'关闭'
    }
  }
}
