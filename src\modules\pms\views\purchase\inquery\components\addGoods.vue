<template>
  <el-drawer
    title="添加询价商品"
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    direction="rtl"
    size="800"
    :before-close="handleClose"
  >
    <div class="add-goods-container">
      <!-- Search Filters -->
      <div class="filter-section">
        <el-form :model="filterForm" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="商品名称">
                <el-input
                  v-model="filterForm.productName"
                  placeholder="请输入商品名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商品编码">
                <el-input
                  v-model="filterForm.productCode"
                  placeholder="请输入商品编码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商品分类">
                <!--    <el-select v-model="filterForm.category" placeholder="请选择">
                  <el-option label="请选择" value=""></el-option>
                </el-select> -->
                <el-cascader
                  v-model="filterForm.category"
                  :options="classificationList"
                  clearable
                  :props="{
                    value: 'id',
                    label: 'categoryName',
                    children: 'children',
                  }"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="供应商">
                <el-select
                  v-model="filterForm.supplierId"
                  filterable
                  placeholder="请输入供应商名称"
                  :remote-method="handleSupplierSearch"
                  :loading="supplierLoading"
                >
                  <el-option
                    v-for="item in supplierOptions"
                    :key="item.supplierId"
                    :label="item.supplierName"
                    :value="item.supplierId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="text-align: right">
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- Selected Products Table -->
      <div class="selected-products">
        <div class="table-header">
          <span>
            已选
            <span style="color: #007aff">{{ selectedGoods.length }}</span>
            个商品
          </span>
        </div>
        <el-table
          :data="selectedProducts"
          @selection-change="handleSelectionChange"
          :loading="loading"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="商品信息">
            <template #default="scope">
              <div class="product-info">
                <el-image
                  :src="scope.row.productImg"
                  class="product-image"
                ></el-image>
                <div class="product-details">
                  <div>商品编码: {{ scope.row.productCode }}</div>
                  <div>{{ scope.row.productName }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="productCategoryFullName"
            label="商品分类"
          ></el-table-column>
          <el-table-column
            prop="unitName"
            label="采购单位"
          ></el-table-column>
        </el-table>
      </div>

      <!-- Pagination -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="page.page"
          v-model:page-size="page.limit"
          :total="page.total"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageChange"
          @current-change="handlePageChange"
        />
      </div>

      <!-- Footer Buttons -->
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import InqueryAPI from "@pms/api/inquery";
import ProductCategoryAPI from "@pms/api/productCategory";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible"]);
const loading = ref(false);
const filterForm = ref({
  // keywords: "",
  productCode: '',
  productName: '',
  supplierId: "", // 供应商Id
  category: [], // 商品分类
  firstCategoryId: "", // 一级分类Id
  secondCategoryId: "", // 二级分类Id
  thirdCategoryId: "", // 三级分类Id
});
const classificationList = ref([]);
const selectedProducts = ref([
  /*  {
    code: "SI235434624",
    name: "云南昭通苹果甜可口脆甜味美多汁",
    category: "蔬菜水果",
    specification: "1kg装",
    image: "",
  }, */
]);

const page = ref({
  page: 1,
  limit: 20,
  total: null,
});

const supplierLoading = ref(false);
const supplierOptions = ref([]);

const getTime = (date) => {
  return new Date(date).getTime();
};
const getGoods = () => {
  loading.value = true;
  InqueryAPI.getGoods({
    page: page.value.page,
    limit: page.value.limit,
    ...props.formData,
    startDeliveryDate: getTime(`${props.formData.startDeliveryDate}`),
    endDeliveryDate: getTime(`${props.formData.endDeliveryDate}`),
    chooseType: 1, // 1-新增询价单选择产品 2-新增采购需求选择产品 3-新增采购任务选择产品 4-新增采购单选择产品
    ...filterForm.value
  })
    .then((res) => {
      selectedProducts.value = res.records;
      page.value.total = parseInt(res.total || 0);
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(() => {
  getGoods();
  handleSupplierSearch("");
  getCategoryList();
});

const handleClose = () => {
  emit("update:visible", false);
};

const handleConfirm = () => {
  // Handle confirmation logic
  emit("update:visible", false);
  emit("add-goods", selectedGoods.value);
};

const selectedGoods = ref([]);
const handleSelectionChange = (selection) => {
  console.log(selection);
  // Handle selection change
  selectedGoods.value = selection;
};

const handlePageChange = (currentPage) => {
  page.value.page = currentPage;
  // Fetch new page data
};

const supplierForm = ref({
  inquiryType: '', // 询价类型：0->国内供应，1->国外直采
  keywords: '', // 供应商名称或关键
  warehouseId: '',
});
const handleSupplierSearch = async (query) => {
  supplierForm.value.inquiryType = props.formData.inquiryType;
  supplierForm.value.warehouseId = props.formData.warehouseId;
  supplierLoading.value = true;
  try {
    const res = await InqueryAPI.querySupplierList(supplierForm.value);
    supplierOptions.value = res;
  } finally {
    supplierLoading.value = false;
  }
};

const getCategoryList = () => {
  ProductCategoryAPI.queryCategoryTreeList({})
    .then((data) => {
      classificationList.value = data;
    })
    .finally(() => {});
};

const handleReset = () => {
  filterForm.value = {
    keywords: "",
    supplierId: "",
    firstCategoryId: "",
    secondCategoryId: "",
    thirdCategoryId: "",
  };
  // Reset category cascader
  filterForm.value.category = [];
  // Refresh goods list with reset filters
  page.value.page = 1;
  getGoods();
};

const handleSearch = () => {
  // Reset to first page when searching
  page.value.page = 1;
  // Extract category IDs from cascader selection
  if (filterForm.value.category?.length) {
    filterForm.value.firstCategoryId = filterForm.value.category[0];
    filterForm.value.secondCategoryId = filterForm.value.category[1];
    filterForm.value.thirdCategoryId = filterForm.value.category[2];
  }
  getGoods();
};
</script>

<style lang="scss" scoped>
.add-goods-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .filter-section {
    margin-bottom: 20px;
  }

  .selected-products {
    flex: 1;
    overflow-y: auto;

    .table-header {
      margin-bottom: 10px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #52585f;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }

    .product-info {
      display: flex;
      align-items: center;

      .product-image {
        width: 60px;
        height: 60px;
        margin-right: 10px;
      }
    }
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .drawer-footer {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
