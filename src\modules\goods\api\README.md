# Goods API 模块

本目录包含商品模块相关的所有API调用文件，基于Apifox接口文档自动生成。

## 文件结构

```
src/modules/goods/api/
├── index.ts                      # API入口文件
├── deliveryMethods.ts            # 配送方式表API
├── productAttributes.ts          # 商品属性表API
├── deliveryMethodsCategory.ts    # 配送方式分类表API
├── warehouseAreaStorageTypes.ts  # 库区存储类型表API
├── systemParameters.ts           # 系统参数表API
├── systemParameters.example.ts   # 系统参数API使用示例
└── README.md                     # 本说明文档
```

## 新增API模块

### 1. 配送方式表 API (DeliveryMethodsAPI)

管理不同的配送方式信息，支持以下操作：
- 分页查询配送方式
- 获取配送方式详情
- 新增/编辑/删除配送方式
- 批量删除配送方式
- 批量启用/禁用配送方式

### 2. 商品属性表 API (ProductAttributesAPI)

管理商品的属性信息，支持以下操作：
- 分页查询商品属性
- 获取商品属性详情
- 新增/编辑/删除商品属性
- 批量删除商品属性
- 批量启用/禁用商品属性
- 查询商品属性字段枚举列表

### 3. 配送方式分类表 API (DeliveryMethodsCategoryAPI)

管理配送方式的分类，支持以下操作：
- 分页查询配送方式分类
- 获取配送方式分类详情
- 新增/编辑/删除配送方式分类
- 批量删除配送方式分类

### 4. 库区存储类型表 API (WarehouseAreaStorageTypesAPI)

管理不同类型的存储区域，支持以下操作：
- 分页查询库区存储类型
- 获取库区存储类型详情
- 新增/编辑/删除库区存储类型
- 批量删除库区存储类型
- 批量启用/禁用库区存储类型

### 5. 系统参数表 API (SystemParametersAPI)

管理系统的各项参数配置，支持以下操作：
- 分页查询系统参数
- 获取系统参数详情
- 新增/编辑/删除系统参数
- 批量删除系统参数

## 使用方式

### 1. 单独导入使用

```typescript
import DeliveryMethodsAPI from "@/modules/goods/api/deliveryMethods";

// 分页查询配送方式
const result = await DeliveryMethodsAPI.getPageList({
  page: 1,
  limit: 10,
  enableStatus: 1
});
```

### 2. 从入口文件统一导入

```typescript
import { 
  DeliveryMethodsAPI, 
  ProductAttributesAPI,
  EnableStatus 
} from "@/modules/goods/api";

// 查询启用状态的配送方式
const deliveryMethods = await DeliveryMethodsAPI.getPageList({
  enableStatus: EnableStatus.ENABLED
});

// 查询商品属性
const attributes = await ProductAttributesAPI.getPageList({
  page: 1,
  limit: 20
});
```

### 3. 类型安全

所有API都提供了完整的TypeScript类型定义：

```typescript
import type { 
  DeliveryMethodsForm, 
  DeliveryMethodsVO 
} from "@/modules/goods/api";

// 创建配送方式
const formData: DeliveryMethodsForm = {
  methodName: "快递配送",
  deliveryMethodsCategoryId: 1,
  enableStatus: EnableStatus.ENABLED
};

const result = await DeliveryMethodsAPI.add(formData);
```

## API规范

- 所有接口都使用POST方法
- 分页查询支持最大1000条/页
- 启禁用状态：0=禁用，1=启用
- 所有接口需要Authorization和APP_ID请求头
- 响应数据遵循统一的分页结构

## 注意事项

1. 这些API基于Apifox接口文档生成，请确保后端接口与文档保持一致
2. 使用前请确认request工具已正确配置
3. 所有枚举值请使用定义的常量，避免硬编码
4. 批量操作时注意数组长度限制

## 相关文档

- [Apifox项目地址](https://app.apifox.com/web/project/5764243)
- [系统参数API使用示例](./systemParameters.example.ts) 