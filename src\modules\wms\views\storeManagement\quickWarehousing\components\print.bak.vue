<template>
  <PrintTemplate ref="printRef">
    <div class="print-container">
      <div class="print-header">
        <h1 class="print-title">{{ printData.title }}</h1>
      </div>

      <div class="print-info">
        <div class="barcode">
          <Barcode
            :value="printData.receiptNoticeCode"
            :options="barcodeOptions"
          />
          <div
            class="barcode_text"
            v-if="
              printData.receiptNoticeCode &&
              printData.receiptNoticeCode.length > 6
            "
          >
            {{ printData.receiptNoticeCode.slice(-6) }}
          </div>
        </div>
        <div class="info-row">
          <!-- 仓库名称 -->
          <div class="info-item-2">
            <span class="info-label">
              {{ labelName.warehouse }}：
              <!-- {{ $t("warehouseEntryNotice.label.warehouse") }}： -->
            </span>
            <span class="info-value">
              {{ printData.warehouseName || "" }}
            </span>
          </div>
          <!-- 采购/销售员 -->
          <div class="info-item-2">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.purchaseSalesPerson") }}：
            </span>
            <span class="info-value">
              {{ printData.purchaseSalesPerson || "" }}
            </span>
          </div>
        </div>

        <div class="info-row">
          <!-- 入库通知单号 -->
          <div class="info-item-2">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.receiptNoticeCode") }}：
            </span>
            <span class="info-value">
              {{ printData.receiptNoticeCode || "" }}
            </span>
          </div>
          <!-- 客户 -->
          <div class="info-item-2" v-if="printData.receiptType == 2">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.customerName") }}：
            </span>
            <span class="info-value">
              {{ printData.customerName || "" }}
            </span>
          </div>
          <!-- 供应商 -->
          <div class="info-item-2" v-else>
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.supplierName") }}：
            </span>
            <span class="info-value">
              {{ printData.supplierName || "" }}
            </span>
          </div>
        </div>

        <div class="info-row">
          <!-- 来源单号 -->
          <div class="info-item-2">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.sourceOrderCode") }}：
            </span>
            <span class="info-value">
              {{ printData.sourceOrderCode || "" }}
            </span>
          </div>
          <!-- 计划交货时间 -->
          <div class="info-item-2">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.plannedDeliveryTime") }}：
            </span>
            <span class="info-value">
              {{ printData.plannedDeliveryTime || "" }}
            </span>
          </div>
        </div>

        <div class="info-row">
          <!-- 单据备注 -->
          <div class="info-item-2">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.documentRemark") }}：
            </span>
            <span class="info-value">
              {{ printData.remark || "" }}
            </span>
          </div>
          <!-- 采购/销售员 -->
          <!-- <div class="info-item-2">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.purchaseSalesPerson") }}：
            </span>
            <span class="info-value">
              {{ printData.purchaseSalesPerson || "" }}
            </span>
          </div> -->
        </div>
      </div>

      <div class="print-table-container">
        <div>
          {{ $t("warehouseEntryNotice.label.productType") }}：{{
            printData.items ? printData.items.length : 0
          }}
        </div>
        <table class="print-table">
          <thead>
            <tr>
              <th style="width: 30px">{{ $t("common.sort") }}</th>
              <th style="width: 130px">{{ $t("warehouseEntryNotice.label.barcode") }}</th>
              <th>{{ $t("warehouseEntryNotice.label.productName") }}</th>
              <th style="width: 70px">{{ $t("warehouseEntryNotice.label.productSpecs") }}</th>
              <th style="width: 40px">{{ $t("warehouseEntryNotice.label.unitName") }}</th>
              <!--<th>{{ $t("warehouseEntryNotice.label.productPlanQty") }}</th>-->
              <th>{{ $t("warehouseEntryNotice.label.productPlanQty") }}/{{ $t("warehouseEntryNotice.label.productPlanWeight") }}</th>
             <!-- <th>{{ $t("warehouseEntryNotice.label.actualQuantity") }}</th>-->
              <th>{{ $t("warehouseEntryNotice.label.receivedQty") }}/{{ $t("warehouseEntryNotice.label.receivedWeight") }}</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in printData.items || defaultItems"
              :key="index"
            >
              <td style="width: 30px">{{ index + 1 }}</td>
              <td style="width: 130px">
                  <div class="barcode">
                      <Barcode
                              :value="item.productCode"
                              :options="productCodeBarcodeOptions"
                      />
                      <div class="barcode_text" v-if="item.productCode">
                          {{ item.productCode }}
                      </div>
                  </div>
              </td>
              <td>{{ item.productName }}</td>
              <td style="width: 70px">{{ item.productSpecs }}</td>
              <td style="width: 40px">{{ item.productUnitName }}</td>
              <td>{{ item.productExpectQty }}/{{ item.expectedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
              <td>{{item.productActualQty }}/{{ item.receivedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
            </tr>



            <tr
              v-for="(item, index) in printData.items || defaultItems"
              :key="index"
            >
              <td style="width: 30px">{{ index + 1 }}</td>
              <td style="width: 130px">
                  <div class="barcode">
                      <Barcode
                              :value="item.productCode"
                              :options="productCodeBarcodeOptions"
                      />
                      <div class="barcode_text" v-if="item.productCode">
                          {{ item.productCode }}
                      </div>
                  </div>
              </td>
              <td>{{ item.productName }}</td>
              <td style="width: 70px">{{ item.productSpecs }}</td>
              <td style="width: 40px">{{ item.productUnitName }}</td>
              <td>{{ item.productExpectQty }}/{{ item.expectedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
              <td>{{item.productActualQty }}/{{ item.receivedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
            </tr>
            <tr
              v-for="(item, index) in printData.items || defaultItems"
              :key="index"
            >
              <td style="width: 30px">{{ index + 1 }}</td>
              <td style="width: 130px">
                  <div class="barcode">
                      <Barcode
                              :value="item.productCode"
                              :options="productCodeBarcodeOptions"
                      />
                      <div class="barcode_text" v-if="item.productCode">
                          {{ item.productCode }}
                      </div>
                  </div>
              </td>
              <td>{{ item.productName }}</td>
              <td style="width: 70px">{{ item.productSpecs }}</td>
              <td style="width: 40px">{{ item.productUnitName }}</td>
              <td>{{ item.productExpectQty }}/{{ item.expectedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
              <td>{{item.productActualQty }}/{{ item.receivedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
            </tr>
            <tr
              v-for="(item, index) in printData.items || defaultItems"
              :key="index"
            >
              <td style="width: 30px">{{ index + 1 }}</td>
              <td style="width: 130px">
                  <div class="barcode">
                      <Barcode
                              :value="item.productCode"
                              :options="productCodeBarcodeOptions"
                      />
                      <div class="barcode_text" v-if="item.productCode">
                          {{ item.productCode }}
                      </div>
                  </div>
              </td>
              <td>{{ item.productName }}</td>
              <td style="width: 70px">{{ item.productSpecs }}</td>
              <td style="width: 40px">{{ item.productUnitName }}</td>
              <td>{{ item.productExpectQty }}/{{ item.expectedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
              <td>{{item.productActualQty }}/{{ item.receivedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
            </tr>
            <tr
              v-for="(item, index) in printData.items || defaultItems"
              :key="index"
            >
              <td style="width: 30px">{{ index + 1 }}</td>
              <td style="width: 130px">
                  <div class="barcode">
                      <Barcode
                              :value="item.productCode"
                              :options="productCodeBarcodeOptions"
                      />
                      <div class="barcode_text" v-if="item.productCode">
                          {{ item.productCode }}
                      </div>
                  </div>
              </td>
              <td>{{ item.productName }}</td>
              <td style="width: 70px">{{ item.productSpecs }}</td>
              <td style="width: 40px">{{ item.productUnitName }}</td>
              <td>{{ item.productExpectQty }}/{{ item.expectedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
              <td>{{item.productActualQty }}/{{ item.receivedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
            </tr>
            <tr
              v-for="(item, index) in printData.items || defaultItems"
              :key="index"
            >
              <td style="width: 30px">{{ index + 1 }}</td>
              <td style="width: 130px">
                  <div class="barcode">
                      <Barcode
                              :value="item.productCode"
                              :options="productCodeBarcodeOptions"
                      />
                      <div class="barcode_text" v-if="item.productCode">
                          {{ item.productCode }}
                      </div>
                  </div>
              </td>
              <td>{{ item.productName }}</td>
              <td style="width: 70px">{{ item.productSpecs }}</td>
              <td style="width: 40px">{{ item.productUnitName }}</td>
              <td>{{ item.productExpectQty }}/{{ item.expectedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
              <td>{{item.productActualQty }}/{{ item.receivedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
            </tr>
            <tr
              v-for="(item, index) in printData.items || defaultItems"
              :key="index"
            >
              <td style="width: 30px">{{ index + 1 }}</td>
              <td style="width: 130px">
                  <div class="barcode">
                      <Barcode
                              :value="item.productCode"
                              :options="productCodeBarcodeOptions"
                      />
                      <div class="barcode_text" v-if="item.productCode">
                          {{ item.productCode }}
                      </div>
                  </div>
              </td>
              <td>{{ item.productName }}</td>
              <td style="width: 70px">{{ item.productSpecs }}</td>
              <td style="width: 40px">{{ item.productUnitName }}</td>
              <td>{{ item.productExpectQty }}/{{ item.expectedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
              <td>{{item.productActualQty }}/{{ item.receivedWeight }}{{$t("warehouseEntryNotice.label.productPlanWeightUnit")}}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="print-info footerStyle">
        <div class="info-row">
          <div class="info-item-4">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.printPerson") }}：
            </span>
            <span class="info-value">{{ printData.printPerson || "-" }}</span>
          </div>
          <div class="info-item-4">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.printTime") }}：
            </span>
            <span class="info-value">{{ printData.printTime || "-" }}</span>
          </div>
          <div class="info-item-4">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.receiver") }}：
            </span>
            <span class="info-value">{{ printData.receiver || "" }}</span>
          </div>
          <div class="info-item-4">
            <span class="info-label">
              {{ $t("warehouseEntryNotice.label.receivingDate") }}：
            </span>
            <span class="info-value">{{ printData.receivingTime || "" }}</span>
          </div>
        </div>
      </div>
    </div>
  </PrintTemplate>
</template>

<script setup lang="ts">
import { ref } from "vue";
// import PrintTemplate from "@/core/components/Print/PrintTemplate.vue";
import PrintTemplate from "@/core/components/NewPrint/index.vue";
const { t } = useI18n();

const barcodeOptions = ref({
  format: "CODE128", // 条形码格式
  width: 2, // 条形码宽度
  height: 100, // 条形码高度
  displayValue: true, // 是否显示条形码下方的文本
});


const productCodeBarcodeOptions = ref({
    format: "CODE128", // 条形码格式
    width: 1, // 条形码宽度
    height: 22, // 条形码高度
    displayValue: false, // 是否显示条形码下方的文本
});

const labelName = ref({
  warehouse: t("warehouseEntryNotice.label.warehouse"),
});

const printData = ref<any>({});
const printRef = ref<InstanceType<typeof PrintTemplate>>();
// 默认商品数据（用于预览或测试）
const defaultItems = ref([
  {
    productCode: "--",
    productName: "--",
    productSpecs: "--",
    productUnitName: "--",
    productExpectQty: "--",
    expectedWeight: "--",
    productActualQty: "--",
    receivedWeight: "--",
  },
]);

// 暴露打印方法给父组件
const handlePrint = (data: any) => {
  printData.value = data;
 /*  nextTick(() => {
    printRef.value?.print();
  }); */
  nextTick(() => {
    printRef.value?.onPrint();
  });
};

defineExpose({
  handlePrint,
});
</script>

<style lang="scss">
/* 基础样式 - 最小化但保留结构 */
.print-container {
}

/* 打印媒体查询样式 */
@media print {
  .print-container {
    display: block;
    width: calc(100vw - 40px) !important; /* 使用!important确保优先级 */
    max-width: 100% !important; /* 添加最大宽度限制 */
    height: calc(100vh - 40px) !important;
    margin: 20px !important; /* 确保没有外边距 */
    padding: 20px;
    box-sizing: border-box;
    font-family: SimSun, "宋体", Arial, sans-serif;
    color: black;
    border: 1px solid #000;
    position: fixed !important; /* 使用fixed定位 */
    top: 0 !important;
    left: 0 !important;
    right: 0 !important; /* 确保右侧贴合 */
  }

  /* 确保打印时页面没有默认边距 */
  @page {
    margin: 0;
    size: auto;
  }

  body {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 标题样式 */
  .print-header {
    text-align: center;
    margin-bottom: 15px;

    .print-title {
      font-size: 18pt;
      font-weight: 700;
      margin: 0;
      color: #000;
    }
  }

  /* 信息区域样式 */
  .print-info {
    margin-bottom: 15px;

    .barcode {
      text-align: center;
      margin-bottom: 16px;
      .barcode_text {
        font-weight: 600;
        font-size: 20px;
        color: #151719;
      }
    }

    .info-row {
      display: flex;
      margin-bottom: 8px;
      flex-wrap: wrap;
      page-break-inside: avoid;

      .info-item-4 {
        width: 24% !important;
        display: flex;
        margin-bottom: 5px;

        .info-label {
          font-weight: bold;
          padding-right: 8px;
        }

        .info-value {
          flex: 1;
          padding-right: 8px;
        }
      }
      .info-item-2 {
        width: 50% !important;
        display: flex;
        margin-bottom: 5px;

        .info-label {
          font-weight: 600 !important;
          padding-right: 8px;
          box-sizing: border-box;
        }

        .info-value {
          flex: 1;
          // word-wrap: break-word; /* 允许长单词换行 */
          // overflow-wrap: break-word; /* 更标准的写法 */
          // white-space: normal; /* 默认换行行为 */ */
          word-break: break-all; /* 强制所有字符换行 */
        }
      }
    }
  }

  /* 表格样式 */
  .print-table-container {
    margin-bottom: 60px;

    .print-table {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
      page-break-inside: avoid;

      th,
      td {
        border: 1px solid black;
        padding: 5px;
        text-align: center;
        font-size: 10pt;
      }

      th {
        // background-color: #e0e0e0;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      tfoot td {
        font-weight: bold;
      }

      .text-right {
        text-align: right;
      }
    }
  }

  .footerStyle {
    position: fixed;
    bottom: 0;
    width: 100%;
  }
}
</style>
