<template>
  <div class="app-container">
    <div class="addCustomer">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon><Back /></el-icon>
          <span v-if="type == 'add'">
            {{ $t("omsCustomer.title.addCustomer") }}
          </span>
          <span v-else-if="type == 'edit'">
            {{ $t("omsCustomer.title.editCustomer") }}
          </span>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="112px"
          label-position="right"
        >
          <div class="basicInformation item_content">
            <div class="title">
              {{ $t("omsCustomer.label.basicInformation") }}
            </div>
            <el-row justify="space-between">
              <el-col :span="6">
                <!-- 客户名称 -->
                <el-form-item
                  :label="$t('omsCustomer.label.customerName')"
                  prop="customerName"
                >
                  <el-input
                    v-model="form.customerName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 客户简称 -->
                <el-form-item
                  :label="$t('omsCustomer.label.customerAbbreviation')"
                  prop="customerShortName"
                >
                  <el-input
                    v-model="form.customerShortName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="20"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 客户类型 -->
                <el-form-item
                  :label="$t('omsCustomer.label.customerType')"
                  prop="customerTypeCode"
                >
                  <el-select
                    v-model="form.customerTypeCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in customerTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" />
            </el-row>
          </div>
          <div class="contactInformation item_content">
            <div class="title">
              {{ $t("omsCustomer.label.contacts") }}
            </div>
            <el-row>
              <el-col :span="6">
                <!-- 联系人 -->
                <el-form-item
                  :label="$t('omsCustomer.label.contactsPerson')"
                  prop="contact"
                >
                  <el-input
                    v-model="form.contact"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-row>
                  <el-col :span="14">
                    <!-- 手机号码 -->
                    <el-form-item
                      :label="$t('omsCustomer.label.mobileNumber')"
                      prop="contactCountryAreaCode"
                      class="custom-select"
                    >
                      <el-select
                        v-model="form.contactCountryAreaCode"
                        :placeholder="$t('omsCustomer.label.areaCode')"
                        clearable
                      >
                        <el-option
                          v-for="(item, index) in areaList"
                          :key="index"
                          :label="item.internationalCode"
                          :value="item.internationalCode"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-form-item prop="contactMobile" label-width="0">
                      <el-input
                        v-model="form.contactMobile"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                        maxlength="30"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
          <div class="contactInformation item_content">
            <div class="title">
              {{ $t("omsCustomer.label.receiptInformation") }}
            </div>
            <el-row>
              <el-col :span="6">
                <!-- 收货人姓名 -->
                <el-form-item
                  :label="$t('omsCustomer.label.consigneeName')"
                  prop="receiverName"
                >
                  <el-input
                    v-model="form.receiverName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-row>
                  <el-col :span="14">
                    <!-- 手机号码 -->
                    <el-form-item
                      :label="$t('omsCustomer.label.mobileNumber')"
                      prop="receiverCountryAreaCode"
                      class="custom-select"
                    >
                      <el-select
                        v-model="form.receiverCountryAreaCode"
                        :placeholder="$t('omsCustomer.label.areaCode')"
                        clearable
                      >
                        <el-option
                          v-for="(item, index) in areaList"
                          :key="index"
                          :label="item.internationalCode"
                          :value="item.internationalCode"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-form-item prop="receiverMobile" label-width="0">
                      <el-input
                        v-model="form.receiverMobile"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                        maxlength="30"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="12">
                <!-- 收货地址 -->
                <el-form-item
                  :label="$t('omsCustomer.label.receivingAddress')"
                  class="address_style"
                >
                  <el-form-item prop="receiverCountryCode" label-width="0">
                    <!-- 国家 -->
                    <el-select
                      v-model="form.receiverCountryCode"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="countryChange"
                      clearable
                      class="!w-[120px]"
                    >
                      <el-option
                        v-for="(item, index) in countryList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item prop="receiverProvinceCode" label-width="0">
                    <!-- 省 -->
                    <el-select
                      :disabled="!form.receiverCountryCode"
                      v-model="form.receiverProvinceCode"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleProvinceChange"
                      class="!w-[120px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in provinceList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item
                    prop="receiverCityCode"
                    label-width="0"
                    v-if="showCityInput"
                  >
                    <!-- 市 -->
                    <el-select
                      v-model="form.receiverCityCode"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleCityChange"
                      class="!w-[120px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in cityList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item
                    prop="receiverDistrictCode"
                    label-width="0"
                    v-if="showDistrictInput"
                  >
                    <!-- 区 -->
                    <el-select
                      v-model="form.receiverDistrictCode"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleDistrictChange"
                      class="!w-[120px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in districtList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 详细地址 -->
                <el-form-item
                  :label="$t('omsCustomer.label.address')"
                  prop="receiverAddress"
                >
                  <el-input
                    v-model="form.receiverAddress"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>
                  {{ $t("omsCustomer.label.contractInformation") }}
                </div>
                <div>
                  <el-button
                    type="primary"
                    key="primary"
                    text
                    @click="handleContractSelect"
                  >
                    {{ $t("omsCustomer.button.contractsBtn") }}
                  </el-button>
                </div>
              </div>
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 关联合同列表 -->
                <el-form-item prop="contractList" label-width="0">
                  <el-table
                    :data="form.contractList"
                    stripe
                    v-loading="formLoading"
                  >
                    <el-table-column
                      :label="$t('omsCustomer.label.contractName')"
                      prop="contractName"
                    />
                    <el-table-column
                      :label="$t('omsCustomer.label.signatory')"
                      prop="contractPartner"
                    />
                    <el-table-column
                      :label="$t('omsCustomer.label.contractCode')"
                      prop="contractCode"
                    />
                    <el-table-column
                      :label="$t('omsCustomer.label.contractPeriod')"
                    >
                      <template #default="scope">
                        {{ parseDateTime(scope.row.startDate, "date") }}
                        {{ t("omsCustomer.label.to") }}
                        {{ parseDateTime(scope.row.endDate, "date") }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('omsCustomer.label.createTime')"
                    >
                      <template #default="scope">
                        <span>
                          {{ parseDateTime(scope.row.signDate, "date") }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')">
                      <template #default="scope">
                        <el-button
                          type="danger"
                          size="small"
                          link
                          @click="handleDeleteContract(scope.$index)"
                        >
                          {{ $t("common.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="qualificationInformation item_content">
            <div class="title">
              {{ $t("omsCustomer.label.financeInformation") }}
            </div>
            <el-row>
              <el-col :span="6">
                <!-- 开户银行 -->
                <el-form-item
                  :label="$t('omsCustomer.label.bankName')"
                  prop="financialBank"
                >
                  <el-input
                    type="text"
                    v-model="form.financialBank"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 银行账号 -->
                <el-form-item
                  :label="$t('omsCustomer.label.bankAccount')"
                  prop="financialAccount"
                >
                  <el-input
                    type="text"
                    v-model="form.financialAccount"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 开户名 -->
                <el-form-item
                  :label="$t('omsCustomer.label.accountName')"
                  prop="financialName"
                >
                  <el-input
                    type="text"
                    v-model="form.financialName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 财务联系人 -->
                <el-form-item
                  :label="$t('omsCustomer.label.financialContactPerson')"
                  prop="financialContact"
                >
                  <el-input
                    type="text"
                    v-model="form.financialContact"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 联系电话 -->
              <el-col :span="6">
                <el-row>
                  <el-col :span="14">
                    <!-- 手机号码 -->
                    <el-form-item
                      :label="$t('omsCustomer.label.mobileNumber')"
                      prop="financialCountryAreaCode"
                      class="custom-select"
                    >
                      <el-select
                        v-model="form.financialCountryAreaCode"
                        :placeholder="$t('omsCustomer.label.areaCode')"
                        clearable
                      >
                        <el-option
                          v-for="(item, index) in areaList"
                          :key="index"
                          :label="item.internationalCode"
                          :value="item.internationalCode"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-form-item prop="financialMobile" label-width="0">
                      <el-input
                        v-model="form.financialMobile"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                        maxlength="30"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="6">
                <!-- 结算币种 -->
                <el-form-item
                  :label="$t('omsCustomer.label.settlementCurrency')"
                  prop="financialSettlementCurrency"
                >
                  <el-select
                    v-model="form.financialSettlementCurrency"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in currencyList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div
            class="tradingAndLogistics item_content"
            style="border-bottom: unset !important"
          >
            <div class="title">
              {{ $t("omsCustomer.label.licenseInformation") }}
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 营业执照 -->
                <el-form-item
                  :label="$t('omsCustomer.label.businessLicense')"
                  prop="businessLicenseUrl"
                >
                  <upload-multiple
                    :custom-tips="$t('omsCustomer.message.pictureTip')"
                    :fileSize="5"
                    :fileType="['png', 'jpg', 'jpeg']"
                    :modelValue="form.businessLicenseUrl"
                    @update:model-value="onChangeMultiple"
                    ref="detailPicsRef"
                    :limit="1"
                    :formRef="formUpdateRef"
                    class="modify-multipleUpload"
                    name="detailPic"
                  >
                    <template #default="{ file }">点击上传</template>
                  </upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <!-- 统一社会信用代码 -->
                <el-form-item
                  :label="$t('supplierManagement.label.creditCode')"
                  prop="creditCode"
                  label-width="160"
                >
                  <el-input
                    type="text"
                    v-model="form.creditCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="35"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>

      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("omsCustomer.button.cancel") }}
          </el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{ $t("omsCustomer.button.save") }}
          </el-button>
        </div>
      </div>
    </div>

    <AddContract
      ref="addContractRef"
      v-model:visible="contractDialog.visible"
      :title="contractDialog.title"
      @on-submit="onSubmitContract"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "OmsAddCustomer",
  inheritAttrs: false,
});

import CustomerApi, { customerForm } from "@/modules/oms/api/customer";
import CommonAPI from "@/modules/wms/api/common";
import AddContract from "./components/addContract.vue";
import { useRoute, useRouter } from "vue-router";
import { parseDateTime } from "@/core/utils/index.js";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const type = route.query.type;
// const supplierId = route.query.supplierId;
const addContractRef = ref();
const formUpdateRef = ref();
const formRef = ref(ElForm);
const areaList = ref([]);
const loading = ref(false);
const formLoading = ref(false);
const countryList = ref([]);
const provinceList = ref([]);
const cityList = ref([]);
const districtList = ref([]);
const showCityInput = ref(false);
const showDistrictInput = ref(false);

const contractDialog = reactive({
  title: "",
  visible: false,
});

// 角色表单
const form = reactive<customerForm>({
  contractList: [],
  // areaInfo: [],
  contractIds: [],
});
const customerTypeList = ref([
  {
    code: 1,
    name: t("omsCustomer.customerTypeList.enterpriseCustomers"),
  },
  {
    code: 2,
    name: t("omsCustomer.customerTypeList.governmentClients"),
  },
  {
    code: 3,
    name: t("omsCustomer.customerTypeList.individualCustomers"),
  },
]);

const rules = reactive({
  customerName: [
    {
      required: true,
      message: t("omsCustomer.rules.customerNameTip"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("omsCustomer.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  customerShortName: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("omsCustomer.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  customerTypeCode: [
    {
      required: true,
      message: t("omsCustomer.rules.customerTypeTip"),
      trigger: "change",
    },
  ],
  contact: [
    {
      required: true,
      message: t("omsCustomer.rules.contactTip"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
      message: t("omsCustomer.rules.nameCopyFomart"),
      trigger: "blur",
    },
  ],
  contactCountryAreaCode: [
    {
      required: true,
      message: t("omsCustomer.rules.countryAreaCodeTip"),
      trigger: "change",
    },
  ],
  contactMobile: [
    {
      required: true,
      message: t("omsCustomer.rules.phoneTip"),
      trigger: "blur",
    },
  ],
  receiverName: [
    {
      required: true,
      message: t("omsCustomer.rules.receiverNameTip"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
      message: t("omsCustomer.rules.nameCopyFomart"),
      trigger: "blur",
    },
  ],
  receiverCountryAreaCode: [
    {
      required: true,
      message: t("omsCustomer.rules.countryAreaCodeTip"),
      trigger: "change",
    },
  ],
  receiverMobile: [
    {
      required: true,
      message: t("omsCustomer.rules.phoneTip"),
      trigger: "blur",
    },
  ],

  receiverCountryCode: [
    {
      required: true,
      message: t("omsCustomer.rules.countryTip"),
      trigger: "change",
    },
  ],
  // areaInfo: [
  //   {
  //     required: true,
  //     message: t("omsCustomer.rules.areaTip"),
  //     trigger: ["change", "blur"],
  //   },
  // ],
  // receiverCountryCode: [
  //   {
  //     required: true,
  //     message: t("omsCustomer.rules.areaTip"),
  //     trigger: ["change", "blur"],
  //   },
  // ],
  receiverProvinceCode: [
    {
      required: true,
      message: t("omsCustomer.rules.receiverProvinceTip"),
      trigger: ["change", "blur"],
    },
  ],
  receiverCityCode: [
    {
      required: true,
      message: t("omsCustomer.rules.receiverCityTip"),
      trigger: ["change", "blur"],
    },
  ],
  receiverDistrictCode: [
    {
      required: true,
      message: t("omsCustomer.rules.receiverDistrictTip"),
      trigger: ["change", "blur"],
    },
  ],
  receiverAddress: [
    {
      required: true,
      message: t("omsCustomer.rules.addressTip"),
      trigger: "blur",
    },
    // {
    //   pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
    //   message: t("omsCustomer.rules.nameFomart"),
    //   trigger: "blur",
    // },
  ],
  financialBank: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("omsCustomer.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  financialAccount: [
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: t("omsCustomer.rules.accountFomart"),
      trigger: "blur",
    },
  ],
  financialName: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("omsCustomer.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  financialContact: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
      message: t("omsCustomer.rules.nameCopyFomart"),
      trigger: "blur",
    },
  ],
  financialSettlementCurrency: [
    {
      required: true,
      message: t("omsCustomer.rules.settlementCurrencyTip"),
      trigger: "change",
    },
  ],
  creditCode: [
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: t("omsCustomer.rules.accountFomart"),
      trigger: "blur",
    },
  ],
});

const currencyList = ref([
  {
    code: "CNY",
    name: t("omsCustomer.label.RMB"),
  },
  {
    code: "USD",
    name: t("omsCustomer.label.dollar"),
  },
]);

function onChangeMultiple(val: any) {
  form.businessLicenseUrl = val;
}

/** 获取国家列表 */
function queryAllCountry() {
  const queryParams = {
    pid: "0",
  };
  CommonAPI.getAreaList(queryParams)
    .then((data: any) => {
      countryList.value = data;
    })
    .finally(() => {});
}

function getAreaApi(val: any, tab?: any) {
  let params = {
    pid: val,
  };
  CommonAPI.getAreaList(params)
    .then((data: any) => {
      if (tab == "province") {
        provinceList.value = data;
      } else if (tab == "city") {
        if (data.length > 0) {
          cityList.value = data;
          showCityInput.value = true;
          // form.areaInfo[1] = form.receiverCityCode;
        } else {
          showCityInput.value = false;
        }
      } else {
        if (data.length > 0) {
          showDistrictInput.value = true;
          // form.areaInfo[2] = form.receiverDistrictCode;
          districtList.value = data;
        } else {
          showDistrictInput.value = false;
        }
      }
      // checkAddress();
    })
    .finally(() => {});
}

function countryChange(val: any) {
  // if (type == "add") {
  form.receiverProvinceCode = "";
  provinceList.value = [];
  form.receiverCityCode = "";
  cityList.value = [];
  form.receiverDistrictCode = "";
  districtList.value = [];
  showCityInput.value = false;
  showDistrictInput.value = false;
  // form.areaInfo = [];
  form.receiverProvinceName = "";
  form.receiverCityName = "";
  form.receiverDistrictName = "";
  if (form.receiverCountryCode) {
    let data: any = countryList.value.find(
      (item: any) => item.id === form.receiverCountryCode
    );
    if (data) {
      form.receiverCountryName = data.shortName ? data.shortName : "";
    } else {
      form.receiverCountryName = "";
    }
    getAreaApi(val, "province");
  }
  // }
}

function handleProvinceChange(val: any) {
  // if (type == "add") {
  form.receiverCityCode = "";
  cityList.value = [];
  form.receiverDistrictCode = "";
  districtList.value = [];
  showCityInput.value = false;
  showDistrictInput.value = false;
  // form.areaInfo = [];
  let data: any = provinceList.value.find(
    (item: any) => item.id === form.receiverProvinceCode
  );
  if (data) {
    form.receiverProvinceName = data.shortName ? data.shortName : "";
  } else {
    form.receiverProvinceName = "";
  }
  if (val) {
    getAreaApi(val, "city");
  }
}

function handleCityChange(val: any) {
  // if (type == "add") {
  form.receiverDistrictCode = "";
  districtList.value = [];
  showDistrictInput.value = false;
  // form.areaInfo[1] = "";
  let data: any = cityList.value.find(
    (item: any) => item.id === form.receiverCityCode
  );
  if (data) {
    form.receiverCityName = data.shortName ? data.shortName : "";
  } else {
    form.receiverCityName = "";
  }
  if (val) {
    getAreaApi(val, "district");
  }
}

function handleDistrictChange(val: any) {
  let data: any = districtList.value.find(
    (item: any) => item.id === form.receiverDistrictCode
  );
  if (data) {
    form.receiverDistrictName = data.shortName ? data.shortName : "";
  } else {
    form.receiverDistrictName = "";
  }
  // checkAddress();
}

function checkAddress() {
  if (form.receiverProvinceCode && showCityInput.value == false) {
    form.areaInfo[0] = form.receiverProvinceCode;
  } else if (
    form.receiverProvinceCode &&
    form.receiverCityCode &&
    showDistrictInput.value == false
  ) {
    form.areaInfo[0] = form.receiverProvinceCode;
    form.areaInfo[1] = form.receiverCityCode;
  } else if (
    form.receiverProvinceCode &&
    form.receiverCityCode &&
    form.receiverDistrictCode &&
    showDistrictInput.value == true &&
    showCityInput.value == true
  ) {
    form.areaInfo[0] = form.receiverProvinceCode;
    form.areaInfo[1] = form.receiverCityCode;
    form.areaInfo[2] = form.receiverDistrictCode;
  } else {
    form.areaInfo = [];
  }
}

// 获取区号
function getAreaList() {
  CustomerApi.getAllCountry()
    .then((data: any) => {
      areaList.value = data;
    })
    .finally(() => {});
}

// 合同
function onSubmitContract(data: any) {
  form.contractList = [...(form.contractList || []), ...data];
  let ids: any = [];
  if (form.contractList && form.contractList.length > 0) {
    form.contractList.forEach((item: any) => {
      ids.push(item.contractId);
    });
  }
  form.contractIds = form.contractIds.concat(ids);
  form.contractIds = form.contractIds.filter(
    (item: any, index: any) => form.contractIds.indexOf(item) === index
  );
}

// 选择合同
function handleContractSelect() {
  contractDialog.title = t("omsCustomer.title.addContracts");
  let ids: any = [];
  if (form.contractList && form.contractList.length > 0) {
    form.contractList.forEach((item: any) => {
      ids.push(item.contractId);
    });
  }
  addContractRef.value.setFormData(ids);
  addContractRef.value.fetchContracts();
  contractDialog.visible = true;
}

// 删除合同
function handleDeleteContract(index?: number) {
  form.contractIds.splice(index, 1);
  form.contractList.splice(index, 1);
}

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.push({
    path: "/oms/omsCustomer/customerManagement",
  });
};

// 提交
function handleSubmit() {
  console.log(form);
  // checkAddress();
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    loading.value = true;
    const dataCopy = JSON.parse(JSON.stringify(form));
    if (dataCopy && Array.isArray(dataCopy.businessLicenseUrl)) {
      dataCopy.businessLicenseUrl = JSON.stringify(dataCopy.businessLicenseUrl);
    }
    let params = {
      ...dataCopy,
    };
    delete params.contractList;
    // delete params.areaInfo;
    if (type == "add") {
      delete params.id;
      CustomerApi.add(params)
        .then((data: any) => {
          ElMessage.success(t("omsCustomer.message.addSucess"));
          loading.value = false;
          handleCancel();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      CustomerApi.update(params)
        .then((data: any) => {
          ElMessage.success(t("omsCustomer.message.editSucess"));
          loading.value = false;
          handleCancel();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function querySupplierDetail() {
  formLoading.value = true;
  let params = {
    id: route.query.id,
  };
  CustomerApi.queryDetail(params)
    .then((data: any) => {
      Object.assign(form, data);
      // countryChange(form.receiverCountryCode);
      let contractIds: any = [];
      if (form.contractList && form.contractList.length > 0) {
        form.contractList.forEach((item: any) => {
          contractIds.push(item.contractId);
        });
      }
      form.contractIds = contractIds;
      // form.areaInfo[0] = form.receiverProvinceCode
      //   ? form.receiverProvinceCode
      //   : "";
      // form.areaInfo[1] = form.receiverCityCode ? form.receiverCityCode : "";
      // form.areaInfo[2] = form.receiverDistrictCode
      //   ? form.receiverDistrictCode
      //   : "";
      if (form.receiverCountryCode) {
        getAreaApi(form.receiverCountryCode, "province");
      }
      if (form.receiverProvinceCode) {
        getAreaApi(form.receiverProvinceCode, "city");
        // let obj: any = {
        //   id: form.receiverProvinceCode ? form.receiverProvinceCode : "",
        //   shortName: form.receiverProvinceName ? form.receiverProvinceName : "",
        // };
        // provinceList.value.push(obj);
      }
      if (form.receiverCityCode) {
        getAreaApi(form.receiverCityCode);
        // showCityInput.value = true;
        // let obj: any = {
        //   id: form.receiverCityCode ? form.receiverCityCode : "",
        //   shortName: form.receiverCityName ? form.receiverCityName : "",
        // };
        // cityList.value.push(obj);
      }
      // if (form.receiverDistrictCode) {
      // showDistrictInput.value = true;
      // let obj: any = {
      //   id: form.receiverDistrictCode ? form.receiverDistrictCode : "",
      //   shortName: form.receiverDistrictName ? form.receiverDistrictName : "",
      // };
      // districtList.value.push(obj);
      // }
      if (typeof form.businessLicenseUrl === "string") {
        form.businessLicenseUrl = JSON.parse(form.businessLicenseUrl); // 数据库中存储的imagesUrl是一个序列化后的对象数组字符串
      }
    })
    .finally(() => {
      formLoading.value = false;
    });
}

onMounted(() => {
  queryAllCountry();
  getAreaList();
  if (type == "edit") {
    querySupplierDetail();
  }
});
</script>
<style scoped lang="scss">
.addCustomer {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 13px 21px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 20px 9px 20px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .select_goods:hover {
      color: var(--el-color-primary);
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 9px 20px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .encryptBox {
    word-wrap: break-word;
    word-break: break-all;
  }

  .encryptBox-icon {
    margin-left: 4px;
    cursor: pointer;
    vertical-align: text-top;
  }

  .address_style {
    :deep(.el-form-item__label::before) {
      color: var(--el-color-danger);
      content: "*";
      margin-right: 4px;
    }
  }
}
</style>
