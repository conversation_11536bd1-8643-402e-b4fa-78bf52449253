import { RouteRecordRaw } from "vue-router";
import { constantRoutes } from "@/core/router";
import { useUserStore } from "@/core/store";
import { store } from "@/core/store";
import MenuAPI, { RouteVO } from "@/core/api/menu";
import { SYSTEM_ID_KEY } from "@/core/enums/CacheEnum";
import router from "@/core/router";
const Layout = () => import("@/core/layout/index.vue");
const modules = import.meta.glob("../../../**/**.vue");
import {SYSTEM_LIST, APPLICATIONCENTER} from "@/core/constant";
/*
 * key:value形式存储的路由表
 * */
const routesObj = ref<{ [key: string]: any }>({});

export const usePermissionStore = defineStore("permission", () => {
  const userStore = useUserStore();
  const cacheSystemId = localStorage.getItem(SYSTEM_ID_KEY);
  // console.log("usePermissionStore---localStorage.getItem(SYSTEM_ID_KEY)---", cacheSystemId);
  const hasRoles = ref(false);
  // Add systemId to store,use localstorage to cache systemId
  const systemId = ref<string>(cacheSystemId || "");
  const matchedSystems = ref([]);
  const isShowSystem = ref(false);
  const setSystemId = (sid: string) => {
    systemId.value = sid;
  };

  /**
   * 应用中所有的路由列表，包括静态路由和动态路由
   */
  const routes = ref<RouteRecordRaw[]>([]);

  /**
   * 混合模式左侧菜单列表
   */
  const mixLeftMenus = ref<RouteRecordRaw[]>([]);
  const systemList = ref(SYSTEM_LIST);
  /**
   * 生成动态路由
   * @param systemId - 系统ID (wms/oms/tms/pms/system)
   */
  function generateRoutes() {
    return new Promise<RouteRecordRaw[]>((resolve, reject) => {
      MenuAPI.getRoutes({ systemType: null })
        .then((data) => {
          // 判断是否有系统角色
          hasRoles.value = Object.values(data).some(
            (arr) => Array.isArray(arr) && arr.length > 0
          );

          // 根据 data 中的 key 匹配 systemList，生成新的系统列表
          matchedSystems.value = systemList.value.filter((system) =>
            Object.keys(data).includes(system.value)
          );

          isShowSystem.value = Object.keys(data).includes("supply");

          // 找到第一个数组长度不为0的key
          const firstNotEmptyKey = Object.entries(data).find(
            ([_, arr]) => Array.isArray(arr) && arr.length > 0
          )?.[0];
          // console.info('firstNotEmptyKey---------',firstNotEmptyKey);
          // console.info('cacheSystemId---------', cacheSystemId);
          // console.log('localStorage.getItem(SYSTEM_ID_KEY)---', localStorage.getItem(SYSTEM_ID_KEY));
          const cacheSystemId = localStorage.getItem(SYSTEM_ID_KEY);
          // cacheSystemId在退出登录和登录成功都会清除，保证只有在刷新页面时使用cacheSystemId作为选定的系统id
          // 如果cacheSystemId不在data中，则取firstNotEmptyKey
          if(cacheSystemId && !Object.keys(data).includes(cacheSystemId)){
            userStore.setSystemId(firstNotEmptyKey || "supply"); 
          } else {
            userStore.setSystemId(cacheSystemId || firstNotEmptyKey || "supply");
          }

          // 转换路由
          const dynamicRoutesObj: { [key: string]: RouteRecordRaw } = {};
          const dynamicRoutes = [];

          // 默认添加应用中心
          data["application"] = APPLICATIONCENTER;
          console.info(data);
          for (const key in data) {
            dynamicRoutesObj[key] = transformRoutes(data[key], key);
            dynamicRoutes.push(...dynamicRoutesObj[key]);
          }

          for (const key in dynamicRoutesObj) {
            routesObj.value[key] = dynamicRoutesObj[key];
            routes.value.push(...dynamicRoutesObj[key]);
          }

          routes.value = constantRoutes.concat(routes.value);

          dynamicRoutes.forEach((route: RouteRecordRaw) =>
            router.addRoute(route)
          );

          resolve(dynamicRoutes);
        })
        .catch(reject);
    });
  }

  /**
   * 混合模式菜单下根据顶部菜单路径设置左侧菜单
   *
   * @param topMenuPath - 顶部菜单路径
   */
  const setMixLeftMenus = (topMenuPath: string) => {
    const matchedItem = routes.value.find((item) => item.path === topMenuPath);
    if (matchedItem && matchedItem.children) {
      mixLeftMenus.value = matchedItem.children;
    }
  };

  return {
    hasRoles,
    routes,
    routesObj,
    generateRoutes,
    mixLeftMenus,
    setMixLeftMenus,
    systemId,
    setSystemId,
    matchedSystems,
    isShowSystem,
  };
});

/**
 * 转换路由数据为组件
 */

const transformRoutes = (routes: RouteVO[], systemId: string) => {
  const asyncRoutes: RouteRecordRaw[] = [];
  routes.forEach((route) => {
    const tmpRoute = { ...route } as RouteVO;

    // 添加系统前缀
    if (tmpRoute.path && tmpRoute.path.startsWith("/")) {
      tmpRoute.path = `/${systemId}${tmpRoute.path}`;
    }

    // 顶级目录，替换为 Layout 组件
    if (tmpRoute.componentPath?.toString() == "Layout") {
      tmpRoute.component = Layout as any;
    } else {
      // 其他菜单，根据组件路径动态加载组件
      // eslint-disable-next-line prettier/prettier
      if(tmpRoute.componentPath){
        const component =
          systemId === "supply" || systemId === "application"
            ? modules[`../../views/${tmpRoute.componentPath}.vue`]
            : modules[
                `../../../modules/${systemId}/views/${tmpRoute.componentPath}.vue`
              ];

        if (component) {
          tmpRoute.component = component as any; // Type assertion to handle dynamic import
        } else {
          tmpRoute.component = modules[`../../views/error-page/404.vue`] as any; // Type assertion for fallback
        }
      }
    }

    if (tmpRoute.children) {
      tmpRoute.children.forEach((ch) => {
        ch.meta = {
          title: ch.menuName,
        };
        if (ch.level == 2 && ch.visible == 1) {
          ch.meta.keepAlive = true;
          ch.name = ch.routeName;
        }
        // ch.name=ch.path?.substring(0,1).toUpperCase()+ ch.path?.substring(1,ch.path?.length)
      });
      tmpRoute.children = transformRoutes(route.children, systemId) as any;
    }

    asyncRoutes.push(tmpRoute);
  });

  return asyncRoutes;
};

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
