import request from "@/core/utils/request";

const BASE_URL = "/supply-wms/fast/sorting";

class API {
  /**
   * 列表
   */
  static getPageList(data: any) {
    return request({
      url: `${BASE_URL}/page`,
      method: "post",
      data: data,
    });
  }


  /**
   * 保存分拣信息
   * @param data
   */
  static saveOrder(data: any) {
    return request<any>({
      url: `${BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }

  /**
   * 获取分拣详情
   * @param sortingCode
   */
  static queryDetail(sortingCode: string) {
    return request<any>({
      url: `${BASE_URL}/detail/${sortingCode}`,
      method: "get",
    });
  }

  /**
   * 删除分拣单
   * @param sortingCode
   */
  static deleteOrder(sortingCode: string) {
    return request<any>({
      url: `${BASE_URL}/delete/${sortingCode}`,
      method: "post",
    });
  }


}

export default API;
