import { placeholderSign } from "element-plus/es/components/table-v2/src/private";

export default {
    userWareHouse: {
        label: {
            sort: "序号",
            userName: "账号",
            nickName: "姓名",
            mobile: "手机号",
            status: "状态",
            roleName: "角色",
            deptName: "部门",
            warehouseCoding: "仓库编码",
            warehouseName: "仓库名称",
            affiliatedDepartment:'所属部门',
        },
        placeholder: {

        },
        button: {
            assignAuth: '权限分配',
        },
        title: {
            selWareHouse: "选择仓库",
        },
        message: {
            deleteTips: "确定删除此部门吗？",
            deleteSucess: "删除成功！",
            deleteFail: "删除失败！",
            addSucess: "添加成功",
            editSucess: "编辑成功",
            deleteCancel: "已取消删除",
        },
        rules: {

        },
    },
};
