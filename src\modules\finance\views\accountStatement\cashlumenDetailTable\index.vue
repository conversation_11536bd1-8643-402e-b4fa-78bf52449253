<template>
  <div class="delivery-method-container-block">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true" label-width="84px">
        <el-form-item :label="t('cashlumenDetailTable.label.accountingPeriod')">
          <el-date-picker
            :clearable="false"
            @change="monthRangeChange"
            v-model="searchForm.period"
            type="monthrange"
            range-separator="至"
            :start-placeholder="t('bankJournal.label.startDate')"
            :end-placeholder="t('bankJournal.label.endDate')"
            format="YYYY-MM"
            value-format="YYYYMM"
            :disabled-date="
              (date) => {
                const start = loadPeriodList.periodYearMonthStart;
                const end = loadPeriodList.periodYearMonthEnd;
                if (!start || !end) return false;
                const y = date.getFullYear();
                const m = (date.getMonth() + 1).toString().padStart(2, '0');
                const ym = `${y}${m}`;
                return ym < start || ym > end;
              }
            "
          />
        </el-form-item>
        <el-form-item :label="t('cashlumenDetailTable.label.voucher')" prop="voucherWord">
          <el-select clearable v-model="searchForm.voucherWord" style="width: 100px; margin-right: 10px">
            <el-option v-for="item in voucherTypeList" :key="item.id" :label="item.voucherWord" :value="item.voucherWord" />
          </el-select>
          <el-input v-model="searchForm.voucherNumberStart" placeholder="" clearable style="width: 80px" />
          <span style="margin: 0 8px">{{ t('detailsTable.label.to') }}</span>
          <el-input v-model="searchForm.voucherNumberEnd" placeholder="" clearable style="width: 80px" />
        </el-form-item>
        <el-form-item :label="t('cashlumenDetailTable.label.accountingSubject')" prop="subjectId">
          <el-select :clearable="false" filterable v-model="searchForm.subjectId" :placeholder="t('cashlumenDetailTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="item in cashAccountList" :key="item.id" :label="item.subjectCode + '_' + item.subjectName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('cashlumenDetailTable.label.isReviewed')" prop="isAudit">
          <el-select clearable v-model="searchForm.isAudit" :placeholder="t('cashlumenDetailTable.label.pleaseSelect')" style="width: 200px">
            <el-option v-for="item in statusList" :key="item.statusId" :label="item.statusName" :value="item.statusId" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('cashlumenDetailTable.label.summary')" prop="summary">
          <el-input clearable v-model="searchForm.summary" :placeholder="t('cashlumenDetailTable.label.summaryInput')" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchHandlerEvent" v-hasPerm="['finance:cashlumenDetailTable:search']">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="onResetHandlerEvent" v-hasPerm="['finance:cashlumenDetailTable:reset']">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <section class="delivery-method-container">
      <div class="right-panel">
        <div class="table-container">
          <el-table ref="tableRef" v-loading="loading" :data="tableData" highlight-current-row stripe border>
            <template #empty>
              <Empty />
            </template>
            <el-table-column type="index" :label="t('cashlumenDetailTable.table.serialNo')" width="80" align="center" />
            <el-table-column :label="t('cashlumenDetailTable.table.voucherDate')" prop="voucherDate" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.voucherNo')" show-overflow-tooltip
              ><template #default="{ row }">
                <span @click="openVoucherBillDrawer(row.voucherId)" style="color: #762adb;cursor:pointer">{{ row.voucherWord }}<span>-</span>{{ row.voucherNumber }}</span>
              </template></el-table-column
            >
            <el-table-column :label="t('cashlumenDetailTable.table.voucherSummary')" prop="summary" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.account')" prop="methodName" show-overflow-tooltip min-width="120">
              <template #default="{ row }">
                <span>{{ `${row.subjectFullCode}_${row.subjectFullName}` }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="t('cashlumenDetailTable.table.debitAmount')" prop="debitAmount" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.creditAmount')" prop="creditAmount" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.flowAmount')" prop="occurAmount" show-overflow-tooltip />
            <el-table-column :label="t('cashlumenDetailTable.table.item')" prop="auxiliaryName" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
    </section>
    <voucherBillDrawer ref='voucherBillDrawerRef' v-model:visible="voucherBillDrawerVisible"></voucherBillDrawer>
  </div>
</template>

<script setup lang="ts">
// 现金流量项目明细
import voucherBillDrawer from '@/modules/finance/components/voucherBillDrawer.vue';
import { voucher } from '@/modules/finance/api/index';
import tableMixin from '@/modules/finance/mixins/table';
import API from '@/modules/finance/api/accountStatementApi';
import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import type { TVoucherTypeItem } from '@/modules/finance/types/voucher';
// defineOptions({
//   name: "CashlumenDetailTable",
//   inheritAttrs: false,
// })
const loadPeriodList = reactive({
  periodYearMonthEnd: '',
  periodYearMonthStart: '',
  currentPeriodYearMonth:''
});
const loadPeriod = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    loadPeriodList.periodYearMonthStart = String(response.periodYearMonthStart);
    loadPeriodList.periodYearMonthEnd = String(response.periodYearMonthEnd);
    loadPeriodList.currentPeriodYearMonth = String(response.currentPeriodYearMonth);
    searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
    searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
    searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
    loadcashAccountList();
  } catch (error) {
    if(error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    loading.value = false;
  }
};
const statusList = ref([
  {
    statusId: -1 ,
    statusName: t('cashlumenDetailTable.label.all'),
  },
  {
    statusId: 0,
    statusName: t('cashlumenDetailTable.label.pendingReview'),
  },
  {
    statusId: 1,
    statusName: t('cashlumenDetailTable.label.approved'),
  },
]);
const searchForm = reactive({
  period: [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth],
  voucherWord: undefined,
  subjectId: undefined,
  voucherNumberStart: undefined,
  voucherNumberEnd: undefined,
  summary: undefined,
  isAudit: undefined,
  isAudit1: undefined,
  periodYearMonthStart: loadPeriodList.currentPeriodYearMonth,
  periodYearMonthEnd: loadPeriodList.currentPeriodYearMonth,
});
const { loading, tableData, total, paginationInfo, headFormRef, router, path, onSearchHandler, onResetHandler, onPaginationChangeHandler, onDeleteHandler, onStatusChangeHandler } = tableMixin({
  searchForm,
  isLimit: false,
  tableGetApi: API.getCashFlowList,
});
const monthRangeChange = (val: [string, string]) => {
  if (val?.length) {
    searchForm.periodYearMonthStart = val[0];
    searchForm.periodYearMonthEnd = val[1];
  } else {
    searchForm.periodYearMonthStart = '';
    searchForm.periodYearMonthEnd = '';
  }
  searchForm.subjectId = undefined;
  loadcashAccountList()
};
const onSearchHandlerEvent = () => {
  // if (searchForm.isAudit1 === -1) {
  //   searchForm.isAudit = undefined;
  // } else {
  //   searchForm.isAudit = searchForm.isAudit1;
  // }
  onSearchHandler()
}
const onResetHandlerEvent = () => {
  searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
  searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
  searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
  searchForm.voucherNumberStart = undefined;
  searchForm.voucherNumberEnd = undefined;
  searchForm.isAudit = undefined;
  searchForm.subjectId = undefined;
  loadcashAccountList()
  onResetHandler();
};
/* 凭证字 */
const voucherTypeList = reactive<TVoucherTypeItem[]>([]);
const getVoucherTypeList = async () => {
  try {
    const res = await voucher.queryVoucherTypeList({});
    voucherTypeList.push(...res);
  } catch (e) {}
};
/* 会计科目 */
interface CashAccountItem {
  id: number | string;
  subjectCode: string;
  subjectName: string;
  statusId: number;
  statusName: string;
}
const cashAccountList = ref<CashAccountItem[]>([]);
const loadcashAccountList = async () => {
  try {
    const response = await API.getCashFlowSubjectSelect({
      periodYearMonthEnd: searchForm.periodYearMonthEnd,
      periodYearMonthStart: searchForm.periodYearMonthStart,
    });
    cashAccountList.value = response || [];
    onSearchHandler();
  } catch (error) {
    cashAccountList.value = [];
  }
};
const voucherBillDrawerVisible = ref(false);
const voucherBillDrawerRef = ref(null);
const openVoucherBillDrawer = (voucherId:string) => {
  if(!voucherId)return
  voucherBillDrawerVisible.value = true;
   if (voucherBillDrawerRef.value) {
    voucherBillDrawerRef.value.openDrawer('detail', voucherId);
  }
};
onMounted(() => {
  loadPeriod();
  getVoucherTypeList();
});
</script>

<style lang="scss" scoped>
.delivery-method-container {
  .right-panel {
    padding: 20px;
    background-color: #fff;

    .search-card,
    .toolbar-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }

    .search-card {
      :deep(.el-form--inline .el-form-item) {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 20px;
    }
  }
}

:deep(.el-table) {
  .el-switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
  }
}
</style>
