export default {
  detailsTable: {
    label: {
      accountingPeriod: "会计期间：",
      startAccount: "起始科目：",
      endAccount: "结束科目：",
      accountLevel: "科目级别：",
      pleaseSelect: "请选择",
      to: "至",
      summary: "摘要：",
      summaryInput: "请输入摘要",
      paixu: "排序依据：",
      voucherNoSort: "凭证号排序",
      voucherDateSort: "凭证日期排序",
      showContent: "显示内容：",
      start: "开始日期",
      end: "结束日期",
      showauxiliaryAccounting: "显示辅助核算",
      showOppositeAccount: "显示对方科目",
      showDetailAccount: "显示最明细科目",
      hideZeroBalance: "余额为0不显示",
      hideNoTransactionZeroBalance: "无发生额且余额为0不显示",
      hideSummaryIfNoTransaction: "无发生额不显示本期合计、本年累计",
      accountingSubject: "会计科目：",
      chooseaccountingSubject: "选择会计科目",
      subjectInputPrompt: "请输入科目编号和科目名称"
    },
    table: {
      serialNo: "序号",
      date: "日期",
      voucherNo: "凭证字号",
      account: "科目",
      summary: "摘要",
      debit: "借方",
      credit: "贷方",
      direction: "方向",
      balance: "余额"
    }
  }
}