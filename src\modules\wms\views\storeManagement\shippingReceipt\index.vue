<template>
  <div class="app-container">
    <div class="shippingReceipt">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form
            ref="queryFormRef"
            :model="queryParams"
            :inline="true"
            label-width="96px"
          >
            <!-- 收运单号 -->
            <el-form-item
              prop="receivingOrderCode"
              :label="$t('warehouseEntryNotice.label.receivingOrderCode')"
            >
              <el-input
                v-model="queryParams.receivingOrderCode"
                :placeholder="
                    $t('warehouseEntryNotice.placeholder.inputLimtTips')
                  "
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <!-- 入库通知单号 -->
            <el-form-item
              prop="receiptNoticeCode"
              :label="$t('warehouseEntryNotice.label.receiptNoticeCode')"
            >
              <el-input
                v-model="queryParams.receiptNoticeCode"
                :placeholder="
                    $t('warehouseEntryNotice.placeholder.inputLimtTips')
                  "
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <!-- 收运状态 -->
            <el-form-item
              :label="$t('warehouseEntryNotice.label.receivingStatus')"
              prop="isReceivedStatusList"
            >
              <el-select
                v-model="queryParams.isReceivedStatusList"
                multiple
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                collapse-tags
                collapse-tags-tooltip
                class="!w-[256px]"
              >
                <el-option
                  v-for="item in receivingStatusList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
            <!-- 状态 -->
            <el-form-item
              :label="$t('warehouseEntryNotice.label.status')"
              prop="status"
            >
              <el-select
                v-model="queryParams.statusList"
                multiple
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                collapse-tags
                collapse-tags-tooltip
                class="!w-[256px]"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
            <!-- 来源单号 -->
            <el-form-item
              prop="sourceOrderCode"
              :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
            >
              <el-input
                v-model="queryParams.sourceOrderCode"
                :placeholder="
                    $t('warehouseEntryNotice.placeholder.inputLimtTips')
                  "
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <!-- 是否领单 -->
            <el-form-item
              prop="useStatus"
              :label="$t('warehouseEntryNotice.label.isReceived')"
            >
              <el-select
                v-model="queryParams.useStatus"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[256px]"
              >
                <el-option
                  v-for="item in useStatusList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
            <!-- 时间-->
            <el-form-item
              prop="dateRange"
            >
              <el-select
                v-model="queryParams.queryType"
                :placeholder="$t('common.placeholder.selectTips')"
                class="!w-[200px] ml5px"
              >
                <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
              <el-date-picker
                :editable="false"
                class="!w-[370px]"
                v-model="dateRange"
                type="datetimerange"
                :range-separator="$t('warehouseEntryNotice.label.to')"
                :start-placeholder="$t('warehouseEntryNotice.label.startTime')"
                :end-placeholder="$t('warehouseEntryNotice.label.endTime')"
                :default-time="defaultTime"
                :placeholder="$t('common.placeholder.selectTips')"
              />
              <span
                class="ml16px mr14px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(1)"
              >
                  {{ $t("warehouseEntryNotice.label.today") }}
                </span>
              <span
                class="mr14px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(2)"
              >
                  {{ $t("warehouseEntryNotice.label.yesterday") }}
                </span>
              <span
                class="mr16px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="handleChangeDateRange(3)"
              >
                  {{ $t("warehouseEntryNotice.label.weekday") }}
                </span>
            </el-form-item>
            <el-form-item>
              <el-button
                v-hasPerm="['wms:storeManagement:shippingReceipt:search']"
                type="primary"
                @click="handleQuery"
              >
                {{ $t("common.search") }}
              </el-button>
              <el-button
                v-hasPerm="['wms:storeManagement:shippingReceipt:reset']"
                @click="handleResetQuery"
              >
                {{ $t("common.reset") }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <el-card class="content-card">
        <!--<template #header>
          <el-button
            type="primary"
            @click="handleAdd"
            v-hasPerm="['wms:storeManagement:shippingReceipt:add']"
          >
            {{ $t("warehouseEntryNotice.button.receivingOrderBtn") }}
          </el-button>
        </template>-->

        <el-table
          v-loading="loading"
          :data="tableList"
          highlight-current-row
          stripe
        >
          <template #empty>
            <Empty />
          </template>
          <el-table-column
            fixed="left"
            type="index"
            :label="$t('common.sort')"
            width="80"
            align="center"
          />
          <!-- 收运单号 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.receivingOrderCode')"
            prop="receivingOrderCode"
            show-overflow-tooltip
            min-width="180"
          />
          <!--计划量-->
          <el-table-column :label="$t('warehouseEntryNotice.label.plannedQty')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <div>{{$t('warehouseEntryNotice.label.productQty')}}：<span style="color: #90979E ">{{scope.row.productQty}}</span></div>
              <div>{{$t('warehouseEntryNotice.label.quantity')}}：<span style="color: #90979E ">{{scope.row.expectedQty?scope.row.expectedQty:'-'}}</span></div>
              <div>{{$t('warehouseEntryNotice.label.weight')}}：<span style="color: #90979E ">{{scope.row.expectedWeight?scope.row.expectedWeight:'-'}}</span></div>
            </template>
          </el-table-column>
          <!--收运量-->
          <el-table-column :label="$t('warehouseEntryNotice.label.receivedQuantity')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <div>{{$t('warehouseEntryNotice.label.productQty')}}：<span style="color: #90979E ">{{scope.row.skuQty}}</span></div>
              <div>{{$t('warehouseEntryNotice.label.quantity')}}：<span style="color: #90979E ">{{scope.row.receivedQty?scope.row.receivedQty:'-'}}</span></div>
              <div>{{$t('warehouseEntryNotice.label.weight')}}：<span style="color: #90979E ">{{scope.row.receivedWeight?scope.row.receivedWeight:'-'}}</span></div>
            </template>
          </el-table-column>
          <!--领用信息-->
          <el-table-column :label="$t('warehouseEntryNotice.label.useMessage')" min-width="220" show-overflow-tooltip>
            <template #default="scope">
              <div>{{$t('warehouseEntryNotice.label.useUserName')}}：<span style="color: #90979E ">{{scope.row.useUserName?scope.row.useUserName:'-'}}</span></div>
              <div>{{$t('warehouseEntryNotice.label.useTime')}}：<span style="color: #90979E ">{{scope.row.useTime?parseTime(scope.row.useTime, "{y}-{m}-{d} {h}:{i}:{s}"):'-'}}</span></div>
            </template>
          </el-table-column>
          <!--是否全部收运 isAllReceived-->
          <el-table-column :label="$t('warehouseEntryNotice.label.isAllReceived')" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.isAllReceived == 1">{{$t('warehouseEntryNotice.label.yes')}}</span>
              <span v-if="scope.row.isAllReceived == 0">{{$t('warehouseEntryNotice.label.not')}}</span>
            </template>
          </el-table-column>
          <!--是否分拣-->
          <el-table-column :label="$t('warehouseEntryNotice.label.isSorting')" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.isSorting == 1">{{$t('warehouseEntryNotice.label.yes')}}</span>
              <span v-if="scope.row.isSorting == 0">{{$t('warehouseEntryNotice.label.not')}}</span>
            </template>
          </el-table-column>
          <!-- 收运时间 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.receivingTime')"
            prop="receivingTime"
            show-overflow-tooltip
            min-width="180"
          >
            <template #default="scope">
              {{
              parseTime(scope.row.receivingTime, "{y}-{m}-{d} {h}:{i}:{s}")
              }}
            </template>
          </el-table-column>
          <!-- 收运人 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.receiver')"
            prop="receiver"
            show-overflow-tooltip
            min-width="110"
          />
          <!-- 供应商 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.supplierName')"
            prop="supplierName"
            show-overflow-tooltip
            min-width="110"
          />
          <!-- 客户 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.customerName')"
            prop="customerName"
            show-overflow-tooltip
            min-width="110"
          />
          <!-- 入库通知单号 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.receiptNoticeCode')"
            prop="receiptNoticeCode"
            show-overflow-tooltip
            min-width="180"
          />
          <!-- 来源单号 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
            prop="sourceOrderCode"
            show-overflow-tooltip
            min-width="180"
          />
          <!-- 入库 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.storageCode')"
            prop="entryOrderCodes"
            show-overflow-tooltip
            min-width="180"
          />
          <!-- 备注 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.remark')"
            prop="remark"
            show-overflow-tooltip
            min-width="130"
          />
          <!-- 收运状态 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.receivingStatus')"
            prop="receivedStatus"
            show-overflow-tooltip
            min-width="120"
            fixed="right"
          >
            <template #default="scope">
              <div class="purchase">
                <!-- 状态:0:初始、1:收运中 2:已收运 -->
                <div
                  v-if="scope.row.receivedStatus == 0"
                  type="success"
                  class="purchase-status purchase-status-color1"
                >
                  {{ t("warehouseEntryNotice.label.initial") }}
                </div>
                <div
                  v-else-if="scope.row.receivedStatus == 1"
                  type="info"
                  class="purchase-status purchase-status-color2"
                >
                  {{ t("warehouseEntryNotice.label.receivingShipped") }}
                </div>
                <div
                  v-else-if="scope.row.receivedStatus == 2"
                  type="info"
                  class="purchase-status purchase-status-color3"
                >
                  {{ t("warehouseEntryNotice.label.receivedShipped") }}
                </div>
                <div v-else>-</div>
              </div>
            </template>
          </el-table-column>
          <!-- 入库状态 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.status')"
            prop="status"
            show-overflow-tooltip
            min-width="120"
            fixed="right"
          >
            <template #default="scope">
              <div class="purchase">
                <!-- 状态:0:初始、1:部分入库 2:全部入库 -->
                <div
                  v-if="scope.row.status == 0"
                  type="success"
                  class="purchase-status purchase-status-color1"
                >
                  {{ t("warehouseEntryNotice.label.initial") }}
                </div>
                <div
                  v-else-if="scope.row.status == 1"
                  type="info"
                  class="purchase-status purchase-status-color2"
                >
                  {{ t("warehouseEntryNotice.label.partialStorage") }}
                </div>
                <div
                  v-else-if="scope.row.status == 2"
                  type="info"
                  class="purchase-status purchase-status-color3"
                >
                  {{ t("warehouseEntryNotice.label.allStorage") }}
                </div>
                <div v-else>-</div>
              </div>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column
            fixed="right"
            :label="$t('common.handle')"
            width="240"
          >
            <template #default="scope">
              <!-- 领单 -->
              <el-button
                v-if="scope.row.useStatus == 0 && scope.row.receivedStatus == 0"
                v-hasPerm="['wms:storeManagement:shippingReceipt:pickOrder']"
                type="primary"
                link
                @click="pickOrder(scope.row.id)"
              >
                {{ $t("common.pickOrder") }}
              </el-button>
              <!-- 取消领单 -->
              <el-button
                v-if="scope.row.useStatus == 1 && scope.row.receivedStatus == 0"
                v-hasPerm="['wms:storeManagement:shippingReceipt:cancelOrder']"
                type="primary"
                link
                @click="cancelOrder(scope.row.id)"
              >
                {{ $t("common.cancelOrder") }}
              </el-button>
              <!-- 去收运 -->
              <el-button
                v-if="scope.row.useStatus == 1 && scope.row.receivedStatus == 0 && scope.row.useUserId == userId"
                v-hasPerm="['wms:storeManagement:shippingReceipt:toReceivingOrders']"
                type="primary"
                link
                @click="toReceivingOrders(scope.row.id, scope.row.receivingOrderCode)"
              >
                {{ $t("warehouseEntryNotice.button.toReceive") }}
              </el-button>
              <!-- 详情 -->
              <el-button
                v-hasPerm="['wms:storeManagement:shippingReceipt:detail']"
                type="primary"
                link
                @click="handleCheck(scope.row.id, scope.row.receivingOrderCode)"
              >
                {{ $t("common.detailBtn") }}
              </el-button>
              <!-- 打印  -->
              <el-button
                v-if="scope.row.receivedStatus == 2"
                v-hasPerm="['wms:storeManagement:shippingReceipt:print']"
                type="primary"
                link
                @click="handerPrint(scope.row)"
              >
                {{ $t("common.print") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.limit"
            @pagination="handleQuery"
          />
        </div>
      </el-card>
    </div>
    <Print ref="printRef" class="display-none" />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "WMSShippingReceipt",
  inheritAttrs: false,
});
import shippingReceiptAPI, {
  shippingReceiptPageQuery,
} from "@/modules/wms/api/shippingReceipt";
import { useRouter } from "vue-router";
import { parseTime, changeDateRange } from "@/core/utils/index.js";
import { useUserStore } from "@/core/store";
import Print from "./components/print.vue";
import moment from "moment";
import { emitter } from "@/core/utils/eventBus";

const router = useRouter();
const { t } = useI18n();
const userId = localStorage.getItem("userId");
const userStore = useUserStore();
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const dateRange = ref([
  moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
  moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
]);

const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];
const receivingStatusList = ref([
  {
    key: 0,
    value: t("warehouseEntryNotice.label.initial"),
  },
  {
    key: 1,
    value: t("warehouseEntryNotice.label.receivingShipped"),
  },
  {
    key: 2,
    value: t("warehouseEntryNotice.label.receivedShipped"),
  },
]);
const statusList = ref([
  {
    key: 0,
    value: t("warehouseEntryNotice.label.initial"),
  },
  {
    key: 1,
    value: t("warehouseEntryNotice.label.partialStorage"),
  },
  {
    key: 2,
    value: t("warehouseEntryNotice.label.allStorage"),
  },
]);
const useStatusList = ref([
  {
    key: 0,
    value: t("warehouseEntryNotice.label.not"),
  },
  {
    key: 1,
    value: t("warehouseEntryNotice.label.yes"),
  },

]);
const dateTypeList = ref([
  {
    key: 1,
    value: t('warehouseEntryNotice.dateTypeList.receivingTime')
  },
  {
    key: 2,
    value:t('warehouseEntryNotice.dateTypeList.useTime')
  },
  {
    key: 3,
    value:t('warehouseEntryNotice.dateTypeList.lastUpdateTime')
  }
]);
const queryParams = reactive<shippingReceiptPageQuery>({
  page: 1,
  limit: 20,
  queryType:3,
});

const tableList = ref([]);
const printRef = ref();

/** 时间转换 */
function handleChangeDateRange(val: any) {
  dateRange.value = changeDateRange(val);
}

/** 查询 */
function handleQuery() {
  if (
    queryParams.receivingOrderCode &&
    queryParams.receivingOrderCode.length < 4
  ) {
    return ElMessage.error(t("warehouseEntryNotice.message.codeValideTips"));
  }
  if (
    queryParams.receiptNoticeCode &&
    queryParams.receiptNoticeCode.length < 4
  ) {
    return ElMessage.error(t("warehouseEntryNotice.message.codeValideTips"));
  }
  if (queryParams.sourceOrderCode && queryParams.sourceOrderCode.length < 4) {
    return ElMessage.error(t("warehouseEntryNotice.message.codeValideTips"));
  }

  loading.value = true;
  let params = {
    ...queryParams,
  };
  if (dateRange.value && dateRange.value.length == 2) {
    params.queryStartTime = new Date(dateRange.value[0]).getTime();
    params.queryEndTime = new Date(dateRange.value[1]).getTime();
  }
  shippingReceiptAPI
    .queryPageList(params)
    .then((data: any) => {
      tableList.value = data.records.map((item: any, index: any) => {
        item.mobilePhoneShow = true;
        return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  queryParams.queryType = 3;
  queryParams.statusList = [];
  dateRange.value = [
    moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
    moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
  ];
  handleQuery();
}

/** 新增*/
function handleAdd() {
  router.push({
    path: "/wms/storeManagement/addReceivingOrders",
  });
}

/** 去收运 */
function toReceivingOrders(id?: string, code?: string) {
  router.push({
    path: "/wms/storeManagement/toReceivingOrders",
    query: { id: id, receivingOrderCode: code },
  })
}

/** 详情*/
function handleCheck(id?: string, code?: string) {
  router.push({
    path: "/wms/storeManagement/receivingOrderDetail",
    query: { id: id, receivingOrderCode: code },
  });
}
/** 领单 */
function pickOrder(id) {
  let params = {
    receivingOrderId:id,
    isCancel:false
  }
  shippingReceiptAPI.receivingOrder(params).then((res)=>{
    ElMessage.success(t("warehouseEntryNotice.message.pickSuccess"));
    handleQuery();
  }).finally(()=>{

  })
}
/** 取消领单 */
function cancelOrder(id) {
   ElMessageBox.confirm(
     t("warehouseEntryNotice.message.cancelOrderTips"),
     t("common.tipTitle"),
     {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
     }
   ).then(
   () => {
      loading.value = true;
      let params = {
        receivingOrderId:id,
        isCancel:true
      }
      shippingReceiptAPI.receivingOrder(params).then((res)=>{
        ElMessage.success(t("warehouseEntryNotice.message.cancelSuccess"));
        handleQuery();
      }).finally(() => (loading.value = false));
    },
   () => {

    }
   );
}

const fetchDetailData = async (id: string) => {
  try {
    const data = await shippingReceiptAPI.queryDetail({ id: id });
    return data;
  } catch (error) {
    console.error("Failed to fetch detail data:", error);
    return null;
  } finally {
  }
};

/**打印*/
const print = ref(false);
async function handerPrint(row: any) {
  print.value = false;
  const detailData: any = await fetchDetailData(row.id);
  print.value = true;
  const printData = {
    title: t("warehouseEntryNotice.label.receivingOrderCodeCopy"),
    warehouseName: detailData.warehouseName || "-",
    receiver: detailData.receiver || "-",
    receivingOrderCode: detailData.receivingOrderCode || "-",
    receivingTime: detailData.receivingTime
      ? parseTime(detailData.receivingTime, "{y}-{m}-{d} {h}:{i}:{s}")
      : "-",

    receiptNoticeCode: detailData.receiptNoticeCode || "-",
    remark: detailData.remark || "-",
    printPerson: userStore.user.nickName || "-",
    printTime: moment().format("YYYY-MM-DD HH:mm:ss"), // 格式化时间
    items: [],
  };
  printData.items = detailData.productList.map((product: any) => ({
    /** 商品编码 */
    productCode: product.productCode || "-",

    /** 商品名称  */
    productName: product.productName || "-",

    /** 规格 */
    productSpecs: product.productSpecs || "-",

    /** 单位 */
    productUnitName: product.productUnitName || "-",

    /** 收运数量  */
    productActualQty: product.productActualQty || "-",

    /** 收运重量 */
    receivedWeight: product.receivedWeight || "-",

    /** 入库数量  */
    productInventoryQty: product.productInventoryQty || "-",

    /** 入库重量  */
    productInventoryWeight: product.productInventoryWeight || "-",
  }));

  // 执行打印
  nextTick(() => {
    printRef.value.handlePrint(printData);
  });
}
onActivated(() => {
  handleQuery();
  console.log("userStore===",userStore)
});
emitter.on("reloadListByWarehouseId", (e) => {
  nextTick(() => {
    handleQuery();
  });
});
</script>

<style lang="scss" scoped>
  :deep(.el-button--primary.el-button--default.is-link) {
    color: #762adb;
  }
  :deep(.el-button--danger.el-button--default.is-link) {
    color: #c00c1d;
  }
  .shippingReceipt{
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }
    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
</style>
