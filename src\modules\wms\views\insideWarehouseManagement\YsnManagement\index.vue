<template>
  <div class="app-container">
    <div class="ysnManagement">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
            <el-form-item prop="productQueryStr" :label="$t('ysnManagement.label.product')">
              <el-input class="!w-[256px]" v-model="queryParams.productQueryStr" :placeholder="$t('ysnManagement.placeholder.productPlaceholder')" clearable/>
            </el-form-item>
            <el-form-item prop="optTypeList" :label="$t('ysnManagement.label.optType')">
              <el-select
                v-model="queryParams.optTypeList"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                filterable
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-[256px]"
              >
                <el-option v-for="(item,index) in optTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('ysnManagement.label.printStatus')" prop="printStatusList">
              <el-select
                v-model="queryParams.printStatusList"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                filterable
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-[256px]"
              >
                <el-option v-for="item in printStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="newYsnCode" :label="$t('ysnManagement.label.newYsnCode')">
              <el-input class="!w-[256px]" v-model="queryParams.newYsnCode" :placeholder="$t('ysnManagement.placeholder.newYsnCodePlaceholder')" clearable/>
            </el-form-item>
            <el-form-item prop="oldYsnCode" :label="$t('ysnManagement.label.oldYsnCode')">
              <el-input class="!w-[256px]" v-model="queryParams.oldYsnCode" :placeholder="$t('ysnManagement.placeholder.oldYsnCodePlaceholder')" clearable/>
            </el-form-item>
            <el-form-item>
              <el-button v-hasPerm="['wms:ysnManagement:search']" type="primary" @click="handleQuery">
                {{$t('common.search')}}
              </el-button>
              <el-button v-hasPerm="['wms:ysnManagement:reset']" @click="handleResetQuery">
                {{$t('common.reset')}}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="content-card">
        <div class="action-bar">
          <el-button v-hasPerm="['wms:ysnManagement:add']"  type="primary" @click="addYSN">
            {{$t('ysnManagement.button.add')}}
          </el-button>
        </div>
        <el-table v-loading="loading" :data="tableData" highlight-current-row stripe>
          <template #empty>
            <Empty/>
          </template>
          <el-table-column type="index" :label="$t('common.sort')" width="60" />
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.productCode')" prop="productCode" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.productName')" prop="productName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.optType')" prop="optType" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.optType == 1">{{$t('ysnManagement.optTypeList.new')}}</span>
              <span v-else-if="scope.row.optType == 2">{{$t('ysnManagement.optTypeList.edit')}}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.quantity')" prop="quantity" show-overflow-tooltip>
            <template #default="scope">
              <el-button link type="primary" v-if="scope.row.quantity" @click="openDetail(scope.row)">{{scope.row.quantity}}</el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.oldYsnCode')" prop="oldYsnCode" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.newYsnCode')" prop="newYsnCode" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.weight')" prop="newWeight" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.updateUser')" prop="updateUserName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.updateTime')" prop="updateTime" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.updateTime ? parseDateTime(scope.row.updateTime, "dateTime") : '-'}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.printUser')" prop="printUserName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.printTime')" prop="printTime" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.printTime ? parseDateTime(scope.row.printTime, "dateTime") : '-'}}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.limit"
            @pagination="handleQuery"
          />
        </div>
      </el-card>
      <el-drawer v-model="showDialog" :title="$t('ysnManagement.title.createYSN')" :close-on-click-modal="false" size="850px" :before-close="onCloseHandler">
        <el-form :model="addForm" ref="addFormRef" label-position="top">
          <el-form-item :label="$t('ysnManagement.label.optType')" prop="optType" :rules="[{required:true,message:$t('ysnManagement.rule.optType')}]">
            <el-radio-group v-model="addForm.optType" @change="changeType">
              <el-radio :value="2">{{$t('ysnManagement.optTypeList.edit')}}</el-radio>
              <el-radio :value="1">{{$t('ysnManagement.optTypeList.new')}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="addForm.optType == 2">
            <el-form-item :label="$t('ysnManagement.label.oldYsnCode')" prop="oldYsnCode" :rules="[{required:true,message:$t('ysnManagement.rule.oldYsnCode')}]">
              <el-input type="text" :placeholder="$t('common.placeholder.inputTips')" @change="getProductInfo" v-model="addForm.oldYsnCode" clearable/>
            </el-form-item>
            <el-form-item :label="$t('ysnManagement.label.productMessage')" prop="productMessage">
              <el-input type="text" disabled :placeholder="$t('ysnManagement.placeholder.productMessage')" v-model="addForm.productMessage" clearable/>
            </el-form-item>
            <el-form-item :label="$t('ysnManagement.label.productWeight')" prop="productWeight">
              <el-input type="text" disabled :placeholder="$t('ysnManagement.placeholder.productWeight')" v-model="addForm.productWeight" clearable/>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item :label="$t('ysnManagement.label.productMessage')" prop="productCode" :rules="[{required:true,message:$t('ysnManagement.rule.productMessage'),trigger: 'blur',}]">
              <el-select :placeholder="$t('ysnManagement.placeholder.productPlaceholder')" v-model="addForm.productCode" filterable remote reserve-keyword :remote-method="remoteMethod" :loading="searchLoading" @change="setName" @clear="clearProduct">
                <el-option v-for="(item,index) in productList" :key="index" :value="item.productCode" :label="item.productCode + ' | ' + item.productName"/>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('ysnManagement.label.productWeight')" prop="productWeight" :rules="[{required:true,message:$t('ysnManagement.rule.productWeight'),trigger: 'blur',},{pattern:
                                        /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                      message: t(
                                        'ysnManagement.rule.productWeightFormat'
                                      ),
                                      trigger: 'blur',}]">
              <el-input type="text" :placeholder="$t('common.placeholder.inputTips')" v-model="addForm.productWeight" clearable><template #append>kg</template></el-input>
            </el-form-item>
            <el-form-item :label="$t('ysnManagement.label.quantity')" prop="quantity" :rules="[{required:true,message:$t('ysnManagement.rule.quantity'),trigger: 'blur',},{pattern:/(^[1-9]$|[1-4][0-9]$|[50]$)/,message:$t('ysnManagement.rule.quantityFormat'),trigger: 'blur',}]">
              <el-input type="text" :placeholder="$t('ysnManagement.placeholder.quantityPlaceholder')" v-model="addForm.quantity" clearable/>
            </el-form-item>
          </template>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onCloseHandler()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" @click="onSaveHandler">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
      </el-drawer>
      <el-dialog width="600" style="max-height: 87%;overflow: hidden;" v-model="detailDialog" :title="$t('ysnManagement.title.newYsnCode') + detailTitle" :close-on-click-modal="false" @close="close">
        <el-table v-loading="detailLoading" :data="detailDataList" highlight-current-row stripe :max-height="425" style="overflow:auto;">
          <template #empty>
            <Empty />
          </template>
          <el-table-column type="index" :label="$t('common.sort')" width="60" />
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.ysnCode')" prop="ysnCode" width="350" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('ysnManagement.tableColumnLabel.detailWeight')" align="right" prop="weight" show-overflow-tooltip></el-table-column>
        </el-table>
        <pagination
          v-if="detailTotal > 0"
          v-model:total="detailTotal"
          v-model:page="queryDetailParams.page"
          v-model:limit="queryDetailParams.limit"
          @pagination="handleQueryDetail"
        />
        <template #footer>
          <div class="dialog-footer" style="padding: 12px 0px;margin-right: 30px">
            <el-button @click="onCloseDetailHandler">{{ $t("ysnManagement.button.closeBtn") }}</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script setup lang="ts">
  import ysnManagementApi,{queryPageDto,queryPageResponse,queryDetailPageDto,queryDetailPageResponse,saveYSNDto} from "@/modules/wms/api/ysnManagement";
  import { parseDateTime } from "@/core/utils/index.js";
  defineOptions({
    name: "YsnManagement",
    inheritAttrs: false,
  });
  const { t } = useI18n();
  const queryFormRef = ref(null);
  const loading = ref(false);
  const tableData = ref<queryPageResponse[]>();
  const total = ref(0);
  const optTypeList = ref([
    {
      label:t('ysnManagement.optTypeList.new'),
      value:1,
    },
    {
      label:t('ysnManagement.optTypeList.edit'),
      value:2,
    },
  ]);
  const printStatusList = ref([
    {
      label:t('ysnManagement.printStatusList.unprinted'),
      value:0,
    },
    {
      label:t('ysnManagement.printStatusList.printed'),
      value:1,
    },
  ]);
  const queryParams = reactive<queryPageDto>({
    page: 1,
    limit: 20,
  });
  const showDialog = ref(false);
  const addForm = ref<saveYSNDto>({});
  const addFormRef = ref(ElForm);
  const searchLoading = ref(false);
  const productList = ref([]);
  const queryDetailParams = reactive<queryDetailPageDto>({
    page: 1,
    limit: 20,
  });
  const detailDialog = ref(false);
  const detailTitle = ref('');
  const detailLoading = ref(false);
  const detailTotal = ref(0);
  const detailDataList = ref<queryDetailPageResponse[]>();
  /** 远程搜索商品下拉列表 */
  function remoteMethod(queryString) {
    if(queryString){
      searchLoading.value = true
      let params = {
        keyword: queryString
      }
      ysnManagementApi.queryProductListByKeyword(params).then((res)=>{
        productList.value = res
      }).finally(() => {
        searchLoading.value = false
      })
    }
  }
  function changeType(val) {
    addForm.value.oldYsnCode = ''
    addForm.value.productMessage = ''
    addForm.value.productCode = ''
    addForm.value.productName = ''
    addForm.value.productSpec = ''
    addForm.value.firstCategoryId = ''
    addForm.value.firstCategoryName = ''
    addForm.value.secondCategoryId = ''
    addForm.value.secondCategoryName = ''
    addForm.value.thirdCategoryId = ''
    addForm.value.thirdCategoryName = ''
    addForm.value.productWeight = ''
    addForm.value.quantity = ''
    addForm.value.optType = val
    nextTick(()=>{
      addFormRef.value.clearValidate()
    })
    productList.value = []
  }
  function getProductInfo() {
    if(addForm.value.oldYsnCode == '' || addForm.value.oldYsnCode == null || addForm.value.oldYsnCode == undefined){
      addForm.value.productMessage = ''
      clearProduct()
    }else {
      let params = {
        ysnCode:addForm.value.oldYsnCode
      }
      ysnManagementApi.queryYsnDetailByObj(params).then((res)=>{
        addForm.value.productMessage = res.productCode + ' | ' + res.productName
        addForm.value.productWeight = res.actualWeight
        addForm.value.productCode = res.productCode
        addForm.value.productName = res.productName
        addForm.value.productSpec = res.productVO?res.productVO.productSpec:''
        addForm.value.firstCategoryId = res.productVO?res.productVO.firstCategoryId:''
        addForm.value.firstCategoryName = res.productVO?res.productVO.firstCategoryName:''
        addForm.value.secondCategoryId = res.productVO?res.productVO.secondCategoryId:''
        addForm.value.secondCategoryName = res.productVO?res.productVO.secondCategoryName:''
        addForm.value.thirdCategoryId = res.productVO?res.productVO.thirdCategoryId:''
        addForm.value.thirdCategoryName = res.productVO?res.productVO.thirdCategoryName:''
      }).catch(()=>{
        addForm.value.productMessage = ''
        addForm.value.productWeight = ''
        clearProduct()
      }).finally(()=>{

      })
    }
  }
  function setName() {
    if(addForm.value.productCode != '' && addForm.value.productCode != null && addForm.value.productCode != undefined){
      productList.value.forEach(list=>{
        if(addForm.value.productCode == list.productCode){
          addForm.value.productName = list.productName
          addForm.value.productSpec = list.productSpec
          addForm.value.firstCategoryId = list.firstCategoryId
          addForm.value.firstCategoryName = list.firstCategoryName
          addForm.value.secondCategoryId = list.secondCategoryId
          addForm.value.secondCategoryName = list.secondCategoryName
          addForm.value.thirdCategoryId = list.thirdCategoryId
          addForm.value.thirdCategoryName = list.thirdCategoryName
          addForm.value.productWeight = list.weight
        }
      })
    }else {
      addForm.value.productName = ''
      addForm.value.productSpec = ''
      addForm.value.firstCategoryId = ''
      addForm.value.firstCategoryName = ''
      addForm.value.secondCategoryId = ''
      addForm.value.secondCategoryName = ''
      addForm.value.thirdCategoryId = ''
      addForm.value.thirdCategoryName = ''
      addForm.value.productWeight = ''
    }
  }
  function clearProduct() {
    addForm.value.productCode = ''
    addForm.value.productName = ''
    addForm.value.productSpec = ''
    addForm.value.firstCategoryId = ''
    addForm.value.firstCategoryName = ''
    addForm.value.secondCategoryId = ''
    addForm.value.secondCategoryName = ''
    addForm.value.thirdCategoryId = ''
    addForm.value.thirdCategoryName = ''
    addForm.value.productWeight = ''
  }
  /** 查询YSN管理列表 */
  function handleQuery(){
    loading.value = true;
    let params = {
      ...queryParams,
    }
    ysnManagementApi.queryPage(params).then((data) =>{
      tableData.value = data.records
      total.value = parseInt(data.total)
    }).finally(()=>{
      loading.value = false;
    })
  }
  /** 重置查询 */
  function handleResetQuery() {
    queryFormRef.value.resetFields();
    queryParams.page = 1;
    queryParams.limit = 20;
    handleQuery();
  }
  /** 新增 */
  function addYSN(){
    showDialog.value = true
    addForm.value.optType = 2
  }
  /** 新增保存 */
  function onSaveHandler(){
    addFormRef.value.validate((valid: any) => {
      if (!valid) return;
      let params = {
        optType:addForm.value.optType,
        productCode:addForm.value.productCode,
        productName:addForm.value.productName,
        productSpec:addForm.value.productSpec,
        firstCategoryId:addForm.value.firstCategoryId,
        firstCategoryName:addForm.value.firstCategoryName,
        secondCategoryId:addForm.value.secondCategoryId,
        secondCategoryName:addForm.value.secondCategoryName,
        thirdCategoryId:addForm.value.thirdCategoryId,
        thirdCategoryName:addForm.value.thirdCategoryName,
        quantity:addForm.value.quantity,
        newWeight:addForm.value.productWeight
      }
      if(addForm.value.optType == 1){
        //新生成
        ysnManagementApi.add(params).then((res)=>{
          ElMessage.success(t("ysnManagement.message.addSuccess"));
          onCloseHandler();
          queryParams.page = 1;
          handleQuery();
        }).catch(()=>{

        }).finally(()=>{

        })
      }else {
        //更换
        params.oldYsnCode = addForm.value.oldYsnCode
        delete params.quantity
        ysnManagementApi.ysnChange(params).then((res)=>{
          ElMessage.success(t("ysnManagement.message.changeSuccess"));
          onCloseHandler();
          queryParams.page = 1;
          handleQuery();
        }).catch(()=>{

        }).finally(()=>{

        })

      }
    })
  }
  /** 取消新增 */
  function onCloseHandler(){
    showDialog.value = false
    addForm.value.optType = 2
    addForm.value.oldYsnCode = ''
    addForm.value.productMessage = ''
    addForm.value.productCode = ''
    addForm.value.productName = ''
    addForm.value.productSpec = ''
    addForm.value.firstCategoryId = ''
    addForm.value.firstCategoryName = ''
    addForm.value.secondCategoryId = ''
    addForm.value.secondCategoryName = ''
    addForm.value.thirdCategoryId = ''
    addForm.value.thirdCategoryName = ''
    addForm.value.productWeight = ''
    addForm.value.quantity = ''
    productList.value = []
    nextTick(() => {
      addFormRef.value?.clearValidate()
    })
  }
  function openDetail(row){
    queryDetailParams.id = row.id
    handleQueryDetail()
    detailTitle.value = '-' + row.productCode + ' | ' + row.productName
    detailDialog.value = true
  }
  function onCloseDetailHandler() {
    queryDetailParams.page = 1
    queryDetailParams.limit = 20
    detailDialog.value = false
    detailTitle.value = ''
    detailDataList.value = []
  }
  function handleQueryDetail(){
    detailLoading.value = true
    let params = {
      ...queryDetailParams
    }
    ysnManagementApi.queryDetail(params).then((res)=>{
      detailDataList.value = res.ysnManageDetailVOList.records
      detailTotal.value = parseInt(res.ysnManageDetailVOList.total)
    }).catch(()=>{
      detailLoading.value = false
    }).finally(()=>{
      detailLoading.value = false
    })
  }
  onActivated(() => {
    handleQuery();
  });
</script>
<style lang="scss" scoped>
  :deep(.el-button--primary.el-button--default.is-link) {
    color: #762adb;
  }
  :deep(.el-button--danger.el-button--default.is-link) {
    color: #c00c1d;
  }
  .ysnManagement{
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }

    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .action-bar {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
</style>
<style lang="scss">
  .ysnManagement{
    .el-dialog{
      padding: 0px;
      .el-dialog__header {
        padding: 16px 30px;
        border-bottom: 1px solid #E5E7F3;
        margin-bottom: 45px;
      }
      .el-dialog__body{
        padding: 0px 30px;
      }
      .el-dialog__footer{
        border-top: 1px solid #E5E7F3;
        margin-top: 16px;
      }
      .el-dialog__headerbtn{
        width: 58px;
        height: 58px;
        .el-icon{
          width: 22px;
          height: 22px;
        }
        .el-icon svg{
          width: 22px;
          height: 22px;
        }
      }
    }
  }
</style>

