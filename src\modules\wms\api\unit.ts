import request from "@/core/utils/request";

class unitAPI {
  /**
   * 获取分页列表
   *
   * @param queryParams 查询参数
   */
  static getPage(queryParams: any) {
    return request({
      url: `/supply-wms/product/unit/page`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 添加
   *
   * @param data 用户表单数据
   */
  static add(data: any) {
    return request({
      url: `/supply-wms/product/unit/save`,
      method: "post",
      data: data,
    });
  }

  /**
   * 修改
   *
   * @param id 用户ID
   * @param data 用户表单数据
   */
  static update(data: any) {
    return request({
      url: `/supply-wms/product/unit/update`,
      method: "post",
      data: data,
    });
  }
  /**
   * 删除
   *
   */
  static deleteByIds(data: any) {
    return request({
      url: `/supply-wms/product/unit/delete`,
      method: "post",
      data: data,
    });
  }

  /** 获取基本单位下拉数据源 */
  static getUnitList() {
    return request({
      url: `/supply-wms/product/unit/select`,
      method: "post",
      data:{}
    });
  }

}

export default unitAPI;

/**
 * 用户分页查询对象
 */
export interface PageQuery {
  unitName?: string;
  page?: number;
  limit?: number;
}

/** 用户表单类型 */
export interface unitForm {
  sort?: number;
  id?: string;
  unitName?: string;
}
