// form.ts  该文件主要封装了 新增或者编辑表单--可抽离的交互逻辑
import { ref, nextTick, toRaw } from "vue";
import { ElMessage, type MessageParamsWithType } from "element-plus";
import type { FormInstance } from "element-plus";
/*
 * @description usePage 接收一个 opts 参数，返回表单所需数据
 * @param {Object} opts.contentForm - 默认表单数据
 * @param {String} opts.idName - id别名
 * @param {Array} opts.uselessParams - 无用的表单字段
 * @param {Function} opts.formAddApi  - 新增表单的接口
 * @param {Function} opts.formEditApi  - 编辑表单的接口
 * @param {Function} opts.addCallback  - 新增事件回调函数
 * @param {Function} opts.editCallback  - 编辑事件回调函数
 * @param {Function} opts.saveCallback  - 提交事件回调函数
 * @param {Function} opts.closeCallback  - 取消事件回调函数
 * @param {Function} opts.formatParamsCallback  - 提交参数之前处理数据回调函数
 * @param {Function} opts.checkCallback  - 提交之前校验其他数据
 */
interface ResponseType {
  code: number;
  message: string;
  data: any;
}
export default function (opts?: any) {
  const {
    contentForm = {},
    idName = "id",
    uselessParams = [],
    formAddApi,
    formEditApi,
    addCallback = () => {},
    editCallback = () => {},
    saveCallback = () => {},
    closeCallback = () => {},
    formatParamsCallback = () => {},
    checkCallback = () => {},
  } = opts;

  const showDialog = ref(false);
  const dialogLoading = ref(false);
  const formType = ref("add");
  const contentFormRef = ref<FormInstance>();
  const { t } = useI18n();

  // 新增事件
  const onAddHandler = async () => {
    dialogLoading.value = true;
    formType.value = "add";
    showDialog.value = true;
    nextTick(() => {
      contentFormRef.value?.clearValidate();
    });
    await addCallback();
    dialogLoading.value = false;
  };
  // 编辑事件
  const onEditHandler = async (row: any) => {
    dialogLoading.value = true;
    formType.value = "edit";
    showDialog.value = true;
    // await nextTick();
    contentFormRef.value?.clearValidate();
    for (const k in contentForm) {
      contentForm[k] = row[k];
    }
    await editCallback(row);
    dialogLoading.value = false;
  };
  // 关闭弹窗事件
  const onCloseHandler = () => {
    if (!contentFormRef.value) return;
    contentFormRef.value.resetFields();
    closeCallback();
    showDialog.value = false;
  };
  // 提交事件
  const onSaveHandler = async () => {
    console.log("onSaveHandler");
    if (!contentFormRef.value) return;
    const flag = checkCallback();
    if (flag) {
      return;
    }
    await contentFormRef.value.validate((valid, fields) => {
      if (valid) {
        onUpdateHandler();
      } else {
        console.log(fields);
      }
    });
  };
  // 数据提交
  const onUpdateHandler = () => {
    dialogLoading.value = true;
    //使用structuredClone深克隆contentForm，防止其中有引用属性值在formatParamsCallback中修改的时候同步修改了contentForm的值
    // const params = structuredClone(toRaw(contentForm));
    // console.log("=======1======", contentForm);
    // const params = JSON.parse(JSON.stringify(toRaw(contentForm)));
    // console.log("====2======", params);
    const dataCopy = JSON.parse(JSON.stringify(contentForm));
    if (dataCopy && Array.isArray(dataCopy.imagesUrl)) {
      dataCopy.imagesUrl = JSON.stringify(dataCopy.imagesUrl);
    }
    const params: any = {
      ...dataCopy,
    };

    if (uselessParams.length) {
      uselessParams.forEach((item: string) => {
        delete params[item];
      });
    }
    if (formType.value === "add") {
      delete params[idName];
    }
    formatParamsCallback(params);
    const saveApi = formType.value === "add" ? formAddApi : formEditApi;
    const msg =
      formType.value === "add"
        ? t("common.addSuccess")
        : t("common.editSuccess");
    saveApi(params)
      .then((res: ResponseType) => {
        onCloseHandler();
        saveCallback();
        dialogLoading.value = false;
        ElMessage.success(msg);
      })
      .catch((err: MessageParamsWithType) => {
        dialogLoading.value = false;
        // ElMessage.error(err)
      });
  };

  return {
    showDialog,
    dialogLoading,
    formType,
    contentFormRef,
    onAddHandler,
    onEditHandler,
    onCloseHandler,
    onSaveHandler,
  };
}
