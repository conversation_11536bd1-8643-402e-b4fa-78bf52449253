<template>
  <div class="app-container">
    <div class="purchaseOrder">
      <div class="search-container">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="96px">
          <el-row :gutter="6">
            <el-col :span="6">
              <!-- 入库通知单号 -->
              <el-form-item prop="receiptNoticeCode" :label="$t('quickWarehousing.label.receiptNoticeCode')"
                class="w-full">
                <el-input v-model="queryParams.receiptNoticeCode"
                  :placeholder="$t('quickWarehousing.placeholder.inputTips')" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 主题描述 -->
              <el-form-item prop="themeDesc" :label="$t('quickWarehousing.label.themeDesc')" class="w-full">
                <el-input v-model="queryParams.themeDesc" :placeholder="$t('quickWarehousing.placeholder.inputTips')"
                  clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 入库类型 -->
              <el-form-item :label="$t('quickWarehousing.label.receiptType')" prop="receiptTypeList" class="w-full">
                <el-select v-model="queryParams.receiptTypeList" multiple
                  :placeholder="$t('quickWarehousing.placeholder.selectTips')" clearable collapse-tags
                  collapse-tags-tooltip class="w-full">
                  <el-option v-for="item in receiptTypeList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 状态 -->

            <el-col :span="6">
              <el-form-item :label="$t('quickWarehousing.label.status')" prop="statusList" class="w-full">
                <el-select v-model="queryParams.statusList" multiple
                  :placeholder="$t('quickWarehousing.placeholder.selectTips')" clearable collapse-tags
                  collapse-tags-tooltip class="w-full">
                  <el-option v-for="item in statusList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 来源单号 -->
              <el-form-item prop="sourceOrderCode" :label="$t('quickWarehousing.label.sourceOrderCode')" class="w-full">
                <el-input v-model="queryParams.sourceOrderCode"
                  :placeholder="$t('quickWarehousing.placeholder.inputTips')" class="w-full" clearable />
              </el-form-item>
            </el-col>
            <!-- 来源sourceList：1:手动创建 2:同步 -->
            <el-col :span="6">
              <el-form-item :label="$t('quickWarehousing.label.sourceList')" prop="sourceList" class="w-full">
                <el-select v-model="queryParams.sourceList" :placeholder="$t('quickWarehousing.placeholder.selectTips')"
                  class="w-full" multiple collapse-tags collapse-tags-tooltip>
                  <el-option v-for="item in sourceList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 时间-->
              <el-form-item prop="dateRange" lable="">
                <!-- 查询类型:1:计划交货时间、2:单据同步时间 -->
                <el-select v-model="queryParams.queryType" :placeholder="$t('quickWarehousing.placeholder.selectTips')"
                  class="!w-[140px] multipleSelect">
                  <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
                <el-date-picker :editable="false" class="!w-[356px]" v-model="dateRange" type="datetimerange"
                  :range-separator="$t('quickWarehousing.label.to')"
                  :start-placeholder="$t('quickWarehousing.label.startTime')"
                  :end-placeholder="$t('quickWarehousing.label.endTime')" :default-time="defaultTime"
                  :placeholder="$t('quickWarehousing.placeholder.selectTips')" />
                <span class="ml16px mr14px cursor-pointer" style="color: var(--el-color-primary)"
                  @click="handleChangeDateRange(1)">
                  {{ $t("quickWarehousing.label.today") }}
                </span>
                <span class="mr14px cursor-pointer" style="color: var(--el-color-primary)"
                  @click="handleChangeDateRange(2)">
                  {{ $t("quickWarehousing.label.yesterday") }}
                </span>
                <span class="mr16px cursor-pointer" style="color: var(--el-color-primary)"
                  @click="handleChangeDateRange(3)">
                  {{ $t("quickWarehousing.label.weekday") }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-button v-hasPerm="['wms:quickWarehousing:search']" type="primary" @click="handleQuery">
                  {{ $t("common.search") }}
                </el-button>
                <el-button v-hasPerm="['wms:quickWarehousing:search']" @click="handleResetQuery">
                  {{ $t("common.reset") }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </div>
      <el-card shadow="never" class="table-container">
        <template #header>
          <el-button type="primary" @click="handleAdd(undefined, 'add')" v-hasPerm="['wms:quickWarehousing:add']">
            {{ $t("quickWarehousing.button.addBtn") }}
          </el-button>
        </template>

        <el-table v-loading="loading" :data="tableList" highlight-current-row stripe :max-height="tableMaxHeight">
          <template #empty>
            <Empty />
          </template>
          <el-table-column type="index" :label="$t('common.sort')" width="60" align="center" fixed="left" />
          <!-- 主题描述 -->
          <el-table-column :label="$t('quickWarehousing.label.themeDesc')" min-width="180" prop="themeDesc"fixed="left" />
          <!-- 计划量 expectedWeight expectedWeight-->
          <el-table-column :label="$t('quickWarehousing.label.planQty')" prop="expectedWeight" show-overflow-tooltip
            min-width="180">
            <template #default="scope">
              <div>总量：{{ scope.row.expectedQty }} </div>
              <!-- <div>转换量：{{ scope.row.expectedWeight }}</div> -->
            </template>
          </el-table-column>
          <!-- 入库量 -->
          <el-table-column :label="$t('quickWarehousing.label.inQty')" prop="expectedWeight" show-overflow-tooltip
            min-width="180">
            <template #default="scope">
              <div>总量：{{ scope.row.totalInWarehouseQty }} </div>
              <!-- <div>转换量：{{ scope.row.totalInWarehouseWeight }}</div> -->
            </template>
          </el-table-column>
           <!-- 入库人 entryOperatorList-->
           <el-table-column :label="$t('quickWarehousing.label.entryOperatorList')" prop="entryOperatorList"
            show-overflow-tooltip min-width="220">
            <template #default="scope">
              <span>入库人：{{(scope.row.entryOperatorList || []).map((item: any) => item.entryOperator).join(',') || '-'}}
              </span>
              <span>({{(scope.row.entryOperatorList || []).map((item: any) => parseTime(item.entryTime, "{y}-{m}-{d} {h}:{i}:{s}")).join(',') || '-'}})</span>
            </template>
          </el-table-column>
           <!-- 供应商 supplierName-->
           <el-table-column :label="$t('quickWarehousing.label.supplierName')" prop="supplierName" show-overflow-tooltip
            min-width="110">
          </el-table-column>
          <!-- 打印 ("打印状态:0:否 1:是")-->
           <el-table-column :label="$t('quickWarehousing.label.print')" prop="printStatus" show-overflow-tooltip
            min-width="110">
            <template #default="scope">
              {{ scope.row.printStatus == 1 ? $t('common.statusYesOrNo.yes') : $t('common.statusYesOrNo.no') }}
              </template>
          </el-table-column>
          <!-- 计划交货时间 plannedDeliveryTime-->
          <el-table-column :label="$t('quickWarehousing.label.plannedDeliveryTime')" prop="plannedDeliveryTime"
            show-overflow-tooltip min-width="180">
            <template #default="scope">
              {{ parseTime(scope.row.plannedDeliveryTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
           <!-- 创建人 -->
           <el-table-column :label="$t('quickWarehousing.label.createUserName')" prop="createUserName"
            show-overflow-tooltip min-width="130" />
          <!-- 创建时间 -->
          <el-table-column :label="$t('quickWarehousing.label.createTime')" prop="createTime" show-overflow-tooltip
            min-width="170">
            <template #default="scope">
              {{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
           <!-- 入库通知单号 -->
          <el-table-column :label="$t('quickWarehousing.label.receiptNoticeCode')" prop="receiptNoticeCode"
            show-overflow-tooltip min-width="160" />
             <!-- 计划转换量 expectedWeight expectedWeight-->
          <el-table-column :label="$t('quickWarehousing.label.planQtyTransfer')" prop="expectedWeight" show-overflow-tooltip
            min-width="180">
            <template #default="scope">
              <!-- <div>总量：{{ scope.row.expectedQty }} </div> -->
              <div>转换量：{{ scope.row.expectedWeight }}</div>
            </template>
          </el-table-column>
          <!-- 入库转换量 -->
          <el-table-column :label="$t('quickWarehousing.label.inQtyTransfer')" prop="expectedWeight" show-overflow-tooltip
            min-width="180">
            <template #default="scope">
              <!-- <div>总量：{{ scope.row.totalInWarehouseQty }} </div> -->
              <div>转换量：{{ scope.row.totalInWarehouseWeight }}</div>
            </template>
          </el-table-column>
            <!-- 采购员/销售员 salesmanName-->
          <el-table-column :label="$t('quickWarehousing.label.salesmanName')" prop="purchaseSalesPerson"
            show-overflow-tooltip min-width="130">
          </el-table-column>
          <!-- 客户 customerName-->
          <el-table-column :label="$t('quickWarehousing.label.customerName')" prop="customerName" show-overflow-tooltip
            min-width="110">
          </el-table-column>
            <!-- 入库类型-->
          <el-table-column :label="$t('quickWarehousing.label.receiptType')" show-overflow-tooltip min-width="110">
            <template #default="scope">
              <span v-if="scope.row.receiptType == 1">{{ $t('quickWarehousing.label.purchaseInventory') }}</span>
              <span v-if="scope.row.receiptType == 2">{{ $t('quickWarehousing.label.returnStorage') }}</span>
              <span v-if="scope.row.receiptType == 3">{{ $t('quickWarehousing.label.transferInventory') }}</span>
              <span v-if="scope.row.receiptType == 4">{{ $t('quickWarehousing.label.directStorage') }}</span>
              <span v-if="scope.row.receiptType == 5">{{ $t('quickWarehousing.label.groundStorage') }}</span>
            </template>
          </el-table-column>
           <!-- 备注 remark-->
           <el-table-column :label="$t('quickWarehousing.label.remark')" prop="remark" show-overflow-tooltip
            min-width="110">
          </el-table-column>
          <!-- 来源单号 -->
          <el-table-column :label="$t('quickWarehousing.label.sourceOrderCode')" prop="sourceOrderCode"
            show-overflow-tooltip min-width="160" />
         
          <!-- 入库人 [{entryOperator入库人,entryTime入库时间}] -->
          <!--  <el-table-column :label="$t('quickWarehousing.label.entryOperator')" prop="entryOperator"
            show-overflow-tooltip min-width="110">
            <template #default="scope">
              <div>{{(scope.row.entryOperator || []).map((item: any) => item.entryOperator).join(',') || '-'}}</div>
            </template>
          </el-table-column> -->
         
          <!-- 状态 -->
          <el-table-column :label="$t('quickWarehousing.label.status')" prop="status" show-overflow-tooltip
            align="center" width="60" fixed="right">
            <template #default="scope">
              <div class="purchase w-50">
                <!-- 状态:0:草稿、1:初始 2：完结 3:取消 4:入库中 -->
                <div v-if="scope.row.status == 0" type="success" class="purchase-status purchase-status-color1 w-50">
                  {{ $t('quickWarehousing.label.draft') }}
                </div>
                <div v-if="scope.row.status == 1" type="success" class="purchase-status purchase-status-color1 w-50">
                  {{ $t('quickWarehousing.label.initial') }}
                </div>
                <div v-if="scope.row.status == 2" type="warning" class="purchase-status purchase-status-color2 w-50">
                  {{ $t('quickWarehousing.label.completed') }}
                </div>
                <div v-if="scope.row.status == 3" type="info" class="purchase-status purchase-status-color0 w-50">
                  {{ $t('quickWarehousing.label.canceled') }}
                </div>
                <div v-if="scope.row.status == 4" type="info" class="purchase-status purchase-status-color0 w-50">
                  {{ $t('quickWarehousing.label.processing') }}
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column fixed="right" :label="$t('common.handle')" width="80" align="center">
            <template #default="scope">
              <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, scope.row)">
                <el-button type="primary" size="small">
                  {{ $t('common.handle') }}
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- 编辑：状态=草稿 -->
                    <el-dropdown-item 
                      v-if="scope.row.status == 0" 
                      command="edit"
                      v-hasPerm="['wms:quickWarehousing:edit']">
                      <el-icon><Edit /></el-icon>
                      {{ $t("common.edit") }}
                    </el-dropdown-item>
                    
                    <!-- 删除：状态=草稿且来源=手工创建-->
                    <el-dropdown-item 
                      v-if="scope.row.status === 0 && scope.row.source === 1" 
                      command="delete"
                      v-hasPerm="['wms:quickWarehousing:delete']">
                      <el-icon><Delete /></el-icon>
                      {{ $t("common.delete") }}
                    </el-dropdown-item>
                    
                    <!-- 去入库：状态=初始或去入库 -->
                    <el-dropdown-item 
                      v-if="scope.row.status === 1 || scope.row.status === 4" 
                      command="goWarehousing"
                      v-hasPerm="['wms:quickWarehousing:goWarehousing']">
                      <el-icon><Box /></el-icon>
                      {{ $t("quickWarehousing.button.goWarehousing") }}
                    </el-dropdown-item>
                    
                    <!-- 完结：状态=入库中 -->
                    <el-dropdown-item 
                      v-if="scope.row.status === 4" 
                      command="complete"
                      v-hasPerm="['wms:quickWarehousing:complete']">
                      <el-icon><Check /></el-icon>
                      {{ $t("quickWarehousing.button.complete") }}
                    </el-dropdown-item>
                    
                    <!-- 打印：状态=入库中或完结 -->
                    <el-dropdown-item 
                      v-if="scope.row.status === 4 || scope.row.status === 2" 
                      command="print"
                      v-hasPerm="['wms:quickWarehousing:print']">
                      <el-icon><Printer /></el-icon>
                      {{ $t("common.print") }}
                    </el-dropdown-item>
                    
                    <!-- 详情：状态=入库中或完结或者取消 -->
                    <el-dropdown-item 
                      v-if="scope.row.status === 4 || scope.row.status === 2 || scope.row.status === 3"
                      command="detail"
                      v-hasPerm="['wms:quickWarehousing:detail']">
                      <el-icon><View /></el-icon>
                      {{ $t("common.detailBtn") }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.page"
          v-model:limit="queryParams.limit" @pagination="handleQuery" />
      </el-card>
    </div>
    <Print ref="printRef" class="display-none" />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "quickWarehousing",
  inheritAttrs: false,
});

import QuickWarehousingAPI, {
  QuickWarehousingPageQuery,
} from "@/modules/wms/api/quickWarehousing";
import { useUserStore } from "@/core/store";
import { useQuickWarehousing } from "./composables";

const { receiptTypeList } = useQuickWarehousing();
import { useNavigation } from "@/core/composables/useNavigation";
const { refreshAndNavigate } = useNavigation();
import { useRouter } from "vue-router";
import { parseTime, changeDateRange } from "@/core/utils/index.js";
import Print from "./components/print.vue";
import moment from "moment";
import { emitter } from "@/core/utils/eventBus";
const router = useRouter();
const { t } = useI18n();
const userStore = useUserStore();
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const dateRange = ref([
  moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
  moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
]);

const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];

const sourceList = ref([
  {
    key: 1,
    value: t("quickWarehousing.label.manualCreate"),
  },
  {
    key: 2,
    value: t("quickWarehousing.label.sync"),
  },
]);
/* const receiptTypeList = ref([
  // 入库类型:1:采购入库、2:退货入库、3:调拨入库、4:直接入库、5:地头入库
  {
    key: 1,
    value: t("quickWarehousing.label.purchaseInventory"),
  },
  {
    key: 2,
    value: t("quickWarehousing.label.returnStorage"),
  },
  {
    key: 3,
    value: t("quickWarehousing.label.transferInventory"),
  },
  {
    key: 4,
    value: t("quickWarehousing.label.directStorage"),
  },
  {
    key: 5,
    value: t("quickWarehousing.label.groundStorage"),
  },
]); */
const statusList = ref([
  //0：草稿， 1：初始，2：完结，3取消，4：入库中
  {
    key: 1,
    value: t("quickWarehousing.label.initial"),
  },
  {
    key: 2,
    value: t("quickWarehousing.label.completed"),
  },
  {
    key: 0,
    value: t("quickWarehousing.label.draft"),
  },
  {
    key: 3,
    value: t("quickWarehousing.label.canceled"),
  },
  {
    key: 4,
    value: t("quickWarehousing.label.processing"),
  },
]);
const dateTypeList = ref([
  {
    key: 1,
    value: t("quickWarehousing.label.plannedDeliveryTime"),
  },
  {
    key: 2,
    value: t("quickWarehousing.label.orderSyncTime"),
  },
]);

const queryParams = reactive<QuickWarehousingPageQuery>({
  queryType: 2,
  sourceList: [],
  page: 1,
  limit: 20,
  statusList: [0, 1, 4],
});

const tableList = ref([]);
const printRef = ref();
const tableMaxHeight = ref(600); // 添加表格最大高度响应式变量

// 计算表格最大高度
function calculateTableHeight() {
  nextTick(() => {
    const windowHeight = window.innerHeight;

    // 尝试获取实际DOM元素高度，如果获取不到则使用预估值
    const searchContainer = document.querySelector('.search-container');
    const cardHeader = document.querySelector('.el-card__header');
    const pagination = document.querySelector('.pagination');

    const searchFormHeight = searchContainer ? searchContainer.clientHeight + 20 : 140; // 搜索表单区域高度
    const cardHeaderHeight = cardHeader ? cardHeader.clientHeight : 60; // 卡片头部高度
    const paginationHeight = pagination ? pagination.clientHeight + 20 : 60; // 分页组件高度
    const otherHeight = 60; // 其他边距和padding

    tableMaxHeight.value = windowHeight - searchFormHeight - cardHeaderHeight - paginationHeight - otherHeight - 60;

    // 设置最小高度，避免表格过小
    if (tableMaxHeight.value < 300) {
      tableMaxHeight.value = 300;
    }
  });
}

function handleChangeDateRange(val: any) {
  const result = changeDateRange(val);
  if (result && Array.isArray(result)) {
    dateRange.value = result as string[];
  }
}

/** 查询 */
function handleQuery() {
  loading.value = true;
  let params: any = {
    ...queryParams,
  };
  if (dateRange.value && dateRange.value.length == 2) {
    params.queryStartTime = new Date(dateRange.value[0]).getTime();
    params.queryEndTime = new Date(dateRange.value[1]).getTime();
  }
  QuickWarehousingAPI
    .queryPageList(params)
    .then((data: any) => {
      tableList.value = data.records.map((item: any, index: any) => {
        return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.queryType = 1;
  queryParams.page = 1;
  queryParams.limit = 20;
  queryParams.receiptTypeList = [];
  queryParams.statusList = [];
  queryParams.receiptNoticeCode = '';
  queryParams.themeDesc = '';
  queryParams.sourceOrderCode = '';
  dateRange.value = [
    moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
    moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
  ];
  handleQuery();
}

/** 新增/编辑*/
function handleAdd(id?: string, type?: string) {
  let title = type == "edit" ? t("quickWarehousing.title.editQuickWarehousing") : t("quickWarehousing.title.addQuickWarehousing");
  refreshAndNavigate({
    path: "/wms/storeManagement/quickWarehousing/add",
    query: { title: title, id: id, type: type },
  });
}

/** 详情*/
function handleCheck(id?: string, code?: string, receiptType?: number) {
  router.push({
    path: "/wms/storeManagement/quickWarehousing/detail",
    query: { id: id, receiptNoticeCode: code, receiptType: receiptType },
  });
}

function handleDelete(id?: string) {
  ElMessageBox.confirm(
    t("quickWarehousing.message.deleteTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let params: any = {
        id: id,
      };
      QuickWarehousingAPI
        .delete(params)
        .then(() => {
          ElMessage.success(t("quickWarehousing.message.deleteSuccess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("quickWarehousing.message.deleteCancel"));
    }
  );
}

const fetchDetailData = async (id: string) => {
  try {
    const data = await QuickWarehousingAPI.queryDetail({ id: id });
    return data;
  } catch (error) {
    console.error("Failed to fetch detail data:", error);
    return null;
  } finally {
  }
};

/**打印*/
const print = ref(false);
async function handerPrint(row: any) {
  print.value = false;
  const detailData: any = await QuickWarehousingAPI.queryPrintDetail({ id: row.id });
  print.value = true;
  const printData = detailData;
  printData.receiptNoticeCode = detailData.warehouseReceiptNoticeVO.receiptNoticeCode;
  printData.supplierName = detailData.warehouseReceiptNoticeVO.supplierName;
  printData.title = "灵宝市高山天然果品有限公司入库单"
  // 执行打印
  nextTick(() => {
    printRef.value.handlePrint(printData);
  });
}

/** 编辑 */
function handleEdit(id?: string, type?: string, receiptType?: number) {
  refreshAndNavigate({
    path: "/wms/storeManagement/quickWarehousing/edit",
    query: { id: id, type: type, receiptType: receiptType },
  });
}

/** 去入库 */
function handleGoWarehousing(row: any) {
  refreshAndNavigate({
    path: "/wms/storeManagement/quickWarehousing/warehousing",
    query: { id: row.id, entryOrderCode: row.entryOrderCode, receiptType: row.receiptType },
  });
}

/** 完结 */
function handleComplete(row: any) {
  ElMessageBox.confirm(
    t("quickWarehousing.message.completeTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    loading.value = true;
    QuickWarehousingAPI
      .goWarehousingComplete({ id: row.id })
      .then(() => {
        ElMessage.success(t("quickWarehousing.message.completeSuccess"));
        handleQuery();
      })
      .catch((error: any) => {
        // ElMessage.error(error.message || t("quickWarehousing.message.completeFail"));
      })
      .finally(() => (loading.value = false));
  });
}

/** 处理下拉菜单命令 */
function handleDropdownCommand(command: string, row: any) {
  switch (command) {
    case 'edit':
      handleEdit(row.id, 'edit', row.receiptType);
      break;
    case 'delete':
      handleDelete(row.id);
      break;
    case 'goWarehousing':
      handleGoWarehousing(row);
      break;
    case 'complete':
      handleComplete(row);
      break;
    case 'print':
      handerPrint(row);
      break;
    case 'detail':
      handleCheck(row.id, row.receiptNoticeCode, row.receiptType);
      break;
    default:
      break;
  }
}

onActivated(() => {
  handleQuery();
});
emitter.on("reloadListByWarehouseId", (e) => {
  nextTick(() => {
    handleQuery();
  });
});
onMounted(() => {
  handleQuery();
  calculateTableHeight(); // 初始化计算表格高度
  window.addEventListener('resize', calculateTableHeight); // 监听窗口大小变化
});

onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight); // 移除监听器
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item ){
  margin-bottom: 16px;
}
.app-container {
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* .search-container{
  max-width: 100%;
} */
.encryptBox {
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  vertical-align: text-top;
}

.skuQtyColor {
  color: #762adb;
}

:deep(.multipleSelect .el-select__selected-item) {
  color: #151719 !important;
}
</style>
