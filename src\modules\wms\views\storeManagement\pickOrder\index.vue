<template>
    <div class="app-container">
        <div class="container-wrapper">
            <div class="search-container">
                <el-form
                  ref="queryFormRef"
                  :model="queryParams"
                  :inline="true"
                  label-width="96px"
                >
                    <el-form-item
                      prop="sortingCode"
                      :label="$t('pickOrder.label.sortingCode')"
                    >
                        <el-input
                          v-model="queryParams.sortingCode"
                          :placeholder="$t('pickOrder.placeholder.pickingCodeTip')"
                          clearable
                          class="!w-[256px]"
                        />
                    </el-form-item>

                    <el-form-item
                      prop="receiptCode"
                      :label="$t('pickOrder.label.receiptCode')"
                    >
                        <el-input
                          v-model="queryParams.receiptCode"
                          :placeholder="$t('pickOrder.placeholder.pickingCodeTip')"
                          clearable
                          class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item
                      prop="sortingStatus"
                      :label="$t('pickOrder.label.sortingStatus')"
                    >
                        <el-select
                          v-model="queryParams.sortingStatus"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                          class="!w-[256px]"
                        >
                            <el-option
                              v-for="(item, index) in sortingStatusOption"
                              :key="index"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                      prop="inventoryStatus"
                      :label="$t('pickOrder.label.inventoryStatus')"
                    >
                        <el-select
                          v-model="queryParams.inWarehouseStatus"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                          class="!w-[256px]"
                        >
                            <el-option
                              v-for="(item, index) in inWarehouseStatusOption"
                              :key="index"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                      prop="receiptStatus"
                      :label="$t('pickOrder.label.receiptStatus')"
                    >
                        <el-select
                          v-model="queryParams.receiptStatus"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                          class="!w-[256px]"
                        >
                            <el-option
                              v-for="(item, index) in receiptStatusOption"
                              :key="index"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                      prop="printStatus"
                      :label="$t('pickOrder.label.printStatus')"
                    >
                        <el-select
                          v-model="queryParams.printStatus"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                          class="!w-[256px]"
                        >
                            <el-option
                              v-for="(item, index) in printStatusOption"
                              :key="index"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-form-item prop="queryTimeType" style="margin-right: 5px">
                            <el-select
                              v-model="queryParams.queryTimeType"
                              :placeholder="$t('common.placeholder.selectTips')"
                              class="!w-[200px] ml5px"
                              @change="changeTimeType"
                            >
                                <el-option
                                  v-for="(item, index) in dateTypOption"
                                  :key="index"
                                  :label="item.label"
                                  :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item prop="dateRange">
                            <el-date-picker
                              :editable="false"
                              class="!w-[370px]"
                              v-model="queryParams.dateRange"
                              type="datetimerange"
                              :range-separator="$t('pickOrder.label.to')"
                              :start-placeholder="$t('pickOrder.label.startTime')"
                              :end-placeholder="$t('pickOrder.label.endTime')"
                              :default-time="defaultTime"
                              :placeholder="$t('common.placeholder.selectTips')"
                            />
                            <span
                              class="ml16px mr14px cursor-pointer"
                              style="color: var(--el-color-primary)"
                              @click="handleChangeDateRange(1)"
                            >
                {{ $t("pickOrder.label.today") }}
              </span>
                            <span
                              class="mr14px cursor-pointer"
                              style="color: var(--el-color-primary)"
                              @click="handleChangeDateRange(2)"
                            >
                {{ $t("pickOrder.label.yesterday") }}
              </span>
                            <span
                              class="mr16px cursor-pointer"
                              style="color: var(--el-color-primary)"
                              @click="handleChangeDateRange(3)"
                            >
                {{ $t("pickOrder.label.weekday") }}
              </span>
                        </el-form-item>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                          v-hasPerm="['wms:storeManagement:pickOrder:search']"
                          type="primary"
                          @click="handleQuery"
                        >
                            {{ $t("common.search") }}
                        </el-button>
                        <el-button
                          v-hasPerm="['wms:storeManagement:pickOrder:reset']"
                          @click="handleResetQuery"
                        >
                            {{ $t("common.reset") }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-card shadow="never" class="table-container">
                <el-table
                  v-loading="loading"
                  :data="tableData"
                  highlight-current-row
                  stripe
                >
                    <template #empty>
                        <Empty />
                    </template>
                    <el-table-column type="index" :label="$t('common.sort')" width="60" />
                    <el-table-column
                      :label="$t('pickOrder.label.sortingCode')"
                      prop="sortingCode"
                      show-overflow-tooltip
                      min-width="160px"
                    ></el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.receiptCode')"
                      prop="receiptCode"
                      show-overflow-tooltip
                      min-width="190px"
                    ></el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.receiptGoods')"
                      prop="receiptProductCount"
                      show-overflow-tooltip
                      min-width="160px"
                    >
                        <template #default="scope">
                            <div class="item">
                                {{ $t("pickOrder.label.totality") }}:{{
                                    scope.row.receiptProductCount
                                }}
                            </div>
                            <div class="item">
                                {{ $t("pickOrder.label.quantity") }}:{{
                                    scope.row.receiptProductQty
                                }}
                            </div>
                            <div class="item">
                                {{ $t("pickOrder.label.weight") }}(kg):{{
                                    scope.row.receiptProductWeight
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.sortedGoods')"
                      prop="sortingProductCount"
                      show-overflow-tooltip
                      min-width="160px"
                    >
                        <template #default="scope">
                            <div class="item">
                                {{ $t("pickOrder.label.totality") }}:{{
                                    scope.row.sortingProductCount
                                }}
                            </div>
                            <div class="item">
                                {{ $t("pickOrder.label.quantity") }}:{{
                                    scope.row.sortingProductQty
                                }}
                            </div>
                            <div class="item">
                                {{ $t("pickOrder.label.weight") }}(kg):{{
                                    scope.row.sortingProductWeight
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.inventoryGoods')"
                      prop="inventoryGoods"
                      show-overflow-tooltip
                      min-width="160px"
                    >
                        <template #default="scope">
                            <div class="item">
                                {{ $t("pickOrder.label.quantity") }}:{{
                                    scope.row.totalInWarehouseQty
                                }}
                            </div>
                            <div class="item">
                                {{ $t("pickOrder.label.weight") }}(kg):{{
                                    scope.row.totalInWarehouseWeight
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.receiveInfo')"
                      prop="handleUserName"
                      show-overflow-tooltip
                      min-width="160px"
                    >
                        <template #default="scope">
                            <div class="item">
                                {{ $t("pickOrder.label.receiveName") }}:{{
                                    scope.row.handleUserName
                                }}
                            </div>
                            <div class="item">
                                {{ $t("pickOrder.label.receiveTime") }}:{{
                                    parseDateTime(scope.row.handleTime, "dateTime")
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.printStatus')"
                      prop="printStatus"
                      show-overflow-tooltip
                      min-width="120px"
                    >
                        <template #default="scope">
                            <div
                              class="purchase"
                              v-html="filterPrintStatus(scope.row.printStatus)"
                            ></div>
                        </template>
                    </el-table-column>

                    <el-table-column
                      :label="$t('pickOrder.label.lastPrintUserName')"
                      prop="lastPrintUserName"
                      show-overflow-tooltip
                      min-width="120px"
                    ></el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.lastPrintTime')"
                      prop="lastPrintTime"
                      show-overflow-tooltip
                      min-width="120px"
                    >
                        <template #default="scope">
                            {{ parseDateTime(scope.row.lastPrintTime, "dateTime") }}
                        </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.lossOrderCode')"
                      prop="lossOrderCode"
                      show-overflow-tooltip
                      min-width="120px"
                    ></el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.createUserName')"
                      prop="createUserName"
                      show-overflow-tooltip
                      min-width="120px"
                    ></el-table-column>
                    <el-table-column
                      :label="$t('pickOrder.label.createTime')"
                      prop="createTime"
                      show-overflow-tooltip
                      min-width="120px"
                    >
                        <template #default="scope">
                            {{ parseDateTime(scope.row.createTime, "dateTime") }}
                        </template>
                    </el-table-column>
                    <el-table-column
                      fixed="right"
                      :label="$t('pickOrder.label.inventoryStatus')"
                      prop="inWarehouseStatus"
                      show-overflow-tooltip
                      min-width="120px"
                    >
                        <template #default="scope">
                            <div
                              class="purchase"
                              v-html="filterInventoryStatus(scope.row.inWarehouseStatus)"
                            ></div>
                        </template>
                    </el-table-column>
                    <el-table-column
                      fixed="right"
                      :label="$t('pickOrder.label.sortingStatus')"
                      prop="sortingStatus"
                      show-overflow-tooltip
                      min-width="120px"
                    >
                        <template #default="scope">
                            <div
                              class="purchase"
                              v-html="filterSortingStatus(scope.row.sortingStatus)"
                            ></div>
                        </template>
                    </el-table-column>

                    <el-table-column
                      fixed="right"
                      :label="$t('common.handle')"
                      width="160"
                    >
                        <template #default="scope">
                            <template v-if="scope.row.sortingStatus === 0">
                                <template v-if="scope.row.receiptStatus === 0">
                                    <el-button
                                      v-hasPerm="['wms:storeManagement:pickOrder:receive']"
                                      type="primary"
                                      link
                                      @click="handleReceive(scope.row)"
                                    >
                                        {{ $t("pickOrder.button.actionReceive") }}
                                    </el-button>
                                </template>
                                <template v-if="scope.row.receiptStatus === 1">
                                    <el-button
                                      v-hasPerm="['wms:storeManagement:pickOrder:release']"
                                      type="primary"
                                      link
                                      @click="handleCancelReceive(scope.row)"
                                    >
                                        {{ $t("pickOrder.button.cancelReceive") }}
                                    </el-button>

                                    <el-button
                                      v-if="userStore.user.userId==scope.row.handleUser"
                                      v-hasPerm="['wms:storeManagement:pickOrder:edit']"
                                      type="primary"
                                      link
                                      @click="handleDetail(scope.row, 'edit')"
                                    >
                                        {{ $t("pickOrder.button.actionSorting") }}
                                    </el-button>
                                </template>
                            </template>
                            <el-button
                              v-hasPerm="['wms:storeManagement:pickOrder:detail']"
                              type="primary"
                              link
                              @click="handleDetail(scope.row, 'detail')"
                            >
                                {{ $t("common.detailBtn") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                  v-if="total > 0"
                  v-model:total="total"
                  v-model:page="queryParams.page"
                  v-model:limit="queryParams.limit"
                  @pagination="handleQuery"
                />
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">
defineOptions({
    name: "pickOrder",
    inheritAttrs: false
});
import {
    changeDateRange,
    convertToTimestamp,
    parseDateTime,
    isEmpty
} from "@/core/utils";
import { emitter } from "@/core/utils/eventBus";
import moment from "moment";
import { useUserStore } from "@/core/store";

import API from "@/modules/wms/api/pickOrder";

import { useNavigation } from "@/core/composables/useNavigation";
const { refreshAndNavigate } = useNavigation();

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();

const state = reactive({
    loading: false,
    queryFormRef: null,
    queryParams: {
        page: 1,
        limit: 20,
        sortingCode: "",
        receiptCode: "",
        sortingStatus: [],
        inWarehouseStatus: [],
        receiptStatus: "",
        printStatus: [],
        queryTimeType: 2,
        dateRange: [
            moment()
              .subtract(29, "days")
              .startOf("days")
              .format("YYYY-MM-DD HH:mm:ss"),
            moment().endOf("days").format("YYYY-MM-DD HH:mm:ss")
        ]
    },
    defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
    ],
    total: 0,
    tableData: [],
    sortingStatusOption: [
        { label: t("pickOrder.label.sortingStatusOption[0]"), value: 0 },
        { label: t("pickOrder.label.sortingStatusOption[1]"), value: 1 },
        { label: t("pickOrder.label.sortingStatusOption[2]"), value: 2 }
    ],
    inWarehouseStatusOption: [
        { label: t("pickOrder.label.inventoryStatusOption[0]"), value: 0 },
        { label: t("pickOrder.label.inventoryStatusOption[1]"), value: 1 },
        { label: t("pickOrder.label.inventoryStatusOption[2]"), value: 2 }
    ],
    receiptStatusOption: [
        { label: t("pickOrder.label.receiptStatusOption[0]"), value: 0 },
        { label: t("pickOrder.label.receiptStatusOption[1]"), value: 1 }
    ],
    printStatusOption: [
        { label: t("pickOrder.label.printStatusOption[0]"), value: 0 },
        { label: t("pickOrder.label.printStatusOption[1]"), value: 1 },
        { label: t("pickOrder.label.printStatusOption[2]"), value: 2 }
    ],
    dateTypOption: [
        { label: t("pickOrder.label.receiptTime"), value: 1 },
        { label: t("pickOrder.label.sortingTime"), value: 2 },
        { label: t("pickOrder.label.receiveTime"), value: 3 }
    ]
}) as any;

const {
    loading,
    queryFormRef,
    queryParams,
    total,
    tableData,
    sortingStatusOption,
    inWarehouseStatusOption,
    receiptStatusOption,
    printStatusOption,
    dateTypOption,
    defaultTime
} = toRefs(state);

/**
 * 时间类型切换
 * @param val
 */
function changeTimeType(val:  any) {
    queryParams.value.dateRange = [
        moment()
          .subtract(29, "days")
          .startOf("days")
          .format("YYYY-MM-DD HH:mm:ss"),
        moment().endOf("days").format("YYYY-MM-DD HH:mm:ss")
    ]
}

/**
 * 时间转换
 * @param val
 */
function handleChangeDateRange(val: number) {
    queryParams.value.dateRange = changeDateRange(val);
    console.log(queryParams.value.dateRange);
}

/**
 * 搜索
 */
function handleQuery() {

    if (queryParams.value.sortingCode && queryParams.value.sortingCode.length < 4) {
        return ElMessage.error(t("pickOrder.rules.sortingCode"));
    }
    if (queryParams.value.receiptCode && queryParams.value.receiptCode.length < 4) {
        return ElMessage.error(t("pickOrder.rules.receiptCode"));
    }

    loading.value = true;
    const [startTime, endTime] = queryParams.value.dateRange || [];
    let params = {
        ...queryParams.value,
        startTime: convertToTimestamp(startTime + " 00:00:00"),
        endTime: convertToTimestamp(endTime + " 23:59:59")
    };
    delete params.dateRange;

    console.log(queryParams.value);
    API.getPageList(params)
      .then((res: any) => {
          const { records, pages } = res;
          tableData.value = records || [];
          total.value = parseInt(res.total);
      })
      .finally(() => {
          loading.value = false;
      });
}

/**
 * 重置
 */
function handleResetQuery() {
    queryFormRef.value.resetFields();
    queryParams.value.page = 1;
    queryParams.value.limit = 20;
    handleQuery();
}

/**
 * 领单
 * @param item
 */
function handleReceive(item: any) {
    let sortingCode = item.sortingCode;
    API.pickOrder(sortingCode).then(() => {
        ElMessage.success(t("pickOrder.message.actionSucess"));
        handleQuery();
    });
}

/**
 * 取消领单
 * @param item
 */
function handleCancelReceive(item: any) {
    ElMessageBox.confirm(
      t("pickOrder.message.receiveTips"),
      t("common.tipTitle"),
      {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: "warning"
      }
    ).then(() => {
        let sortingCode = item.sortingCode;
        API.releaseOrder(sortingCode).then(() => {
            ElMessage.success(t("pickOrder.message.actionSucess"));
            handleQuery();
        });
    });
}

/**
 * 详情
 * @param row
 * @param type
 */
function handleDetail(row: any, type: string) {
    refreshAndNavigate({
        path: "/wms/storeManagement/editPickOrder",
        query: {
            sortingCode: row.sortingCode,
            type
        }
    });
}

/**
 * 打印状态
 * @param val
 */

function filterPrintStatus(val: any) {
    let html = "-";
    if (!isEmpty(val)) {
        html = `<span class="purchase-status  purchase-status-color${val + 1}">${t(`pickOrder.label.printStatusOption[${val}]`)}</span>`;
    }
    return html;
}

/**
 * 入库状态
 * @param val
 */

function filterInventoryStatus(val: any) {
    let html = "-";
    if (!isEmpty(val)) {
        html = `<span class="purchase-status  purchase-status-color${val + 1}">${t(`pickOrder.label.inventoryStatusOption[${val}]`)}</span>`;
    }

    return html;
}

/**
 * 分拣状态
 * @param val
 */

function filterSortingStatus(val: any) {
    let html = "-";
    if (!isEmpty(val)) {
        html = `<span class="purchase-status  purchase-status-color${val + 1}">${t(`pickOrder.label.sortingStatusOption[${val}]`)}</span>`;
    }

    return html;
}

onActivated(() => {
    handleQuery();
});

emitter.on("reloadListByWarehouseId", (e) => {
    nextTick(() => {
        handleQuery();
    });
});
</script>

<style lang="scss" scoped>
.container-wrapper {
}
</style>
