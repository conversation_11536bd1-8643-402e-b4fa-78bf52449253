<template>
  <div class="incomestatement-pageContainer">
    <div class="filter-container">
      <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
        <el-menu-item index="0">{{ $t('incomestatement.label.month') }}</el-menu-item>
        <el-menu-item index="1">{{ $t('incomestatement.label.quarter') }}</el-menu-item>
      </el-menu>
    </div>
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true" label-width="84px">
        <el-form-item v-if="activeIndex == '0'" :label="t('incomestatement.label.accountingPeriod')" prop="periodYearMonth">
          <el-date-picker :clearable="false" format="YYYY-MM" value-format="YYYYMM" v-model="searchForm.periodYearMonth" type="month" :placeholder="t('incomestatement.label.start')" />
        </el-form-item>

        <el-form-item prop="year" v-if="activeIndex == '1'" :label="t('incomestatement.label.accountingPeriod')">
          <el-date-picker :clearable="false" @change="yearRangeChange" value-format="YYYY" format="YYYY 年" v-model="searchForm.year" type="year" :placeholder="t('incomestatement.label.selectYear')" style="width: 150px" />
          <el-select :clearable="false" learable :disabled="!searchForm.year" v-model="searchForm.quarter" :placeholder="t('incomestatement.label.selectQuarter')" style="width: 120px; margin-left: 10px">
            <el-option :label="t('incomestatement.label.firstQuarter')" value="1" />
            <el-option :label="t('incomestatement.label.secondQuarter')" value="2" />
            <el-option :label="t('incomestatement.label.thirdQuarter')" value="3" />
            <el-option :label="t('incomestatement.label.fourthQuarter')" value="4" />
          </el-select>
        </el-form-item>

        <el-form-item :label="t('incomestatement.label.showType')" prop="isShowPrevious" @change="onSearchHandler">
          <el-checkbox :true-value="1" :false-value="0" v-model="searchForm.isShowPrevious" v-if="activeIndex == '1'" :value="t('incomestatement.label.currentYearAllQuarters')">{{ t('incomestatement.label.currentYearAllQuarters') }}</el-checkbox>
          <el-checkbox :true-value="1" :false-value="0" v-model="searchForm.isShowPrevious" v-if="activeIndex == '0'" :value="t('incomestatement.label.previousPeriodAmount')">{{ t('incomestatement.label.previousPeriodAmount') }}</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchHandlerEvent" v-hasPerm="['finance:cashflowStatement:search']">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="onResetHandlerEvent" v-hasPerm="['finance:cashflowStatement:reset']">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <section class="delivery-method-container">
      <div class="right-panel">
        <div class="table-container">
          <!-- <div class="toolbar-card">
            <el-button type="primary">{{ t('incomestatement.button.export') }}</el-button>
            <el-button type="default">{{ t('incomestatement.button.print') }}</el-button>
          </div> -->
          <el-table v-loading="loading" :data="tableData" row-key="id" style="width: 100%" :row-class-name="tableRowClassName" :tree-props="{ children: 'childrenList', hasChildren: 'hasChildren' }" ref="treeTable" :default-expand-all="true">
            <template #empty>
              <Empty />
            </template>
            <!-- 折叠按钮和名称 -->
            <el-table-column min-width="200px" prop="projectName" :label="t('incomestatement.table.project')">
              <template #default="scope">
                <span class="name" :style="scope.row.isParent ? 'color: #762ADB; font-weight: 500;' : ''">
                  {{ scope.row.projectName }}
                  <!-- <span class="edit-btn" @click="editFormula(scope.row)">
                    <el-icon>
                      <Edit />
                    </el-icon>
                    <span>编辑公式</span></span
                  > -->
                </span>
              </template>
            </el-table-column>
            <!-- 其他列 -->
            <el-table-column prop="lineNo" :label="t('incomestatement.table.lineNo')"></el-table-column>
            <!-- 本年累计金额 -->
            <el-table-column prop="currentYearAmount" :label="t('incomestatement.table.yearToDateAmount')">
              <template #default="scope">
                <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto" v-if="scope.row.currentYearAmount">
                  <template #default>
                    <div style="max-height: 260px; overflow-y: auto">
                      <div v-for="(item, index) in scope.row.formulaVoList">
                        <span v-if="item.formulaOperator == 1">+</span>
                        <span v-if="item.formulaOperator == 2">-</span>
                        <span>{{ item.dataProjectName }}</span>
                        <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                        <span class="pl10">{{ item.currentYearAmount }}</span>
                      </div>
                    </div>
                  </template>
                  <template #reference>
                    <div class="formuladescription">
                      <span> {{ scope.row.currentYearAmount }}</span>
                      <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                    </div>
                  </template>
                </el-popover>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <!-- 本期金额 -->
            <el-table-column prop="currentPeriodAmount" v-if="activeIndex == '0' || (activeIndex == '1' && searchForm.isShowPrevious === 0)" :label="t('incomestatement.table.currentPeriodAmount')">
              <template #default="scope">
                <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto" v-if="scope.row.currentPeriodAmount">
                  <template #default>
                    <div style="max-height: 260px; overflow-y: auto">
                      <div v-for="(item, index) in scope.row.formulaVoList">
                        <span v-if="item.formulaOperator == 1">+</span>
                        <span v-if="item.formulaOperator == 2">-</span>
                        <span>{{ item.dataProjectName }}</span>
                        <span v-if="item.formulaRulesName" >( {{ item.formulaRulesName }} )</span>
                        <span class="pl10">{{ item.currentPeriodAmount }}</span>
                      </div>
                    </div>
                  </template>
                  <template #reference>
                    <div class="formuladescription">
                      <span> {{ scope.row.currentPeriodAmount }}</span>
                      <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                    </div>
                  </template>
                </el-popover>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <!-- 上期金额 -->
            <el-table-column prop="previousPeriodAmount" :label="t('incomestatement.table.previousPeriodAmount')" v-if="activeIndex == '0' && searchForm.isShowPrevious === 1">
              <!-- <template #default="scope">
                <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto" v-if="scope.row.previousPeriodAmount">
                  <template #default>
                    <div style="max-height: 260px; overflow-y: auto">
                      <div v-for="(item, index) in scope.row.formulaVoList">
                        <span v-if="item.formulaOperator == 1">+</span>
                        <span v-if="item.formulaOperator == 2">-</span>
                        <span>{{ item.dataProjectName }}</span>
                        <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                        <span class="pl10">{{ item.previousPeriodAmount }}</span>
                      </div>
                    </div>
                  </template>
                  <template #reference>
                    <div class="formuladescription">
                      <span> {{ scope.row.previousPeriodAmount }}</span>
                      <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                    </div>
                  </template>
                </el-popover>
                <div v-else>-</div>
              </template> -->
            </el-table-column>
            <el-table-column prop="firstPeriodAmount" :label="t('incomestatement.table.firstQuarter')" v-if="activeIndex == '1' && searchForm.isShowPrevious === 1">
              <!-- <template #default="scope">
                <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto" v-if="scope.row.firstPeriodAmount">
                  <template #default>
                    <div style="max-height: 260px; overflow-y: auto">
                      <div v-for="(item, index) in scope.row.formulaVoList">
                        <span v-if="item.formulaOperator == 1">+</span>
                        <span v-if="item.formulaOperator == 2">-</span>
                        <span>{{ item.dataProjectName }}</span>
                        <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                        <span class="pl10">{{ item.firstPeriodAmount }}</span>
                      </div>
                    </div>
                  </template>
                  <template #reference>
                    <div class="formuladescription">
                      <span> {{ scope.row.firstPeriodAmount }}</span>
                      <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                    </div>
                  </template>
                </el-popover>
                <div v-else>-</div>
              </template> -->
            </el-table-column>
            <el-table-column prop="secondPeriodAmount" :label="t('incomestatement.table.secondQuarter')" v-if="activeIndex == '1' && searchForm.isShowPrevious === 1">
              <!-- <template #default="scope">
                <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto" v-if="scope.row.secondPeriodAmount">
                  <template #default>
                    <div style="max-height: 260px; overflow-y: auto">
                      <div v-for="(item, index) in scope.row.formulaVoList">
                        <span v-if="item.formulaOperator == 1">+</span>
                        <span v-if="item.formulaOperator == 2">-</span>
                        <span>{{ item.dataProjectName }}</span>
                        <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                        <span class="pl10">{{ item.secondPeriodAmount }}</span>
                      </div>
                    </div>
                  </template>
                  <template #reference>
                    <div class="formuladescription">
                      <span> {{ scope.row.secondPeriodAmount }}</span>
                      <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                    </div>
                  </template>
                </el-popover>
                <div v-else>-</div>
              </template> -->
            </el-table-column>
            <el-table-column prop="thirdPeriodAmount" :label="t('incomestatement.table.thirdQuarter')" v-if="activeIndex == '1' && searchForm.isShowPrevious === 1">
              <!-- <template #default="scope">
                <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto" v-if="scope.row.thirdPeriodAmount">
                  <template #default>
                    <div style="max-height: 260px; overflow-y: auto">
                      <div v-for="(item, index) in scope.row.formulaVoList">
                        <span v-if="item.formulaOperator == 1">+</span>
                        <span v-if="item.formulaOperator == 2">-</span>
                        <span>{{ item.dataProjectName }}</span>
                        <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                        <span class="pl10">{{ item.thirdPeriodAmount }}</span>
                      </div>
                    </div>
                  </template>
                  <template #reference>
                    <div class="formuladescription">
                      <span> {{ scope.row.thirdPeriodAmount }}</span>
                      <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                    </div>
                  </template>
                </el-popover>
                <div v-else>-</div>
              </template> -->
            </el-table-column>
            <el-table-column prop="fourPeriodAmount" :label="t('incomestatement.table.fourthQuarter')" v-if="activeIndex == '1' && searchForm.isShowPrevious === 1">
              <!-- <template #default="scope">
                <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto" v-if="scope.row.fourPeriodAmount">
                  <template #default>
                    <div style="max-height: 260px; overflow-y: auto">
                      <div v-for="(item, index) in scope.row.formulaVoList">
                        <span v-if="item.formulaOperator == 1">+</span>
                        <span v-if="item.formulaOperator == 2">-</span>
                        <span>{{ item.dataProjectName }}</span>
                        <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                        <span class="pl10">{{ item.fourPeriodAmount }}</span>
                      </div>
                    </div>
                  </template>
                  <template #reference>
                    <div class="formuladescription">
                      <span> {{ scope.row.fourPeriodAmount }}</span>
                      <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                    </div>
                  </template>
                </el-popover>
                <div v-else>-</div>
              </template> -->
            </el-table-column>
          </el-table>
        </div>
      </div>
    </section>
    <!-- 其他内容 -->
    <FormulaDialog v-model:visible="formulaDialogVisible" :formulaVoList="formulaVoList" @save="onFormulaSave" />
  </div>
</template>
<script setup lang="ts">
// 利润表月度-季度 月度
import { ref, reactive, onMounted } from 'vue';
import tableMixin from '@/modules/finance/mixins/table';
import API from '@/modules/finance/api/accountStatementApi';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import FormulaDialog from '../formula/index.vue';
const activeIndex = ref('0');
const loadPeriodList = reactive({
  currentPeriodYearMonth: '',
  currentPeriodYearQuarter: '',
});
const loadPeriod = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    loadPeriodList.currentPeriodYearMonth = String(response.currentPeriodYearMonth);
    loadPeriodList.currentPeriodYearQuarter = String(response.currentPeriodYearQuarter);
    searchForm.year = loadPeriodList.currentPeriodYearQuarter.substring(0, 4); // 截取前4位 "2025"
    searchForm.quarter = loadPeriodList.currentPeriodYearQuarter.substring(5); // 从第5位开始截取 "2"
    searchForm.periodYearMonth = loadPeriodList.currentPeriodYearMonth;
    onSearchHandler();
  } catch (error) {
    if (error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    loading.value = false;
  }
};
function handleSelect(key: string) {
  activeIndex.value = key;
  if (Number(activeIndex.value) === 0) {
    searchForm.isQuarter = 0;
    searchForm.periodYearMonth = loadPeriodList.currentPeriodYearMonth;
    searchForm.quarter = undefined;
    searchForm.year = undefined;
  } else {
    searchForm.isQuarter = 1;
    searchForm.quarter = loadPeriodList.currentPeriodYearQuarter.substring(5); // 从第5位开始截取 "2"
    searchForm.year = loadPeriodList.currentPeriodYearQuarter.substring(0, 4); // 截取前4位 "2025"
    searchForm.periodYearMonth = searchForm.year && searchForm.quarter ? searchForm.year + searchForm.quarter : '';
  }
  searchForm.isShowPrevious = 0;
  onSearchHandler();
}

// 合并一级行的部分列
const spanMethod = ({ row, columnIndex }: { row: any; columnIndex: number }) => {
  if (row.isParent) {
    if (columnIndex === 0) {
      return [1, 7]; // 选择框和名称列不合并
    }
    return [1, 0]; // 其他列合并隐藏
  }
};

function tableRowClassName({ row }: { row: any; column: any }) {
  if (row.isParent) {
    return 'red-cell';
  }
  return '';
}

const searchForm = reactive<{
  periodYearMonth: string;
  projectType: string;
  isShowPrevious: number;
  isQuarter: number;
  year: string | undefined;
  quarter: string | undefined;
}>({
  periodYearMonth: loadPeriodList.currentPeriodYearMonth,
  projectType: '4',
  isShowPrevious: 0,
  isQuarter: Number(activeIndex.value),
  year: undefined,
  quarter: undefined,
});

const formulaDialogVisible = ref(false);
const formulaVoList = ref([]); // 传递给弹框的数据

// 若 editFormula 需要用到，建议在模板中解开注释
// function editFormula(row: any) {
//   formulaVoList.value = row.formulaVoList ? JSON.parse(JSON.stringify(row.formulaVoList)) : []
//   console.log(formulaVoList.value, 'formulaVoListformulaVoList')
//   formulaDialogVisible.value = true
// }

function onFormulaSave(row: any) {}

const { loading, tableData, headFormRef, router, onSearchHandler, onResetHandler } = tableMixin({
  searchForm,
  isLimit: false,
  tableGetApi: API.getQueryTreeList,
  tableCallback: tableCallbackFun,
});

// 处理列表数据
function tableCallbackFun() {
  tableData.value.map((item: any) => {
    item.isParent = true;
  });
}

function yearRangeChange(val: any) {
  if (!val) {
    searchForm.quarter = '';
  }
}

function onSearchHandlerEvent() {
  if (activeIndex.value == '0') {
    searchForm.isQuarter = 0;
    searchForm.year = undefined;
    searchForm.quarter = undefined;
  } else {
    searchForm.periodYearMonth = searchForm.year && searchForm.quarter ? searchForm.year + searchForm.quarter : '';
    searchForm.isQuarter = 1;
  }
  onSearchHandler();
}

function onResetHandlerEvent() {
  if (activeIndex.value == '0') {
    searchForm.isQuarter = 0;
    searchForm.periodYearMonth = loadPeriodList.currentPeriodYearMonth;
    searchForm.quarter = undefined;
    searchForm.year = undefined;
  } else {
    searchForm.isQuarter = 1;
    searchForm.quarter = loadPeriodList.currentPeriodYearQuarter.substring(5); // 从第5位开始截取 "2"
    searchForm.year = loadPeriodList.currentPeriodYearQuarter.substring(0, 4); // 截取前4位 "2025"
    searchForm.periodYearMonth = searchForm.year && searchForm.quarter ? searchForm.year + searchForm.quarter : '';
  }
  searchForm.isShowPrevious = 0;
  onSearchHandler();
}

onMounted(() => {
  loadPeriod();
});
</script>

<style lang="scss" scoped>
.incomestatement-pageContainer {
  height: 100%;
  width: 100%;
}

.filter-container {
  padding-top: 20px;
  padding-left: 20px;
  background: #fff !important;
}

.el-menu--horizontal {
  height: 40px;

  .el-menu-item {
    height: 39px !important;
    background: #fff !important;
    padding: 10px 46px !important;

    &.is-active:before {
      display: none;
    }
  }
}

.delivery-method-container {
  .right-panel {
    padding: 20px;
    background-color: #fff;

    .search-card,
    .toolbar-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }

    .search-card {
      :deep(.el-form--inline .el-form-item) {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 20px;
    }
  }
}

:deep(.el-table) {
  .el-switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
  }
}

.table-container {
  background-color: #fff;
}

.status1 {
  width: 56px;
  height: 32px;
  background: rgba(41, 182, 16, 0.08);
  border-radius: 2px;
  border: 1px solid rgba(41, 182, 16, 0.2);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #29b610;
}

.status3 {
  width: 56px;
  height: 32px;
  background: rgba(255, 156, 0, 0.08);
  border-radius: 2px;
  border: 1px solid rgba(255, 156, 0, 0.2);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ff9c00;
}

::v-deep .red-cell {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #762adb !important;
  background: #f6f0ff !important;
  box-shadow: inset 1px 1px 0px 0px #e5e7f3, inset -1px -1px 0px 0px #e5e7f3;
}

::v-deep .red-cell .el-table__expand-icon {
  color: #762adb !important;
}

::v-deep .red-cell .el-table__body .el-table__cell {
  color: #762adb !important;
}

.toolbar-card {
  margin-bottom: 20px;
}

.el-button--default {
  --el-border-color: #762adb !important;
  --el-text-color-regular: #762adb !important;
}

.name:hover {
  box-sizing: border-box;
  cursor: pointer;

  .edit-btn {
    display: inline-block;
  }
}

.edit-btn {
  color: #762adb !important;
  display: none;
  margin-left: 10px;
  cursor: pointer;
  font-size: 12px;

  span {
    vertical-align: top;
  }
}
.formuladescription {
  display: flex;
  align-items: center;
  width: 100%;
  span {
    margin-right: 8px;
  }
}
.pl10 {
  padding-left: 10px;
}
</style>
