import request from "@/core/utils/request";

const DEPT_BASE_URL = "/supply-base/base/dept";

class DeptAPI {
  /**
   * 获取部门列表
   *
   * @param queryParams 查询参数（可选）
   * @returns 部门树形表格数据
   */
  static getList(queryParams?: DeptQuery) {
    return request<any, DeptVO[]>({
      url: `${DEPT_BASE_URL}/list`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 新增部门
   *
   * @param data 部门表单数据
   * @returns 请求结果
   */
  static add(data: DeptForm) {
    return request({
      url: `${DEPT_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }

  /**
   * 修改部门
   *
   * @param id 部门ID
   * @param data 部门表单数据
   * @returns 请求结果
   */
  static update(data: DeptForm) {
    return request({
      url: `${DEPT_BASE_URL}/edit`,
      method: "post",
      data: data,
    });
  }

  /**
   * 删除部门
   *
   * @param ids 部门ID，多个以英文逗号(,)分隔
   * @returns 请求结果
   */
  static deleteByIds(ids: string) {
    return request({
      url: `${DEPT_BASE_URL}/del/${ids}`,
      method: "get",
    });
  }
}

export default DeptAPI;

/** 部门查询参数 */
export interface DeptQuery {
  /** 搜索关键字 */
  deptName?: string;
  tenantId?: string;
}

/** 部门类型 */
export interface DeptVO {
  /** 子部门 */
  children?: DeptVO[];
  /** 创建时间 */
  createTime?: string;
  /** 部门ID */
  id?: string;
  /** 部门名称 */
  deptName?: string;
  /** 部门描述 */
  deptDesc?: string;
  /** 部门级别 */
  deptLevel?: string;
  /** 部门人数 */
  deptUserCount?: string;
  /** 父部门ID */
  parentId?: string;
  /** 修改时间 */
  updateTime?: string;
}

/** 部门表单类型 */
export interface DeptForm {
  /** 部门ID(新增不填) */
  id?: string;
  /** 部门名称 */
  deptName?: string;
  /** 部门描述 */
  deptDesc?: string;
  /** 父部门ID */
  parentId: string;
  /** 部门层级(1 一级，2 二级 3 三级) */
  deptLevel?: number;
  // 租户id
  tenantId?: string;
  setEditType?: string;
}
