<template>
  <el-drawer
    :value="isVisible"
    :title="title"
    :close-on-click-modal="false"
    size="800px"
    @close="close"
  >
    <div class="receiveTransportDiog" v-loading="loading">
      <div class="page-header">
        <span class="mr-20px"><span class="required">*</span>采购单号:</span>
        <el-input
          type="text"
          :placeholder="$t('common.placeholder.inputTips')"
          v-model="purchaseOrderInfo.purchaseCode"
          class="!w-[260px]"
          :disabled="editType !== 'add'"
          @keyup.enter="handleQuery"
        />
        <el-button
          v-if="editType === 'add'"
          type="primary"
          @click="handleQuery"
          style="margin-left: 16px"
        >
          {{ $t("common.search") }}
        </el-button>
      </div>
      <div class="receive-transport-content" v-if="pageContentVisible">
        <div class="purchase-info">
          <el-descriptions :column="2" style="width: 100%">
            <el-descriptions-item
              :label="$t('shippingReceipt.label.purchaseType')"
            >
              {{ purchaseOrderInfo.purchaseOrderType }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="purchaseOrderInfo.purchaseOrderType !== '市场自采'"
              :label="$t('shippingReceipt.label.supplier')"
            >
              {{ purchaseOrderInfo.supplierName }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.source')">
              {{ purchaseOrderInfo.purchaseOrderSource }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.buyerName')"
            >
              {{ purchaseOrderInfo.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.plannedDeliveryDate')"
            >
              {{ parseDateTime(purchaseOrderInfo.planDeliveryDate, "date") }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.createTime')"
            >
              {{ parseDateTime(purchaseOrderInfo.createTime, "date") }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.remark')">
              {{ purchaseOrderInfo.remark }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.creator')">
              {{ purchaseOrderInfo.createUserName }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="product-info">
          <el-select
            v-if="editType !== 'details'"
            v-model="searchProductName"
            clearable
            filterable
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[740px]"
            @change="handleQuery"
          >
            <el-option
              v-for="(
                item, index
              ) in purchaseOrderInfo.receiveTransportDetailVOList"
              :key="index"
              :label="item.productName"
              :value="item.productName"
            />
          </el-select>
          <div
            class="product-list mt-20px"
            v-for="(
              item, index
            ) in purchaseOrderInfo.receiveTransportDetailVOList"
            :key="index"
            v-show="
              !searchProductName || searchProductName === item.productName
            "
          >
            <div class="product-cover">
              <div class="left">
                <el-image
                  style="width: 90px; height: 96px"
                  :src="item.productImg"
                />
              </div>
              <div class="right">
                <div class="right-title">
                  <div class="primary14-font">{{ item.productName }}</div>
                  <div class="right-title-des gray14-font">
                    <div>
                      {{ $t("shippingReceipt.label.unitName") }}:
                      <span>{{ item.unitName }}</span>
                    </div>
                    <div>
                      {{ $t("shippingReceipt.label.plannedQuantity") }}:
                      <span>{{ item.planPurchaseCount }}</span>
                    </div>
                    <div>
                      {{ $t("shippingReceipt.label.receivedGoodsCount") }}:
                      <span>{{ item.receivedCount }}</span>
                    </div>
                    <div>
                      {{ $t("shippingReceipt.label.receivedGoodsAmount") }}:
                      <span>
                        {{ item.amountCurrency === "USD" ? "$" : "￥"
                        }}{{ item.receivedAmount }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="right-form">
                  <el-form
                    :model="item"
                    ref="contentFormRef"
                    label-width="auto"
                    label-position="right"
                    :rules="rules"
                  >
                    <el-form-item
                      :label="$t('shippingReceipt.label.receivedCount')"
                      prop="receivedThisTime"
                      class="custom-form-item"
                    >
                      <el-input
                        class="right-form-input"
                        :placeholder="$t('common.placeholder.inputTips')"
                        v-model="item.receivedThisTime"
                        :disabled="
                          editType === 'details' || editType === 'confirm'
                        "
                        type="number"
                        @change="receivedDataChanged(item)"
                        oninput="if(isNaN(value)) { value = null }"
                      >
                        <template #append>
                          {{ item.unitName }}
                        </template>
                      </el-input>
                    </el-form-item>
                    <div class="form-item-container">
                      <el-form-item
                        :label="
                          $t('shippingReceipt.label.unitPriceReceivedThisTime')
                        "
                        prop="unitPriceReceivedThisTime"
                        class="custom-form-item"
                      >
                        <!-- :disabled="item.unitPriceReceivedThisTime !== null"-->
                        <!--
                        （区分单据来源，①如果来源是手动新增的采购单，自采采购单，收货单价可手动填写；
                         其他类型的，带出采购单对应商品的价格，不可修改）；
                          @blur="handleUnitPriceReceivedThisTimeBlur(item)"

                              -->
                        <el-input
                          type="number"
                          :disabled="unitPriceReceivedThisTimeFilter(item)"
                          class="right-form-input"
                          :placeholder="$t('common.placeholder.inputTips')"
                          v-model="item.unitPriceReceivedThisTime"
                          @change="receivedDataChanged(item)"
                          oninput="if(isNaN(value)) { value = null }"
                        >
                          <template #append>
                            {{ item.amountCurrency === "USD" ? "$" : "￥" }}
                          </template>
                        </el-input>
                      </el-form-item>

                      <el-form-item
                        :label="$t('shippingReceipt.label.receivedAmount')"
                        prop="receivedAmountThisTime"
                        class="custom-form-item"
                      >
                        <el-input
                          class="right-form-input"
                          :placeholder="
                            $t('shippingReceipt.placeholder.inputTips')
                          "
                          v-model="item.receivedAmountThisTime"
                          oninput="if(isNaN(value)) { value = null }"
                          disabled
                        >
                          <template #append>
                            {{ item.amountCurrency === "USD" ? "$" : "￥" }}
                          </template>
                        </el-input>
                      </el-form-item>
                    </div>

                    <el-form-item
                      :label="$t('shippingReceipt.label.remark')"
                      prop="sort"
                      class="custom-form-item"
                    >
                      <el-input
                        class="right-form-input"
                        type="text"
                        :placeholder="$t('common.placeholder.inputTips')"
                        v-model="item.remark"
                        :disabled="
                          editType === 'details' || editType === 'confirm'
                        "
                        max
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer" v-if="pageContentVisible">
        <div class="end">
          <div class="end-left normal14-font">
            <div>{{ $t("shippingReceipt.label.total") }}:</div>
            <div class="ml-20px">
              {{ $t("shippingReceipt.label.receivedCount") }}:
              <span class="primary14-font">
                {{ purchaseOrderInfo.totalReceivedCount }}
              </span>
            </div>
            <div class="ml-20px">
              {{ $t("shippingReceipt.label.includedTax") }}:
              <span class="primary14-font">
                {{ purchaseOrderInfo.amountCurrency === "USD" ? "$" : "￥"
                }}{{ purchaseOrderInfo.totalReceivedAmount }}
              </span>
            </div>
          </div>
          <div class="end-right">
            <el-button @click="close()">
              {{ $t("common.cancel") }}
            </el-button>
            <el-button
              type="primary"
              :loading="submitLoading"
              @click="received(1)"
              v-if="editType === 'add' || editType === 'operate'"
              v-hasPerm="['pms:CT:shippingReceipt:save']"
            >
              {{ $t("shippingReceipt.button.allReceivedBtn") }}
            </el-button>
            <el-button
              type="primary"
              :loading="submitLoading"
              @click="received(0)"
              v-if="editType === 'add' || editType === 'operate'"
              v-hasPerm="['pms:CT:shippingReceipt:save']"
            >
              {{ $t("shippingReceipt.button.partialReceiptBtn") }}
            </el-button>
            <el-button
              type="primary"
              :loading="submitLoading"
              v-if="editType === 'confirm'"
              @click="confirmAction()"
              v-hasPerm="['pms:CT:shippingReceipt:confirm']"
            >
              {{ $t("shippingReceipt.button.confirmTitle") }}
            </el-button>
          </div>
        </div>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import ShippingReceiptAPI, {
  SRPurchaseOrderData,
  SRPurchaseProductData,
} from "@/modules/pms/api/shippingReceipt";
import { ElMessage } from "element-plus";
import { parseDateTime } from "@/core/utils/index.js";

const emit = defineEmits(["onSubmit"]);
const receiveTransportId = ref();
const { proxy } = getCurrentInstance();
const { t } = useI18n();
const pageContentVisible = ref(false);
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

let purchaseOrderInfo = ref<SRPurchaseOrderData>({
  purchaseCode: "",
});
let searchProductName = ref("");
let data = reactive({
  rules: {},
  submitLoading: false,
  editType: "add",
  loading: false,
});

let { submitLoading, editType, loading } = toRefs(data);

const isVisible = computed({
  get: () => props.visible,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const rules = {
  /*
    添加收运单弹框，字段校验，本次收货量大于0 的数字，整数位不超过11位支持小数点后两位；
    本次收货单价大于0的数字，整数位不超过8位支持小数点后两位；备注不超过200个字符
  */
  receivedThisTime: [
    { required: true, message: "请输入收货数量", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === "" || value === null || value === undefined) {
          callback(new Error("请输入收货数量"));
          return;
        }

        const numValue = parseFloat(value);

        // 检查是否为数字且大于0
        if (isNaN(numValue) || numValue <= 0) {
          callback(new Error("收货数量必须大于0"));
          return;
        }

        // 检查整数位不超过11位
        const parts = numValue.toString().split(".");
        if (parts[0].length > 11) {
          callback(new Error("整数部分不能超过11位"));
          return;
        }

        // 检查小数位不超过2位
        if (parts.length > 1 && parts[1].length > 2) {
          callback(new Error("小数部分不能超过2位"));
          return;
        }

        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
  unitPriceReceivedThisTime: [
    { required: true, message: "请输入本次收货单价", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value === "" || value === null || value === undefined) {
          callback(new Error("请输入本次收货单价"));
          return;
        }

        const numValue = parseFloat(value);

        // 检查是否为数字且大于0
        if (isNaN(numValue) || numValue <= 0) {
          callback(new Error("收货单价必须大于0"));
          return;
        }

        // 检查整数位不超过8位
        const parts = numValue.toString().split(".");
        if (parts[0].length > 8) {
          callback(new Error("整数部分不能超过8位"));
          return;
        }

        // 检查小数位不超过2位
        if (parts.length > 1 && parts[1].length > 2) {
          callback(new Error("小数部分不能超过2位"));
          return;
        }

        callback();
      },
      trigger: ["blur", "change"],
    },
  ],
  remark: [{ max: 200, message: "备注不能超过200个字符", trigger: "blur" }],
};

function close() {
  searchProductName.value = "";
  receiveTransportId.value = "";
  isVisible.value = false;
  pageContentVisible.value = false;
  purchaseOrderInfo.value.purchaseCode = "";
}

function submitForm() {
  if (editType.value === "add") {
    submitAddUser();
  } else if (editType.value === "edit") {
    submitUpdateUser();
  }
}
function receivedDataChanged(prodictData: SRPurchaseProductData) {
  // 每个商品的本次收货数量和本次收获单价输入发生变化都需要重新计算对应商品的本次收货金额
  // 本次收货数量和本次收获单价输入发生变化时需要重新重新遍历商品列表计算总的本次收货数量和本次收货金额
  if (
    !isNaN(parseFloat(prodictData.receivedThisTime)) &&
    !isNaN(parseFloat(prodictData.unitPriceReceivedThisTime))
  ) {
    prodictData.receivedAmountThisTime = (
      prodictData.receivedThisTime * prodictData.unitPriceReceivedThisTime
    ).toFixed(2);
  } else {
    prodictData.receivedAmountThisTime = 0;
  }
  purchaseOrderInfo.value.totalReceivedAmount = 0;
  purchaseOrderInfo.value.totalReceivedCount = 0;
  purchaseOrderInfo.value.receiveTransportDetailVOList?.forEach((item) => {
    if (!isNaN(parseFloat(item.receivedAmountThisTime))) {
      purchaseOrderInfo.value.totalReceivedAmount! += parseFloat(item.receivedAmountThisTime);
    }
    purchaseOrderInfo.value.totalReceivedCount! += !isNaN(
      parseFloat(item.receivedThisTime)
    )
      ? parseFloat(item.receivedThisTime)
      : 0;
  });
}

const unitPriceReceivedThisTimeFilter = (item: SRPurchaseProductData) => {
  /* （区分单据来源，①如果来源是手动新增的采购单，自采采购单，收货单价可手动填写；
      其他类型的，带出采购单对应商品的价格，不可修改）； */
  // 只有市场自采可以输入
  return purchaseOrderInfo.value.purchaseOrderType !== "市场自采" || editType.value === "details";
};

// 本次收获单价改变
/* function handleUnitPriceReceivedThisTimeBlur (item) {
  // if (item.unitPriceReceivedThisTime !== null) item.disabled = true;
  if (purchaseOrderInfo.value.purchaseOrderType === "市场自采") {
    item.disabled = false;
  } else {
    item.disabled = true;
  }
} */
/** 采购单号查询 */
function handleQuery() {
  if(!purchaseOrderInfo.value.purchaseCode){
    ElMessage.warning("请输入采购单号");
    return;
  }
  loading.value = true;
  let params = {
    purchaseCode: purchaseOrderInfo.value.purchaseCode,
  };
  ShippingReceiptAPI.detailForAddReceiveTransport(params)
    .then((data) => {
      pageContentVisible.value = true;
      Object.assign(purchaseOrderInfo.value, data);
      purchaseOrderInfo.value.totalReceivedAmount = 0;
      purchaseOrderInfo.value.totalReceivedCount = 0;
    })
    .catch((err) => {
      pageContentVisible.value = false
      purchaseOrderInfo.value.totalReceivedAmount = 0;
      purchaseOrderInfo.value.totalReceivedCount = 0;
      purchaseOrderInfo.value.purchaseOrderType = "";
      purchaseOrderInfo.value.supplierName = "";
      purchaseOrderInfo.value.purchaseOrderSource = "";
      purchaseOrderInfo.value.buyerName = "";
      purchaseOrderInfo.value.planDeliveryDate = "";
      purchaseOrderInfo.value.createTime = "";
      purchaseOrderInfo.value.remark = "";
      purchaseOrderInfo.value.createUserName = "";
      purchaseOrderInfo.value.receiveTransportDetailVOList = [];
    })
    .finally(() => {
      loading.value = false;
    });
}
/** 收货 */
function received(receiveTransportType: number) {
  ElMessageBox.confirm(
    receiveTransportType === 1
      ? t("shippingReceipt.message.receiveTips")
      : t("shippingReceipt.message.receiveSectionTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let params = {
        buyerId: purchaseOrderInfo.value.buyerId,
        buyerName: purchaseOrderInfo.value.buyerName,
        purchaseCode: purchaseOrderInfo.value.purchaseCode,
        receiveTransportType: receiveTransportType,
        supplierId: purchaseOrderInfo.value.supplierId,
        supplierName: purchaseOrderInfo.value.supplierName,
        receiveTransportDetailDTOList:
          purchaseOrderInfo.value.receiveTransportDetailVOList,
        receivedAmountThisTime: "",
      };
      let dataStr = JSON.stringify(params);
      ShippingReceiptAPI.addReceiveTransport(dataStr)
        .then(() => {
          close();
          emit("onSubmit");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("shippingReceipt.message.receiveCancel"));
    }
  );
}
/** 确认收运单 */
function confirmAction() {
  let receiveTransportConfirmListRequestDTOS = ref([]);
  purchaseOrderInfo.value.receiveTransportDetailVOList?.forEach((item) => {
    let data = {
      amountCurrency: item.amountCurrency,
      receiveTransportDetailId: item.receiveTransportDetailId,
      unitPriceReceivedThisTime: item.unitPriceReceivedThisTime,
    };
    receiveTransportConfirmListRequestDTOS.value.push(data);
  });
  let params = {
    receiveTransportConfirmListRequestDTOS:
      receiveTransportConfirmListRequestDTOS.value,
    receiveTransportId: receiveTransportId.value,
  };
  ShippingReceiptAPI.confirmReceiveTransport(params)
    .then(() => {
      close();
      emit("onSubmit");
    })
    .finally(() => (loading.value = false));
}
function submitAddUser() {}

function submitUpdateUser() {}
function setEditType(data: any) {
  editType.value = data.type;
  /** 获取收运单详情 */
  if (editType.value === "details" || editType.value === "confirm") {
    receiveTransportId.value = data.receiveTransportId;
    let params = {
      receiveTransportId: receiveTransportId.value,
    };
    loading.value = true;
    ShippingReceiptAPI.receiveTransportDetails(params)
      .then((data) => {
        pageContentVisible.value = true;
        Object.assign(purchaseOrderInfo.value, data);
      })
      .finally(() => {
        loading.value = false;
      });
  }
  if (editType.value === "operate") {
    purchaseOrderInfo.value.purchaseCode = data.orderCode;
    handleQuery();
  }
}

defineExpose({
  setEditType,
});
</script>

<style scoped lang="scss">
.required{
  color: red;
  font-size: 18px;
  margin-right: 6px;
  line-height: 32px;
}
.receiveTransportDiog {
  padding: 5px;

  .receive-transport-content {
    margin-top: 20px;

    .product-cover {
      padding: 20px 20px 10px;
      width: 740px;
      // height: 177px;
      background-color: #f9f9fb;
      border: 1px solid #d7dbdf;
      border-radius: 2px;
      display: flex;

      .right {
        margin-left: 10px;
        width: 592px;

        .right-title {
          display: flex;
          justify-content: space-between;

          .right-title-des {
            display: flex;
            font-size: 14px;

            div {
              margin-right: 10px;
            }

            span {
              color: #52585f;
            }
          }
        }

        .right-form {
          margin-top: 10px;

          .right-form-input {
            height: 32px;
          }

          .form-item-container {
            display: flex;
            flex-wrap: wrap;
            // margin-bottom: 10px;
          }

          .form-item-container .el-form-item {
            margin-right: 20px;
            width: 45%;
            height: 36px;
          }
        }
      }
    }
  }
}

.end {
  display: flex;
  justify-content: space-between;

  .end-left {
    display: flex;

    div {
      align-content: center;
    }

    span {
      color: #c00c1d;
    }
  }
}
</style>
<style scoped>
::v-deep .el-input-group__append {
  padding: 10px;
}

::v-deep .custom-form-item {
  margin-bottom: 15px;
}
</style>
