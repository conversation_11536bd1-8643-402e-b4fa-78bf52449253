<template>
  <div class="app-container">
    <div class="bankJournal">
      <div class="search-container">
        <el-form ref="headFormRef" :model="searchForm" :inline="true" label-width="84px">
          <el-form-item :label="$t('bankJournal.label.accountingPeriod')">
            <el-date-picker
              :clearable="false"
              @change="monthRangeChange"
              v-model="searchForm.period"
              type="monthrange"
              range-separator="至"
              :start-placeholder="t('bankJournal.label.startDate')"
              :end-placeholder="t('bankJournal.label.endDate')"
              format="YYYY-MM"
              value-format="YYYYMM"
              :disabled-date="
                (date) => {
                  const start = loadPeriodList.periodYearMonthStart;
                  const end = loadPeriodList.periodYearMonthEnd;
                  if (!start || !end) return false;
                  const y = date.getFullYear();
                  const m = (date.getMonth() + 1).toString().padStart(2, '0');
                  const ym = `${y}${m}`;
                  return ym < start || ym > end;
                }
              "
            />
          </el-form-item>
          <el-form-item :label="$t('bankJournal.label.cashAccount')">
            <el-select filterable v-model="searchForm.subjectId" :placeholder="$t('common.placeholder.selectTips')" :clearable="false">
              <el-option v-for="item in cashAccountList" :key="item.id" :label="item.subjectCode + '_' + item.subjectName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('bankJournal.label.voucherStatus')" prop="auditStatus">
            <el-select v-model="searchForm.auditStatus" :placeholder="$t('common.placeholder.selectTips')" clearable class="!w-[190px]">
              <el-option v-for="item in voucherStatusList" :key="item.statusId" :label="item.statusName" :value="item.statusId" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearchHandler" v-hasPerm="['finance:bankJournal:search']">
              {{ $t('common.search') }}
            </el-button>
            <el-button @click="onResetHandlerEvent" v-hasPerm="['finance:bankJournal:reset']">
              {{ $t('common.reset') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-card shadow="never" class="table-container">
        <el-table show-summary :summary-method="getSummaries" v-loading="loading" :data="tableData" highlight-current-row stripe border>
          <template #empty>
            <Empty />
          </template>
          <el-table-column :label="$t('bankJournal.table.voucherDate')" prop="voucherDate" show-overflow-tooltip />
          <el-table-column :label="$t('cashJournal.table.voucherNo')" show-overflow-tooltip
            ><template #default="{ row }">
              <span @click="openVoucherBillDrawer(row.voucherId)" style="color: #762adb; cursor: pointer">{{ row.voucherWord }}<span>-</span>{{ row.voucherNumber }}</span>
            </template></el-table-column
          >
          <el-table-column :label="$t('bankJournal.table.summary')" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.summary }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('bankJournal.table.debitAmount')" prop="debitAmount" show-overflow-tooltip />
          <el-table-column :label="$t('bankJournal.table.creditAmount')" prop="creditAmount" show-overflow-tooltip />
          <el-table-column :label="$t('bankJournal.table.balanceDirection')" prop="balanceDirection" show-overflow-tooltip>
            <template #default="scope">
              <div
                v-if="scope.row.balanceDirection"
                class="balanceType"
                :class="{
                  creditBalance: scope.row.balanceDirection === '贷',
                  debitBalance: scope.row.balanceDirection === '借',
                }"
              >
                {{ scope.row.balanceDirection }}
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('bankJournal.table.balance')" show-overflow-tooltip>
           <template #default="scope">
              {{ scope.row.balanceAmount }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    <voucherBillDrawer ref="voucherBillDrawerRef" v-model:visible="voucherBillDrawerVisible"></voucherBillDrawer>
  </div>
</template>

<script setup lang="ts">
// 银行日记账
// defineOptions({
//   name: 'BankJournal',
//   inheritAttrs: false,
// });
const { t } = useI18n();
import tableMixin from '@/modules/finance/mixins/table';
import API from '@/modules/finance/api/accountStatementApi';
import voucherBillDrawer from '@/modules/finance/components/voucherBillDrawer.vue';
const loadPeriodList = reactive({
  periodYearMonthEnd: '',
  periodYearMonthStart: '',
  currentPeriodYearMonth: '',
});
const loadPeriod = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    loadPeriodList.periodYearMonthStart = String(response.periodYearMonthStart);
    loadPeriodList.periodYearMonthEnd = String(response.periodYearMonthEnd);
    loadPeriodList.currentPeriodYearMonth = String(response.currentPeriodYearMonth);
    searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
    searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
    searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
    loadcashAccountList();
  } catch (error) {
    if (error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    loading.value = false;
  }
};
const voucherStatusList = ref([
  {
    statusId: 0,
    statusName: t('bankJournal.label.pendingReview'),
  },
  {
    statusId: 1,
    statusName: t('bankJournal.label.inReview'),
  },
  {
    statusId: 2,
    statusName: t('bankJournal.label.approved'),
  },
  {
    statusId: 3,
    statusName: t('bankJournal.label.recorded'),
  },
]);
const searchForm = reactive({
  period: [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth],
  subjectId: '',
  auditStatus: '',
  periodYearMonthStart: loadPeriodList.currentPeriodYearMonth,
  periodYearMonthEnd: loadPeriodList.currentPeriodYearMonth,
});
const { loading, tableData, total, paginationInfo, headFormRef, router, path, onSearchHandler, onResetHandler, onPaginationChangeHandler, onDeleteHandler, onStatusChangeHandler } = tableMixin({
  searchForm,
  isLimit: false,
  tableGetApi: API.getQueryBankJournalList,
});
const onResetHandlerEvent = () => {
  searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
  searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
  searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
  searchForm.subjectId = orginSubjectId.value;
  onResetHandler();
};
const monthRangeChange = (val: [string, string]) => {
  if (val?.length) {
    searchForm.periodYearMonthStart = val[0];
    searchForm.periodYearMonthEnd = val[1];
  } else {
    searchForm.periodYearMonthStart = '';
    searchForm.periodYearMonthEnd = '';
  }
};
// 现金科目列表筛选
interface CashAccountItem {
  id: number | string;
  subjectCode: string;
  subjectName: string;
  statusId: number;
  statusName: string;
}
const orginSubjectId = ref('');
const cashAccountList = ref<CashAccountItem[]>([]);
const loadcashAccountList = async () => {
  try {
    const response = await API.subjectGetList({
      subjectAttribute: 2,
      journalFlag: 1,
      status: 1,
    });
    cashAccountList.value = response || [];
    const exist = cashAccountList.value.find((item) => item.subjectName == '银行科目');
    if (exist) {
      searchForm.subjectId = exist.id;
    } else if (cashAccountList.value.length > 0) {
      searchForm.subjectId = cashAccountList.value[0].id;
    } else {
      searchForm.subjectId = '';
    }
    orginSubjectId.value = searchForm.subjectId;
    onSearchHandler();
  } catch (error) {
    cashAccountList.value = [];
    searchForm.subjectId = '';
    loading.value = false;
  }
};
interface TableColumn {
  property: string;
}

interface SummaryMethodProps {
  columns: TableColumn[];
  data: Record<string, any>[];
}

const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: (string | VNode)[] = [];
  columns.forEach((column: TableColumn, index: number) => {
    if (index === 0) {
      sums[index] = h('div', { style: { fontWeight: 'bold' } }, ['合计']);
      return;
    }
     const values = data
      .filter((item: Record<string, any>) => item.flowType === 2)
      .map((item: Record<string, any>) => Number(item[column.property]));
    if (!values.every((value: number) => Number.isNaN(value))) {
      sums[index] = `${values
        .reduce((prev: number, curr: number) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return Number((prev + curr).toFixed(2));
          } else {
            return prev;
          }
        }, 0)
        .toFixed(2)}`;
    } else {
      sums[index] = '-';
    }
  });
  return sums;
};
const voucherBillDrawerVisible = ref(false);
const voucherBillDrawerRef = ref(null);
const openVoucherBillDrawer = (voucherId: string) => {
  if(!voucherId)return
  voucherBillDrawerVisible.value = true;
  if (voucherBillDrawerRef.value) {
    voucherBillDrawerRef.value.openDrawer('detail', voucherId);
  }
};
onMounted(() => {
  loadPeriod();
});
</script>

<style lang="scss" scoped>
.balanceType {
  width: 56px;
  height: 32px;
  background: rgba(64, 158, 255, 0.08);
  border-radius: 2px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #409eff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.balanceType.debitBalance {
  background: rgba(41, 182, 16, 0.08);
  border: 1px solid rgba(41, 182, 16, 0.2);
  color: #29b610;
}

.balanceType.creditBalance {
  background: rgba(255, 156, 0, 0.08);
  border: 1px solid rgba(255, 156, 0, 0.2);
  color: #ff9c00;
}
</style>
