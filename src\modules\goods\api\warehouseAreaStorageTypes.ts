import request from "@/core/utils/request";

const WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL = "/supply-biz-common/warehouseAreaStorageTypes";

class WarehouseAreaStorageTypesAPI {
  /**
   * 分页查询库区存储类型
   * @param queryParams 查询参数
   * @returns 分页结果
   */
  static getPageList(queryParams?: WarehouseAreaStorageTypesPageQuery) {
    return request<any, WarehouseAreaStorageTypesPageResult>({
      url: `${WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取库区存储类型详情
   * @param id 库区存储类型ID
   * @returns 库区存储类型详情
   */
  static getDetail(id: number) {
    return request<any, WarehouseAreaStorageTypesVO>({
      url: `${WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL}/queryDetail`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 新增库区存储类型
   * @param data 库区存储类型表单数据
   * @returns 操作结果
   */
  static add(data: WarehouseAreaStorageTypesForm) {
    return request<any, string>({
      url: `${WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL}/add`,
      method: "post",
      data,
    });
  }

  /**
   * 编辑库区存储类型
   * @param data 库区存储类型表单数据（包含ID）
   * @returns 操作结果
   */
  static update(data: WarehouseAreaStorageTypesForm) {
    return request<any, string>({
      url: `${WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL}/edit`,
      method: "post",
      data,
    });
  }

  /**
   * 删除库区存储类型
   * @param id 库区存储类型ID
   * @returns 操作结果
   */
  static deleteById(id: number) {
    return request<any, string>({
      url: `${WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL}/delete`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 批量删除库区存储类型
   * @param ids ID数组
   * @returns 操作结果
   */
  static deleteBatch(ids: number[]) {
    return request<any, string>({
      url: `${WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL}/deleteBatch`,
      method: "post",
      data: ids,
    });
  }

  /**
   * 批量启用库区存储类型
   * @param ids ID数组
   * @returns 操作结果
   */
  static enableBatch(ids: number[]) {
    return request<any, string>({
      url: `${WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL}/enableStatusBatch`,
      method: "post",
      data: ids,
    });
  }

  /**
   * 批量禁用库区存储类型
   * @param ids ID数组
   * @returns 操作结果
   */
  static disableBatch(ids: number[]) {
    return request<any, string>({
      url: `${WAREHOUSE_AREA_STORAGE_TYPES_BASE_URL}/disableStatusBatch`,
      method: "post",
      data: ids,
    });
  }
}

/**
 * 库区存储类型分页查询参数
 */
export interface WarehouseAreaStorageTypesPageQuery {
  /** 当前页 */
  page?: number;
  /** 每页条数（最大1000） */
  limit?: number;
  /** 主键ID */
  id?: number;
  /** 类型编码，唯一标识 */
  typeCode?: string;
  /** 类型名称，描述存储区域的名称,唯一 */
  typeName?: string;
  /** 排序 */
  sort?: number;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 库区存储类型表单数据
 */
export interface WarehouseAreaStorageTypesForm {
  /** 主键ID（编辑时必填） */
  id?: number;
  /** 类型编码，唯一标识 */
  typeCode?: string;
  /** 类型名称，描述存储区域的名称,唯一 */
  typeName?: string;
  /** 排序 */
  sort?: number;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 库区存储类型视图对象
 */
export interface WarehouseAreaStorageTypesVO {
  /** 组织编码 */
  orgCode?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 创建用户ID */
  createUser?: number;
  /** 更新用户ID */
  updateUser?: number;
  /** 主键ID */
  id?: number;
  /** 类型编码，唯一标识 */
  typeCode?: string;
  /** 类型名称，描述存储区域的名称,唯一 */
  typeName?: string;
  /** 排序 */
  sort?: number;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 库区存储类型分页结果对象
 */
export interface WarehouseAreaStorageTypesPageResult {
  /** 当前页 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 数据列表 */
  records: WarehouseAreaStorageTypesVO[];
}

/**
 * 启禁用状态枚举
 */
export enum EnableStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
}

export default WarehouseAreaStorageTypesAPI; 