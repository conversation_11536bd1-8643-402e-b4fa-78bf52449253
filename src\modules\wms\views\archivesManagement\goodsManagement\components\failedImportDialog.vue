<template>
  <el-dialog v-model="dialogVisible" :title="$t('purchase.title.failedImport')" width=700 :close-on-click-modal="false" @close="close">
    <div class="dialog-main-container">
      <div v-for="(item, index) in data" :key="index" class="failed-mian-container">
        <div class="left-container">
          {{$t('purchase.message.failedImportTips1')}}{{item?.rowIndex}}{{$t('purchase.message.failedImportTips2')}}
        </div>
        <div class="right-container">
          {{item?.errorMsg}}
        </div>
      </div>
    </div>

  </el-dialog>
</template>

<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["update:visible"])
const dialogVisible = ref(false)
watchEffect(() => {
  dialogVisible.value = props.visible
})
function close () {
  emit("update:visible", false)
}
</script>

<style scoped lang="scss">
.dialog-main-container {
  padding: 18px 0;
  max-height: 600px;
  overflow: auto;
}
.failed-mian-container {
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  margin: 6px 0;
  .left-container {
    margin-right: 10px;
  }
  .right-container {
    flex: 1;
  }
}
</style>
