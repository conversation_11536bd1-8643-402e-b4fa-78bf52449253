import request from "@/core/utils/request";

const AUTH_BASE_URL = "/supply-base";

class UpdatePasswordAPI {
    /** 修改密码接口*/
    static updatePassword(data: updatePasswordData) {
        return request({
            url: `${AUTH_BASE_URL}/current/user/update/password`,
            method: "post",
            data: data,
        });
    }
}

export default UpdatePasswordAPI;

/** 修改密码请求参数 */
export interface updatePasswordData {
  /** 旧密码 */
  oldPassword: string;
  /** 新密码 */
  newPassword: string;
  /** 确认密码 */
  confirmPassword: string;
}
