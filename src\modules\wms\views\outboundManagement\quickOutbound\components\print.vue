<template>
  <NewPrint ref="printRef">
    <div style="display: flex;justify-content: flex-end;align-items: center;margin-top: 10px;margin-bottom: 10px">
      <div class="box-title" style="width: 50%">
        {{ printData.title }}
      </div>
      <div class="barcode" style="width: 30%">
        <Barcode :value="printData.outboundNoticeCode" :options="barcodeOptions"/>
      </div>
    </div>
    <div class="print-info">
      <div class="info-row">
        <div class="info-item-1">
            <span class="info-label">{{$t('quickOutbound.label.customerName')}}：</span>
            <span class="info-value">{{printData.customerName}}</span>
        </div>
        <div class="info-item-4">
            <span class="info-label">{{$t('quickOutbound.label.salesperson')}}：</span>
            <span class="info-value">{{ printData.purchaseSalesPerson}}</span>
        </div>
        <div class="info-item-4">
            <span class="info-label">{{ $t("quickOutbound.label.contractCode") }}：</span>
            <span class="info-value">{{ printData.contractCode }}</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item-1">
            <span class="info-label">{{ $t("quickOutbound.label.outboundNoticeCode") }}：</span>
            <span class="info-value">{{ printData.outboundNoticeCode }}</span>
        </div>
        <div class="info-item-4">
          <span class="info-label">{{ $t("quickOutbound.label.settlementMethod") }}：</span>
          <span class="info-value">{{ printData.paymentType }}</span>
        </div>
        <div class="info-item-4">
          <span class="info-label">{{ $t("quickOutbound.label.contractClassification") }}：</span>
          <span class="info-value">{{ printData.contractType }}</span>
        </div>
      </div>
    </div>
    <table class="print-table">
      <thead>
      <tr>
        <th style="width: 180px">{{ $t("quickOutbound.label.productName") }}</th>
        <th style="width: 50px">{{ $t("quickOutbound.label.unit") }}</th>
        <th>{{ $t("quickOutbound.label.quantity") }}</th>
        <th>{{ $t("quickOutbound.label.outQuantity") }}</th>
        <th>{{ $t("quickOutbound.label.unitPriceNoUnit")}}</th>
        <th>{{ $t("quickOutbound.label.amountNoUnit") }}</th>
        <th>{{ $t("quickOutbound.label.remark") }}</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(item, index) in printData.warehouseOutboundDetailVOList" :key="index">
        <td style="width: 180px">
          <div style="text-align: center">
            <!--<div>
              <Barcode :value="item.productCode" :options="productCodeBarcodeOptions"/>
            </div>-->
            <div>{{ item.productName }}</div>
          </div>
        </td>
        <td style="width: 50px">{{ item.productUnitName }}</td>
        <td>{{ item.planProductQty }}</td>
        <td>{{ item.alreadyOutboundQty }}</td>
        <td>{{ item.salePrice }}</td>
        <td>{{ item.saleAmount }}</td>
        <td>{{ item.remark }}</td>
      </tr>
      <tr>
        <td colspan="2" style="text-align: left !important;">合计金额：{{printData.totalSaleAmount}}</td>
        <td colspan="1" style="text-align: left !important;">{{printData.totalPlanProductQty}}</td>
        <td colspan="1" style="text-align: left !important;">{{printData.totalOutboundQty}}</td>
        <td></td>
        <td></td>
        <td style="text-align: left !important;">优惠：{{printData.totalDiscountAmount}}</td>
      </tr>
      <tr>
        <td colspan="1" style="text-align: left !important;">收货人：{{printData.contactPerson}}</td>
        <td colspan="3" style="text-align: left !important;">电话：{{printData.customerMobile}}</td>
        <td colspan="3" style="text-align: left !important;">收货地址：{{printData.addressFormat ? printData.addressFormat : '-'}}</td>
      </tr>
      </tbody>
    </table>
    <div class="print-info">
      <div class="info-row">
        <div class="info-item-4">
            <span class="info-label" style="margin-left: 15px">
              {{ $t("quickOutbound.label.createOrder") }}：
            </span>
          <span class="info-value">{{ printData.purchaseSalesPerson}}</span>
        </div>
        <div class="info-item-4">
            <span class="info-label">
              {{ $t("quickOutbound.label.outboundUserName") }}：
            </span>
          <span class="info-value">{{ printData.outboundUserName}}</span>
        </div>
        <div class="info-item-4">
            <span class="info-label">
              {{ $t("quickOutbound.label.sendPerson") }}：
            </span>
          <span class="info-value">{{ printData.sendPerson }}</span>
        </div>
        <div class="info-item-4">
            <span class="info-label">
              {{ $t("quickOutbound.label.extractPerson") }}：
            </span>
          <span class="info-value">{{ printData.extractPerson }}</span>
        </div>
      </div>
    </div>
  </NewPrint>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PrintTemplate from "@/core/components/Print/PrintTemplate.vue";
import NewPrint from "@/core/components/NewPrint/index.vue";
const { t } = useI18n();

const barcodeOptions = ref({
  format: "CODE128", // 条形码格式
  width: 1, // 条形码宽度
  height: 40, // 条形码高度
  displayValue: true, // 是否显示条形码下方的文本
  fontSize: 12, // 设置文字大小为12px
  fontOptions: "bold", // 可以设置字体的粗细等
});
const productCodeBarcodeOptions = ref({
  format: "CODE128", // 条形码格式
  width: 1, // 条形码宽度
  height: 20, // 条形码高度
  displayValue: false, // 是否显示条形码下方的文本
});

const printData = ref<any>({});
const printRef = ref<InstanceType<typeof PrintTemplate>>();
// 暴露打印方法给父组件
const handlePrint = (data: any) => {
  printData.value = data;
  nextTick(() => {
    printRef.value?.onPrint();
  });
};

defineExpose({
  handlePrint,
});
</script>

<style scoped lang="scss">

  .box-title {
    color: #000000;
    text-align: center !important;
    font-weight: 600 !important;
    font-size: 18px !important;
  }
  .barcode {
    text-align: center;
  }

  .print-info {
    margin-top: 10px;

    .info-row {
      display: flex;
      margin-bottom: 8px;
      flex-wrap: wrap;
      page-break-inside: avoid;

      .info-item-1{
        width: 50% !important;
        display: flex;
        .info-label {
          width: 90px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #000000 !important;
          line-height: 20px;
          text-align: right !important;
          font-style: normal;
        }

        .info-value {
          flex: 1;
          word-wrap: break-word; /* 允许长单词换行 */
          overflow-wrap: break-word; /* 更标准的写法 */
          white-space: normal; /* 默认换行行为 */
          word-break: break-all; /* 强制所有字符换行 */
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #000000 !important;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }

      .info-item-4 {
        width: 25% !important;
        display: flex;

        .info-label {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #000000 !important;
          line-height: 20px;
          text-align: right !important;
          font-style: normal;
        }

        .info-value {
          flex: 1;
          padding-right: 8px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #000000 !important;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          word-break: break-word;
        }
      }
    }
  }
  .print-table {
    border: 1px solid #000000 !important;
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed;
    -webkit-print-color-adjust: exact;
  }


  table,
  th,
  td {
    color: #000000;
    border: 1px solid #000000;
  }

  th,
  td {
    padding: 5px 12px;
    word-break: break-word;
    font-size: 12px;
    font-weight: 500;
  }



</style>
