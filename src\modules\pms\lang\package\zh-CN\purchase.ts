export default {
  purchase: {
    label: {
      productName: "商品名称",
      productCode: "商品编码",
      productCategory: "商品分类",
      status: "状态",
      defaults: "默认",
      supplier: "供应商",
      supplierCode: "供应商编码",
      supplierName: "供应商名称",
      product: "商品",
      productSpecName: "规格名称",
      conversionRelSecondUnitName: "基本单位",
      isStandard: "标品",
      createTime: "创建时间",

      supplierCategoryName: "可供商品分类",
      supplierWarehouseName: "关联仓库",
      isStandardCopy: "是否标品",
      conversionRelSecondUnitNameCopy: "采购单位",
      changeRelationship: "换算关系",
      productlwhv: "商品长宽高",
      length: "长(m)",
      width: "宽(m)",
      height: "高(m)",
      volume: "体积(m^3)",
      weight: "重量",
      brand: "商品品牌",
      shelfLife: "保质期",
      lossRatio: "损耗比例",
      storageCondition: "存储条件",
      remark: "备注",
      imagesUrls: "商品图片",
      statusCopy: "商品状态",
      basicInformation: "基本信息",
      graphicInformation: "图文信息",
      supplyChainInformation: "供应链信息",
      productStatusInformation: "商品状态信息",
    },
    placeholder:{
        // keywords: "请输入商品名称/编码",
        productCode: "请输入商品编码",
        productName: "请输入商品名称",
        supplierName: "请输入供应商名称",
        productCategory: "请输入关键词搜索分类",
        weight: "请输入(单位：KG)",
        lossRatio: "请输入0-100%",
    },
    statusList: {
        haveBeenPutOnShelves: "已上架",
        haveBeenGetOffShelves: "已下架",
    },
    shelfLifeUnitList:{
        day: "天",
        month: "月",
        year: "年",
    },
    button: {
      exportProductInformation: "导出商品信息",
      setSuppliers: "设置供应商",
      setDefaultSuppliers: "设置默认供应商",
      addPurchaseMaterial: "新增采购原料",
      editPurchaseMaterial: "编辑采购原料",
      putOnShelves: "上架",
      getOffShelves: "下架",
      selectDetail: "查看",
      selectSuppliers: "选择供应商",
      addCategory: "新增分类",
      editCategory: "编辑分类",
    },
    title: {
        selectSuppliers: "选择供应商",
        setSuppliers: "设置供应商",
        setDefaultSuppliers: "设置默认供应商",
        suppliers: "供应商",
    },
    message: {
      putOnShelvesTips: "是否上架当前选中商品？",
      putOnShelvesConcel: "已取消上架！",
      putOnShelvesSucess: "上架成功！",
      putOnShelvesFail: "上架失败！",
      getOffShelvesTips: "是否下架当前选中商品？",
      getOffShelvesConcel: "已取消下架！",
      getOffShelvesSucess: "下架成功！",
      getOffShelvesFail: "下架失败！",
      deleteTips: "确定删除此商品吗？",
      deleteSupplierTips: "确定删除此供应商吗？",
      deleteConcel: "已取消删除！",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      setSuppliersSucess: "设置供应商成功",
      setSuppliersTips: "只能设置一条供应商",
      setDefaultSuppliersTips: "只能设置一条默认供应商",
      setDefaultSuppliersSucess: "设置默认供应商成功",
      addSucess: "添加成功",
      editSucess: "编辑成功",
    },
    rules: {
        productCategory: "请选择商品分类",
        productName: "请输入商品名称",
        productNameFormat: "商品名称支持2到120个字符",
        isStandard: "请选择是否标品",
        productUnitId: "请选择采购单位",
        conversionRelFirstNum: "请输入",
        conversionRelFirstNumFormat: "支持小数点前最多14位，小数点后最多2位",
        conversionRelSecondNum: "请输入",
        conversionRelSecondNumFormat: "支持小数点前最多14位，小数点后最多2位",
        conversionRelSecondUnitId: "请选择基本单位",
        lossRatio: "请选择损耗比例",
        lossRatioFormat: "损耗比例支持0到100的数字，支持小数点后2位",
        shelfLifeFormat: "保质期支持0到50个汉字、英文和数字",
        storageConditionFormat: "存储条件支持0到50个汉字、英文和数字",
        lengthFormat: "商品长支持大于0的数字，支持小数点后2位",
        widthFormat: "商品宽支持大于0的数字，支持小数点后2位",
        heightFormat: "商品高支持大于0的数字，支持小数点后2位",
        volumeFormat: "商品体积支持大于0的数字，支持小数点后2位",
        weightFormat: "重量支持大于0的数字，支持小数点后2位",
        // weightFormat: "重量支持小数点前最多14位，小数点后最多2位",
        status: "请选择商品状态",
        imagesUrls: "请上传商品图片",
    },
  },
};
