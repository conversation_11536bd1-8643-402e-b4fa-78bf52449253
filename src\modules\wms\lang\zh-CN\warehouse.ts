import { placeholderSign } from "element-plus/es/components/table-v2/src/private";

export default {
  warehouse: {
    label: {
      sort: "序号",
      warehouseCoding: "仓库编码",
      warehouseName: "仓库名称",
      warehouseAddress: "仓库地址",
      country: "国家",
      province: "省",
      city: "市",
      area: "区/县",
      detailAddress: "详细地址",
      contact: "联系人",
      contactNumber: "联系电话",
      fixedLine: "固话",
      remark: "备注",
      status: "状态",
      operate: "操作",
      ownWarehouseOrg: "所属仓库组织",
      affiliatedDepartment: "所属部门",

    },
    placeholder: {
      detailAddressInputTips: "详细地址:街道/道路/门牌号(限100字)",
      countryInputTips: "请选择国家",
      areaInputTips: "请选择省-市-区/县",
      input30Tips: "请输入(30个字以内)",
      input300Tips: "请输入(300个字以内)",
      input200Tips: "请输入(200个字以内)",
    },
    button: {
      addWarehouse: '新增',
    },
    title: {
      addWarehouseTitle: "添加仓库",
      editWarehouseTitle: "编辑仓库",
      selWarehouseTitle: "选择仓库",
    },
    message: {
      deleteTips: "确定删除此部门吗？",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      deleteCancel: "已取消删除",
      deleteWarehouse: '删除后无此仓库，是否确认删除？'
    },
    rules: {
      wareHouseName:"请输入仓库名称",
      address:"请完善仓库地址",
      contactPerson: "请输入仓库联系人",
      mobile:"请输入仓库联系电话",

    },
  },
};
