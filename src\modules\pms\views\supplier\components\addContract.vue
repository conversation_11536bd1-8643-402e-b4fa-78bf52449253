<template>
  <el-drawer
    :value="isVisible"
    :title="title"
    :close-on-click-modal="false"
    size="800px"
    @close="close"
  >
    <el-form ref="contractRef" :inline="true" :model="queryParams">
      <div class="flex-center-but">
        <div class="supplier-div">
          <el-form-item prop="warehouseName">
            <el-select
              class="select"
              v-model="queryParams.contractType"
              :placeholder="$t('common.placeholder.selectTips')"
              @change="handleTypeChange"
            >
              <el-option
                v-for="item in contractTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-input
              v-model="queryParams.contractName"
              :placeholder="$t('common.placeholder.inputTips')"
              clearable
              v-if="queryParams.contractType == 0"
              class="input_w"
            />
            <el-input
              v-model="queryParams.contractPartner"
              :placeholder="$t('common.placeholder.inputTips')"
              clearable
              v-if="queryParams.contractType == 1"
              class="input_w"
            />
          </el-form-item>
        </div>
        <div>
          <el-form-item>
            <el-button type="primary" @click="getContractList">
              {{ $t("common.search") }}
            </el-button>
            <el-button @click="handleResetQuery">
              {{ $t("common.reset") }}
            </el-button>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <div>
      <el-table
        ref="dataTableRef"
        :data="dataList"
        v-loading="loading"
        highlight-current-row
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          :label="$t('supplierManagement.label.contractName')"
          prop="contractName"
        />
        <el-table-column
          :label="$t('supplierManagement.label.signatory')"
          prop="contractPartner"
        />
        <el-table-column
          prop="contractCode"
          :label="$t('supplierManagement.label.contractCode')"
        />
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="getContractList"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          :disabled="multipleSelection.length < 1"
        >
          {{ $t("common.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import supplierAPI from "@/modules/pms/api/supplier";

const emit = defineEmits(["onSubmit"]);
const { t } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

const dataList = ref([]);
const multipleSelection = ref([]);
const total = ref(0);
const loading = ref(false);

const queryParams = reactive({
  page: 1,
  limit: 20,
  contractName: "",
  contractPartner: "",
  contractType: 0,
  excludeContractIds: [],
});

const contractTypeList = ref([
  {
    value: 0,
    label: t("supplierManagement.label.contractName"),
  },
  {
    value: 1,
    label: t("supplierManagement.label.signatory"),
  },
]);

const isVisible = computed({
  get: () => props.visible,
  set: (val: any) => {
    emit("update:modelValue", val);
  },
});
let contractRef = ref();

function handleTypeChange() {
  queryParams.contractName = "";
  queryParams.contractPartner = "";
}

function getContractList(data?: any) {
  loading.value = true;
  let params = {
    ...queryParams,
  };
  // if(data && data.length > 0){
  //    params.excludeContractIds = data
  // }
  supplierAPI
    .getContract(params)
    .then((data: any) => {
      dataList.value = data.records;
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleResetQuery() {
  queryParams.contractName = "";
  queryParams.contractPartner = "";
  queryParams.contractType = 0;
  queryParams.page = 1;
  queryParams.limit = 20;
  getContractList();
}

function reset() {
  multipleSelection.value = [];
}

function submitForm() {
  emit("onSubmit", multipleSelection.value);
  close();
}

function close() {
  isVisible.value = false;
  reset();
}

function handleSelectionChange(selection: any) {
  multipleSelection.value = selection;
}

function setFormData(data: any) {
  queryParams.excludeContractIds = data;
}

defineExpose({
  setFormData,
  getContractList,
});
</script>

<style scoped lang="scss">
:deep(.select) {
  width: 100px !important;
  .el-select__wrapper {
    background: #f8f9fc;
  }
}
.supplier-div {
  width: calc(100% - 180px);
  .el-form-item {
    width: 100%;
  }
  .input_w {
    width: calc(100% - 110px);
  }
}
</style>
