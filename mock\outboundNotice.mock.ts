import { defineMockWms } from "./base";
import {supplierList} from "@/modules/pms/api/purchase";

export default defineMockWms([
  {
    url: "outboundNotice/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
             {
                 id: '1',
                 outboundNoticeCode: '出库通知单号',
                 outboundType: 1,
                 warehouseName: '仓库名称',
                 warehouseAddress: '仓库地址',
                 productLinesNumber: 5,
                 customerName: '客户',
                 customerAddress: '客户地址',
                 customerContact: '客户联系人',
                 customerMobile: '1345678988',
                 supplierName: '供应商',
                 status: 1,
             },
             {
                 id: '2',
                 outboundNoticeCode: '出库通知单号',
                 outboundType: 2,
                 warehouseName: '仓库名称',
                 warehouseAddress: '仓库地址',
                 productLinesNumber: 5,
                 customerName: '客户',
                 customerAddress: '客户地址',
                 customerContact: '客户联系人',
                 customerMobile: '1345678988',
                 supplierName: '供应商',
                 status: 2,
             },
             {
                 id: '3',
                 outboundNoticeCode: '出库通知单号',
                 outboundType: 3,
                 warehouseName: '仓库名称',
                 warehouseAddress: '仓库地址',
                 productLinesNumber: 5,
                 customerName: '客户',
                 customerAddress: '客户地址',
                 customerContact: '客户联系人',
                 customerMobile: '1345678988',
                 supplierName: '供应商',
                 status: 3,
             },
             {
                 id: '4',
                 outboundNoticeCode: '出库通知单号',
                 outboundType: 3,
                 warehouseName: '仓库名称',
                 warehouseAddress: '仓库地址',
                 productLinesNumber: 5,
                 customerName: '客户',
                 customerAddress: '客户地址',
                 customerContact: '客户联系人',
                 customerMobile: '1345678988',
                 supplierName: '供应商',
                 status: 4,
             },
        ],
        total: '2',
      },
      msg: "一切ok",
    },
  },


  // 删除采购商品
  {
      url: "outboundNotice/concel",
      method: ["POST"],
      body:{
          code: 0,
          data: null,
          msg: "取消成功",
      },
  },

  //  根据商品id查询采购商品详情
  {
      url: "product/product/detail",
      method: ["POST"],
      body: {
          code: 0,
          data:{
              id: '2',
              firstCategoryId: '4',
              secondCategoryId: '5',
              thirdCategoryId: '7',
              productName: '商品名称称',
              isStandard: 0,
              productUnitId: 1,
              conversionRelFirstNum: 11,
              conversionRelSecondNum: 22,
              conversionRelSecondUnitId: 2,
              length: 2,
              width: 2,
              height: 2,
              volume: 2,
              weight: 2,
              productBrandId: 2,
              shelfLife: 12,
              shelfLifeUnit: 1,
              lossRatio: 15,
              storageCondition: '存储条件',
              remark: 'remark',
              imageUrlList: [],
              status:1,
              supplierList:[
                  {
                      id: '11',
                      supplierCode: 'gfgfg',
                      supplierName: '供应商名称',
                      supplierWarehouseName: '22',
                      isDefault:0,
                  },
                  {
                      id: '22',
                      supplierCode: 'gfg22fg',
                      supplierName: '供应商名称22',
                      supplierWarehouseName: '22',
                      isDefault:1,
                  }
              ]
          },
          msg: "一切ok",
      },
  },

    //  添加采购商品
    {
        url: "product/product/save",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

    //  编辑采购商品
    {
        url: "product/product/update",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

    // 设置供应商
    {
        url: "product/product/batchUpdateProductSupplier",
        method: ["POST"],
        body: {
            code: 0,
            data: null,
            msg: "设置供应商成功",
        },
    },

    // 设置默认供应商
    {
        url: "product/product/batchUpdateProductDefaultSupplier",
        method: ["POST"],
        body: {
            code: 0,
            data: null,
            msg: "设置默认供应商成功",
        },
    },


  // 新增采购商品
  {
    url: "product/add",
    method: ["POST"],
    body: {
        code: 0,
        data: null,
        msg: "新增采购商品成功",
    },
  },

]);
