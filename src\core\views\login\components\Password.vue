<template>
	<el-form ref="loginForm" :model="data.loginForm" :rules="data.loginRules" class="login-form" auto-complete="on"
		label-position="left">
		<el-form-item prop="tenantId">
			<el-input ref="tenantId" v-model="data.loginForm.tenantId" :placeholder="$t('login.tenantIdLabel')"
				name="tenantId" type="text" tabindex="1" autocomplete="given-name" maxlength="20"
				@input="handleTenantIdInput"
				@keyup.enter.native="openVerify" />
		</el-form-item>
		<el-form-item prop="userName" v-if="loginType === 'username'">
			<el-input ref="userName" v-model="data.loginForm.userName" :placeholder="$t('login.username')" name="userName"
				type="text" tabindex="1" auto-complete="on" maxlength="20"
				@input="handleUserNameInput"
				@keyup.enter.native="openVerify" />
		</el-form-item>
		<el-form-item prop="userName" v-if="loginType === 'mobile'">
			<el-input v-model="data.loginForm.userName" name="userName" oninput="value=value.replace(/^0|[^0-9]/g, '')"
				tabindex="1" auto-complete="on" maxlength="20" :placeholder="$t('login.mobile')"
				@input="handleUserNameInput"
				@keyup.enter.native="openVerify">
				<template #prepend>
					<el-select class="custom-select" v-model="countryNumCode" style="width: 80px;background: white;">
						<el-option v-for="item in countryNumCodeList" :key="item.id" :label="item.internationalCode"
							:value="item.internationalCode" />
					</el-select>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item prop="password">
			<el-input ref="password" v-model="data.loginForm.password" type="password" show-password
				:placeholder="$t('login.password')" name="password" tabindex="2" auto-complete="on"
				@input="data.loginForm.password = data.loginForm.password.trim()"
				@keyup.enter.native="openVerify" />
		</el-form-item>

		<div class="provision">
			<el-checkbox-group v-model="data.checkList">
				<el-checkbox :value="true" />
			</el-checkbox-group>
			<div class="provision-item">

				我已阅读并同意相关 <span class="text-link" @click="handleProvision('qygj_privacy_agreement')">隐私政策</span> 和 <span
					class="text-link" @click="handleProvision('qygj_privacy_agreement_abstract')">隐私政策摘要
				</span>

			</div>
		</div>
		<el-button class="submit-button" :loading="data.loading" type="primary" @click.native.prevent="openVerify"> {{
			$t('login.login') }}</el-button>

		<div style="padding-top: 12px; text-align: right;">
			<span @click="goResetPwd" class="forgot">{{ $t('login.forgotPassword') }}</span>
		</div>
	</el-form>
	<Detail ref="detailRef" v-model:dialog-visible="detailDialog.visible" :title="detailDialog.title" />
</template>

<script setup>

import {
	useUserStore,
} from "@/core/store";
import UserAPI from "@/core/api/accountManagement";
import Detail from "./detail.vue";

// 使用导入的依赖和库
const userStore = useUserStore();

const currentRoute = useRoute()
const { proxy } = getCurrentInstance()

const emit = defineEmits(['submit'])
const loginForm = ref(null)
const password = ref(null)
const loginType = ref('username')
const countryNumCode = ref('+86')
const countryNumCodeList = ref([])
let showVerify = inject('showVerify')
let loginSuccess = inject('loginSuccess')

const data = reactive({
	loginForm: {
		tenantId: '',
		userName: '',
		password: '',
		/* tenantId: '',
		userName: '',
		password: '', */
	},
	loginRules: {
		userName: [
			{ required: true, trigger: 'blur', message: proxy.$t('login.message.username.required') },
		],
		password: [{ required: true, trigger: 'blur', message: proxy.$t('login.message.password.required') }],
		tenantId: [
			{ required: true, trigger: 'blur', message: proxy.$t('login.message.tenantId.required') },
			{ pattern: /^[^\u4e00-\u9fa5]*$/, message: proxy.$t('login.message.tenantId.noChinese'), trigger: 'blur' }
		],
	},
	loading: false,
	redirect: undefined,
	detailRef: null,
	checked: false,
	checkList: [],
	detailDialog: {
		title: "",
		visible: false,
	}
})
let { detailRef, detailDialog } = toRefs(data);
watch(currentRoute, (newRoute) => {
	data.redirect = newRoute.query && newRoute.query.redirect;
}, { immediate: true });


function onSuccessVerify(params) {
	// 登录成功后，保存租户编码和勾选状态
	const tenantIdKey = (data.loginForm.tenantId || '').trim().toLowerCase();
	const userNameKey = (data.loginForm.userName || '').trim().toLowerCase();
	const agreeKey = `agree_${tenantIdKey}_${userNameKey}`;

	// 保存企业编码（如果有输入）
	if (tenantIdKey) {
		localStorage.setItem('tenantId', tenantIdKey);
	}

	// 保存协议勾选状态
	if (data.checkList.length > 0) {
		localStorage.setItem(agreeKey, JSON.stringify(data.checkList));
	} else {
		localStorage.removeItem(agreeKey);
	}
	console.log(params);
	let captchaVO = {
		...params,
	}
	const formData = {
		...data.loginForm,
	}
	if (loginType.value === 'mobile') {
		formData.userName = countryNumCode.value + '-' + formData.userName;
		formData.loginType = 'mobilePassword';
	} else {
		formData.loginType = 'username';
	}
	loginForm.value.validate(valid => {
		if (!valid) return
		data.loading = true
		emit('submit', {
			...formData,
			captchaVO,
		})
		userStore.login({
			systemType: "platform",
			...formData,
			captchaVO,
		}).then((res) => {
			loginSuccess()
		}).catch((error) => {
			data.loading = false;
		});
	})
}

function resetData(tab) {
	loginType.value = tab;
	data.loginForm.userName = '';
	data.loginForm.tenantId = '';
	data.loginForm.password = '';
}

function openVerify() {
	// 去除两端空格
	data.loginForm.userName = data.loginForm.userName.trim();
	data.loginForm.password = data.loginForm.password.trim();
	data.loginForm.tenantId = data.loginForm.tenantId.trim();

	if (data.checkList.length == 0) {
		return ElMessage({
			message: proxy.$t('login.message.agreePrivacyPolicy'),
			type: 'error',
		})
	}
	loginForm.value.validate(valid => {
		if (!valid) return;
		showVerify()
	})
}

function goResetPwd() {

	ElMessage({
		message: proxy.$t('login.contactAdmin'),
		type: 'warning',
	})

	// router.push('/resetPasswordBySMS')
}
// 获取区号
function getAreaList() {
	UserAPI.getAllCountry()
		.then((data) => {
			countryNumCodeList.value = data;
		})
		.finally(() => { });
}


function handleProvision(contentCode) {
	detailDialog.value.visible = true

	let params = {
		contentCode: contentCode,
	};
	detailRef.value.handleQuery(params)
}

// 处理企业编码输入
function handleTenantIdInput() {
	data.loginForm.tenantId = data.loginForm.tenantId.trim();
	restoreAgreementStatus();
}

// 处理用户名输入
function handleUserNameInput() {
	data.loginForm.userName = data.loginForm.userName.trim();
	restoreAgreementStatus();
}

// 恢复协议勾选状态
function restoreAgreementStatus() {
	const tenantIdKey = (data.loginForm.tenantId || '').trim().toLowerCase();
	const userNameKey = (data.loginForm.userName || '').trim().toLowerCase();

	// 只有当企业编码和用户名都有值时才恢复协议状态
	if (tenantIdKey && userNameKey) {
		const agreeKey = `agree_${tenantIdKey}_${userNameKey}`;
		const storedCheckList = localStorage.getItem(agreeKey);
		if (storedCheckList) {
			try {
				data.checkList = JSON.parse(storedCheckList);
			} catch (e) {
				data.checkList = [];
			}
		} else {
			data.checkList = [];
		}
	}
}
onMounted(() => {
	getAreaList();

	// 恢复企业编码
	const storedTenantId = localStorage.getItem('tenantId');
	if (storedTenantId) {
		data.loginForm.tenantId = storedTenantId;
	}

	// 如果企业编码和用户名都有值，则恢复协议勾选状态
	restoreAgreementStatus();
})
defineExpose({
	onSuccessVerify,
	resetData,
})
</script>

<style lang="scss" scoped>
@use "../login.scss";

::v-deep .custom-select .el-select__wrapper {
	background: #fff !important;
	height: 50px;
}

.provision {
	display: flex;
	padding-bottom: 20px;

	.provision-item {
		padding-top: 5px;
		font-size: 14px;
		color: #52585F;

		.text-link {
			cursor: pointer;
			color: var(--el-color-primary);
		}
	}

}
</style>
