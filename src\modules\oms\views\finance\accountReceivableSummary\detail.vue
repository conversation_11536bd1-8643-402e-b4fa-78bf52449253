<template>
  <div class="finance-detail-container">
    <div class="detail-header">
        <div >
          <div class="page-title">
            <div class="purchase-title">
              <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
              <div> {{t('omsFinance.label.detail')}}</div>
            </div>
          </div>
        </div>
        <div class="header-title">
          <div class="header-title-left">
            <img :src="FinanceTitle" alt="" />
            <span class="header-title-name">{{ headerTitle }}</span>
          </div>
          <div class="header-title-right">
            <el-button @click="addCollectionHandler" plain class="collection-btn">
              <img :src="FinanceCollection" alt="collection-icon" class="custom-icon" />
              <span class="btn-text">{{$t('omsFinance.label.collection')}}</span>
            </el-button>
          </div>
        </div>
        <div class="header-content">
          <div class="header-content-item">
            <div class="header-content-item-img">
              <img :src="FinanceIcon1" alt="" />
            </div>
            <div class="header-content-item-value-content">
              <div class="header-content-item-value">{{ formatPrice(summaryListData.receivableAmount) }}</div>
              <div class="header-content-item-value-title">应收总额(元)</div>
            </div>
          </div>
          <div class="header-content-item">
            <div class="header-content-item-img">
              <img :src="FinanceIcon2" alt="" />
            </div>
            <div class="header-content-item-value-content">
              <div class="header-content-item-value">{{ formatPrice(summaryListData.actualReceivedAmount) }}</div>
              <div class="header-content-item-value-title">已收金额(元)</div>
            </div>
          </div>
          <div class="header-content-item">
            <div class="header-content-item-img">
              <img :src="FinanceIcon3" alt="" />
            </div>
            <div class="header-content-item-value-content">
              <div class="header-content-item-value">{{ formatPrice(summaryListData.remainingReceivableAmount) }}</div>
              <div class="header-content-item-value-title">剩余应收(元)</div>
            </div>
          </div>
        </div>
    </div>
    <div class="detail-content">
      <el-card shadow="never" class="table-container">
        <el-tabs v-model="activeName" class="finance-tabs">
          <el-tab-pane :label="t(`omsFinance.label.collectionRecord`)" name="collectionRecord">
            <template v-if="activeName == 'collectionRecord'">
              <CollectionRecord ref="collectionRef" :receivableAccountId="receivableAccountId" />
            </template>
          </el-tab-pane>
          <el-tab-pane :label="t(`omsFinance.label.order`)" name="order">
            <template v-if="activeName == 'order'">
              <OrderRecord :attributesType="attributesType" :receivableAccountId="receivableAccountId"  />
            </template>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <template v-if="addCollectionVisible">
      <AddCollection v-model:visible="addCollectionVisible" :receivableAccountId="receivableAccountId" @close="closeHandler" @confirm="confirmHandler"/>
    </template>
  </div>
</template>

<script setup lang="ts">
import {formatPrice} from "@/core/utils";
import FinanceAPI, { FinanceSummary } from "@/modules/oms/api/finance";
import {useRoute, useRouter} from "vue-router";
import FinanceIcon1 from "@/core/assets/images/finance_icon1.png";
import FinanceIcon2 from "@/core/assets/images/finance_icon2.png";
import FinanceIcon3 from "@/core/assets/images/finance_icon3.png";
import FinanceTitle from "@/core/assets/images/finance_title_icon.png";
import FinanceCollection from "@/core/assets/images/finance_collection.png";
import CollectionRecord from "@/modules/oms/views/finance/accountReceivableSummary/components/collectionRecord.vue";
import OrderRecord from "@/modules/oms/views/finance/accountReceivableSummary/components/orderRecord.vue";
import AddCollection from "@/modules/oms/views/finance/accountReceivableSummary/components/addCollection.vue";

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const attributesType = ref(route.query?.attributesType)

const headerTitle = computed(() => {
  if(attributesType.value == 1){
    return route.query?.name
  }else {
    return t(`omsFinance.label.salesName`) + ' ' + route.query?.name
  }
})
const receivableAccountId = computed(() => {
  return route.query?.id
})
const collectionRef = ref()
const activeName = ref<string>('collectionRecord')
const summaryListData = reactive<FinanceSummary>({
  receivableAmount: 0,
  actualReceivedAmount: 0,
  remainingReceivableAmount: 0,
})

/*获取详情汇总*/
const queryDetail = () => {
  FinanceAPI.getCollectionDetail(receivableAccountId.value).then((summaryData) => {
    summaryListData.receivableAmount = summaryData?.receivableAmount;
    summaryListData.actualReceivedAmount = summaryData?.actualReceivedAmount;
    summaryListData.remainingReceivableAmount = summaryData?.remainingReceivableAmount;
  })
}
const addCollectionVisible = ref(false);

/*添加收款*/
const addCollectionHandler = () => {
  addCollectionVisible.value = true;
}

/*关闭收款*/
const closeHandler = () => {
  addCollectionVisible.value = false;
}

/*添加收款并刷新收款记录*/
const confirmHandler = () => {
  addCollectionVisible.value = false;
  //刷新详情汇总数据
  queryDetail()
  if(activeName.value === 'collectionRecord'){
    collectionRef.value.reload()
  }
}

/*返回应收账单汇总列表*/
const handleClose = () => {
  router.push({
    path: "/oms/finance/accountReceivableSummary",
  });
}

onMounted(() => {
  queryDetail()
})
</script>

<style scoped lang="scss">
.page-title {
  padding: 20px;
  .purchase-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
}
.detail-header {
  margin-bottom: 10px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 2px;
  .header-title {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-title-left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      img {
        width: 24px;
        margin-right: 12px;
      }
      .header-title-name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #151719;
        line-height: 33px;
        text-align: left;
        font-style: normal;
      }
    }
    .header-title-right{
      .collection-btn {
        width: 80px;
        height: 32px;
        background: rgba(118,42,219,0.08);
        border-radius: 2px;
        border: 1px solid rgba(118,42,219,0.15);
        .custom-icon {
          margin-right: 5px;
        }
        .btn-text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #762ADB;
          line-height: 20px;
        }
      }
    }
  }
  .header-content{
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 32px 0;
    .header-content-item{
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding-left: 64px;
      border-left: 1px solid #E5E7F3;;
      .header-content-item-img {
        width: 64px;
        height: 64px;
        margin-right: 16px;
      }
      .header-content-item-value-content {
        font-family: DINAlternate, DINAlternate;
        .header-content-item-value {
          font-weight: bold;
          font-size: 32px;
          color: #151719;
          line-height: 37px;
          text-align: left;
          font-style: normal;
          margin-bottom: 5px;
        }
        .header-content-item-value-title {
          font-weight: 400;
          font-size: 16px;
          color: #90979E;
          line-height: 22px;
          text-align: left;
          font-style: normal;
        }
      }
    }
    :first-child{
      border-left: none;
    }
  }
}
:deep(.el-tabs__item) {
  width: 120px;
}
</style>
