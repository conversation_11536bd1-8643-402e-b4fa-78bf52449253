export default {
  pickOrder: {
    label: {

      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      startTime: "开始时间",
      endTime: "结束时间",
      to: "至",

      sort: "序号",
      sortingCode: "分拣单号",
      receiptCode: "收运单号",
      receiptGoods: "收运商品",
      sortedGoods: "分拣后商品",
      inventoryGoods: "入库商品",
      receiveInfo: "领用信息",
      printStatus: "打印状态",
      printUserName: "打印人",
      printTime: "打印时间",
      lossOrderCode: "报损单号",
      createUserName: "创建人",
      createTime: "创建时间",
      inventoryStatus: "入库状态",
      sortingStatus: "分拣状态",
      operate: "操作",
      receiptStatus: "是否领单",

      receiptStatusOption: {
        0: "否",
        1: "是",
      },
      sortingStatusOption: {
        0: "初始",
        1: "分拣中",
        2: "完结",
      },
      inventoryStatusOption: {
        0: "初始",
        1: "部分入库",
        2: "全部入库",
      },
      printStatusOption: {
        0: "未打印",
        1: "部分打印",
        2: "全部打印",
      },
      sortingTypeOption: {
        0: "普通分拣",
        1: "快速分拣",
      },
      quickSortingStatusOption: {
        0: "草稿",
        2: "完结",
      },
      totality: "个数",
      quantity:  "数量",
      weight:"重量",
      receiveName:  "领单人",
      receiveTime:"领单时间",
      receiptTime: "收运时间",
      sortingTime: "分拣时间",

      receiptTotalQuantity:  "收运总数量",
      receiptTotalWeight: "收运总重量",
      sortingTotalQuantity:  "分拣后总量",
      sortingTotalWeight: "分拣后总重量",
      lastPrintUserName: "最后打印人",
      lastPrintTime: "最后打印时间",
      inventoryTotalQuantity: "入库总数量",
      inventoryTotalWeight: "入库总重量",
      goodsCategory:  "商品分类",
      goods:  "商品",
      specification: '规格',
      units: '单位',
      sortingAfterQty: "分拣量",
      sortingAfterWeight: "分拣重量",
      receiptQuantity:  "收运数量",
      receiptWeight: "收运重量",
      ysnCode:  "YSN码",
      operation:  "操作",
      summation:  "合计",
      selected:"已选",
      selectedQuantity:"个商品",

      sortingType:"分拣类型",
      useGoods:  "使用商品",
      sortingUserName: "分拣人",
      totalQuantity:  "总量",
      conversionQuantity:"转换量",
      remark:  "备注",
      useTotalQuantity:  "使用总量",
      sortingBeforeWeight:"使用转换量",
      sortingConversionQuantity: "分拣后转换量",
      sortingBeforeQty:  "使用量",
      warehouseAreaName:  "库区",
      availableStockQty:"可用库存量",
      availableConversionQuantity:"可用转换量",
      sortingAfterConversionQuantity: "分拣转换量",
      warehouseAreaCode:  "入库库区",
      inWarehouseQty:  "入库量",
      inWarehouseWeight:  "入库转换量",
      totalStockQty: "总库存量",

    },
    title:{
      editSorting: "去分拣",
      sortingDetail: "分拣详情",
      basicInformation: "基本信息",
      sortingTable: "分拣明细",
      ynsCodeTitle:  "商品YSN码",
    },

    button: {
      actionSorting: "去分拣",
      actionReceive: "领单",
      cancelReceive: "取消领单",
      actionDetail: "详情",
      actionView: "查看",
      cancel: " 取消",
      confirmSorting: " 确认分拣",
      addGoods: "添加商品",
      actionDelete: "删除",
      actionAdd: "新增",
      confirmDraft: " 保存草稿",
      selectProduct: "选择商品",

    },
    placeholder:{
      pickingCodeTip: "请输入大于等于4位的单号",
      calcWeight:"数量*重量，系统计算，不可编辑",
      productInputTips: "请输入商品名称或商品编码",
      calcConversionQuantity:  "根据转换关系系统计算，不可编辑",



    },
    message: {

      actionSucess: "操作成功",
      receiveTips:  "是否确认取消领单？",
      saveDataTips:  "数据未保存，是否确认退出？",
      isEmptyProduct:  "分拣明细存在未分拣的商品,请分拣",
      deleteTips: "是否确认删除？",
    },
    rules: {

      sortingCode: "分拣单号必须大于4位",
      receiptCode: "收运单号必须大于4位",
      positiveInteger: "请输入大于0的正整数,支持4位",
      positiveInteger8: "支持整数8位小数3位且不为0",

      totalNumberLess:"商品总数不能大于{message}",

    },
    dialog:{


    },
  },
};
