import path from "path";
import { createDefineMock } from "vite-plugin-mock-dev-server";

export const defineMock = createDefineMock((mock) => {
  // 拼接url
  mock.url = path.join(
    import.meta.env.VITE_APP_BASE_API + "/supply-base/",
    mock.url
  );
});

export const defineMockPms = createDefineMock((mock) => {
    // 拼接url
    mock.url = path.join(
        import.meta.env.VITE_APP_BASE_API + "/supply-pms/",
        mock.url
    );
});

export const defineMockWms = createDefineMock((mock) => {
    // 拼接url
    mock.url = path.join(
        import.meta.env.VITE_APP_BASE_API + "/supply-wms/",
        mock.url
    );
});

export const defineMockOms = createDefineMock((mock) => {
    // 拼接url
    mock.url = path.join(
        import.meta.env.VITE_APP_BASE_API + "/supply-oms/",
        mock.url
    );
});
