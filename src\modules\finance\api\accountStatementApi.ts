import request from "@/core/utils/request";
const SUPPLY_FINANCE_SERVER = 'supply-finance-server';

class API {


  /**
  * 账期范围   periodYearMonthStart  periodYearMonthEnd currentPeriodYearMonth
  * @param data 查询参数
  * @returns
  */
  static getPeriodScope(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/period/scope`,
      method: "post",
      data,
    });
  }


  /**
  * 现金日记账列表查询
  * @param data 查询参数
  * @returns
  */
  static getQueryCashJournalList(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/financialStatements/queryCashJournalList`,
      method: "post",
      data,
    });
  }

  /**
  * 银行日记账列表查询
  * @param data 查询参数
  * @returns
  */
  static getQueryBankJournalList(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/financialStatements/queryBankJournalList`,
      method: "post",
      data,
    });
  }

  /**
   * 科目余额表查询
   * @param data 查询参数
   * @returns
   */
  static getQuerySubjectBalanceFlowList(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/financialStatements/querySubjectBalanceFlowList`,
      method: "post",
      data,
    });
  }

  /**
   * 明细表：会计科目列表
   * @param data 查询参数
   * @returns
   */
  static getSubjectByFlow(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/financialStatements/getSubjectByFlow`,
      method: "post",
      data,
    });
  }

  /**
  * 明细表列表
  * @param data 查询参数
  * @returns
  */
  static getDetailFlow(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/financialStatements/detailFlow`,
      method: "post",
      data,
    });
  }

  /**
  * 查询科目列表 启始科目 结束科目列表  会计科目
  * @param data 查询参数
  * @returns
  */
  static subjectGetList(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/subject/getList`,
      method: "post",
      data,
    });
  }

  /**
  * 财务报表 总账表
  * @param data 查询参数
  * @returns
  */
  static getGeneralLedger(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/financialStatements/generalLedger`,
      method: "post",
      data,
    });
  }

  /**
  * 财务报表 利润表1 资产负债表2 3 现金流量表4
  * @param data 查询参数
  * @returns
  */
  static getQueryTreeList(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/projectPeriod/queryTreeList`,
      method: "post",
      data,
    });
  }

 /**
  * 现金流量项目明细列表
  * @param data 查询参数
  * @returns
  */
  static getCashFlowList(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/financialStatements/getCashFlowList`,
      method: "post",
      data,
    });
  }

  /**
  * 现金流量项目明细列表-会计科目下拉框
  * @param data 查询参数
  * @returns
  */
  static getCashFlowSubjectSelect(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/financialStatements/getCashFlowSubjectSelect`,
      method: "post",
      data,
    });
  }

  /**
  * 编辑公式
  * @param data 查询参数
  * @returns
  */
  static editFormula(data: any) {
    return request({
      url: `${SUPPLY_FINANCE_SERVER}/projectPeriod/editFormula`,
      method: "post",
      data,
    });
  }
}

export default API; 