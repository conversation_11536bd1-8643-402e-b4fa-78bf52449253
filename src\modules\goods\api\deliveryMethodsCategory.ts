import request from "@/core/utils/request";

const DELIVERY_METHODS_CATEGORY_BASE_URL = "/supply-biz-common/deliveryMethodsCategory";

class DeliveryMethodsCategoryAPI {
  /**
   * 分页查询配送方式分类
   * @param queryParams 查询参数
   * @returns 分页结果
   */
  static getPageList(queryParams?: DeliveryMethodsCategoryPageQuery) {
    return request<any, DeliveryMethodsCategoryPageResult>({
      url: `${DELIVERY_METHODS_CATEGORY_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取配送方式分类详情
   * @param id 配送方式分类ID
   * @returns 配送方式分类详情
   */
  static getDetail(id: number) {
    return request<any, DeliveryMethodsCategoryVO>({
      url: `${DELIVERY_METHODS_CATEGORY_BASE_URL}/queryDetail`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 新增配送方式分类
   * @param data 配送方式分类表单数据
   * @returns 操作结果
   */
  static add(data: DeliveryMethodsCategoryForm) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_CATEGORY_BASE_URL}/add`,
      method: "post",
      data,
    });
  }

  /**
   * 编辑配送方式分类
   * @param data 配送方式分类表单数据（包含ID）
   * @returns 操作结果
   */
  static update(data: DeliveryMethodsCategoryForm) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_CATEGORY_BASE_URL}/edit`,
      method: "post",
      data,
    });
  }

  /**
   * 删除配送方式分类
   * @param id 配送方式分类ID
   * @returns 操作结果
   */
  static deleteById(id: number) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_CATEGORY_BASE_URL}/delete`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 批量删除配送方式分类
   * @param ids ID数组
   * @returns 操作结果
   */
  static deleteBatch(ids: number[]) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_CATEGORY_BASE_URL}/deleteBatch`,
      method: "post",
      data: ids,
    });
  }
}

/**
 * 配送方式分类分页查询参数
 */
export interface DeliveryMethodsCategoryPageQuery {
  /** 当前页 */
  page?: number;
  /** 每页条数（最大1000） */
  limit?: number;
  /** 主键ID */
  id?: number;
  /** 配送方式分类名称 */
  deliveryMethodsCategoryName?: string;
  /** 排序 */
  sort?: number;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 配送方式分类表单数据
 */
export interface DeliveryMethodsCategoryForm {
  /** 主键ID（编辑时必填） */
  id?: number;
  /** 配送方式分类名称 */
  deliveryMethodsCategoryName?: string;
  /** 排序 */
  sort?: number;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 配送方式分类视图对象
 */
export interface DeliveryMethodsCategoryVO {
  /** 组织编码 */
  orgCode?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 创建用户ID */
  createUser?: number;
  /** 更新用户ID */
  updateUser?: number;
  /** 主键ID */
  id?: number;
  /** 配送方式分类名称 */
  deliveryMethodsCategoryName?: string;
  /** 排序 */
  sort?: number;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 配送方式分类分页结果对象
 */
export interface DeliveryMethodsCategoryPageResult {
  /** 当前页 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 数据列表 */
  records: DeliveryMethodsCategoryVO[];
}

export default DeliveryMethodsCategoryAPI; 