<template>
  <div class="app-container">
    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          size="large"
          type="primary"
          @click="handleOpenDialog('add', null)"
        >
          新建功能按钮
        </el-button>
      </template>
      <el-table stripe v-loading="loading" :data="actionTableData">
        <template #empty>
          <img src="@/core/assets/images/empty.png" />
        </template>
        <el-table-column
          prop="actionName"
          label="功能名称"
          show-overflow-tooltip
        />
        <el-table-column
          prop="actionCode"
          label="功能标识"
          show-overflow-tooltip
        />
        <el-table-column prop="menuId" label="所属页面" show-overflow-tooltip>
          {{ menuName }}
        </el-table-column>
        <el-table-column prop="status" label="状态" show-overflow-tooltip>
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              inline-prompt
              active-text="启用"
              inactive-text="禁用"
              @change="handleDisable(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200">
          <template #default="scope">
            <el-button
              size="small"
              type="success"
              @click="handleOpenDialog('update', scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="success"
              @click="onConfigAction(scope.row)"
            >
              接口权限
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      @close="handleCloseDialog"
      :close-on-click-modal="false"
      width="600"
    >
      <el-form
        ref="actionFormRef"
        :model="actionForm"
        :rules="actionFormRules"
        label-position="right"
        label-width="82px"
      >
        <el-form-item label="功能名称" prop="actionName">
          <el-input
            v-model.trim="actionForm.actionName"
            placeholder="请输入功能名称"
            clearable
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="功能标识" prop="actionCode">
          <el-input
            v-model.trim="actionForm.actionCode"
            placeholder="菜单标识+自定义标识，如actionAdd"
            clearable
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input
            v-model.trim="actionForm.priority"
            placeholder="请输入功能按钮展示顺序"
            type="number"
            clearable
            :min="0"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="功能描述" prop="actionDesc">
          <el-input
            v-model.trim="actionForm.actionDesc"
            type="textarea"
            :rows="2"
            placeholder="请输入功能按钮描述"
            maxlength="100"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submitForm"
            :loading="loadingDialog"
          >
            确 定
          </el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 接口权限配置 -->
    <el-dialog
      :title="transferTitle"
      v-model="showTransfer"
      width="780px"
      :close-on-click-modal="false"
      @close="onTransferClose"
      class="common-dialog"
    >
      <el-transfer
        v-model="authorityIds"
        :data="transferData"
        filterable
        :titles="['未选接口', '已选接口']"
        ref="transferRef"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="onSubmitTranster"
            :loading="loadingDialog"
          >
            确定
          </el-button>
          <el-button @click="onTransferClose">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "menuAuthority",
  inheritAttrs: false,
});

import MenuAPI, { MenuVO } from "@/core/api/authorityMenu";

const router = useRouter();
const route = useRoute();
const menuId = ref();
const menuName = ref();

const loading = ref(false);
const dialog = reactive({
  title: "新建功能按钮",
  visible: false,
});
const systemType = route.query.systemType;
const loadingDialog = ref(false);
const typeButton = ref();
// 菜单表格数据
const actionTableData = ref();
const actionFormRef = ref();
const actionForm = ref({
  menuId: "",
  actionId: "",
  actionCode: "",
  actionName: "",
  priority: "",
  actionDesc: "",
});
const actionFormRules = reactive({
  actionName: [{ required: true, message: "请输入功能名称", trigger: "blue" }],
  actionCode: [{ required: true, message: "请输入功能标识", trigger: "blur" }],
});
const transferRef = ref();
const transferTitle = ref();
const showTransfer = ref(false);
const transferData = ref([]);
const authorityIds = ref([]);
const actionId = ref();

// 选择表格的行菜单ID
const selectedMenuId = ref<number | undefined>();

function handleQuery() {
  loading.value = true;
  MenuAPI.getActionList(menuId.value)
    .then((data) => {
      actionTableData.value = data;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleOpenDialog(type, row) {
  typeButton.value = type;
  const title = typeButton.value == "update" ? "编辑功能按钮" : "添加功能按钮";
  if (typeButton.value == "update") {
    loading.value = true;
    MenuAPI.getActionDetail(row.actionId).then((data) => {
      actionForm.value = { ...data };
      dialog.title = title;
      dialog.visible = true;
      loading.value = false;
    });
  } else {
    actionForm.value.menuId = menuId.value;
    dialog.title = title;
    dialog.visible = true;
  }
}

function submitForm() {
  actionFormRef.value.validate((isValid: boolean) => {
    if (isValid) {
      let params = {
        systemType,
        menuId: actionForm.value.menuId,
        actionCode: actionForm.value.actionCode,
        actionName: actionForm.value.actionName,
        priority: Number(actionForm.value.priority),
        actionDesc: actionForm.value.actionDesc,
        actionId: undefined,
      };
      loadingDialog.value = true;
      const msg =
        typeButton.value == "update" ? "编辑功能按钮成功" : "添加功能按钮成功";
      const url =
        typeButton.value == "update" ? MenuAPI.actionUpdate : MenuAPI.actionAdd;
      if (typeButton.value == "update") {
        params.actionId = actionForm.value.actionId;
      }
      url(params)
        .then(() => {
          ElMessage.success(msg);
          handleCloseDialog();
          handleQuery();
        })
        .finally(() => (loadingDialog.value = false));
    }
  });
}

function handleDisable(row: any) {
  let title: string = row.status == 1 ? "启用" : "禁用";
  ElMessageBox.confirm(`确定${title}吗？`, "提示", {
    type: "warning",
  })
    .then(() => {
      let param = {
        status: row.status,
        actionId: row.actionId,
      };
      MenuAPI.actionUpdate(param).then((res) => {
        ElMessage.success(`${title}成功`);
        handleQuery();
      });
    })
    .catch(() => {
      row.status = row.status === 1 ? 0 : 1;
    });
}

function handleDelete(row) {
  if (row.status === 1) {
    ElMessage.error(`功能按钮【${row.actionName}】处于启用状态，不可删除！`);
    return false;
  }
  ElMessageBox.confirm(`确定将功能按钮【${row.actionName}】删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    function () {
      loading.value = true;
      let params = {
        actionId: row.actionId,
      };
      MenuAPI.actionDelete(params)
        .then(() => {
          ElMessage.success("删除功能按钮成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    },
    function () {
      ElMessage.info("已取消");
    }
  );
}

// 关闭弹窗
function handleCloseDialog() {
  dialog.visible = false;
  actionFormRef.value.resetFields();
  actionFormRef.value.clearValidate();
  actionForm.value.menuId = undefined;
  actionForm.value.actionId = undefined;
  actionForm.value.actionCode = undefined;
  actionForm.value.actionName = undefined;
  actionForm.value.priority = undefined;
  actionForm.value.actionDesc = undefined;
}

// 穿梭框关闭事件
function onTransferClose() {
  transferRef.value.clearQuery("left");
  transferRef.value.clearQuery("right");
  showTransfer.value = false;
}

// 接口权限配置
async function onConfigAction(row) {
  loading.value = true;
  await getAllApi();
  transferTitle.value = `接口授权->${row.actionCode}`;
  actionId.value = row.actionId;
  MenuAPI.querySelectApi(row.actionId)
    .then((data) => {
      authorityIds.value = data.map((item) => item.authorityId);
      showTransfer.value = true;
    })
    .finally(() => (loading.value = false));
}

// 穿梭框保存事件
function onSubmitTranster() {
  loadingDialog.value = true;
  let params = {
    actionId: actionId.value,
    authorityIds: authorityIds.value,
  };
  MenuAPI.updateSelectApi(params)
    .then(() => {
      ElMessage.success("权限配置成功");
      onTransferClose();
    })
    .finally(() => (loadingDialog.value = false));
}

// 拉取所有接口
function getAllApi() {
  loading.value = true;
  MenuAPI.queryApi()
    .then((data) => {
      transferData.value = data.map((item) => {
        return {
          key: item.authorityId,
          label: `${item.path}-${item.apiName}`,
        };
      });
    })
    .finally(() => (loading.value = false));
}

onMounted(() => {
  menuId.value = route.query.menuId;
  menuName.value = route.query.menuName;
  handleQuery();
});
</script>
<style scoped lang="scss"></style>
<style lang="scss">
.common-dialog {
  .el-transfer {
    margin-bottom: 18px;
    display: flex;
    align-items: center;

    .el-input__wrapper {
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #d7dbdf;
      box-shadow: 0 0 0 0px;
    }

    .el-checkbox__input.is-indeterminate .el-checkbox__inner:before {
      top: 6px;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner:after {
      left: 5px;
      top: 1px;
      width: 4px;
      height: 8px;
    }

    .el-transfer-panel
      .el-transfer-panel__header
      .el-checkbox
      .el-checkbox__label
      span {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #151719;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }

    .el-transfer-panel__filter .el-input__inner {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }

    .el-transfer-panel {
      width: 296px;
      /*flex: 1;*/
      .el-transfer-panel__header {
        width: 296px;
      }

      .el-transfer-panel__body {
        width: 296px;
      }

      .el-checkbox__inner {
        border-radius: 3px;
        height: 16px;
        width: 16px;
      }

      .el-transfer-panel__header {
        background: #ffffff;

        .el-checkbox .el-checkbox__label {
          height: 20px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #151719;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }
    }

    .el-transfer-panel__item {
      .el-checkbox__input {
        position: absolute;
        top: 3px;
      }

      .el-checkbox .el-checkbox__label {
        height: 20px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #151719;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }

    .el-transfer__buttons {
      margin: 0px 16px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      /*padding: 0 16px;*/
      .el-button {
        /*display: block;*/
        /*padding: 8px 20px;*/
        /*margin-right: 0;*/
        /*margin-left:8px;*/
        width: 32px;
        height: 32px;
        margin-left: 5px;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
