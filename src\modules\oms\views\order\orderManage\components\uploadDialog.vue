<template>
  <div>
    <el-drawer :model-value="visible" :title="$t('common.uploadFiLe')" :close-on-click-modal="false" @close="close" size="500px">
      <div>
          <upload-multiple
                  :listType="`text`"
                  :tips="''"
                  :fileSize="2"
                  :fileType="['xls', 'xlsx']"
                  :isPrivate="`public-read`"
                  :modelValue="imagesUrls"
                  ref="detailPicsRef"
                  @update:model-value="onChangeMultiple"
                  @file="onChangeMultipleFile"
                  :limit="1"
                  :formRef="fileRef"
                  class="modify-multipleUpload"
                  name="detailPic"
          />
        <div class="downLoadOrder cursor-pointer" @click="downLoadOrder">下载订购单导入模板.xlsx</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="info" @click="close">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ $t("common.confirm") }}
          </el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import FileAPI from "@/core/api/file";
import OrderAPI, {OrderFrom} from "@/modules/oms/api/order";
import {previewSingle} from "@/core/utils/commonUpload";

const emit = defineEmits(["update:visible", "onSubmit"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  showUploadBtn: {
    type: Boolean,
    default: true,
  },
  submitLoading: {
    type: Boolean,
    default: false,
  },
  isClose: {
    type: Boolean,
    default: false,
  },
});


const imagesUrls = ref([]);
const submitLoading = ref();

watch(
    () => props.visible,
     (val) => {
        if (!val) {
            imagesUrls.value = [];
        }
    },
    { deep: true, immediate: true }
);

watch(
    () => props.submitLoading,
    (val) => {
        submitLoading.value = val;
    },
    { deep: true, immediate: true }
);

watch(
    () => props.isClose,
    (val) => {
        if(val){
            close()
        }
    },
    { deep: true, immediate: true }
);


const file = ref();
const fileRef = ref();
const { t } = useI18n();

function close() {
  emit("update:visible", false);
  reset();
}
function reset() {
  imagesUrls.value = [];
  submitLoading.value = false;
}

function onChangeMultiple(val: any) {
  imagesUrls.value = val;
}
function onChangeMultipleFile(val: any) {
 file.value = val;
}

function handleSubmit() {
    if(imagesUrls.value.length==0){
       return ElMessage.error(t('omsOrder.message.uploadTips'));
    }else{
        let data={
            imagesUrls:imagesUrls.value,
            file:file.value,
        }
        emit("onSubmit", data);
        // close();
    }
}

function downLoadOrder() {
    OrderAPI.getImportTemplateUrl()
        .then((data) => {
            if(data){
                FileAPI.downloadFile(data, '订购单导入模板.xlsx');
            }
        })
}

defineExpose({
});
</script>

<style lang="scss" scoped>
    .downLoadOrder{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        font-style: normal;
        color: var(--el-color-primary) ;
    }
</style>
