import { nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { useTagsViewStore } from '@/core/store';
import router from '@/core/router';

/**
 * 导航相关的组合式函数
 */
export function useNavigation() {
  const route = useRoute();
  const tagsViewStore = useTagsViewStore();

  /**
   * 删除指定路径的视图缓存并重新打开（解决缓存问题）
   * @param {string | object} pathObj - 要重新打开的路径或路由对象
   * @returns {Promise<void>}
   */
  const refreshAndNavigate = async (pathObj: string | any): Promise<void> => {
    const targetPath = typeof pathObj === 'string' ? pathObj : pathObj.path;
    
    // 先删除目标路径的视图缓存
    await tagsViewStore.delViewByPath(targetPath);

    // 等待视图删除完成后再进行路由跳转，重新打开页面
    nextTick(() => {
      router.push(pathObj);
    });
  };

  return {
    refreshAndNavigate
  };
} 