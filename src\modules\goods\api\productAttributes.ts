import request from "@/core/utils/request";

const PRODUCT_ATTRIBUTES_BASE_URL = "/supply-biz-common/productAttributes";

class ProductAttributesAPI {
  /**
   * 分页查询商品属性
   * @param queryParams 查询参数
   * @returns 分页结果
   */
  static getPageList(queryParams?: ProductAttributesPageQuery) {
    return request<any, ProductAttributesPageResult>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取商品属性详情
   * @param id 商品属性ID
   * @returns 商品属性详情
   */
  static getDetail(id: number) {
    return request<any, ProductAttributesVO>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/queryDetail`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 新增商品属性
   * @param data 商品属性表单数据
   * @returns 操作结果
   */
  static add(data: ProductAttributesForm) {
    return request<any, string>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/add`,
      method: "post",
      data,
    });
  }

  /**
   * 编辑商品属性
   * @param data 商品属性表单数据（包含ID）
   * @returns 操作结果
   */
  static update(data: ProductAttributesForm) {
    return request<any, string>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/edit`,
      method: "post",
      data,
    });
  }

  /**
   * 删除商品属性
   * @param id 商品属性ID
   * @returns 操作结果
   */
  static deleteById(id: number) {
    return request<any, string>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/delete`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 批量删除商品属性
   * @param ids ID数组
   * @returns 操作结果
   */
  static deleteBatch(ids: number[]) {
    return request<any, string>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/deleteBatch`,
      method: "post",
      data: ids,
    });
  }

  /**
   * 批量启用商品属性
   * @param ids ID数组
   * @returns 操作结果
   */
  static enableBatch(ids: number[]) {
    return request<any, string>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/enableStatusBatch`,
      method: "post",
      data: ids,
    });
  }

  /**
   * 批量禁用商品属性
   * @param ids ID数组
   * @returns 操作结果
   */
  static disableBatch(ids: number[]) {
    return request<any, string>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/disableStatusBatch`,
      method: "post",
      data: ids,
    });
  }

  /**
   * 查询商品属性字段枚举列表
   * @returns 字段枚举列表
   */
  static getFieldEnumList() {
    return request<any, string[]>({
      url: `${PRODUCT_ATTRIBUTES_BASE_URL}/queryProductAttributesFieldEnumList`,
      method: "post",
    });
  }
}

/**
 * 商品属性分页查询参数
 */
export interface ProductAttributesPageQuery {
  /** 当前页 */
  page?: number;
  /** 每页条数（最大1000） */
  limit?: number;
  /** 主键ID */
  id?: number;
  /** 字段名称，描述商品属性的名称 */
  fieldName?: string;
  fieldCode?: string;
  /** 类型名称，描述商品属性的类型 */
  typeName?: string;
  /** 排序 */
  sort?: number;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 商品属性表单数据
 */
export interface ProductAttributesForm {
  /** 主键ID（编辑时必填） */
  id?: number;
  /** 字段名称，描述商品属性的名称 */
  fieldName?: string;
  /** 字段编码，描述商品属性的编码 */
  fieldCode?: string;
  /** 类型名称，描述商品属性的类型 */
  typeName?: string;
  /** 排序 */
  sort?: number;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 商品属性视图对象
 */
export interface ProductAttributesVO {
  /** 组织编码 */
  orgCode?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 创建用户ID */
  createUser?: number;
  /** 更新用户ID */
  updateUser?: number;
  /** 主键ID */
  id?: number;
  /** 字段名称，描述商品属性的名称 */
  fieldName?: string;
  /** 类型名称，描述商品属性的类型 */
  typeName?: string;
  /** 排序 */
  sort?: number;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 商品属性分页结果对象
 */
export interface ProductAttributesPageResult {
  /** 当前页 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 数据列表 */
  records: ProductAttributesVO[];
}

/**
 * 启禁用状态枚举
 */
export enum EnableStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
}

export default ProductAttributesAPI; 