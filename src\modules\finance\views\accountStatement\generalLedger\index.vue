<template>
  <div class="delivery-method-container-block">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true" label-width="84px">
        <el-form-item :label="t('generalLedgerList.label.accountingPeriod')">
          <el-date-picker
            :clearable="false"
            @change="monthRangeChange"
            v-model="searchForm.period"
            type="monthrange"
            range-separator="至"
            :start-placeholder="t('generalLedgerList.label.start')"
            :end-placeholder="t('generalLedgerList.label.end')"
            format="YYYY-MM"
            value-format="YYYYMM"
            :disabled-date="
              (date) => {
                const start = loadPeriodList.periodYearMonthStart;
                const end = loadPeriodList.periodYearMonthEnd;
                if (!start || !end) return false;
                const y = date.getFullYear();
                const m = (date.getMonth() + 1).toString().padStart(2, '0');
                const ym = `${y}${m}`;
                return ym < start || ym > end;
              }
            "
          />
        </el-form-item>
        <el-form-item :label="t('generalLedgerList.label.startAccount')" prop="subjectCodeStart1">
          <el-select clearable filterable v-model="searchForm.subjectCodeStart1" :placeholder="t('generalLedgerList.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in cashAccountList" :key="category.subjectCode" :label="category.subjectCode + '_' + category.subjectName" :value="category.subjectCode" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('generalLedgerList.label.endAccount')" prop="subjectCodeEnd1">
          <el-select clearable filterable v-model="searchForm.subjectCodeEnd1" :placeholder="t('generalLedgerList.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in cashAccountList" :key="category.subjectCode" :label="category.subjectCode + '_' + category.subjectName" :value="category.subjectCode" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('generalLedgerList.label.accountLevel')" prop="subjectLevelStart">
          <el-select v-model="searchForm.subjectLevelStart" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100px">
            <el-option v-for="category in levelList" :key="category.id" :label="category.label" :value="category.id" />
          </el-select>
          <span style="margin: 0 8px">{{ t('detailsTable.label.to') }}</span>
          <el-select filterable v-model="searchForm.subjectLevelEnd" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100px">
            <el-option v-for="category in levelList" :key="category.id" :label="category.label" :value="category.id" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item :label="t('generalLedgerList.label.showContent')" prop="checkList">
          <el-select filterable v-model="searchForm.checkList" :placeholder="t('balanceTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in showContentList" :key="category.statusId" :label="category.statusName" :value="category.statusId" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <!-- v-hasPerm="['product:purchase:search']" -->
          <el-button type="primary" @click="onSearchHandlerEvent" v-hasPerm="['finance:generalLedger:search']">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="onResetHandlerEvent" v-hasPerm="['finance:generalLedger:reset']">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <section class="delivery-method-container">
      <div class="right-panel">
        <div class="table-container">
          <el-table
            v-el-table-infinite-scroll="loadMore"
            v-loading="loading"
            stripe
            :data="tableData1"
            row-key="id"
            style="width: 100%"
            height="calc(100vh - 240px)"
            :tree-props="{ children: 'balanceFlows', hasChildren: 'hasChildren' }"
            :span-method="spanMethod"
            :default-expand-all="true"
            ref="treeTable"
          >
            <template #empty>
              <Empty />
            </template>
            <!-- 折叠按钮和名称 -->
            <el-table-column prop="subjectFullName" :label="t('generalLedgerList.table.accountName')" min-width="120" />
            <el-table-column prop="index1" :label="t('balanceTable.table.serialNo')" width="80" align="center" />
            <!-- 其他列 -->
            <el-table-column prop="subjectFullCode" :label="t('generalLedgerList.table.accountCode')" />
            <el-table-column prop="periodYearMonth" :label="t('generalLedgerList.table.period')" />
            <el-table-column prop="summary" :label="t('generalLedgerList.table.summary')" />
            <el-table-column prop="debitAmount" :label="t('generalLedgerList.table.debit')" />
            <el-table-column prop="creditAmount" :label="t('generalLedgerList.table.credit')" />
            <el-table-column prop="subjectBalanceDirection" :label="t('generalLedgerList.table.direction')">
              <template #default="scope">
                <div
                  class="balanceType"
                  :class="{
                    creditBalance: scope.row.balanceDirection === '贷',
                    debitBalance: scope.row.balanceDirection === '借',
                  }"
                >
                  {{ scope.row.balanceDirection }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="balanceAmount" :label="t('generalLedgerList.table.balance')" />
          </el-table>
        </div>
      </div>
    </section>
  </div>
</template>
<script setup lang="ts">
// 总账
import tableMixin from '@/modules/finance/mixins/table';
import API from '@/modules/finance/api/accountStatementApi';
import { ref, reactive, onMounted, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
// defineOptions({
//   name: "BalanceTable",
//   inheritAttrs: false,
// })
const loadPeriodList = reactive({
  periodYearMonthEnd: '',
  periodYearMonthStart: '',
  currentPeriodYearMonth:''
});
const loadPeriod = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    loadPeriodList.periodYearMonthStart = String(response.periodYearMonthStart);
    loadPeriodList.periodYearMonthEnd = String(response.periodYearMonthEnd);
    loadPeriodList.currentPeriodYearMonth = String(response.currentPeriodYearMonth);
    searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
    searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
    searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
    onSearchHandler()
  } catch (error) {
    if(error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    loading.value = false;
  }
};
function tableRowClassName({ row, column }) {
  // 让第一行变红
  if (row.isParent) {
    return 'red-cell';
  }
  return '';
}
const searchForm = reactive({
  period: [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth],
  subjectCodeEnd1: undefined,
  subjectCodeStart1: undefined,
  subjectCodeEnd: undefined,
  subjectCodeStart: undefined,
  subjectLevelStart: 1,
  subjectLevelEnd: 4,
  periodYearMonthStart: loadPeriodList.currentPeriodYearMonth,
  periodYearMonthEnd: loadPeriodList.currentPeriodYearMonth
})
// const showContentList = ref([
//   {
//     statusId: 1,
//     statusName: t("detailsTable.label.hideZeroBalance"),
//   },
//   {
//     statusId: 2,
//     statusName: t("detailsTable.label.hideNoTransactionZeroBalance"),
//   },
//   {
//     statusId: 3,
//     statusName: t("detailsTable.label.hideSummaryIfNoTransaction"),
//   }
// ]);
const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (row.balanceFlows) {
    if (columnIndex === 0) {
      return [1, 9];
    }
    return [1, 0] // 其他列合并隐藏
  }
}
const levelList = ref([
  { id: 1, label: '1' },
  { id: 2, label: '2' },
  { id: 3, label: '3' },
  { id: 4, label: '4' }
])
const { loading, tableData, total, paginationInfo, headFormRef, router, path, onSearchHandler, onResetHandler, onPaginationChangeHandler, onDeleteHandler, onStatusChangeHandler } = tableMixin({
  searchForm,
  isLimit: false,
  tableGetApi: API.getGeneralLedger,
  tableCallback: tableCallbackFun,
});
// 处理列表数据
function tableCallbackFun() {
  tableData.value.map((item: any,index) => {
    item.index = index + 1;
    item.id = index + 1;
    if(item.balanceFlows && item.balanceFlows.length){
      item.balanceFlows.map((item1: any,index1) => {
        item1.index1 = index1 + 1;
        item1.id = item.id + '-'+ item1.index1;
      })
    }
  });
}
const monthRangeChange = (val: [string, string]) => {
  if (val?.length) {
    searchForm.periodYearMonthStart = val[0];
    searchForm.periodYearMonthEnd = val[1];
  } else {
    searchForm.periodYearMonthStart = '';
    searchForm.periodYearMonthEnd = '';
  }
};
interface CashAccountItem {
  id: number | string;
  subjectCode: string;
  subjectName: string;
  statusId?: number | string;
  statusName?: string;
}
const cashAccountList = ref<CashAccountItem[]>([]);
const loadcashAccountList = async () => {
  try {
    const response = await API.subjectGetList({
      status: 1,
    });
    cashAccountList.value = response || [];
  } catch (error) {
    cashAccountList.value = [];
  }
};
const onResetHandlerEvent = () => {
  searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
  searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
  searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
  searchForm.subjectCodeStart = undefined;
  searchForm.subjectCodeEnd = undefined;
  searchForm.subjectCodeStart1 = undefined;
  searchForm.subjectCodeEnd1 = undefined;
  searchForm.subjectLevelStart = 1;
  searchForm.subjectLevelEnd = 4;
  index.value = 0;
  allTableData.value = [];
  tableData1.value = [];
  onResetHandler();
};
const onSearchHandlerEvent = () => {
  // 补齐 subjectCodeStart 到12位
  searchForm.subjectCodeStart = undefined;
  searchForm.subjectCodeEnd = undefined;
  if (searchForm.subjectCodeStart1 && searchForm.subjectCodeStart1.length < 11) {
    searchForm.subjectCodeStart = searchForm.subjectCodeStart1.padEnd(11, '0')
  }
  // 补齐 subjectCodeEnd 到12位
  if (searchForm.subjectCodeEnd1 && searchForm.subjectCodeEnd1.length < 11) {
    searchForm.subjectCodeEnd = searchForm.subjectCodeEnd1.padEnd(11, '0')
  }
  index.value = 0;
  allTableData.value = [];
  tableData1.value = [];
  onSearchHandler();
};


let allTableData = ref([]);
let tableData1 = ref([]);
let index = ref(0);
const PAGE_SIZE = 5;

watch(
  () => tableData.value,
  (val) => {
    if (val.length >= PAGE_SIZE) {
      allTableData.value = JSON.parse(JSON.stringify(val));
      tableData1.value = val.slice(0, PAGE_SIZE);
      index.value = 1; // 关键：初始加载后，index设为1
    } else {
      tableData1.value = val;
      index.value = 1;
    }
  },
  { deep: true }
);

const loadMore = () => {
  const start = index.value * PAGE_SIZE;
  const end = start + PAGE_SIZE;
  if (start >= allTableData.value.length) return; // 没有更多数据
  let newTableData = allTableData.value.slice(start, end);
  tableData1.value = tableData1.value.concat(newTableData);
  index.value++;
};

onMounted(() => {
  loadPeriod()
  loadcashAccountList();
})
</script>

<style lang="scss" scoped>
.delivery-method-container {
  .right-panel {
    padding: 20px;
    background-color: #fff;

    .search-card,
    .toolbar-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }

    .search-card {
      :deep(.el-form--inline .el-form-item) {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 20px;
    }
  }
}

:deep(.el-table) {
  .el-switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
  }
}

.table-container {
  background-color: #fff;
}

.balanceType {
  width: 56px;
  height: 32px;
  background: rgba(64, 158, 255, 0.08);
  border-radius: 2px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #409eff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.balanceType.debitBalance {
  background: rgba(41, 182, 16, 0.08);
  border: 1px solid rgba(41, 182, 16, 0.2);
  color: #29b610;
}

.balanceType.creditBalance {
  background: rgba(255, 156, 0, 0.08);
  border: 1px solid rgba(255, 156, 0, 0.2);
  color: #ff9c00;
}

::v-deep .red-cell {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #762adb !important;
  background: #f6f0ff !important;
  box-shadow: inset 1px 1px 0px 0px #e5e7f3, inset -1px -1px 0px 0px #e5e7f3;
}

::v-deep .red-cell .el-table__expand-icon {
  color: #762adb !important;
}

::v-deep .red-cell .el-table__body .el-table__cell {
  color: #762adb !important;
}
</style>
