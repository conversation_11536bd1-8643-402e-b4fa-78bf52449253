<template>
  <div class="app-container">
    <div class="addPurchaseReturn"  v-loading="loading">
      <div class="page-content">
        <el-form :model="form" :rules="rules" ref="fromRef" label-width="108px" label-position="right">
          <div class="title-lable">
            <div class="title-content">{{ $t("purchaseReturn.label.basicInformation") }}</div>
          </div>
          <div>
            <el-row class="flex-center-start">
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.orderCode')" prop="orderCode">
                  <el-input class="!w-[256px]" :placeholder="$t('common.placeholder.inputTips')" v-model="form.orderCode" @change="getOrderDetail"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.warehouse')" prop="warehouseCode">
                  <el-select
                    v-model="form.warehouseCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    filterable
                    class="!w-[256px]"
                    @change="setWarehouseName"
                  >
                    <el-option v-for="item in storehouseList" :key="item.warehouseId" :label="item.warehouseName" :value="item.warehouseCode"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.purchaseUserName')" prop="purchaseUserName">
                  <el-input
                    class="!w-[256px]"
                    v-model="form.purchaseUserName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.returnMethod')" prop="deliveryType" :rules="[{required: true, message: t('purchaseReturn.rules.returnMethod'), trigger:  ['change', 'blur']}]">
                  <el-select v-model="form.deliveryType" @change="getMethodName" clearable :placeholder="$t('common.placeholder.selectTips')" class="!w-[256px]">
                    <el-option v-for="item in returnMethodList" :key="item.key" :value="item.key" :label="item.value"   />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.orderType')" prop="orderType">
                  <el-select
                    v-model="form.orderType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    disabled
                    class="!w-[256px]"
                  >
                    <el-option v-for="item in orderTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.supplierName')" prop="supplierName" >
                  <el-input
                    class="!w-[256px]"
                    v-model="form.supplierName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('purchaseReturn.label.planDeliveryTime')" class="address_style">
                  <el-form-item label-width="0" prop="returnDate" :rules="[{required:true,message:t('purchaseReturn.rules.returnDate')}]">
                    <el-date-picker
                      v-model="form.returnDate"
                      type="date"
                      :placeholder="$t('purchaseReturn.placeholder.chooseDate')">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label-width="0" prop="returnTime" :rules="[{required:true,message:t('purchaseReturn.rules.returnTime')}]">
                    <el-time-picker
                      v-model="form.returnTime"
                      format="HH:mm:ss"
                      value-format="HH:mm:ss"
                      :placeholder="$t('purchaseReturn.placeholder.deliveryTime')">
                    </el-time-picker>
                  </el-form-item>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">{{ $t("purchaseReturn.label.returnAddress") }}</div>
          </div>
          <div>
            <el-row class="flex-center-start">
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.returnUserName')" prop="returnUserName" >
                  <el-input
                    class="!w-[256px]"
                    v-model="form.returnUserName"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    maxlength="50"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('purchaseReturn.label.returnUserMobile')" class="address_style">
                  <el-form-item
                    prop="returnUserArea"
                    label-width="0"
                  >
                    <el-select
                      v-model="form.returnUserArea"
                      :placeholder="$t('purchaseReturn.label.areaCode')"
                      clearable
                      class="!w-[80px]"
                    >
                      <el-option
                        v-for="(item, index) in areaList"
                        :key="index"
                        :label="item.internationalCode"
                        :value="item.internationalCode"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="returnUserMobile" label-width="0">
                    <el-input
                      v-model="form.returnUserMobile"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      maxlength="30"
                      class="!w-[175px]"
                    />
                  </el-form-item>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('purchaseReturn.label.receiveAddress')" class="address_style">
                  <el-form-item prop="returnCountryId" label-width="0" :rules="[{required:true,message:t('purchaseReturn.rules.countryId')}]">
                    <!-- 国家 -->
                    <el-select
                      v-model="form.returnCountryId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      clearable
                      @change="countryChange"
                      class="!w-[100px]"
                    >
                      <el-option
                        v-for="(item, index) in countryList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="returnProvinceId" label-width="0" v-if="showProvinceInput" :rules="[{required:true,message:t('afterSaleManagement.rules.provinceId')}]">
                    <!-- 省 -->
                    <el-select
                      v-model="form.returnProvinceId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleProvinceChange"
                      class="!w-[100px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in provinceList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item prop="returnCityId" label-width="0" v-if="showCityInput" :rules="[{required:true,message:t('afterSaleManagement.rules.cityId')}]">
                    <!-- 市 -->
                    <el-select
                      v-model="form.returnCityId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleCityChange"
                      class="!w-[120px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in cityList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    prop="returnDistrictId"
                    label-width="0"
                    v-if="showDistrictInput"
                    :rules="[{required:true,message:t('afterSaleManagement.rules.districtId')}]"
                  >
                    <!-- 区 -->
                    <el-select
                      v-model="form.returnDistrictId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleDistrictChange"
                      class="!w-[120px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in districtList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    prop="returnAddress"
                    label-width="0"
                    :rules="[{required:true,message:t('afterSaleManagement.rules.address')}]"
                  >
                    <el-input
                      v-model="form.returnAddress"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="100"
                      clearable
                      class="!w-[256px]"
                    />
                  </el-form-item>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">{{ $t("purchaseReturn.label.productListInformation") }}</div>
          </div>
          <div>
            <el-table :data="form.detailList" highlight-current-row stripe>
              <el-table-column type="index" :label="$t('common.sort')" fixed="left" width="60" />
              <el-table-column :label="$t('purchaseReturn.label.productName')" prop="productName" min-width="120" show-overflow-tooltip></el-table-column>
              <el-table-column :label="$t('purchaseReturn.label.unitName')" prop="unitName"  min-width="100" show-overflow-tooltip/>
              <el-table-column :label="$t('purchaseReturn.label.receiveCount')" prop="receivedCount"min-width="150" show-overflow-tooltip/>
              <el-table-column :label="'*'+$t('purchaseReturn.label.returnCount')" prop="returnCount" min-width="200" show-overflow-tooltip>
                <template #default="scope">
                  <el-form-item class="mt15px" label-width="0px" :prop="'detailList.'+scope.$index +'.returnCount'" :rules="[{required:true,message:t('purchaseReturn.rules.returnCount'),trigger:'blur'},{ required: true, validator: validateReturnCount, trigger: ['blur','change'] },{pattern:/^([1-9]\d{0,7}(\.\d{1,3})?|0|0.00|0.000|0\.(?!0+$)\d{1,3})$/,message:t('purchaseReturn.rules.returnCountFormat'),trigger:'blur'}]">
                    <el-input
                      @change="setTotalCount(scope.$index)"
                      v-model="scope.row.returnCount"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column :label="'*'+$t('purchaseReturn.label.returnPrice')" show-overflow-tooltip min-width="200" align="right">
                <template #default="scope">
                  <el-form-item class="mt15px" label-width="0px" :prop="'detailList.'+scope.$index + '.returnPrice'" :rules="[{required:true,message:t('purchaseReturn.rules.returnPrice'),trigger:'blur'},{pattern:/^(-?0(?:\.\d{1,4})?|-?[1-9]\d{0,6}(?:\.\d{1,4})?)$/,message:t('purchaseReturn.rules.returnPriceFormat'),trigger:'blur'}]">
                    <el-input
                      v-model="scope.row.returnPrice"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      @change="setAmount(scope.$index)"
                    >
                      <template #prefix>
                        <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column :label="'*'+$t('purchaseReturn.label.returnAmount')" show-overflow-tooltip min-width="200" align="right">
                <template #default="scope">
                  <el-form-item class="mt15px" label-width="0px" :prop="'detailList.'+scope.$index +'.returnAmount'" :rules="[{required:true,message:t('purchaseReturn.rules.returnAmount'),trigger:'blur'},{pattern:/^(-?0(?:\.\d{1,2})?|-?[1-9]\d{0,8}(?:\.\d{1,2})?)$/,message:t('purchaseReturn.rules.returnAmountFormat'),trigger:'blur'}]">
                    <el-input
                      @change="setTotalAmount"
                      v-model="scope.row.returnAmount"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    >
                      <template #prefix>
                        <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column :label="$t('purchaseReturn.label.remark')" min-width="200" show-overflow-tooltip>
                <template #default="scope">
                  <el-form-item class="mt15px" label-width="0px" :prop="'detailList.'+scope.$index +'.remark'" :rules="[{pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,message: t('purchaseReturn.rules.remarkFormat'),trigger: 'blur', },]">
                    <el-input
                      v-model="scope.row.remark"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="200"
                      clearable
                    />
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
            <div  class="table-title" v-if="form.detailList && form.detailList.length > 0">
              <div><span>{{t('purchaseReturn.label.total')}}：</span></div>
              <div>
                <span class="mr16px">{{t('purchaseReturn.label.returnCount')}}：<span  style="font-size: 18px; color: #C00C1D ">{{form.totalReturnCount}}</span></span>
                <span>{{t('purchaseReturn.label.expectedReturnAmount')}}：
                        <span  style="font-size: 18px; color: #C00C1D ">
                          <span v-if="form.detailList[0].currencyCode == 'CNY'">￥</span>
                          <span v-else>$</span>
                          {{form.totalReturnAmount}}
                        </span>
                </span>
              </div>
            </div>
          </div>
          <div class="total-amount">
            <el-row class="flex-center-start">
              <el-col :span="8">
                <el-form-item :label="$t('purchaseReturn.label.totalReturnAmount')" prop="returnAmount" >
                  <el-input
                    class="!w-[256px]"
                    v-model="form.returnAmount"
                    :placeholder="$t('common.placeholder.inputTips')"
                  >
                    <template #prefix v-if="form.detailList && form.detailList.length > 0">
                      <span v-if="form.detailList[0].currencyCode == 'CNY'">￥</span>
                      <span v-else>$</span>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">{{ $t("purchaseReturn.label.otherInformation") }}</div>
          </div>
          <div>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  :label="$t('purchaseReturn.label.attachmentFiles')"
                  prop="attachmentFiles"
                >
                  <upload-multiple
                    :listType="`text`"
                    :tips="''"
                    :fileSize="10"
                    :fileType="['rar','zip','docx','xls','xlsx','pdf','jpg','png','jpeg']"
                    :isPrivate="`public-read`"
                    :modelValue="form.attachmentFiles"
                    @update:model-value="onChangeMultiple"
                    ref="detailPicsRef"
                    :limit="10"
                    :formRef="formUpdateRef"
                    class="modify-multipleUpload"
                    name="detailPic">
                    <template #default="{ file }">
                      点击上传
                    </template>
                  </upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('purchaseReturn.label.remark')" prop="remark">
                  <el-input
                    :rows="4"
                    type="textarea"
                    show-word-limit
                    v-model="form.remark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-content">{{ $t("purchaseReturn.label.approve") }}</div>
          </div>
          <div >
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('purchaseReturn.label.approveUserName')" prop="approveUserId">
                  <el-select
                    v-model="form.approveUserId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    filterable
                    class="!w-[256px]"
                    @change="setApproveUserName"
                  >
                    <el-option v-for="(item,index) in approveUserOption" :key="index" :label="item.nickName" :value="item.userId"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleBack">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">{{ $t("common.confirm") }}</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

  import PurchaseReturnApi, {detailForm} from "@/modules/pms/api/purchaseReturn";
  import WarehouseAPI from "@/modules/pms/api/warehouse";
  import {useRoute,useRouter} from "vue-router";
  import {useTagsViewStore} from "@/core/store";
  import PurchaseOrderAPI from "@/modules/pms/api/purchaseOrder";
  import supplierAPI from "@/modules/pms/api/supplier";
  import CommonAPI from "@/modules/wms/api/common";
  import {parseDateTime} from "@/core/utils";

  defineOptions({
    name: "PurchaseReturnAddOrEdit",
    inheritAttrs: false,
  });

  const route = useRoute();
  const router = useRouter();
  const tagsViewStore = useTagsViewStore()
  const { t } = useI18n();
  const fromRef = ref()
  const submitLoading = ref(false)
  const queryFormRef = ref(ElForm);
  const loading = ref(false);
  const returnOrderCode = route.query.returnOrderCode;
  const type = route.query.type;
  const addProductRef = ref();
  const supplierList = ref([])
  const formUpdateRef = ref(null);
  const orderTypeList = ref([
    {
      key: 2,
      value: t('purchaseReturn.orderTypeList.marketDirectSupply')
    },
    {
      key: 1,
      value:t('purchaseReturn.orderTypeList.suppliersDirectSupply')
    }
  ])
  const storehouseList = ref([])
  const returnMethodList = ref([])
  const areaList = ref([])
  const countryList = ref([]);
  const provinceList = ref([]);
  const cityList = ref([]);
  const districtList = ref([]);
  const showProvinceInput = ref(false);
  const showCityInput = ref(false);
  const showDistrictInput = ref(false);
  const approveUserOption = ref([])

  const form = reactive<detailForm>({
    detailList:[],
    productReturnAmount:''
  });

  const rules = reactive({
    orderCode: [
      {
        required: true,
        message: t("purchaseReturn.rules.orderCodeTip"),
        trigger: "blur",
      },
    ],
    warehouseCode: [
      {
        required: true,
        message: t("purchaseReturn.rules.warehouseCodeTip"),
        trigger: "blur",
      },
    ],
    returnUserName: [
      {
        required: true,
        message: t("purchaseReturn.rules.returnUserNameTip"),
        trigger: "blur",
      },
      {
        pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
        message: t("purchaseReturn.rules.returnUserNameFormat"),
        trigger: "blur",
      },
    ],
    returnUserArea: [
      {
        required: true,
        message: t("purchaseReturn.rules.returnUserAreaTip"),
        trigger: "change",
      },
    ],
    returnUserMobile: [
      {
        required: true,
        message: t("purchaseReturn.rules.returnUserMobileTip"),
        trigger: "change",
      },
    ],
    approveUserId:  [{ required: true, message: t("common.placeholder.selectTips"), trigger: ["change"] }],
  });

  function onChangeMultiple(val) {
    form.attachmentFiles=val?val:''
    console.info('==form.orderAttachmentFiles=='+form)
  }

  function getMethodName() {
    if(form.deliveryType){
      let data: any = returnMethodList.value.find(
        (item: any) => item.key == form.deliveryType
      );
      if (data) {
        form.deliveryName = data.value ? data.value : "";
      } else {
        form.deliveryName = "";
      }
    }else {
      form.deliveryName = ''
    }
  }
  function queryReturnMethodList(){
    returnMethodList.value = []
    let data = {
      page:1,
      limit:100,
      enableStatus:1
    }
    PurchaseReturnApi.queryReturnMethodList(data).then((res)=>{
      if(res.records && res.records.length > 0){
        res.records.forEach(item=>{
          let obj = {
            key:item.id,
            value:item.deliveryMethodsCategoryVO.deliveryMethodsCategoryName + '/' + item.methodName,
            methodName:item.methodName
          }
          returnMethodList.value.push(obj)
        })
      }else {
        returnMethodList.value = []
      }
    })
  }

  /** 查询关联采购单信息 */
  function getOrderDetail() {
    if(form.orderCode){
      let params = {
        orderCode:form.orderCode.replace(/\s+/g, "")
      }
      PurchaseReturnApi.queryDetailByOrderCode(params).then((res)=>{
        if(res.orderPurchaseStatus == 2 || res.orderPurchaseStatus == 3){
          form.purchaseUserName = res.purchaseUserName
          form.supplierName = res.supplierName
          form.orderType = res.orderType
          form.detailList = []
          let arr = res.purchaseOrderDetailVOList.filter(function (list) {
            return Number(list.receivedCount) > 0
          })
          if(arr.length > 0){
            arr.forEach((list,index)=>{
              let obj = {
                productCode: list.productCode,
                productName: list.productName,
                unitName: list.unitName,
                returnCount: list.receivedCount,
                returnPrice: list.lastReceivedUnitPrice,
                currencyCode: list.currencyCode,
                receivedCount: list.receivedCount,
                remark:'',
              }
              form.detailList.push(obj)
              setTotalCount(index)
            })
            setTotalAmount()
          }
        }else {
          fromRef.value.resetFields()
          fromRef.value.clearValidate()
          form.detailList = []
          return ElMessage.error(t('purchaseReturn.message.cannotReturn'))
        }
      }).catch(() => {
        fromRef.value.resetFields()
        fromRef.value.clearValidate()
        form.detailList = []
      }).finally(()=>{

      })
    }
  }

  /** 金额合计 */
  function setTotalAmount() {
    let totalAmount = '0'
    form.detailList.forEach(list =>{
      if(list.returnAmount){
        totalAmount = (parseFloat(totalAmount) + parseFloat(list.returnAmount)).toFixed(2)
      }
      form.totalReturnAmount = totalAmount
      form.returnAmount = totalAmount
    })
  }
  //数量合计
  function setTotalCount(index) {
    setAmount(index)
    let totalCount = '0'
    form.detailList.forEach(list =>{
      if(list.returnCount){
        totalCount = (parseFloat(totalCount) + parseFloat(list.returnCount)).toFixed(2)
      }
      form.totalReturnCount = totalCount
    })
  }
  /** 数量*单价计算金额 */
  function setAmount(index) {
    if(form.detailList[index].returnCount && form.detailList[index].returnPrice){
      form.detailList[index].returnAmount = (form.detailList[index].returnCount * form.detailList[index].returnPrice).toFixed(2)
    }
    setTotalAmount()
  }
  //退货数量检验
  function validateReturnCount(rule, value, callback) {
    let index = rule.field?.split('.')[1]
    let returnCount = form.detailList[index].receivedCount?form.detailList[index].receivedCount:0
    if(value && returnCount){
      if(value > parseFloat(returnCount)){
        callback(new Error(t('purchaseReturn.message.returnCountTips')));
      }else {
        callback();
      }
    }
  }

  /** 查询供应商列表 */
  function getSupplierList() {
    supplierAPI.getSupplierList()
      .then((data) => {
        supplierList.value = data;
      })
  }
  /** 获取国家列表 */
  function queryAllCountry() {
    const queryParams = {
      pid: "0",
    };
    CommonAPI.getAreaList(queryParams)
      .then((data: any) => {
        countryList.value = data;
      })
      .finally(() => {});
  }
  function getAreaApi(val: any, tab?: any) {
    let params = {
      pid: val,
    };
    CommonAPI.getAreaList(params)
      .then((data: any) => {
        if (tab == "province") {
          if(data.length > 0){
            provinceList.value = data;
            showProvinceInput.value = true
          } else {
            showProvinceInput.value = false
          }
        } else if (tab == "city") {
          if (data.length > 0) {
            cityList.value = data;
            showCityInput.value = true;
          } else {
            showCityInput.value = false;
          }
        } else {
          if (data.length > 0) {
            showDistrictInput.value = true;
            districtList.value = data;
          } else {
            showDistrictInput.value = false;
          }
        }
      })
      .finally(() => {});
  }
  function countryChange(val: any) {
    form.returnProvinceId = "";
    form.returnProvinceName = "";
    provinceList.value = [];
    showProvinceInput.value = false
    form.returnCityId = "";
    form.returnCityName = "";
    cityList.value = [];
    form.returnDistrictId = "";
    form.returnDistrictName = "";
    districtList.value = [];
    showCityInput.value = false;
    showDistrictInput.value = false;
    if (form.returnCountryId) {
      let data: any = countryList.value.find(
        (item: any) => item.id === form.returnCountryId
      );
      if (data) {
        form.returnCountryName = data.shortName ? data.shortName : "";
      } else {
        form.returnCountryName = "";
      }
      getAreaApi(form.returnCountryId, "province");
    }
  }
  function handleProvinceChange(val: any) {
    form.returnCityId = "";
    form.returnCityName = "";
    cityList.value = [];
    form.returnDistrictId = "";
    form.returnDistrictName = "";
    districtList.value = [];
    showCityInput.value = false;
    showDistrictInput.value = false;
    let data: any = provinceList.value.find(
      (item: any) => item.id === form.returnProvinceId
    );
    if (data) {
      form.returnProvinceName = data.shortName ? data.shortName : "";
    } else {
      form.returnProvinceName = "";
    }
    if (val) {
      getAreaApi(val, "city");
    }
  }

  function handleCityChange(val: any) {
    form.returnDistrictId = "";
    form.returnDistrictName = "";
    districtList.value = [];
    showDistrictInput.value = false;
    let data: any = cityList.value.find(
      (item: any) => item.id === form.returnCityId
    );
    if (data) {
      form.returnCityName = data.shortName ? data.shortName : "";
    } else {
      form.returnCityName = "";
    }
    if (val) {
      getAreaApi(val, "district");
    }
  }

  function handleDistrictChange(val: any) {
    let data: any = districtList.value.find(
      (item: any) => item.id === form.returnDistrictId
    );
    if (data) {
      form.returnDistrictName = data.shortName ? data.shortName : "";
    } else {
      form.returnDistrictName = "";
    }
  }
  /** 获取区号列表 */
  function getAllCountry() {
    PurchaseReturnApi.getAllCountry().then((data: any) => {
      areaList.value = data;
    }).finally(() => {});
  }

  /** 仓库 */
  function getStorehouseList() {
    WarehouseAPI.getStorehouseSelect()
      .then((data) => {
        storehouseList.value = data;
      })
  }
  function setWarehouseName() {
    if(form.warehouseCode){
      storehouseList.value.forEach(list=>{
        if(list.warehouseCode == form.warehouseCode){
          form.warehouseName = list.warehouseName
        }
      })
    }else {
      form.warehouseName = ''
    }
  }

  async function handleBack() {
    await tagsViewStore.delView(route);
    router.go(-1);
  };

  /** 添加/编辑采购单 */
  function handleSubmit(){
    if(form.detailList.length == 0){
      return ElMessage.error(t('purchaseReturn.message.detailListNull'))
    }
    fromRef.value.validate((valid) => {
      if (!valid) return;
      if(Number(form.totalReturnCount) <= 0){
        return ElMessage.error(t('purchaseReturn.message.totalReturnCount'))
      }
      submitLoading.value = true
      let detailList = []
      form.detailList.forEach(list=>{
        let obj = {
          productCode:list.productCode,
          remark:list.remark,
          returnAmount:list.returnAmount,
          returnCount:list.returnCount,
          returnPrice:list.returnPrice,
        }
        detailList.push(obj)
      })
      let date = parseDateTime(form.returnDate,'date') + ' ' + form.returnTime
      let params = {
        approveUserId: form.approveUserId,
        approveUserName: form.approveUserName,
        attachmentFiles: form.attachmentFiles ? JSON.stringify(form.attachmentFiles) : '',
        orderCode: form.orderCode,
        deliveryType: form.deliveryType,
        deliveryName: form.deliveryName,
        planDeliveryTime: new Date(date).getTime(),
        remark:form.remark,
        returnAddress: form.returnAddress,
        returnCountryId: form.returnCountryId,
        returnCountryName: form.returnCountryName,
        returnProvinceId: form.returnProvinceId,
        returnProvinceName: form.returnProvinceName,
        returnCityId: form.returnCityId,
        returnCityName: form.returnCityName,
        returnDistrictId: form.returnDistrictId,
        returnDistrictName: form.returnDistrictName,
        returnAmount: form.returnAmount,
        returnUserArea: form.returnUserArea,
        returnUserMobile: form.returnUserMobile,
        returnUserName: form.returnUserName,
        warehouseCode: form.warehouseCode,
        warehouseName: form.warehouseName,
        detailList:detailList
      }
      if(type == 'edit'){
        params.returnOrderCode = returnOrderCode
      }
      PurchaseReturnApi.save(params).then((res)=>{
        ElMessage.success(t('purchaseReturn.message.saveSuccess'))
        handleBack()
      }).finally(()=>{
        submitLoading.value = false
      })
    })
  }

  /** 查询采购退货单详情 */
  function queryDetail(){
    loading.value = true;
    let params = {
      returnOrderCode: returnOrderCode,
    };
    PurchaseReturnApi.detail(params).then((data) => {
      Object.assign(form, data);
      if (form.returnCountryId) {
        getAreaApi(form.returnCountryId, "province");
      }
      if (form.returnProvinceId) {
        getAreaApi(form.returnProvinceId, "city");
      }
      if (form.returnCityId) {
        getAreaApi(form.returnCityId);
      }
      if(form.planDeliveryTime){
        let date = parseDateTime(form.planDeliveryTime,'dateTime').split(' ')
        form.returnDate = date[0]
        form.returnTime = date[1]
      }
    }).finally(() => {
      loading.value = false;
    });
  }

  function getApproveUser() {
    PurchaseOrderAPI.queryApproveUser().then(res => {
      approveUserOption.value = res
    })
  }
  function setApproveUserName(){
    if(form.approveUserId){
      approveUserOption.value.forEach(list=>{
        if(list.userId == form.approveUserId){
          form.approveUserName = list.nickName
        }
      })
    }else {
      form.approveUserName = ''
    }
  }
  onMounted(() => {
    getSupplierList();
    getStorehouseList();
    getApproveUser();
    queryAllCountry();
    getAllCountry();
    queryReturnMethodList();
    if(route.query.fromPage == 2){
      form.orderCode = route.query.orderCode
      getOrderDetail()
    }
    if(type=='edit'){
      queryDetail();
    }
  });
</script>
<style scoped lang="scss">
  .addPurchaseReturn {
    background: #FFFFFF;
    border-radius: 4px;
    .page-content{
      .button-add{
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-weight: 600;
        font-size: 14px;
        color: var(--el-color-primary)
      }
      .title-lable {
        padding: 20px 0px 15px 0px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        &::before {
          content: " ";
          display: inline-block;
          width: 4px;
          height: 16px;
          background: var(--el-color-primary);
          border-radius: 2px;
          margin-right: 12px;
        }
      }
      .address_style {
        :deep(.el-form-item__label::before) {
          color: var(--el-color-danger);
          content: "*";
          margin-right: 4px;
        }
      }
      .table-title{
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 50px;
        background: #F4F6FA;
        box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #52585F;
        font-style: normal;
        padding: 15px 12px;
      }
      .total-amount{
        margin-top: 20px;
      }
    }
  }
</style>
