<template>
  <el-dialog v-model="visible" title="选择摘要" width="560" class="finance-moudle">
    <div class="dialog-content summary-dialog">
      <el-input v-model="summaryKeyword" style="width: 100%" placeholder="请输入摘要内容">
        <template #prefix>
          <el-icon class="el-input__icon"><search /></el-icon>
        </template>
      </el-input>
      <div class="summary-title">摘要内容</div>
      <div class="summary-list">
        <el-radio-group v-model="selectedSummary">
          <el-radio v-for="item in filterSummaryList" :key="item.id" :value="item.summaryContent">{{ item.summaryContent }}</el-radio>
        </el-radio-group>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelHandler">取消</el-button>
        <el-button type="primary" @click="confirmHandler">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { voucher } from '@/modules/finance/api/index';
  import type { TSummaryItem } from "@/modules/finance/types/voucher";
  const visible = defineModel();
  const emits = defineEmits(['confirm']);
  // const props = defineProps(['modelValue']);
  // const emits = defineEmits(['update:modelValue']);
  const summaryList = reactive<TSummaryItem[]>([]);
  const summaryKeyword = ref('');
  const selectedSummary = ref('');
  const filterSummaryList = computed(() => {
    if (summaryKeyword.value === '') {
      return summaryList;
    } else {
      const filterList = summaryList.filter(item => item.summaryContent.includes(summaryKeyword.value));
      return filterList?.length ? filterList : [];
    }
  })
  const confirmHandler = () => {
    if (selectedSummary.value) {
      emits('confirm', selectedSummary.value)
    }
    cancelHandler();
  }
  const cancelHandler = () => {
    summaryKeyword.value = '';
    selectedSummary.value = '';
    visible.value = false;
  }
  const getSummaryList = async () => {
    try {
      const res = await voucher.querySummaryList({});
      summaryList.push(...res);
    } catch (err) {

    }
  }
  onMounted(() => {
    getSummaryList();
  })
</script>

<style lang="scss">
  .summary-dialog {
    .summary-title {
      margin-top: 16px;
      background: #F4F6FA;
      border-radius: 2px 2px 0px 0px;
      border: 1px solid #D9DCEB;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #52585F;
    }
    .summary-list {
      border: 1px solid #D9DCEB;
      border-top: none;
      height: 250px;
      overflow-y: auto;
      .el-radio-group {
        display: block;
        .el-radio {
          display: block;
          line-height: 32px;
          height: 32px;
          padding-left: 16px;
          border-bottom: 1px solid #D9DCEB;
          margin-right: 0;
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
</style>