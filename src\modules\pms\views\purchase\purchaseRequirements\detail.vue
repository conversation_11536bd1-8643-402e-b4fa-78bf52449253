<template>
  <div class="app-container purchaseRequirements" v-loading="loading">
    <!-- Basic Information Section -->
    <el-card class="mb-4">
      <div class="top-card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="handleBack"
        />
        <span class="code" @click="handleBack">
          {{ $t("purchaseRequirements.title.detail") }}
        </span>
      </div>

      <div class="card-header">
        <span class="card-title">
          {{ $t("purchaseRequirements.detail.basicInfo") }}
        </span>
      </div>
      <div class="basic-info">
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.purchaseReqNo") }}:
          </span>
          <span class="form-text">{{ basicInfo.reqCode }}</span>
        </div>
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.orderCode") }}:
          </span>
          <span class="form-text">{{ basicInfo.orderCode || "--" }}</span>
        </div>
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.customerName") }}:
          </span>
          <!-- <span class="form-text">{{ basicInfo.customerName }}</span> -->
          <el-tooltip
            :content="basicInfo.customerName"
            placement="top"
            show-after="200"
          >
            <span class="form-text text-ellipsis">
              {{ basicInfo.customerName }}
            </span>
          </el-tooltip>
        </div>
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.name") }}:
          </span>
          <span
            class="form-text cursor-pointer"
            @click="handleViewInfo(basicInfo, 3)"
          >
            {{ basicInfo.receiveName }}
            <el-icon
              v-hasPermEye="['pms:requirements:eye']"
              color="#762ADB "
              size="16"
              v-if="!basicInfo.receiveNameDecrypted"
            >
              <View />
            </el-icon>
          </span>
        </div>
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.receiveMobile") }}:
          </span>
          <span
            class="form-text cursor-pointer"
            @click="handleViewInfo(basicInfo, 1)"
          >
            {{ basicInfo.receiveMobileArea }} {{ basicInfo.receiveMobile }}
            <el-icon
              v-hasPermEye="['pms:requirements:eye']"
              color="#762ADB "
              size="16"
              v-if="!basicInfo.receiveMobileDecrypted"
            >
              <View />
            </el-icon>
          </span>
        </div>
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.createTime") }}:
          </span>
          <span class="form-text">
            {{ parseTimeHandle(basicInfo.createTime) }}
          </span>
        </div>
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.expectedDeliveryTime") }}:
          </span>
          <span class="form-text">
            {{ parseTimeHandle(basicInfo.expectedDeliveryTimeStar) }} -
            {{ parseTimeHandle(basicInfo.expectedDeliveryTimeEnd) }}
          </span>
        </div>
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.warehouse") }}:
          </span>
          <span class="form-text">{{ basicInfo.warehouseName }}</span>
        </div>
        <div class="info-item">
          <span class="form-label mr-20px">
            {{ $t("purchaseRequirements.label.deliveryAddress") }}:
          </span>
          <span>
            <el-tooltip
              :content="basicInfo.deliveryAddress"
              placement="top"
              effect="dark"
            >
              <span
                v-if="
                  basicInfo.deliveryAddress &&
                  basicInfo.deliveryAddress.includes('*')
                "
                class="form-text cursor-pointer"
                @click="handleViewInfo(basicInfo, 2)"
              >
                {{ basicInfo.deliveryAddress }}
                <el-icon
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['pms:requirements:eye']"
                >
                  <View />
                </el-icon>
              </span>
              <span class="form-text text-ellipsis" v-else>
                {{ basicInfo.deliveryAddress }}
              </span>
            </el-tooltip>
          </span>
        </div>
      </div>

      <!-- Product Information Section -->
      <div class="card-header mt-4">
        <span class="card-title">
          {{ $t("purchaseRequirements.detail.productInfo") }}
        </span>
      </div>
      <el-table :data="basicInfo.purchaseReqDetailVOList" border>
        <el-table-column label="商品信息" min-width="300">
          <template #default="scope">
            <div class="product-info">
              <el-image
                :src="scope.row.imagesUrls"
                class="product-image"
              ></el-image>
              <div class="product-details">
                <div>商品编码: {{ scope.row.productCode }}</div>
                <div>{{ scope.row.productName }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="数量" prop="reqCount" align="center" />
        <el-table-column label="单位" prop="unitName" align="center" />
      </el-table>

      <!-- Other Information Section -->
      <div class="card-header mt-24px">
        <span class="card-title">
          {{ $t("purchaseRequirements.detail.attachmentInfo") }}
        </span>
      </div>
      <div class="other-info">
        <div class="info-item flex">
          <span class="form-label">附件:</span>
          <!-- <el-link :href="previewFile.url" type="primary">
            {{ previewFile.name }}
          </el-link> -->
          <UploadMultiple
            ref="detailPicsRef"
            v-model="basicInfo.attachmentFiles"
            :limit="10"
            :formRef="formRef"
            listType="text"
            :isShowTip="false"
            :showUploadBtn="false"
            class="modify-multipleUpload"
            name="detailPic"
            actionType="preview"
          />
        </div>
        <div class="info-item">
          <span class="form-label">备注:</span>
          <span class="form-text">{{ basicInfo.remark || "--" }}</span>
        </div>
      </div>
      <div class="custom-border"></div>
      <div class="section mt-16px padding-right-30px text-right">
        <el-button @click="handleBack">{{ $t("common.reback") }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "PurchaseRequirementsDetail",
  inheritAttrs: false,
});

import {
  getPurchaseRequirementDetail,
  querySrcInfo,
} from "@pms/api/purchaseRequirements";
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
const { id } = useRoute().query;
const { t } = useI18n();
const queryFormRef = ref(ElForm);
import { parseTime } from "@/core/utils/index";
import { useTagsViewStore } from "@/core/store/modules/tagsView";

const loading = ref(false);
const basicInfo = ref({});
const parseTimeHandle = parseTime;
const handleGetDetail = async () => {
  try {
    loading.value = true;
    const data = await getPurchaseRequirementDetail(id);
    basicInfo.value = data;
    basicInfo.value.attachmentFiles = JSON.parse(data.attachmentFiles);
  } catch (error) {
    // ElMessage.error("获取采购需求详情失败");
  } finally {
    loading.value = false;
  }
};

const router = useRouter();
const tagsViewStore = useTagsViewStore();
const route = useRoute();
const handleBack = async () => {
  await tagsViewStore.delView(route);
  router.push({
    path: "/pms/purchase/purchaseRequirements",
  });
};

const previewFile = reactive({
  name: "",
  url: "",
});
const previewSingleHandle = async (
  bucket: string,
  fileName: string,
  originalFileName: string
) => {
  try {
    const res = await previewSingle(bucket, fileName, originalFileName);
    console.log("previewFile------", res);
    previewFile.name = res.name;
    previewFile.url = res.url;
  } catch (error) {
    console.error("Failed to preview file:", error);
    ElMessage.error("预览文件失败");
  }
};

const handleViewInfo = async (row: any, type: number) => {
  try {
    const data = await querySrcInfo({ id: row.id, querySrcType: type });
    if (type === 3) {
      basicInfo.value.receiveName = data.name;
      basicInfo.value.receiveNameDecrypted = true;
    } else if (type === 1) {
      basicInfo.value.receiveMobile = data.mobile;
      basicInfo.value.receiveMobileDecrypted = true;
    } else if (type === 2) {
      basicInfo.value.deliveryAddress = data.address;
      basicInfo.value.deliveryAddressDecrypted = true;
    }
  } catch (e) {}
};

onMounted(() => {
  handleGetDetail();
});
</script>
<style scoped lang="scss">
.form-label {
  display: inline-block;
  width: 100px;
  word-break: keep-all;
  text-align: right;
}
.purchaseRequirements {
  .card-header {
    margin-bottom: 20px;
    .title {
      font-weight: bold;
      position: relative;
      padding-left: 10px;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: #409eff;
      }
    }
  }
  .basic-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    .info-item {
      width: 25%;
      // min-width: 300px;
      display: flex; // Add this to ensure proper flex layout
      align-items: baseline; // Add this for vertical alignment
      .form-text {
        flex: 1;
        min-width: 0; // 确保文本可以正常省略
        max-width: calc(100% - 120px); // Add max-width to ensure truncation
      }

      .text-ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .info-item {
    margin-bottom: 15px;
    .label {
      color: #666;
      margin-right: 10px;
      word-break: keep-all;
    }
    .value {
      color: #333;
    }
  }

  .product-info {
    display: flex;
    align-items: center;
    .product-image {
      width: 60px;
      height: 60px;
      margin-right: 10px;
    }
    .product-details {
      flex: 1;
    }
  }

  .mb-4 {
    margin-bottom: 16px;
  }
  .encryptBox {
    // display: inline-flex;
    // justify-content: space-between;
    // align-items: center;
    word-wrap: break-word;
    word-break: break-all;
  }

  .encryptBox-icon {
    margin-left: 4px;
    cursor: pointer;
    // align-self: flex-start;
    vertical-align: text-top;
  }
}
.top-card-header {
  padding: 0px 10px 20px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #e5e7f3;
  border-radius: 4px 4px 0px 0px;
  cursor: pointer;
  .back-btn {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  .status {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
}
</style>
