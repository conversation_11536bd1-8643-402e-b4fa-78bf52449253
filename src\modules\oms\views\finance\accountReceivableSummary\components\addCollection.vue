<template>
  <el-drawer v-model="showDialog" :title="$t('omsFinance.label.collection')" :close-on-click-modal="false" size="600px" @close="onCloseHandler">
    <el-form :model="collectionForm" :rules="reasonFormRules" ref="collectionFormRef" label-position="top">
      <!--收款方式-->
      <el-form-item
        :label="$t('omsFinance.label.collectionMethod')"
        prop="collectionTypeId"
      >
        <el-select
          v-model="collectionForm.collectionTypeId"
          :placeholder="$t('omsFinance.placeholder.select')"
          clearable
          @change="collectionTypeChange"
        >
          <el-option
            v-for="item in collectMethodList"
            :key="item.id"
            :label="item.methodName"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!--收款金额-->
      <el-form-item
        :label="$t('omsFinance.label.receivableAmountLabel')"
        prop="collectionAmount"
      >
        <el-input
          type="text"
          :placeholder="$t('omsFinance.placeholder.inputTip')"
          v-model="collectionForm.collectionAmount"
          clearable
        />
      </el-form-item>
      <!--收款时间-->
      <el-form-item
        :label="$t('omsFinance.label.collectionTime')"
        prop="collectionTime"
      >
        <el-date-picker
          :editable="false"
          v-model="collectionForm.collectionTime"
          type="datetime"
          style="width: 100%"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="$t('omsFinance.placeholder.collectionTime')"
          :prefix-icon="Calendar"
        />
      </el-form-item>
      <!--收款凭证-->
      <el-form-item
        :label="$t('omsFinance.label.collectionVoucher')"
        prop="collectionVoucher"
      >
        <UploadMultiple
          ref="detailPicsRef"
          class="modify-multipleUpload"
          :name="'collectionVoucher'"
          :formRef="collectionFormRef"
          :limit="1"
          :fileSize="10"
          :fileType="['jpg', 'jpeg', 'png']"
          :modelValue="collectionForm.collectionVoucher"
          @update:model-value="onChangeMultiple"
        />
      </el-form-item>
     <!--备注-->
      <el-form-item
        :label="$t('omsFinance.label.remark')"
        prop="remark"
      >
        <el-input
          :rows="4"
          type="textarea"
          show-word-limit
          v-model="collectionForm.remark"
          :placeholder="$t('common.placeholder.inputTips')"
          maxlength="100"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">{{ $t("common.cancel") }}</el-button>
          <el-button type="primary" :loading="dialogLoading" @click="onSaveHandler">{{ $t("common.confirm") }}</el-button>
        </span>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import FinanceAPI, { CollectionRecordVO, PaymentMethod } from "@/modules/oms/api/finance";
import {convertToTimestamp, isEmpty} from "@/core/utils";
import type { FormInstance } from "element-plus";
import {Calendar} from "@element-plus/icons-vue";
import UploadMultiple from "@/core/components/Upload/UploadMultiple.vue";

const emit = defineEmits(['close', 'confirm'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  receivableAccountId: {
    type: String,
    default: ''
  },
})
const { t } = useI18n();
const collectionFormRef = ref<FormInstance>(null)
const showDialog = ref(false)

watch(() => props.visible, (newVal) => {
  showDialog.value = newVal
}, { immediate: true})

const dialogLoading = ref(false)
const collectionForm = reactive<CollectionRecordVO>({})

const amountPatternRexp = /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,8}|\d{1,9}\.\d{1,2})$/;
const reasonFormRules = reactive({
  collectionTypeId: [
    {
      required: true,
      message: t("omsFinance.rules.collectionTypeId"),
      trigger: ["blur", "change"],
    },
  ],
  collectionAmount: [
    {
      required: true,
      message: t("omsFinance.rules.collectionAmount"),
      trigger: ["blur", "change"],
    },
    {
      pattern: amountPatternRexp,
      message: t("omsFinance.rules.collectionAmountFormat"),
      trigger: ["blur", "change"],
    },
  ],
  collectionTime: [
    {
      required: true,
      message: t("omsFinance.rules.dateRange"),
      trigger: ["blur", "change"],
    },
  ],
  collectionVoucher: [
    {
      required: true,
      message: t("omsFinance.rules.collectionVoucher"),
      trigger: ["blur", "change"],
    },
  ],
})

const collectMethodList = ref<PaymentMethod[]>([])
const queryCollectionMethods = async () => {
  const data = await FinanceAPI.getCollectionMethods()
  collectMethodList.value = data || []
}
/*支付方式切换*/
const collectionTypeChange = (value) => {
  if(!isEmpty(value)){
    collectionForm.collectionTypeName = collectMethodList.value.find(item => item.id == value)?.methodName
  }else{
    collectionForm.collectionTypeName = ''
  }
}

/*凭证上传*/
function onChangeMultiple(val) {
  collectionForm.collectionVoucher = val ? val : "";
}

const onCloseHandler = () => {
  showDialog.value = false
  emit('close')
}

/*收款确认*/
const onSaveHandler = () => {
  collectionFormRef.value?.validate(async (valid) => {
    if (valid) {
      dialogLoading.value = true
      let params = {
        ...collectionForm,
        receivableAccountId: props.receivableAccountId
      }
      if(params?.collectionVoucher?.length > 0){
        params.collectionVoucher = JSON.stringify(params.collectionVoucher);
      }
      if(params?.collectionTime){
        params.collectionTime =  convertToTimestamp(params?.collectionTime);
      }
      FinanceAPI.addCollectionRecord(params).then((data) => {
        showDialog.value = false
        emit('confirm')
      }).finally(() => {
        dialogLoading.value = false
      })
    }
  })
}

onMounted(() => {
  queryCollectionMethods()
})
</script>
<style scoped lang="scss"></style>
