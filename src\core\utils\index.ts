import router from "@/core/router";
import { usePermissionStore, useUserStore } from "@/core/store";
import {
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteRecordRaw,
} from "vue-router";
// const permissionStore = usePermissionStore();
// const userStore = useUserStore();
import moment from "moment";

const replaceStr = (str: any, index: any, char: any, symbol: any = "") => {
  const strArr = str.split(symbol);
  strArr[index] = char;
  return strArr.join(symbol);
};
/**
 * Check if an element has a class
 * @param {HTMLElement} ele
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele: HTMLElement, cls: string) {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * Add class to element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function addClass(ele: HTMLElement, cls: string) {
  if (!hasClass(ele, cls)) ele.className += " " + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function removeClass(ele: HTMLElement, cls: string) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    ele.className = ele.className.replace(reg, " ");
  }
}

/**
 * 判断是否是外部链接
 *
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path: string) {
  const isExternal = /^(https?:|http?:|mailto:|tel:)/.test(path);
  return isExternal;
}

/*export async function getSystemRoutes(systemId: string) {
  await userStore.getUserInfo();
  const dynamicRoutes = await permissionStore.generateRoutes(systemId);
  dynamicRoutes.forEach((route: RouteRecordRaw) => router.addRoute(route));
}*/

/* 手机号加密 */
export function encryptPhone(mobile: any) {
  if (!mobile) {
    return "-";
  }
  if (typeof mobile !== "string") {
    mobile = mobile + "";
  } else if (mobile.length >= 11) {
    return mobile.replace(/(.{3})\d*(\d{4})/, "$1****$2");
  }
  // 非11位手机仅展示后4位
  else if (mobile.length > 4 && mobile.length < 11) {
    return mobile.replace(/\d*(\d{4})/, "****$1");
  }
  // 小于4位不做处理
  return mobile;
}

/* 姓名加密 */
export function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

// 时区转换
export function parseDateTime(time: any, type: any) {
  if (!time) {
    return null;
  }
  const format = type || "date";
  const date = new Date(time);
  const loaclDate = parseTime(
    date.toLocaleDateString("default"),
    "{y}-{m}-{d}"
  );
  const localTime =
    date.toLocaleTimeString("default", { hour12: false }).split(":")[0] === "24"
      ? replaceStr(
          date.toLocaleTimeString("default", { hour12: false }),
          0,
          "00",
          ":"
        )
      : date.toLocaleTimeString("default", { hour12: false });
  let time_str;
  switch (format) {
    case "date":
      time_str = loaclDate;
      break;
    case "time":
      time_str = localTime;
      break;
    case "dateTime":
      time_str = loaclDate + " " + localTime;
      break;
    case "folderDateTime":
      time_str = loaclDate + "T" + localTime;
      break;
  }

  return time_str;
}

/** 时间转换 */
export function changeDateRange(val: any) {
  let dateRange;
  let startDate;
  let endDate;
  if (val === 1) {
    startDate = moment().startOf("days").format("YYYY-MM-DD HH:mm:ss");
    endDate = moment().endOf("days").format("YYYY-MM-DD HH:mm:ss");
  } else if (val === 2) {
    startDate = moment()
      .subtract(1, "days")
      .startOf("days")
      .format("YYYY-MM-DD HH:mm:ss");
    endDate = moment()
      .subtract(1, "days")
      .endOf("days")
      .format("YYYY-MM-DD HH:mm:ss");
  } else if (val === 3) {
    endDate = moment().endOf("days").format("YYYY-MM-DD HH:mm:ss");
    startDate = moment()
      .subtract(6, "days")
      .startOf("days")
      .format("YYYY-MM-DD HH:mm:ss");
  } else if (val === 4) {
    startDate = moment()
      .add(1, "days")
      .startOf("days")
      .format("YYYY-MM-DD HH:mm:ss");
    endDate = moment()
      .add(1, "days")
      .endOf("days")
      .format("YYYY-MM-DD HH:mm:ss");
  }
  return (dateRange = [startDate, endDate]);
}

// 日期格式化
export function parseTime(time: any, pattern: any) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === "string") {
      time = time
        .replace(new RegExp(/-/gm), "/")
        .replace("T", " ")
        .replace(new RegExp(/\.[\d]{3}/gm), "");
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj: any = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(
    /{(y|m|d|h|i|s|a)+}/g,
    (result: any, key: any) => {
      let value: any = formatObj[key];
      // Note: getDay() returns 0 on Sunday
      if (key === "a") {
        return ["日", "一", "二", "三", "四", "五", "六"][value];
      }
      if (result.length > 0 && value < 10) {
        value = "0" + value;
      }
      return value || 0;
    }
  );
  return time_str;
}

// 日期string转换为时间戳
export function convertToTimestamp(datetime) {
  const datetimeArr = datetime.split(" "); // 根据空格将字符串分割为日期和时间部分
  const dateArr = datetimeArr[0].split("-"); // 分割日期部分
  const timeArr = datetimeArr[1].split(":"); // 分割时间部分

  const year = parseInt(dateArr[0]);
  const month = parseInt(dateArr[1]) - 1; // 月份从0开始
  const day = parseInt(dateArr[2]);
  const hour = parseInt(timeArr[0]);
  const minute = parseInt(timeArr[1]);
  const second = parseInt(timeArr[2]);

  const timestamp = new Date(year, month, day, hour, minute, second).getTime();
  return timestamp;
}

export function getTimeZone() {
  // 获取时区
  try {
    const { timeZone } = Intl.DateTimeFormat().resolvedOptions();
    if (timeZone) return timeZone;
  } catch (e) {}

  const offset = new Date().getTimezoneOffset();
  const sign = offset > 0 ? "-" : "+";
  const hours = Math.abs(Math.floor(offset / 60));
  const minutes = Math.abs(offset % 60);
  return `UTC${sign}${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
}

export function parseLanguage(lang = navigator.language) {
  // 解析语言
  const [language, region] = lang.split(/[-_]/);
  return { language, region };
}

//  判断是否为空
export function isEmpty (val:any)  {
  return val === '' || val + '' === 'null' || val + '' === 'undefined'
}

/*
* 金额格式化，添加千分位分隔符并保留两位小数
* @param {number} amount - 要格式化的金额
* @returns {string} - 格式化后的金额字符串
* */
export function formatPrice(amount: number | string) : string {
  if (typeof amount !== 'number') {
    amount = parseFloat(amount);
  }
  if (isNaN(amount)) {
    return `0.00`;
  }
  return `${amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
}



/**
 * 解析OSS链接
 * @param {string} url - OSS链接
 **/
export function parseOSSUrl(url:string) {

  const parsedUrl = new URL(url);

  // 获取 bucket 名称（主机名的第一部分）
  const bucket = parsedUrl.hostname.split('.')[0];

  // 获取路径部分（去除开头的斜杠）
  const ossPath = parsedUrl.pathname.substring(1);

  // 获取解码后的路径
  const fileName = decodeURIComponent(ossPath);

  // 提取文件名（路径中的最后一部分）
  const originalFileName = fileName.substring(fileName.lastIndexOf('/') + 1);


  return {
    bucket,
    ossPath,
    fileName,
    originalFileName
  };
}
