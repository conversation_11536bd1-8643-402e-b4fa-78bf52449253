<template>
  <div class="incomestatement-pageContainer">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true" label-width="84px">
        <el-form-item :label="t('incomestatement.label.accountingPeriod')">
          <el-date-picker format="YYYY-MM" value-format="YYYYMM" v-model="periodYearMonth" type="month" :placeholder="t('incomestatement.label.start')" :clearable="false" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchHandlerEvent" v-hasPerm="['finance:balanceSheet:search']">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="onResetHandlerEvent" v-hasPerm="['finance:balanceSheet:reset']">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="balance-sheet-flex" v-loading="loadingEnd">
      <!-- 资产部分 -->
      <div class="balance-sheet-table">
        <el-table :span-method="spanMethod" :data="tableData" row-key="id" style="width: 100%" :row-class-name="tableRowClassName" :tree-props="{ children: 'childrenList', hasChildren: 'hasChildren' }" ref="treeTable" :default-expand-all="true">
          <el-table-column min-width="200px" prop="projectName" :label="t('incomestatement.table.project')">
            <template #default="scope">
              <span class="name" :style="scope.row.isParent ? 'color: #762ADB; font-weight: 500;' : ''">
                {{ scope.row.projectName }}
                <!-- <span class="edit-btn" @click="editFormula(scope.row)">
                  <el-icon>
                    <Edit />
                  </el-icon>
                  <span>编辑公式</span></span
                > -->
              </span>
            </template>
          </el-table-column>
          <!-- 其他列 -->
          <el-table-column prop="lineNo" :label="t('incomestatement.table.lineNumber')">
            <template #default="scope">
              <span v-if="scope.row.projectName">{{ scope.row.lineNo }}</span>
              <span v-else></span>
            </template>
          </el-table-column>
          <!-- 期末余额 -->
          <el-table-column prop="currentPeriodAmount" :label="t('incomestatement.table.endingBalance')">
            <template #default="scope">
              <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto">
                <template #default>
                  <div v-for="(item, index) in scope.row.formulaVoList">
                    <span v-if="item.formulaOperator == 1">+</span>
                    <span v-if="item.formulaOperator == 2">-</span>
                    <span>{{ item.dataProjectName }}</span>
                    <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                    <span class="pl10">{{ item.currentPeriodAmount }}</span>
                  </div>
                </template>
                <template #reference>
                  <div v-if="scope.row.projectName" class="formuladescription">
                    <span> {{ scope.row.currentPeriodAmount }}</span>
                    <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                  </div>
                  <div v-else></div>
                </template>
              </el-popover>
            </template>
          </el-table-column>
          <!-- 年初余额 -->
          <el-table-column prop="beginingBalanceAmount" :label="t('incomestatement.table.beginningBalance')">
            <template #default="scope">
              <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto">
                <template #default>
                  <div v-for="(item, index) in scope.row.formulaVoList">
                    <span v-if="item.formulaOperator == 1">+</span>
                    <span v-if="item.formulaOperator == 2">-</span>
                    <span>{{ item.dataProjectName }}</span>
                    <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                    <span class="pl10">{{ item.beginingBalanceAmount }}</span>
                  </div>
                </template>
                <template #reference>
                  <div v-if="scope.row.projectName" class="formuladescription">
                    <span> {{ scope.row.beginingBalanceAmount }}</span>
                    <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                  </div>
                  <div v-else></div>
                </template>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 负债和所有者权益部分 -->
      <div class="balance-sheet-table">
        <el-table :span-method="spanMethod" :data="tableData1" row-key="id" style="width: 100%" :row-class-name="tableRowClassName" :tree-props="{ children: 'childrenList', hasChildren: 'hasChildren' }" ref="treeTable" :default-expand-all="true">
          <el-table-column min-width="200px" prop="projectName" :label="t('incomestatement.table.project')">
            <template #default="scope">
              <span class="name" :style="scope.row.isParent ? 'color: #762ADB; font-weight: 500;' : ''">
                {{ scope.row.projectName }}
                <!-- <span class="edit-btn" @click="editFormula(scope.row)">
                  <el-icon>
                    <Edit />
                  </el-icon>
                  <span>编辑公式</span></span
                > -->
              </span>
            </template>
          </el-table-column>
          <!-- 其他列 -->
          <el-table-column prop="lineNo" label="行次">
            <template #default="scope">
              <span v-if="scope.row.projectName">{{ scope.row.lineNo }}</span>
              <span v-else></span>
            </template>
          </el-table-column>
          <!-- 期末余额 -->
          <el-table-column prop="currentPeriodAmount" label="期末余额">
            <template #default="scope">
              <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto">
                <template #default>
                  <div v-for="(item, index) in scope.row.formulaVoList">
                    <span v-if="item.formulaOperator == 1">+</span>
                    <span v-if="item.formulaOperator == 2">-</span>
                    <span>{{ item.dataProjectName }}</span>
                    <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                    <span class="pl10">{{ item.currentPeriodAmount }}</span>
                  </div>
                </template>
                <template #reference>
                  <div v-if="scope.row.projectName" class="formuladescription">
                    <span> {{ scope.row.currentPeriodAmount }}</span>
                    <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                  </div>
                  <div v-else></div>
                </template>
              </el-popover>
            </template>
          </el-table-column>
          <!-- 年初余额 -->
          <el-table-column prop="beginingBalanceAmount" label="年初余额">
            <template #default="scope">
              <el-popover :disabled="!scope.row.formulaVoList" effect="light" trigger="hover" placement="top" width="auto">
                <template #default>
                  <div v-for="(item, index) in scope.row.formulaVoList">
                    <span v-if="item.formulaOperator == 1">+</span>
                    <span v-if="item.formulaOperator == 2">-</span>
                    <span>{{ item.dataProjectName }}</span>
                    <span v-if="item.formulaRulesName">( {{ item.formulaRulesName }} )</span>
                    <span class="pl10">{{ item.beginingBalanceAmount }}</span>
                  </div>
                </template>
                <template #reference>
                  <div v-if="scope.row.projectName" class="formuladescription">
                    <span> {{ scope.row.beginingBalanceAmount }}</span>
                    <img class="rounded-full" src="@/core/assets/images/questionFilled.png" v-show="scope.row.formulaVoList" />
                  </div>
                  <div v-else></div>
                </template>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 其他内容 -->
    <FormulaDialog v-model:visible="formulaDialogVisible" :formulaVoList="formulaVoList" :projectId="projectId" @save="onFormulaSave" />
  </div>
</template>
<script setup lang="ts">
// 资产负债表
import { ref, reactive, onMounted, type Ref } from 'vue'
import tableMixin from '@/modules/finance/mixins/table';
import API from '@/modules/finance/api/accountStatementApi';
import { useI18n } from 'vue-i18n'
import FormulaDialog from '../formula/index.vue'
const { t } = useI18n()
// defineOptions({
//   name: "BalanceSheet",
//   inheritAttrs: false,
// })
const loadPeriodList = reactive({
  currentPeriodYearMonth: ''
});
const loadPeriod = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    loadPeriodList.currentPeriodYearMonth = String(response.currentPeriodYearMonth);
    searchForm.periodYearMonth = loadPeriodList.currentPeriodYearMonth ;
    searchForm1.periodYearMonth = loadPeriodList.currentPeriodYearMonth ;
    periodYearMonth.value = loadPeriodList.currentPeriodYearMonth
    onSearchHandler()
    onSearchHandler1()
  } catch (error) {
    if(error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    loading.value = false;
  }
};
function tableRowClassName({ row, column }) {
  // 让第一行变红
  if (row.isParent || row.isEnd) {
    return 'red-cell';
  }
  return '';
}
let periodYearMonth=ref("")
const searchForm = reactive({
  periodYearMonth:loadPeriodList.currentPeriodYearMonth,
  isQuarter: 0,
  isShowPrevious: 0,
  projectType: '2',
})
const searchForm1 = reactive({
  periodYearMonth:loadPeriodList.currentPeriodYearMonth,
  isQuarter: 0,
  isShowPrevious: 0,
  projectType: '3',
})
// 合并一级行的部分列
const spanMethod = ({
  row,
  columnIndex,
}: {
  row: any;
  columnIndex: number;
}) => {
  if (row.isParent) {
    if (columnIndex === 0) {
      return [1, 5]; // 选择框和名称列不合并
    }
    return [1, 0]; // 其他列合并隐藏
  }
};
const formulaDialogVisible = ref(false)
const formulaVoList = ref([]) // 传递给弹框的数据
const projectId = ref('') // 传递给弹框的数据
function editFormula(row: any) {
  console.log(row.formulaVoList,"rowrowrowrowrow")
  formulaVoList.value = row.formulaVoList ? JSON.parse(JSON.stringify(row.formulaVoList)) : []
  projectId.value=row.projectId
  console.log(formulaVoList.value, 'formulaVoListformulaVoList')
  formulaDialogVisible.value = true
}
function onFormulaSave(row) {
  // onSearchHandler()
  // onSearchHandler1()
}
const { loading:loading, tableData, headFormRef,router, onSearchHandler, onResetHandler } = tableMixin({
  searchForm: searchForm,
  isLimit: false,
  tableGetApi: API.getQueryTreeList,
  tableCallback: tableCallbackFun
});
const { loading:loading1,tableData: tableData1, onSearchHandler: onSearchHandler1, onResetHandler: onResetHandler1 } = tableMixin({
  searchForm: searchForm1,
  isLimit: false,
  tableGetApi: API.getQueryTreeList,
  tableCallback: tableCallbackFun1
});

function tableCallbackFun() {
  tableData.value.map((item, index) => {
    if(index== tableData.value.length-1){
       item.isEnd = true;
    }else{
       item.isParent= true;
    }
  })
}
function tableCallbackFun1() {
  // console.log(tableData1.value, "值值值1")
  tableData1.value.map((item, index) => {
    if(item.projectName && item.projectName!='负债合计' && index!= tableData1.value.length-1){
      item.isParent = true;
    }
    if(index== tableData1.value.length-1){
       item.isEnd = true;
    }
  })
}
function onSearchHandlerEvent() {
  searchForm.periodYearMonth = periodYearMonth.value
  searchForm1.periodYearMonth = periodYearMonth.value
  onSearchHandler()
  onSearchHandler1()
}
function onResetHandlerEvent() {
  periodYearMonth.value = loadPeriodList.currentPeriodYearMonth
  searchForm.periodYearMonth = loadPeriodList.currentPeriodYearMonth
  searchForm1.periodYearMonth = loadPeriodList.currentPeriodYearMonth
  onSearchHandlerEvent()
}
const loadingEnd = ref(true)
watch([loading, loading1], ([val, val1]) => {
  if (!val && !val1) {
    loadingEnd.value=false
  }else{
    loadingEnd.value=true
  }
})
// 初始化
onMounted(() => {
  loadPeriod()
})
</script>

<style lang="scss" scoped>
.incomestatement-pageContainer {
  height: 100%;
  width: 100%;
}

.filter-container {
  padding-top: 20px;
  padding-left: 20px;
  background: #fff !important;
}

.el-menu--horizontal {
  height: 40px;

  .el-menu-item {
    height: 39px !important;
    background: #fff !important;
    padding: 10px 46px !important;

    &.is-active:before {
      display: none;
    }
  }
}

.delivery-method-container {
  .right-panel {
    padding: 20px;
    background-color: #fff;

    .search-card,
    .toolbar-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }

    .search-card {
      :deep(.el-form--inline .el-form-item) {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 20px;
    }
  }
}

:deep(.el-table) {
  .el-switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
  }
}

.table-container {
  background-color: #fff;
}

::v-deep .red-cell {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #762adb !important;
  background: #f6f0ff !important;
  box-shadow: inset 1px 1px 0px 0px #e5e7f3, inset -1px -1px 0px 0px #e5e7f3;
}

::v-deep .red-cell .el-table__expand-icon {
  color: #762adb !important;
  pointer-events: none;
}

::v-deep .red-cell .el-table__body .el-table__cell {
  color: #762adb !important;
}

.toolbar-card {
  margin-bottom: 20px;
}

.el-button--default {
  --el-border-color: #762adb !important;
  --el-text-color-regular: #762adb !important;
}

.balance-sheet-container {
  padding: 24px;
  background: #fff;
}

.balance-sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

.not-balance {
  color: #e74c3c;
  font-size: 20px;
  font-weight: bold;
  border: 2px solid #e74c3c;
  border-radius: 6px;
  padding: 2px 16px;
  margin: 0 16px;
  background: #fff0f0;
}

.balance-sheet-flex {
  display: flex;
  flex-direction: row;
  padding: 20px;
  background-color: #fff;
}

.balance-sheet-table {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.group-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #762adb;
}

.total-title {
  font-weight: bold;
  color: #333;
}

.child-title {
  color: #666;
  padding-left: 2em;
}

.child-row td {
  color: #666 !important;
}

::v-deep .group-row {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #762adb !important;
  background: #f6f0ff !important;
  box-shadow: inset 1px 1px 0px 0px #e5e7f3, inset -1px -1px 0px 0px #e5e7f3;
}

.total-row {
  background: #f0f9eb !important;
  font-weight: bold;
}

.empty-row {
  background: #fff !important;
  height: 32px;
}

.el-link {
  margin-left: 8px;
  font-size: 12px;
}

::v-deep .el-table__border-bottom-patch,
::v-deep .el-table__border-left-patch,
::v-deep .el-table--border .el-table__inner-wrapper:after,
::v-deep .el-table--border:after,
::v-deep .el-table--border:before,
.el-table__inner-wrapper:before {
  background-color: transparent !important;
}

.edit-btn {
  color: #762adb !important;
  display: none;
  margin-left: 10px;
  cursor: pointer;
  font-size: 12px;

  span {
    vertical-align: top;
  }
}

.name {
  &:hover .edit-btn {
    display: inline-block;
  }
}
.formuladescription {
  display: flex;
  align-items: center;
  width: 100%;
  span {
    margin-right: 8px;
  }
}
.rounded-full {
  width: 20px;
  height: 20px;
}
.pl10{
  padding-left: 10px;
}
</style>
