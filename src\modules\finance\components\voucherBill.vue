<template>
  <div class="voucher-bill">
    <div class="voucher-box">
      <div class="voucher-title">
        <span>{{ displayTitle }}</span>
        <img v-if="voucherForm.auditStatus === 2" src="@/core/assets/images/finance_audit.png" alt="">
        <img v-if="voucherForm.auditStatus === 3" src="@/core/assets/images/finance_record.png" alt="">
      </div>
      <div class="voucher-basic">
        <div class="basic-item">
          <span class="item-label mr8">凭证字</span>
          <el-select v-model="voucherForm.voucherWord" placeholder="" style="width: 100px;margin-right: 20px;" @change="voucherTypeChangeHandler" :disabled="disabledFlag.word">
            <el-option v-for="item in voucherTypeList" :key="item.id" :label="item.voucherWord" :value="item.voucherWord"/>
          </el-select>
          <el-input v-model="voucherForm.voucherNumber" style="width: 60px;margin-right: 8px;" disabled/>
          <span class="item-label">号</span>
        </div>
        <div class="basic-item">
          <span class="item-label mr8">日期</span>
          <el-date-picker
            v-model="voucherForm.voucherDate"
            type="date" format="YYYY-MM-DD"
            value-format="YYYYMMDD"
            style="width: 160px;margin-right: 16px;"
            :clearable="false"
            :disabled="disabledFlag.date"
            @change="voucherDateChangeHandler"
            :disabled-date="diabledDateHanlder"/>
          <span>{{ voucherPeriod }}</span>
        </div>
        <div class="basic-item">
          <el-popover placement="left" :width="300" trigger="click">
            <template #reference>
              <span class="item-label remark">备注</span>
            </template>
            <el-input v-model="voucherForm.remark" :rows="3" type="textarea" placeholder="请输入" :disabled="disabledFlag.remark" maxlength="100"/>
          </el-popover>
          <span class="item-label mr8">附单据</span>
          <el-input v-model="voucherForm.attachment.length" style="width: 60px;margin-right: 8px;" readonly/>
          <span class="item-label">张</span>
          <el-button color="#762ADB" plain style="margin-left: 20px;" @click="openUploadDialog">附件</el-button>
        </div>
      </div>
      <div class="voucher-table" v-loading="loading">
        <table class="table-dom">
          <thead>
            <tr>
              <th rowspan="2" colspan="1" style="width: 4%;">序号</th>
              <th rowspan="2" colspan="1" style="width: 18%;">摘要</th>
              <th rowspan="2" colspan="1" style="width: 18%;">会计科目</th>
              <th rowspan="1" colspan="14" :style="{'width': isShowCashflowColumn ? '24%' : '30%'}">借方金额</th>
              <th rowspan="1" colspan="14" :style="{'width': isShowCashflowColumn ? '24%' : '30%'}">贷方金额</th>
              <th v-if="isShowCashflowColumn" rowspan="2" colspan="1" style="width: 12%;">现金项目</th>
            </tr>
            <tr>
              <th>千</th>
              <th>百</th>
              <th class="border-blue">十</th>
              <th>亿</th>
              <th>千</th>
              <th class="border-blue">百</th>
              <th>十</th>
              <th>万</th>
              <th class="border-blue">千</th>
              <th>百</th>
              <th>十</th>
              <th class="border-red">元</th>
              <th>角</th>
              <th>分</th>
              <th>千</th>
              <th>百</th>
              <th class="border-blue">十</th>
              <th>亿</th>
              <th>千</th>
              <th class="border-blue">百</th>
              <th>十</th>
              <th>万</th>
              <th class="border-blue">千</th>
              <th>百</th>
              <th>十</th>
              <th class="border-red">元</th>
              <th>角</th>
              <th>分</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in voucherForm.balanceFlows" :key="index">
              <td>
                <div class="td-no">
                  {{ index + 1 }}
                  <div class="icon-wrap">
                    <el-icon color="#29B610" size="16px" @click="addRowHandler(index)"><CirclePlus /></el-icon>
                    <el-icon color="#C00C1D" size="16px" @click="removeRowHandler(index)"><Remove /></el-icon>
                  </div>
                </div>
              </td>
              <td>
                <div class="td-summary">
                  <input v-if="item.showSummaryInput" v-focus v-model="item.summary" type="text" :disabled="disabledFlag.summary" @keydown="keyupEvents(index, $event, 'summary')" @blur="hideInput(index, 'summary')"/>
                  <!-- <input v-else v-model="item.summary" type="text" readonly @click="showInput(index, 'summary')"/> -->
                  <div class="summary-lable"  v-else @click="showInput(index, 'summary')">{{ item.summary }}</div>
                  <el-icon size="16"><Search @click="openSummaryDialog(index)"/></el-icon>
                </div>
              </td>
              <td>
                <div class="td-summary" @click="openSubjectDialog(index)">
                  <div class="subject-box">
                    <div class="subject-name">{{ item.subjectCode ? `${item.subjectCode}: ${item.subjectName}` : '' }}</div>
                    <div :class="['subject-money', item.finalBalance < 0 ? 'red' : '']" v-if="item.subjectCode">余额: {{ item.finalBalance }}</div>
                  </div>
                  <el-icon size="16"><Search/></el-icon>
                </div>
              </td>
              <td v-if="item.showDebitInput" colspan="14" class="td-input">
                <input v-if="item.showDebitInput" @blur="hideInput(index, 'debit')" v-focus v-model="item.debitAmount" type="text" v-number="'decimal'" maxlength="12" @keydown="keyupEvents(index, $event, 'debit')"/>
              </td>
              <template v-else>
                <td v-for="(debit, idx) in item.debitAmountArr" :key="idx" :class="['number-bold', +item.debitAmount < 0 ? 'red' : '', 'td-debit-' + idx]" @click="showInput(index, 'debit')">{{ debit }}</td>
              </template>
              <td v-if="item.showCreditInput" colspan="14" class="td-input">
                <input v-if="item.showCreditInput" @blur="hideInput(index, 'credit')" v-focus v-model="item.creditAmount"  type="text" v-number="'decimal'" maxlength="12" @keydown="keyupEvents(index, $event, 'credit')"/>
              </td>
              <template v-else>
                <td v-for="(credit, idx) in item.creditAmountArr" :key="idx" :class="['number-bold', +item.creditAmount < 0 ? 'red' : '', 'td-credit-' + idx]" @click="showInput(index, 'credit')">{{ credit }}</td>
              </template>
              <td v-if="isShowCashflowColumn">
                <div class="td-summary" @click="openCashflowDialog(index)" v-if="item.equivalentFlag === 1">
                  <div class="cashflow-name">{{ item.auxiliaryName }}</div>
                  <el-icon size="16"><Search/></el-icon>
                </div>
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="3" class="total-td">合计: {{ amountToChinese(+totalDebitAmount) }}</td>
              <td v-for="(debit, idx) in totalDebitAmountArr" :key="idx" :class="['number-bold', +totalDebitAmount < 0 ? 'red' : '']">{{ debit }}</td>
              <td v-for="(credit, idx) in totalCreditAmountArr" :key="idx" :class="['number-bold', +totalCreditAmount < 0 ? 'red' : '']">{{ credit }}</td>
            </tr>
          </tfoot>
        </table>
      </div>
      <div class="voucher-maker">
        <slot name="user"></slot>
      </div>
    </div>
    <div class="voucher-footer">
      <slot name="btn"></slot>
    </div>
    <!-- 选择摘要弹窗 -->
    <summary-dialog v-model="showSummaryDialog" @confirm="summaryDialogConfirm"></summary-dialog>
    <!-- 选择科目弹窗 -->
    <subject-dialog v-model="showSubjectDialog" @confirm="subjectDialogConfirm"></subject-dialog>
    <!-- 选择现金流项目弹窗 -->
    <cashflow-dialog v-model="showCashflowDialog" @confirm="cashflowDialogConfirm"></cashflow-dialog>
    <!-- 附件上传弹窗 -->
    <upload-dialog v-model:visible="showUploadDialog" ref="uploadDialogRef" @change="uploadDialogChange"></upload-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import amountToChinese from '@/core/utils/amountToChinese';
import summaryDialog from '@/modules/finance/components/summaryDialog.vue';
import subjectDialog from '@/modules/finance/components/subjectDialog.vue';
import uploadDialog from '@/modules/finance/components/uploadDialog.vue';
import cashflowDialog from '@/modules/finance/components/cashflowDialog.vue';
import { voucher } from '@/modules/finance/api/index';
import {useRouter} from "vue-router";
import type { TSubjectItem, TVoucherTypeItem, TVoucherForm } from "@/modules/finance/types/voucher";
import API from '@/modules/finance/api/accountStatementApi';
import moment from 'moment';
const props = defineProps<{
  initData: TVoucherForm,
  type: 'detail' | 'edit' | 'create',
  loading: boolean
}>();
const router = useRouter();
const activeIndex = ref(0);
const voucherForm = reactive(props.initData);
const disabledFlag = computed(() => {
  return {
    word: props.type === 'detail',
    date: props.type === 'detail',
    remark: props.type === 'detail',
    summary: props.type === 'detail',
    subject: props.type !== 'edit',
    cash: props.type === 'detail',
    rowOpt: props.type !== 'edit',
    amount: props.type !== 'edit'
  }
})

/* 监听tab和enter */
const keyupEvents = (index: number, event: KeyboardEvent, type: string) => {
  if (event.code === 'Enter' || event.code === 'Tab') {
    event.preventDefault();
    if (type === 'summary') {
      openSubjectDialog(index);
    } else if (type === 'debit') {
      showInput(index, 'credit');
    } else if (type === 'credit') {
      if (voucherForm.balanceFlows[index].equivalentFlag === 1) {
        openCashflowDialog(index);
      } else {
        if (index < voucherForm.balanceFlows.length - 1) {
          showInput(index + 1, 'summary');
        } else {
          addRowHandler(index);
          showInput(index + 1, 'summary');
        }
      }
    }
  }
}

/* 凭证时间 */
const startVoucherDate = ref('');
const loadPeriodHandler = async () => {
  try {
    const response = await API.getPeriodScope({
      queryType: 2
    });
    const periodYearMonthStart = String(response.periodYearMonthStart);
    const year = periodYearMonthStart.slice(0, 4);
    const month = periodYearMonthStart.slice(4, 6);
    startVoucherDate.value = moment(`${year}-${month}-01`).subtract(1, 'days').format('YYYY-MM-DD');
  } catch (error) {
    if(error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
  }
};
const voucherDateChangeHandler =  async (val: string)  => {
  try {
    const res = await voucher.queryVoucherTypeList({
      voucherDate: val
    });
    voucherForm.voucherNumber = res.find(item => item.voucherWord === voucherForm.voucherWord)!.nextNum;
  } catch(err)  {
    console.log(err)
  }
}
const diabledDateHanlder = (date: Date) => {
  return new Date(date).getTime() <= new Date(startVoucherDate.value).getTime();
}

/* 凭证字 */
const voucherTypeList = reactive<TVoucherTypeItem[]>([]);
const displayTitle = computed(() => {
  const activeItem = voucherTypeList.find(item => item.voucherWord === voucherForm.voucherWord);
  return activeItem ? activeItem.displayTitle : ''
})
const getVoucherTypeList = async () => {
  try {
    const res = await voucher.queryVoucherTypeList({
      voucherDate: voucherForm.voucherDate
    });
    voucherTypeList.push(...res);
    if (!voucherForm.voucherWord) {
      const defaultWordItem = voucherTypeList.find(item => item.isDefault);
      if (defaultWordItem) {
        voucherForm.voucherWord = defaultWordItem.voucherWord;
        voucherForm.voucherNumber = defaultWordItem.nextNum;
      }
    }
    if (voucherForm.voucherWord && !voucherForm.voucherNumber) {
      voucherForm.voucherNumber = voucherTypeList.find(item => item.voucherWord === voucherForm.voucherWord)!.nextNum;
    }
  } catch(e) {
    // ElMessage.error(e as string);
  }
}
const voucherTypeChangeHandler = async (val: string) => {
  voucherTypeList.length = 0;
  try {
    const res = await voucher.queryVoucherTypeList({
      voucherDate: voucherForm.voucherDate
    });
    voucherTypeList.push(...res);
    const avtiveNum = voucherTypeList.find(item => item.voucherWord === val)!.nextNum;
    voucherForm.voucherNumber = avtiveNum;
  } catch (err) {
    console.log(err);
  }
}

/* 凭证日期 */
const voucherPeriod = computed(() => {
  const year = voucherForm.voucherDate.slice(0, 4);
  const month = voucherForm.voucherDate.slice(4, 6);
  return `${year}年 ${month}期`;
})
/* 删除或添加行 */
const addRowHandler = (idx: number) => {
  if (disabledFlag.value.rowOpt) return;
  const rowItem = {
    summary: '',
    showSummaryInput: false,
    subjectId: '',
    equivalentFlag: 0,
    subjectCode: '',
    subjectName: '',
    debitAmount: '',
    debitAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    showDebitInput: false,
    creditAmount: '',
    creditAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
    showCreditInput: false,
    auxiliaryName: '',
    initBalance: '',
    finalBalance: 0,
    subjectBalanceDirection: ''
  }
  voucherForm.balanceFlows.splice(idx + 1, 0, rowItem);
}
const removeRowHandler = (idx: number) => {
  if (disabledFlag.value.rowOpt) return;
  if (voucherForm.balanceFlows.length > 4) {
    voucherForm.balanceFlows.splice(idx, 1);
  } else  {
    voucherForm.balanceFlows.splice(idx, 1);
    addRowHandler(idx - 1);
  }
}

/* 借、贷金额关联 */
const totalDebitAmount = computed(() => {
  return computeTotalAmount('debitAmount');
});
const totalDebitAmountArr = computed(() => {
  return tanslateStringToArr(totalDebitAmount.value);
});
const totalCreditAmount = computed(() => {
  return computeTotalAmount('creditAmount');
});
const totalCreditAmountArr = computed(() => {
  return tanslateStringToArr(totalCreditAmount.value);
});
const computeTotalAmount = (type: string) => { //数组计算某项和值
  const result = voucherForm.balanceFlows.reduce((pre, cur) => {
    return pre + Number(cur[type]) * 100
  }, 0)
  return String(result / 100);
}
const tanslateStringToArr = (value: string) => { // 将数字字符串转换成对应位置的数组
  if (value === '' || value === '0' || value === null) return ['', '', '', '', '', '', '', '', '', '', '', '', '', ''];
  if (!value.includes('.')) {
    value = value+'.00';
  } else {
    const index = value.indexOf('.');
    const len = value.slice(index).length;
    value = len === 2 ? value + '0' : value;
  }
  const formateValue = value.trim().replace(/[-.]/g, '');
  const resultArr = formateValue.split('');
  while(resultArr.length < 14) {
    resultArr.unshift('');
  }
  return resultArr;
}
const showInput = (index: number, type: string) => { // 展示对应单元格中的输入框
  if (disabledFlag.value.amount) return;
  voucherForm.balanceFlows.forEach(item => {
    item.showSummaryInput = false;
    item.showDebitInput = false;
    item.showCreditInput = false;
  })
  switch (type) {
    case 'summary':
      if (index > 0) {
        const prevSummary = voucherForm.balanceFlows[index - 1].summary;
        const curSummary = voucherForm.balanceFlows[index].summary;
        if (curSummary === '' && prevSummary !== '') {
          voucherForm.balanceFlows[index].summary = prevSummary;
        }
      }
      voucherForm.balanceFlows[index].showSummaryInput = true;
      break;
    case 'debit':
      voucherForm.balanceFlows[index].showDebitInput = true;
      break;
    case 'credit':
      voucherForm.balanceFlows[index].showCreditInput = true;
      break;
  }
}
const hideInput = (index: number, type: string) => { // 隐藏对应单元格的输入框
  if (disabledFlag.value.amount) return;
  switch (type) {
    case 'summary':
      voucherForm.balanceFlows[index].showSummaryInput = false;
      break;
    case 'debit':
      voucherForm.balanceFlows[index].showDebitInput = false;
      voucherForm.balanceFlows[index].debitAmountArr = tanslateStringToArr(voucherForm.balanceFlows[index].debitAmount);
      if (voucherForm.balanceFlows[index].debitAmount) {
        voucherForm.balanceFlows[index].creditAmount = '';
        voucherForm.balanceFlows[index].creditAmountArr = tanslateStringToArr(voucherForm.balanceFlows[index].creditAmount);
      }
      const sId = voucherForm.balanceFlows[index].subjectId;
      if (sId) {
        const initBalance = voucherForm.balanceFlows[index].initBalance;
        const subjectBalanceDirection = voucherForm.balanceFlows[index].subjectBalanceDirection;
        const sameSubjectTableData = voucherForm.balanceFlows.filter(subject => subject.subjectId === sId);
        computeFinalBlance(initBalance, subjectBalanceDirection, sameSubjectTableData)
      }
      break;
    case 'credit':
      voucherForm.balanceFlows[index].showCreditInput = false;
      voucherForm.balanceFlows[index].creditAmountArr = tanslateStringToArr(voucherForm.balanceFlows[index].creditAmount);
      if (voucherForm.balanceFlows[index].creditAmount) {
        voucherForm.balanceFlows[index].debitAmount = '';
        voucherForm.balanceFlows[index].debitAmountArr = tanslateStringToArr(voucherForm.balanceFlows[index].debitAmount);
      }
      const subjectId = voucherForm.balanceFlows[index].subjectId;
      if (subjectId) {
        const initBalance = voucherForm.balanceFlows[index].initBalance;
        const subjectBalanceDirection = voucherForm.balanceFlows[index].subjectBalanceDirection;
        const sameSubjectTableData = voucherForm.balanceFlows.filter(subject => subject.subjectId === subjectId);
        computeFinalBlance(initBalance, subjectBalanceDirection, sameSubjectTableData)
      }
      break;
    }
}
/* 附件上传交互 */
const showUploadDialog = ref(false);
const uploadDialogRef = ref();
const openUploadDialog = () => {
  showUploadDialog.value = true;
  uploadDialogRef.value.setEditType(props.type);
  uploadDialogRef.value.setFormData(voucherForm.attachment);
}
const uploadDialogChange = (data: any) => {
  voucherForm.attachment = data;
}

/* 摘要交互 */
const showSummaryDialog = ref(false);
const openSummaryDialog = (index: number) => {
  if (disabledFlag.value.summary) return;
  activeIndex.value = index;
  showSummaryDialog.value = true;
}
const summaryDialogConfirm = (summary: string) => {
  voucherForm.balanceFlows[activeIndex.value].summary = summary;
}

/* 科目交互 */
const showSubjectDialog = ref(false);
const openSubjectDialog = (index: number) => {
  if (disabledFlag.value.subject) return;
  activeIndex.value = index;
  showSubjectDialog.value = true;
}
const computeFinalBlance = (initBalance: string, subjectBalanceDirection: string, subjectData: typeof voucherForm.balanceFlows) => {
  const finalBlance = subjectData.reduce((prev, cur) => {
    if (subjectBalanceDirection === '借') {
      return prev + Number(cur.debitAmount) - Number(cur.creditAmount);
    } else {
      return prev - Number(cur.debitAmount) + Number(cur.creditAmount);
    }
  }, Number(initBalance));
  subjectData.forEach(subject => {
    subject.finalBalance = finalBlance;
  })
}
const subjectDialogConfirm = async (item: TSubjectItem) => {
  voucherForm.balanceFlows[activeIndex.value].subjectCode = item.subjectCode;
  voucherForm.balanceFlows[activeIndex.value].subjectName = item.subjectFullName;
  voucherForm.balanceFlows[activeIndex.value].subjectId = item.id;
  voucherForm.balanceFlows[activeIndex.value].equivalentFlag = item.equivalentFlag;
  voucherForm.balanceFlows[activeIndex.value].subjectBalanceDirection = item.subjectBalanceDirection;
  if (item.equivalentFlag !== 1) {
    voucherForm.balanceFlows[activeIndex.value].auxiliaryName = '';
  }
  try {
    const initBalance = await voucher.querySubjectBalance({id: item.id});
    voucherForm.balanceFlows[activeIndex.value].initBalance = initBalance;
    const sameSubjectTableData = voucherForm.balanceFlows.filter(subject => subject.subjectId === item.id);
    computeFinalBlance(initBalance, item.subjectBalanceDirection, sameSubjectTableData);
  } catch(err) {
    console.log(err as string);
  }
  if (item.subjectBalanceDirection === '借') {
    voucherForm.balanceFlows[activeIndex.value].showDebitInput = true;
  } else {
    voucherForm.balanceFlows[activeIndex.value].showCreditInput = true;
  }
}

/* 现金流项目弹窗 */
const showCashflowDialog = ref(false);
const openCashflowDialog = (index: number) => {
  if (disabledFlag.value.cash) return;
  activeIndex.value = index;
  showCashflowDialog.value = true;
}
const cashflowDialogConfirm = (item: string) => {
  voucherForm.balanceFlows[activeIndex.value].auxiliaryName = item;
  if (activeIndex.value < voucherForm.balanceFlows.length - 1) {
    showInput(activeIndex.value + 1, 'summary');
  } else {
    addRowHandler(activeIndex.value);
    showInput(activeIndex.value + 1, 'summary');
  }
}
const isShowCashflowColumn = computed(() => {
  return voucherForm.balanceFlows.find(item => item.equivalentFlag === 1);
})
/* 参数校验 */
const checkVoucherFormValid = () => {
  if (totalCreditAmount.value !== totalDebitAmount.value) {
    ElMessage.error('借贷金额不平，请检查数据！');
    return false;
  } else {
    const everyDebitValid = voucherForm.balanceFlows.every(item => (item.debitAmount === ''  || item.debitAmount === '0'));
    const everyCreditValid = voucherForm.balanceFlows.every(item => (item.creditAmount === '' || item.debitAmount === '0'));
    if (totalCreditAmount.value === '0' && everyDebitValid  && everyCreditValid) {
      ElMessage.error('借贷金额不能为空！');
      return false;
    } else {
      for (let i = 0; i < voucherForm.balanceFlows.length; i++) {
        if (+voucherForm.balanceFlows[i].creditAmount === 0 && +voucherForm.balanceFlows[i].debitAmount === 0) {
          continue;
        } else {
          if (voucherForm.balanceFlows[i].summary === '') {
            ElMessage.error(`第${i+1}行摘要为空，请输入摘要`);
            return false;
          }
          if (voucherForm.balanceFlows[i].subjectId === '') {
            ElMessage.error(`第${i+1}行会计科目为空，请选择会计科目`);
            return false;
          }
          if (voucherForm.balanceFlows[i].equivalentFlag === 1 && voucherForm.balanceFlows[i].auxiliaryName === '') {
            ElMessage.error(`第${i+1}行现金项目为空，请选择现金项目`);
            return false;
          }
        }
      }
    }
  }
  return true;
}
/* 凭证提交参数 */
const getSubmitParams = () => {
  const validBalanceFlows = voucherForm.balanceFlows.filter(item => (+item.debitAmount !== 0 || +item.creditAmount !== 0));
  const params = {
    ...voucherForm,
    attachment: voucherForm.attachment.length ? JSON.stringify(voucherForm.attachment) : '',
    balanceFlows: validBalanceFlows.map(item => {
      return {
        auxiliaryName: item.auxiliaryName,
        creditAmount: item.creditAmount,
        debitAmount: item.debitAmount,
        subjectId: item.subjectId,
        summary: item.summary
      }
    })
  }
  return params;
}
/* 清空操作 */
const resetHandler = () => { //重填操作
  voucherForm.remark = '';
  voucherForm.attachment = [];
  voucherForm.balanceFlows.length = 0;
  let i = 0;
  while(i < 4) {
    addRowHandler(i);
    i++;
  }
}

defineExpose({
  voucherForm,
  checkVoucherFormValid,
  getSubmitParams,
  resetHandler
})
onMounted(() => {
  loadPeriodHandler();
  getVoucherTypeList();
})
</script>

<style scoped lang='scss'>
.voucher-bill {
  min-height: 100%;
  background-color: #FFFFFF;
  .voucher-footer {
    position: sticky;
    bottom: 0px;
    // background-color: #FFFFFF;
  }
  .voucher-box {
    padding: 0px 30px 50px 30px;
    .voucher-title {
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 30px;
      color: #151719;
      line-height: 42px;
      padding-bottom: 40px;
      border-bottom: 1px solid #E5E7F3;
      position: relative;
      img {
        position: absolute;
        width: 84px;
        height: 73px;
        right: 20px;
        top: 0;
      }
    }
    .voucher-basic {
      margin-top: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .basic-item {
        .item-label {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #151719;
          line-height: 32px;
          &.remark {
            color: #762ADB;
            margin-right: 42px;
            cursor: pointer;
          }
          &.mr8 {
            margin-right: 8px;
          }
        }
      }
    }
    .voucher-table {
      margin-top: 30px;
      .table-dom {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #9496A1;
        // border: 1px solid #E5E7F3;
        thead {
          tr {
            &:last-child {
              th {
                background-color: #FFFFFF;
              }
            }
            th {
              background-color: #F4F6FA;
              height: 50px;
              border: 1px solid #9496A1;
              font-family: PingFangSC, PingFang SC;
              font-weight: 600;
              font-size: 14px;
              color: #52585F;
              &.border-blue {
                border-right-color: #84CDFF;
              }
              &.border-red {
                border-right-color: #FF9F9F;
              }
            }
          }
        }
        tbody {
          tr {
            &:hover {
              background-color: #F4F6FA;
              cursor: pointer;
              td .td-no .icon-wrap {
                opacity: 1;
              }
            }
            td {
              &.td-debit-2, &.td-debit-5, &.td-debit-8 {
                border-right-color: #84CDFF;
              }
              &.td-debit-11 {
                border-right-color: #FF9F9F;
              }
              &.td-credit-2, &.td-credit-5, &.td-credit-8 {
                border-right-color: #84CDFF;
              }
              &.td-credit-11 {
                border-right-color: #FF9F9F;
              }
              .td-summary {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 24px;
                // padding: 24px;
                // height: 52px;
                .subject-box {
                  text-align: left;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  font-size: 14px;
                  color: #151719;
                  .subject-money {
                    margin-top: 8px;
                    &.red {
                      color: #FF192F;
                    }
                  }
                }
                input {
                  outline:none;
                  border:none;
                  background:none;
                  font-family: PingFangSC, PingFang SC;
                  font-size: 14px;
                  font-weight: 400;
                  color: #151719;
                  line-height: 28px;
                  padding-right: 0;
                  width: 80%;
                  height: 80px;
                  text-align: left;
                }
                .summary-lable {
                  display: flex;
                  align-items: center;
                  width: 80%;
                  height: 80px;
                  line-height: 28px;
                  text-align: left;
                  font-family: PingFangSC, PingFang SC;
                  font-size: 14px;
                  font-weight: 400;
                  color: #151719;
                  word-break: break-all;
                }
                .cashflow-name {
                  text-align: left;
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  font-size: 14px;
                  color: #151719;
                  line-height: 20px;
                }
              }
              &.td-input {
                text-align: right;
                background-color: #FFFFFF;
                input {
                  width: 80%;
                  outline:none;
                  border:none;
                  background:none;
                  font-family: DINPro, DINPro;
                  font-weight: bold;
                  font-size: 36px;
                  color: #151719;
                  text-align: right;
                  line-height: 80px;
                  padding-right: 32px;
                }
              }
            }
          }
        }
        tfoot tr td {
          &:nth-child(4) {
            border-right-color: #84CDFF;
          }
          &:nth-child(7) {
            border-right-color: #84CDFF;
          }
          &:nth-child(10) {
            border-right-color: #84CDFF;
          }
          &:nth-child(13) {
            border-right-color: #FF9F9F;
          }
          &:nth-child(18) {
            border-right-color: #84CDFF;
          }
          &:nth-child(21) {
            border-right-color: #84CDFF;
          }
          &:nth-child(24) {
            border-right-color: #84CDFF;
          }
          &:nth-child(27) {
            border-right-color: #FF9F9F;
          }
        }
        td {
          text-align: center;
          height: 100px;
          border: 1px solid #9496A1;
          .td-no {
            height: 100%;
            line-height: 100px;
            position: relative;
            .icon-wrap {
              position: absolute;
              bottom: 16px;
              width: 100%;
              display: flex;
              justify-content: space-around;
              opacity: 0;
            }
          }
          &.number-bold {
            font-family: DINPro, DINPro;
            font-weight: bold;
            font-size: 20px;
            color: #151719;
            &.red {
              color: #FF192F;
            }
          }
          &.total-td {
            text-align: left;
            padding-left: 24px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
            color: #151719;
          }
        }
      }
    }
    .voucher-maker {
      margin-top: 20px;
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #151719;
      line-height: 20px;
    }
  }
  .voucher-footer {
    padding: 12px 30px;
    border-top: 1px solid #E5E7F3;
    text-align: right;
  }
}
</style>
