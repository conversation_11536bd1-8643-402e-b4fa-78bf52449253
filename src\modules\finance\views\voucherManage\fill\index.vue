<template>
  <div class="voucher-fill">
    <div class="voucher-fill-box">
      <voucher-bill :initData="voucherInit" type="edit" ref="voucherRef" :loading="loading">
        <template #user>
          <div style="margin-right: 32px;">
            制单人：{{ userStore.user.nickName }}
          </div>
        </template>
        <template #btn>
          <el-button v-hasPerm="['finance:voucherfill:reset']" plain @click="onResetHandler">重填</el-button>
          <el-button v-hasPerm="['finance:voucherfill:add']" color="#762ADB" plain @click="saveCopyHandler">保存并复制</el-button>
          <el-button v-hasPerm="['finance:voucherfill:add']" color="#762ADB" plain @click="saveAddHandler">保存并新增</el-button>
          <el-button v-hasPerm="['finance:voucherfill:add']" color="#762ADB" @click="saveHandler">保存</el-button>
        </template>
        </voucher-bill>
      </div>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive } from 'vue';
import { useUserStore } from '@/core/store';
import { voucher } from '@/modules/finance/api/index';
import type { TVoucherForm } from "@/modules/finance/types/voucher";
import voucherbill from '@/modules/finance/components/voucherBill.vue';
import { onBeforeRouteLeave } from 'vue-router';
const userStore = useUserStore();
const loading = ref(false);
const voucherRef = ref();
const voucherInit = reactive<TVoucherForm>({
  id: '',
  auditStatus: '',
  voucherWord: '',
  voucherNumber: 1,
  voucherDate: new Date().toISOString().split('T')[0].replace(/-/g, ''),
  remark: '',
  attachment: [],
  balanceFlows: [
    {
      summary: '',
      showSummaryInput: false,
      subjectId: '',
      equivalentFlag: 0,
      subjectCode: '',
      subjectName: '',
      debitAmount: '',
      debitAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      showDebitInput: false,
      creditAmount: '',
      creditAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      showCreditInput: false,
      auxiliaryName: '',
      initBalance: '',
      finalBalance: 0,
      subjectBalanceDirection: ''
    },
    {
      summary: '',
      showSummaryInput: false,
      subjectId: '',
      equivalentFlag: 0,
      subjectCode: '',
      subjectName: '',
      debitAmount: '',
      debitAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      showDebitInput: false,
      creditAmount: '',
      creditAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      showCreditInput: false,
      auxiliaryName: '',
      initBalance: '',
      finalBalance: 0,
      subjectBalanceDirection: ''
    },
    {
      summary: '',
      showSummaryInput: false,
      subjectId: '',
      equivalentFlag: 0,
      subjectCode: '',
      subjectName: '',
      debitAmount: '',
      debitAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      showDebitInput: false,
      creditAmount: '',
      creditAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      showCreditInput: false,
      auxiliaryName: '',
      initBalance: '',
      finalBalance: 0,
      subjectBalanceDirection: ''
    },
    {
      summary: '',
      showSummaryInput: false,
      subjectId: '',
      equivalentFlag: 0,
      subjectCode: '',
      subjectName: '',
      debitAmount: '',
      debitAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      showDebitInput: false,
      creditAmount: '',
      creditAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      showCreditInput: false,
      auxiliaryName: '',
      initBalance: '',
      finalBalance: 0,
      subjectBalanceDirection: ''
    }
  ]
})
const onResetHandler = () => { //重填
  voucherRef.value.resetHandler();
}
const saveHandler = async () => { //保存
  commonSaveHandler('保存成功!', (id: string) => {
    voucherRef.value.voucherForm.id = id;
  })
}
const getNextVoucherNum = async () => {
  try {
    const res = await voucher.queryVoucherTypeList({
      voucherDate: voucherRef.value.voucherForm.voucherDate
    });
    const avtiveNum = res.find(item => item.voucherWord === voucherRef.value.voucherForm.voucherWord)!.nextNum;
    voucherRef.value.voucherForm.voucherNumber = avtiveNum;
  } catch (err) {
    console.log(err);
  }
}
const saveAddHandler = () => { //保存并新增
  commonSaveHandler('保存并新增成功', () => {
    getNextVoucherNum();
    voucherRef.value.voucherForm.id = '';
    voucherRef.value.resetHandler();
  })
}
const saveCopyHandler = () => { //保存并复制
  commonSaveHandler('保存并复制成功', () => {
    getNextVoucherNum();
    voucherRef.value.voucherForm.id = '';
  })
}
const commonSaveHandler = async (successMsg: string, callback: Function) => {
  if (!voucherRef.value) return;
  const isValid = voucherRef.value.checkVoucherFormValid();
  if (isValid) {
    const params = voucherRef.value.getSubmitParams();
    try {
      loading.value = true;
      const res = await voucher.saveVoucher(params);
      if (res) {
        ElMessage.success(successMsg);
        callback && callback(res);
      }
      loading.value = false;
    } catch(err) {
      loading.value = false;
      console.log(err)
    }
  }
}
onBeforeRouteLeave((to, from, next) => {
  if (sessionStorage.getItem('removeVoucherFlag') === 'true') {
    sessionStorage.removeItem('voucherForm');
  }  else {
    sessionStorage.setItem('voucherForm', JSON.stringify(voucherRef.value.voucherForm));
  }
  sessionStorage.setItem('removeVoucherFlag', 'false');
  next();
})
onMounted(() => {
  const storageVoucherFormStr = sessionStorage.getItem('voucherForm');
  if (storageVoucherFormStr) {
    const storageVoucherForm = JSON.parse(storageVoucherFormStr);
    for (let k in voucherRef.value.voucherForm) {
      voucherRef.value.voucherForm[k] = storageVoucherForm[k]
    }
  }
})
</script>
<style scoped lang='scss'>
.voucher-fill {
  padding-top: 30px;
  min-height: calc(100% - 30px);
  background-color: #FFFFFF;
  .voucher-fill-box {
    width: 80%;
    min-width: 1200px;
    margin: 0 auto;
  }
}
</style>