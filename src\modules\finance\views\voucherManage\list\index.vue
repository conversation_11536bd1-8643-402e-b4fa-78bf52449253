<template>
  <div class="app-container voucher-list">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="会计期间">
              <el-date-picker
                @change="monthRangeChange"
                v-model="monthRange"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM"
                value-format="YYYYMM"
                :clearable="false"
                :disabled-date="
                (date) => {
                  const start = periodYearMonthStart;
                  const end = periodYearMonthEnd;
                  if (!start || !end) return false;
                  const y = date.getFullYear();
                  const m = (date.getMonth() + 1).toString().padStart(2, '0');
                  const ym = `${y}${m}`;
                  return ym < start || ym > end;
                }
              "
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="凭证字" prop="voucherWord">
              <el-row>
                <el-col :span="7">
                  <el-select v-model="queryParams.voucherWord" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in voucherTypeList" :key="index" :label="item.voucherWord" :value="item.voucherWord"/>
                  </el-select>
                </el-col>
                <el-col :span="7" :offset="1">
                  <el-input v-model="queryParams.voucherNumberStart" placeholder="" clearable/>
                </el-col>
                <el-col :span="2" style="text-align: center;">
                  <span>至</span>
                </el-col>
                <el-col :span="7">
                  <el-input v-model="queryParams.voucherNumberEnd" placeholder="" clearable/>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="制单人" prop="createUserName">
              <el-input v-model="queryParams.createUserName" placeholder="请输入" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="会计科目" prop="subjectId">
              <el-select v-model="queryParams.subjectId" clearable filterable placeholder="请选择">
                <el-option v-for="(item, index) in subjectList" :key="index" :label="`${item.subjectCode}:${item.subjectFullName}`" :value="item.id"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="摘要" prop="summary">
              <el-input v-model="queryParams.summary" placeholder="请输入" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="凭证状态" prop="auditStatus">
              <el-select v-model="queryParams.auditStatus" clearable placeholder="请选择">
                <el-option v-for="(item, index) in auditStatusList" :key="index" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="金额" prop="amountStart">
              <el-row>
                <el-col :span="11">
                  <el-input v-model="queryParams.amountStart" placeholder="" clearable/>
                </el-col>
                <el-col :span="2" style="text-align: center;">
                  <span>至</span>
                </el-col>
                <el-col :span="11">
                  <el-input v-model="queryParams.amountEnd" placeholder="" clearable/>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="排序依据" prop="orderField">
              <el-radio-group v-model="queryParams.orderField">
                <el-radio :value="0">凭证号排序</el-radio>
                <el-radio :value="1">凭证日期排序</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="排序方式" prop="orderType">
              <el-radio-group v-model="queryParams.orderType">
                <el-radio :value="0">升序</el-radio>
                <el-radio :value="1">降序</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button v-hasPerm="['finance:voucherlist:search']" type="primary" @click="onSearchHanlder">搜索</el-button>
              <el-button v-hasPerm="['finance:voucherlist:search']" @click="onResetHandler">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="btn-wrap">
          <div class="left-btn">
            <el-button v-hasPerm="['finance:voucherlist:add']" type="primary" @click="addVoucherHandler">新增凭证</el-button>
            <el-button v-hasPerm="['finance:voucherlist:audit']" type="primary" plain @click="batchUpdateStatusHandler(1)" :disabled="disableFlag">批量审核</el-button>
            <el-button v-hasPerm="['finance:voucherlist:auditback']" type="primary" plain @click="batchUpdateStatusHandler(3)" :disabled="disableFlag">批量反审核</el-button>
          </div>
          <el-alert v-if="invalidVoucherNumList.length" title="当前期间凭证存在断号！" type="warning" show-icon :closable="false"/>
          <div class="right-btn">
            <el-button v-hasPerm="['finance:voucherlist:checknum']" @click="dialogOpenHandler">整理断号</el-button>
            <el-button v-hasPerm="['finance:voucherlist:record']" @click="batchUpdateStatusHandler(2)" :disabled="disableFlag">批量记账</el-button>
            <el-button v-hasPerm="['finance:voucherlist:recordback']" @click="batchUpdateStatusHandler(4)" :disabled="disableFlag">批量反记账</el-button>
            <el-button v-hasPerm="['finance:voucherlist:delete']" type="danger" plain @click="batchRemoveHandler" :disabled="disableFlag">批量删除</el-button>
            <!-- <el-button v-hasPerm="['finance:voucherlist:print']" type="primary" plain>打印</el-button> -->
            <el-button v-hasPerm="['finance:voucherlist:import']" type="primary">导入凭证</el-button>
            <el-button v-hasPerm="['finance:voucherlist:export']" type="primary">导出凭证</el-button>
          </div>
        </div>
      </template>
      <el-table v-loading="loading" :data="tableData" style="width: 100%" :span-method="rowMergeHanlder" border @selection-change="selectionChangeHandler">
        <template #empty>
          <Empty />
        </template>
        <el-table-column type="selection" width="55" fixed="left"/>
        <el-table-column label="凭证字号" prop="voucherWord" min-width="90">
          <template #default="{ row }">
            <span style="color: #762ADB;">{{ `${row.voucherWord}-${row.voucherNumber}` }}</span>
          </template>
        </el-table-column>
        <el-table-column label="日期" prop="voucherDate" min-width="140">
          <template #default="{ row }">{{ formateData(row.voucherDate) }}</template>
        </el-table-column>
        <el-table-column label="摘要" prop="summary" min-width="160"/>
        <el-table-column label="会计科目" prop="subjectName" min-width="160">
          <template #default="{ row }">{{ row.subjectCode + ':'  + row.subjectFullName }}</template>
        </el-table-column>
        <el-table-column label="借方金额" prop="debitAmount" min-width="120"/>
        <el-table-column label="贷方金额" prop="creditAmount" min-width="120"/>
        <el-table-column label="凭证状态" prop="auditStatus" min-width="120">
          <template #default="{ row }">
            <el-button type="warning" plain v-if="row.auditStatus === 0">未审核</el-button>
            <el-button type="primary" plain v-else-if="row.auditStatus === 1">审核中</el-button>
            <el-button type="success" plain v-else-if="row.auditStatus === 2">已审核</el-button>
            <el-button type="info" plain v-else>已记账</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作人员" prop="operater" min-width="120">
          <template #default="{ row }">
            <div v-for="(opt, index) in row.operateLogs" :key="index">
              {{ opt.operateType === 0 ? '制单人' :  opt.operateType === 1 ? '审核人' : '记账人' }}: {{ opt.createUserName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="{ row }">
            <el-button v-hasPerm="['finance:voucherlist:detail']" link type="primary" @click="openDrawer('detail', row.voucherId)">详情</el-button>
            <el-button v-hasPerm="['finance:voucherlist:edit']" link type="primary" v-if="row.auditStatus === 0" @click="openDrawer('edit', row.voucherId)">修改</el-button>
            <el-button v-hasPerm="['finance:voucherlist:audit']" link type="primary" v-if="row.auditStatus === 0 || row.auditStatus === 1" @click="singleUpdateStatusHanlder(row.voucherId, 1)">审核</el-button>
            <el-button v-hasPerm="['finance:voucherlist:auditback']" link type="primary" v-if="row.auditStatus === 1 || row.auditStatus === 2" @click="singleUpdateStatusHanlder(row.voucherId, 3)">反审核</el-button>
            <el-button v-hasPerm="['finance:voucherlist:record']" link type="primary" v-if="row.auditStatus === 2" @click="singleUpdateStatusHanlder(row.voucherId, 2)">记账</el-button>
            <el-button v-hasPerm="['finance:voucherlist:recordback']" link type="primary" v-if="row.auditStatus === 3" @click="singleUpdateStatusHanlder(row.voucherId, 4)">反记账</el-button>
            <el-button v-hasPerm="['finance:voucherlist:delete']" link type="danger" v-if="row.auditStatus === 0" @click="singleRemoveHanlder(row.voucherId)">删除</el-button>
            <el-button v-hasPerm="['finance:voucherlist:print']" @click="printVoucher(row)" link type="primary">打印</el-button>
            <el-button v-hasPerm="['finance:voucherlist:copy']" link type="primary" @click="copyHandler(row.voucherId)">复制</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :total="total"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes , prev, pager, next, jumper"
          @size-change="getList"
          @current-change="getList"/>
      </div>
    </el-card>
    <!-- 整理断号弹窗 -->
    <el-dialog v-model="showDialog" title="整理凭证" width="680" class="finance-moudle" :close-on-click-modal="false">
      <div class="dialog-content">
        <el-form ref="dialogFormRef" :model="refreshForm" label-width="100px">
          <el-form-item label="整理期间" prop="periodYearMonth">
            <el-date-picker
              v-model="refreshForm.periodYearMonth"
              type="month" format="YYYY-MM"
              value-format="YYYYMM"
              :clearable="false"
              style="width: 100%;"
              @change="periodYearMonthChange"
              :disabled-date="
                (date) => {
                  const start = periodYearMonthStart;
                  const end = periodYearMonthEnd;
                  if (!start || !end) return false;
                  const y = date.getFullYear();
                  const m = (date.getMonth() + 1).toString().padStart(2, '0');
                  const ym = `${y}${m}`;
                  return ym < start || ym > end;
                }
              "
              />
          </el-form-item>
          <el-form-item label="凭证字" prop="voucherWord">
            <el-select v-model="refreshForm.voucherWord" placeholder="请选择" @change="voucherWordChange">
              <el-option v-for="(item, index) in voucherTypeListAll" :key="index" :label="item.displayTitle" :value="item.voucherWord"/>
            </el-select>
          </el-form-item>
          <el-form-item label="整理类型" prop="refreshType">
            <el-radio-group v-model="refreshForm.refreshType">
              <el-radio :value="0">凭证号顺序补齐</el-radio>
              <el-radio :value="1">按凭证日期重排序</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-alert v-if="refreshForm.refreshType === 0 && invalidDialogVoucherNumList.length" :title="dialogElertTitle" type="warning" show-icon :closable="false"/>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogCancelHandler">取消</el-button>
          <el-button type="primary" @click="dialogConfirmHandler">提交</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 凭证修改、详情 -->
    <el-drawer v-model="showDrawer" :title="isCopy ? '复制凭证' : drawerType ===  'detail' ? '凭证详情' : '修改凭证'" size="80%" :close-on-click-modal="false">
      <voucher-bill :initData="voucherInit" v-if="showDrawer" :type="drawerType" ref="voucherRef" :loading="drawLoadind">
        <template #user v-if="isCopy">
          <div style="margin-right: 32px;">
            制单人: {{ userStore.user.nickName }}
          </div>
        </template>
        <template #user v-else>
          <div style="margin-right: 32px;" v-for="(opt, index) in operateLogs" :key="index">
            {{ opt.operateType === 0 ? '制单人' :  opt.operateType === 1 ? '审核人' : '记账人' }}: {{ opt.createUserName }}
          </div>
        </template>
        <template #btn>
          <el-button @click="closedrawerHandler">关闭</el-button>
          <el-button type="primary" @click="drawerSaveHandler" v-if="drawerType === 'edit'">保存</el-button>
        </template>
      </voucher-bill>
    </el-drawer>
    <Print ref="printRef"/>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { FormInstance } from 'element-plus';
import voucherbill from '@/modules/finance/components/voucherBill.vue';
import { voucher } from '@/modules/finance/api/index';
import type { TSubjectItem, TVoucherTypeItem, TVoucherItem, TCombineVoucherItem, TVoucherFormBalanceItem, TVoucherForm, TVoucherDetailFlowItem, TVoucherDetailLogsItem } from "@/modules/finance/types/voucher";
import API from '@/modules/finance/api/accountStatementApi';
import Print from "@/modules/finance/views/voucherManage/list/print.vue";
import { useUserStore } from '@/core/store';
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

/* 会计期间范围 */
const periodYearMonthStart = ref('');
const periodYearMonthEnd = ref('');
const currentPeriodYearMonth = ref('');

const printRef = ref('');
function printVoucher(data) {
  printRef.value.setFormData({id: data.voucherId})
}

const loadPeriodHandler = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    periodYearMonthStart.value = String(response.periodYearMonthStart);
    periodYearMonthEnd.value = String(response.periodYearMonthEnd);
    currentPeriodYearMonth.value = String(response.currentPeriodYearMonth);
    console.log(currentPeriodYearMonth.value)
    monthRange.value = [currentPeriodYearMonth.value, currentPeriodYearMonth.value];
    queryParams.periodYearMonthStart = currentPeriodYearMonth.value;
    queryParams.periodYearMonthEnd = currentPeriodYearMonth.value;
    checkNumVoucherHandler('table');
    getList();
  } catch (error) {
    if(error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    console.log(error)
  }
};
const voucherRef = ref();
const operateLogs = ref<TVoucherDetailLogsItem[]>([])
const voucherInit = reactive<TVoucherForm>({
  id: '',
  auditStatus: '',
  voucherWord: '',
  voucherNumber: 0,
  voucherDate: '',
  remark: '',
  attachment: [],
  balanceFlows: []
})
const showDrawer = ref(false);
const drawLoadind = ref(false);
const drawerType = ref('');
const formateData = (date: number) => {
  const dateStr = String(date)
  const year = dateStr.slice(0, 4);
  const month = dateStr.slice(4, 6);
  const day = dateStr.slice(6, 8);
  return `${year}-${month}-${day}`;
}
const tanslateStringToArr = (value: string) => { // 将数字字符串转换成对应位置的数组
  if (value === '' || value === '0' || value === null) return ['', '', '', '', '', '', '', '', '', '', '', '', '', ''];
  if (!value.includes('.')) {
    value = value+'.00';
  } else {
    const index = value.indexOf('.');
    const len = value.slice(index).length;
    value = len === 2 ? value + '0' : value;
  }
  const formateValue = value.trim().replace(/[-.]/g, '');
  const resultArr = formateValue.split('');
  while(resultArr.length < 14) {
    resultArr.unshift('');
  }
  return resultArr;
}
const initBalanceFlowsHandler = (balanceFlow:TVoucherDetailFlowItem[] ):TVoucherFormBalanceItem[] => {
  return balanceFlow.map(item => {
    return {
      summary: item.summary,
      showSummaryInput: false,
      subjectId: item.subjectId,
      equivalentFlag: item.equivalentFlag,
      subjectCode: item.subjectCode,
      subjectName: item.subjectFullName,
      debitAmount: item.debitAmount || '',
      debitAmountArr: tanslateStringToArr(item.debitAmount),
      showDebitInput: false,
      creditAmount: item.creditAmount || '',
      creditAmountArr: tanslateStringToArr(item.creditAmount),
      showCreditInput: false,
      auxiliaryName: item.auxiliaryName,
      initBalance: item.subjectBalanceDirection === '借' ? Number(item.currentBalanceAmount)  - Number(item.debitAmount) + Number(item.creditAmount) : Number(item.currentBalanceAmount)  - Number(item.creditAmount) + Number(item.debitAmount),
      finalBalance: +item.currentBalanceAmount,
      subjectBalanceDirection: item.subjectBalanceDirection
    }
  })
}

//复制操作
const isCopy = ref(false);
const copyHandler = (id: string) => {
  isCopy.value = true;
  openDrawer('edit', id);
}

const closedrawerHandler = () => {
  isCopy.value = false;
  showDrawer.value = false;
}

const openDrawer = async (type: string, voucherId: string) => {
  loading.value = true;
  drawerType.value = type;
  try {
    const params = { id: voucherId };
    const res = await voucher.queryVoucherDetail(params);
    operateLogs.value = res.operateLogs;
    for (let k in voucherInit) {
      if (k === 'balanceFlows') {
        voucherInit.balanceFlows = initBalanceFlowsHandler(res.balanceFlows);
        while ( type === 'edit' && voucherInit.balanceFlows.length < 4) {
          voucherInit.balanceFlows.push({
            summary: '',
            showSummaryInput: false,
            subjectId: '',
            equivalentFlag: 0,
            subjectCode: '',
            subjectName: '',
            debitAmount: '',
            debitAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
            showDebitInput: false,
            creditAmount: '',
            creditAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
            showCreditInput: false,
            auxiliaryName: '',
            initBalance: '',
            finalBalance: 0,
            subjectBalanceDirection: ''
          })
        }
      } else if (k === 'voucherDate') {
        voucherInit.voucherDate = String(res.voucherDate);
      } else if (k === 'attachment') {
        voucherInit.attachment = res.attachment?.length ? JSON.parse(res.attachment) : [];
      } else {
        voucherInit[k] = res[k];
      }
    }
    if (isCopy.value) {
      voucherInit.id = '';
      voucherInit.auditStatus = '';
      voucherInit.voucherNumber = 0;
    }
    loading.value = false;
    showDrawer.value = true;
  } catch(e) {
    loading.value = false;
    console.log(e);
  }
}
const drawerSaveHandler = async () => {
  drawLoadind.value = true;
  if (!voucherRef.value) return;
  const isValid = voucherRef.value.checkVoucherFormValid();
  if (isValid) {
    const params = voucherRef.value.getSubmitParams();
    try {
      loading.value = true;
      const res = await voucher.saveVoucher(params);
      if (res) {
        let msg = isCopy.value ? '复制成功!' : '修改成功!';
        ElMessage.success(msg);
        getList();
      }
      loading.value = false;
    } catch(err) {
      loading.value = false;
      console.log(err)
    }
  }
  drawLoadind.value = false;
  closedrawerHandler();
}

const queryFormRef = ref<FormInstance>();
const loading = ref(false);
const total = ref(0);
const pagination = reactive({
  pageNum: 1,
  pageSize: 15,
})
const monthRange = ref([]);
const monthRangeChange = (val: [string, string]) => {
  if (val?.length) {
    queryParams.periodYearMonthStart = val[0];
    queryParams.periodYearMonthEnd = val[1];
  } else {
    queryParams.periodYearMonthStart = '';
    queryParams.periodYearMonthEnd = '';
  }
}
/* 凭证字 */
const voucherTypeList = reactive<TVoucherTypeItem[]>([]);
const voucherTypeListAll = computed(() => {
  return [{ displayTitle: '全部', voucherWord: '全部' }, ...voucherTypeList]
})
const getVoucherTypeList = async () => {
  try {
    const res = await voucher.queryVoucherTypeList({});
    voucherTypeList.push(...res);
  } catch(e) {
    console.log(e);
  }
}
/* 科目 */
const subjectList = ref<TSubjectItem[]>([])
const getSubjectList = async () => {
  try {
    const params = {
      showTree: false,
      subjectType: ''
    }
    const res = await voucher.querySubjectList(params);
    subjectList.value = res;
  } catch(err) {
    ElMessage.error(err as string)
  }
}
const queryParams = reactive({
  periodYearMonthStart: '',
  periodYearMonthEnd: '',
  voucherWord: '',
  voucherNumberStart: '',
  voucherNumberEnd: '',
  createUserName: '',
  subjectId: '',
  summary: '',
  auditStatus: '',
  amountStart: '',
  amountEnd: '',
  orderField: 0,
  orderType: 0
})
const auditStatusList = reactive([
  { label: '未审核', value: 0 },
  { label: '审核中', value: 1 },
  { label: '已审核', value: 2 },
  { label: '已记账', value: 3 }
]);
const tableData = reactive<TVoucherItem[]>([]);
const combineTableData = ref<TCombineVoucherItem[]>([]);
const countElement = (arr: TVoucherItem[]) => {
  const newArr = [...new Set(arr.map(item => item.voucherId))];
  return newArr.map(item => {
    return {
      keys: item,
      start: tableData.findIndex(start => start.voucherId === item),
      end: tableData.findLastIndex(start => start.voucherId === item)
    }
  })
}
const rowMergeHanlder = ( { row, column, rowIndex, columnIndex }) => {
  if (columnIndex < 3 || columnIndex > 6) {
    const activeItem = combineTableData.value.find(item => item.keys === row.voucherId);
    if (rowIndex >= activeItem!.start && rowIndex <= activeItem!.end) {
      if (rowIndex === activeItem!.start) {
        return {
          rowspan: activeItem!.end + 1 - activeItem!.start,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    }
  }
}
const onSearchHanlder = () => {
  pagination.pageNum = 1;
  pagination.pageSize = 15;
  getList();
}
const onResetHandler = () => {
  if (!queryFormRef.value) return;
  queryFormRef.value.resetFields();
  monthRange.value = [];
  queryParams.voucherNumberStart = '';
  queryParams.voucherNumberEnd = '';
  queryParams.amountStart = '';
  queryParams.amountEnd = '';
  monthRange.value = [currentPeriodYearMonth.value, currentPeriodYearMonth.value];
  queryParams.periodYearMonthStart = currentPeriodYearMonth.value;
  queryParams.periodYearMonthEnd = currentPeriodYearMonth.value;
  onSearchHanlder();
}
const getList = async () => {
  tableData.length = 0;
  try {
    loading.value = true;
    const params = {
      page: pagination.pageNum,
      limit: pagination.pageSize,
      ...queryParams
    }
    const res = await voucher.queryVoucherPage(params);
    loading.value = false;
    tableData.push(...res.records);
    total.value = +res.total;
    combineTableData.value = countElement(tableData);
  } catch(err) {
    loading.value = false;
    console.log(err)
  }
}
/* table选择 */
const multipleSelection = ref<TVoucherItem[]>([]);
const disableFlag = computed(() => {
  return multipleSelection.value.length === 0;
})
const selectionChangeHandler = (val: any) => {
  console.log(val)
  multipleSelection.value = val;
}
/* 单个删除 */
const singleRemoveHanlder = (voucherId: string) => {
  ElMessageBox.confirm('确定要删除该条数据吗?', '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    voucher.removeVoucher({id: voucherId}).then(() => {
      ElMessage.success('删除成功!');
      getList();
    })
    }).catch(() => {
      ElMessage.info('删除已取消');
    })
}
/* 批量删除 */
const batchRemoveHandler = () => {
  if (multipleSelection.value.some(item => item.auditStatus !== 0)) {
    ElMessage.warning('仅有未审核状态数据才可删除，请确认');
    return;
  }
  const multipleIds = multipleSelection.value.map(item => item.voucherId);
  ElMessageBox.confirm('确定批量删除所选数据吗?', '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    voucher.batchRemoveVoucher({ids: multipleIds}).then(() => {
      ElMessage.success('批量删除成功!');
      getList();
    })
    }).catch(() => {
      ElMessage.info('批量删除已取消');
    })
}
/* 单个更改状态 */
const singleUpdateStatusHanlder = (voucherId: string, status: number) => {
  let statusLabel = '';
  switch (status) {
    case 1:
      statusLabel = '审核';
      break;
    case 2:
      statusLabel = '记账';
      break;
    case 3:
      statusLabel = '反审核';
      break;
    case 4:
      statusLabel = '反记账';
      break;
    default:
      break;
  }
  ElMessageBox.confirm(`确定要${statusLabel}该条数据吗?`, '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const params = {
      id: voucherId,
      auditStatus: status
    }
    voucher.updateStatusVoucher(params).then(() => {
      ElMessage.success(`${statusLabel}成功!`);
      getList();
    })
    }).catch(() => {
      ElMessage.info('已取消');
    })
}
/* 新增凭证 */
const addVoucherHandler = () => {
  sessionStorage.removeItem('voucherForm');
  router.push('/finance/voucherManage/fill');
}
/* 批量修改状态 */
const batchUpdateStatusHandler = (status: number) => {
  const flagAuditStatus = multipleSelection.value[0].auditStatus;
  if (multipleSelection.value.some(item => item.auditStatus !== flagAuditStatus)) {
    ElMessage.warning('仅有同状态的数据才能进行批量操作，请确认');
    return;
  }
  const multipleIds = multipleSelection.value.map(item => item.voucherId);
  let statusLabel = '';
  switch (status) {
    case 1:
      statusLabel = '审核';
      break;
    case 2:
      statusLabel = '记账';
      break;
    case 3:
      statusLabel = '反审核';
      break;
    case 4:
      statusLabel = '反记账';
      break;
    default:
      break;
  }
  ElMessageBox.confirm(`确定${statusLabel}所选数据吗?`, '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const params = {
      ids: multipleIds,
      auditStatus: status
    }
    voucher.batchUpdateStatusVoucher(params).then(() => {
      ElMessage.success(`批量${statusLabel}成功!`);
      getList();
    })
    }).catch(() => {
      ElMessage.info('已取消');
    })
}
/* 检查整理断号 */
const invalidVoucherNumList = ref<string[]>([]); //列表上方展示
const showDialog = ref(false);
const dialogFormRef = ref<FormInstance>()
const refreshForm = reactive({
  refreshType: 0,
  voucherWord: '全部',
  periodYearMonth: ''
})
const invalidDialogVoucherNumList = ref<string[]>([]); //弹窗下方展示
const dialogElertTitle = computed(() => {
  if (invalidDialogVoucherNumList.value.length) {
    return `${refreshForm.voucherWord}共有${invalidDialogVoucherNumList.value.length}个凭证断号: ${invalidDialogVoucherNumList.value.join()}`
  } else {
    return ''
  }
})
const dialogOpenHandler = () => {
  refreshForm.periodYearMonth = currentPeriodYearMonth.value;
  checkNumVoucherHandler('dialog');
  showDialog.value = true;
}
const periodYearMonthChange = (val: string) => {
  console.log(val);
  checkNumVoucherHandler('dialog');
}
const voucherWordChange = (val: string) => {
  console.log(val);
  checkNumVoucherHandler('dialog');
}
const dialogCancelHandler = () => {
  if (!dialogFormRef.value) return;
  dialogFormRef.value.resetFields();
  invalidDialogVoucherNumList.value = [];
  showDialog.value = false;
}
const dialogConfirmHandler = () => {
  ElMessageBox.confirm(`整理后将不存在断号，但部分原凭证号会发生改变。确认按${refreshForm.refreshType ? '凭证日期' : '凭证号'}顺序重排序吗？`, '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const params = {
      ...refreshForm,
      voucherWord: refreshForm.voucherWord === '全部' ? '' : refreshForm.voucherWord
    }
    voucher.refreshNumVoucher(params).then(() => {
      ElMessage.success('整理成功!');
      dialogCancelHandler();
      getList();
      checkNumVoucherHandler('table');
    })
    }).catch((err) => {
      console.log(err);
    })
}
const checkNumVoucherHandler = async (type: 'table' | 'dialog') => {
  const params = {
    periodYearMonthStart: type === 'table' ? queryParams.periodYearMonthStart : refreshForm.periodYearMonth,
    periodYearMonthEnd: type === 'table' ? queryParams.periodYearMonthEnd : refreshForm.periodYearMonth,
    voucherWord: type === 'table' ? queryParams.voucherWord : refreshForm.voucherWord === '全部' ? '' : refreshForm.voucherWord
  }
  try {
    const res = await voucher.checkNumVoucher(params);
    if (type === 'table') {
      invalidVoucherNumList.value = res;
    } else {
      invalidDialogVoucherNumList.value = res;
    }
  } catch(e) {
    console.log(e);
  }
}
onMounted(() => {
  loadPeriodHandler();
  getVoucherTypeList();
  getSubjectList();
})
</script>
<style scoped lang='scss'>
.btn-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-alert--warning {
    width: 300px;
  }
}
.pagination-container {
  padding-top: 16px;
}
</style>
