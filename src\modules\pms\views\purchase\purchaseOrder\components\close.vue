<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" width="500px" @close="close">
        <div>
<!--            <div>确认关闭当前采购单？</div>-->
            <el-form ref="closeFromRef" :rules="rules" :model="closeFrom" label-position="top">
                    <el-form-item label="确认关闭当前采购单？" prop="shutdownReason">
                            <el-input
                                    :rows="4"
                                    type="textarea"
                                    show-word-limit
                                    v-model="closeFrom.shutdownReason"
                                    :placeholder="$t('purchaseOrder.placeholder.shutdownReason')"
                                    maxlength="100"
                                    clearable
                            />
                    </el-form-item>
            </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" :loading="submitLoading" @click="submitForm">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-drawer>
</template>
<script setup lang="ts">
    import PurchaseOrderAPI, { ClosePurchaseOrderFrom} from "@/modules/pms/api/purchaseOrder";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "关闭",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();
    const  closeFromRef= ref()
    const  submitLoading= ref(false)
    const closeFrom = reactive<ClosePurchaseOrderFrom>({
    });
    const rules = reactive({
        shutdownReason: [{ required: true, message: t("purchaseOrder.rules.shutdownReason"), trigger: "blur" },],
    });

    function close() {
        emit("update:visible", false);
        reset();
    }

    function reset() {
        closeFromRef.value.resetFields();
    }

    function submitForm() {
        closeFromRef.value.validate((valid) => {
            if (!valid) return;
            submitLoading.value = true;
            let params = {
                ...closeFrom
            };
            PurchaseOrderAPI.closePurchaseOrder(params)
                .then((res) => {
                    ElMessage.success(t("purchaseOrder.message.closePurchaseOrderSucess"));
                    close();
                    emit("onSubmit");
                })
                .finally(() => {
                    submitLoading.value = false;
                });
        })
    }

    function setFormData(data) {
        closeFrom.id = data.id;
        closeFrom.orderCode = data.orderCode;
    }

    defineExpose({
        setFormData,
    });
</script>

<style scoped lang="scss">
    .supplier-div{
        width: calc(100% - 150px);
    }
</style>
<style lang="scss">
</style>
