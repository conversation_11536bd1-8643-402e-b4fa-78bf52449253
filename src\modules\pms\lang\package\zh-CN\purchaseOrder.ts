export default {
  purchaseOrder: {
    label: {
      purchaseOrderTitle: "采购单",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      orderType: "采购类型",
      orderCode: "采购单号",
      orderSource: "单据来源",
      supplierName: "供应商",
      orderPurchaseStatus: "采购状态",
      purchaseUserName: "采购员",
      productNumber: "商品数量",
      planDeliveryDate: "计划交货日期",
      createUserName: "制单人",
      remark: "备注",
      sendStatus: "要货申请状态",
      storehouse: "仓库",
      receiveStorehouse: " 收货仓库",
      basicInformation: "基本信息",
      otherInformation: "其他信息",
      productListInformation: "商品清单",
      orderAttachmentFiles: "附件",
      productName: "商品",
      productNameCopy: "商品名称",
      productCode: "商品编码",
      productCategory: "商品分类",
      productSpecName: "规格名称",
      productImg: "商品图片",
      unitName: "采购单位",
      onHandStock: "现有库存",
      planPurchaseCount: "计划采购量",
      purchasePrice: "计划采购价",
      planPurchaseAmount: "计划采购金额",
      receivedCount: "已收数量",
      receivedAmount: "已收货金额",
      closeReason: "关闭原因",
      purchaseStatusStr: "单据状态",
      orderCodeCopy: "单号",
      createTime: "创建时间",
      receivingDocumentInformation: '收运单据',
      deliveryAttachmentFileInformation: '送达图片',
      receivingOrder: '收运单号',
      receivingTime: '收运时间',
      receivingNumber: '收货数量',
      receivingMoney: '收货金额',
      deductionAmount: '扣款金额',
      documentOperationRecordInformation: '单据操作记录',
      operationTime: '操作时间',
      operationType: '操作类型',
      operationUser: '操作人',
      total: '合计',
      receivingNumberCopy: '已收货数量',
      receivingMoneyCopy: '已收货金额',
      productType: "商品种类",
      purchaseAmount: "采购金额",

      purchaseTheme: '采购主题',
      approveStatus: '审核状态',
      totalPurchaseAmount: '商品总金额',
      discountType: '优惠方式',
      discountTypeOption: {
        1: "无优惠",
        2: "金额",
        3: "折扣",

      },
      totalDiscountedAmount:  '优惠后金额',
      contractName: '合同',
      approveTime:"审核时间",
      approveRemark: '审核意见',
      approveUserName: '审核人',
      approveReject: '审核拒绝',
      synStatus:  '同步状态',
      approve: '审核',
      paymentType:  '结算方式',
      rejectReason: "驳回原因",
      unit:"单位",
      count: "数量",
      price: "单价",
      amount: "金额",
      totalAmount: '合计金额',
      totalCount: '总数量',
      discount: '优惠',

      purchaseReturnOrder: '采购退单',
      purchaseReturnOrderNumber: '采购退货单号',
      returnRequestInitiationTime: '退货发起时间',
      totalAmountReturned: '退货总金额',

      returnGoodsDispatchNotice:"退货出库通知单",
      outboundOrderNumber: '出库通知单号',
      status: '状态',
      totalPurchaseCount:"计划采购数量",
      totalReceivedCount: "实际收货数量",
      totalPurchaseAmountCopy: "计划采购金额",
      totalDeliveryAmount: "计划收运金额",
      entryTime: "实际入库时间",
    },
    placeholder:{
        shutdownReason: "请输入关闭采购单原因",
        // keywords: "请输入商品名称/编码",
        productCode: "请输入商品编码",
        productName: "请输入商品名称",
        supplierName: "请输入供应商名称",
        productCategory: "请输入关键词搜索分类",
    },
    dateTypeList: {
        createDate: "创建时间",
        planDeliveryDate: "计划交货日期",
      auditDate: "审核日期",
    },
    orderTypeList: {
        marketDirectSupply: "市场自采",
        suppliersDirectSupply: "供应商直供",
    },
    orderSourceList: {
        purchaseTask: "采购任务",
        manuallyAddPurchaseOrder: "手动新增",
    },
    orderPurchaseStatusList: {
         wattingPurchase: "待采购",
         partialPurchase: "部分收货",
         allPurchase: "全部收货",
         closePurchase: "已关闭",
         waitReceive: "待收货",
         draft: "草稿",
    },
    approveStatusList: {
       approveDoing:"待审核",
       approveDone:"已通过",
       approveReject:"已驳回",
       approveCancel:"已撤回",
    },
    approveStatusOption: {
      0: "全部",
      1: "待审核",
      2: "已通过",
      3: "已驳回",
      4: "已撤回",
    },
    synStatusOption: {
      success: "成功",
      fail: "失败",
    },
    paymentTypeOption: {
      1: "现结",
      2: "挂账",
    },
    sendStatusList: {
         send: "已发送",
         noSend: "未发送",
    },
    shelfLifeUnitList:{
        day: "天",
        month: "月",
        year: "年",
    },
    button: {
      export: "导出采购单",
      sendPurchaseOrder: "发送采购单",
      copyPurchaseOrder: "复制",
      closePurchaseOrder: "关闭",
      addPurchaseOrder: "新增采购单",
      editPurchaseOrder: "编辑采购单",
      purchaseOrderDetail: "采购单详情",
      addProduct: "添加商品",
      saveAndSendPurchaseOrder: "确认并发送",
      reject:  "驳回",
      pass: "通过",
      manualSync: "手动同步",
      viewBtn: "查看",
      returnedBtn: "退货",
      recall: "撤回",
      saveDraft: "保存草稿",
    },
    title: {
        sendPurchaseOrder: "发送采购单",
        addProduct: "添加商品",
    },
    message: {
      selectNumTips: "已选",
      sendPurchaseOrderTips1: "共计",
      sendPurchaseOrderStatusTips: "只能选择未发送的采购单！",
      sendPurchaseOrderTips2: "单，确定发送采购单给供应商吗?",
      sendPurchaseOrderConcel: "已取消发送！",
      sendPurchaseOrderSucess: "发送成功！",
      sendPurchaseOrderFail: "发送失败！",
      closePurchaseOrderSucess: "采购单已关闭，请通知仓库",
      addOrEditPurchaseTips:'商品清单不能为空！',
      deleteSucess: "删除成功",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      saveAndSendPurchaseOrderSucess: "确认并发送成功",
      cancelTips: "退出页面将不会保存您所做的更改，您确定退出当前页面吗？",
      resetQueryPurchaseOrder: "重置后，当前页面信息将被清空，确定重置该信息？",
      resetConcel: "已取消重置！",
      syncSucess: "同步成功",

      maxSelect:  "最大勾选条数不能超过20条，请重新选择",
      inconformitySelect:  "您所勾选的{message}不符合同步条件，请重新选择",

      recallOrderTitle: "撤回采购单",
      recallOrderMsg: "确定撤回当前采购单？",
      recallOrderSuccess: "撤回成功",
      recallOrderFail: "撤回失败",
    },
    rules: {
      purchaseTheme:  "请输入采购主体",
        orderType: "请选择采购分类",
        warehouseId: "请选择仓库",
        planDeliveryDate: "请选择计划交货日期",
        purchasePersonnel: "请选择采购员",
        supplierId: "请选择供应商",
      contractName:  "请选择合同",
        shutdownReason: "请输入关闭采购单原因",

        planPurchaseCount: "请输入计划采购量",
        planPurchaseCountFomart: "支持大于0的数字，小数点后3位",
        purchasePrice: "请输入计划采购价",
        purchasePriceFomart: "请输入数字，支持小数点后4位",
        planPurchaseAmount: "请输入计划采购金额",
        planPurchaseAmountFomart: "请输入数字，支持小数点后2位",
      discountValue:  "请输入数字，小数点后2位数字",
      discountValueAmount: "优惠金额不能大于商品总金额",
      discountValuePercentage: "支持大于0的数字,小数点后2位,不能大于100",
      approveRemark: "请输入审核意见，驳回必填",

    },
  },
};
