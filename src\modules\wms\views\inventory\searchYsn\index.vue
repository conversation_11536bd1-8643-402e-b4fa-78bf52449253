<template>
  <div class="app-container">
    <div class="search-ysn">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
            <el-form-item prop="productSearch" :label="$t('searchYsn.label.product')">
              <el-input class="!w-[256px]" v-model="queryParams.productSearch" :placeholder="$t('searchYsn.placeholder.keywords')" clearable/>
            </el-form-item>
            <el-form-item prop="ysnStatusList" :label="$t('searchYsn.label.YSNStatus')">
              <el-select
                v-model="queryParams.ysnStatusList"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                filterable
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-[256px]"
              >
                <el-option v-for="(item,index) in YSNStatusList" :key="index" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('searchYsn.label.warehouseAreaCode')" prop="warehouseAreaCodeList">
              <el-select
                v-model="queryParams.warehouseAreaCodeList"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                filterable
                multiple
                collapse-tags
                collapse-tags-tooltip
                class="!w-[256px]"
              >
                <el-option v-for="item in areaList" :key="item.areaCode" :label="item.warehouseArea" :value="item.areaCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="ysnCode" :label="$t('searchYsn.label.YSNCode')">
              <el-input class="!w-[256px]" v-model="queryParams.ysnCode" :placeholder="$t('searchYsn.placeholder.YSNCodePlaceholder')" clearable/>
            </el-form-item>
            <el-form-item>
              <el-button v-hasPerm="['wms:searchYsn:search']" type="primary" @click="handleQuery">
                {{$t('common.search')}}
              </el-button>
              <el-button v-hasPerm="['wms:searchYsn:reset']" @click="handleResetQuery">
                {{$t('common.reset')}}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="content-card">
        <el-table v-loading="loading" :data="tableData" highlight-current-row stripe>
          <template #empty>
            <Empty/>
          </template>
          <el-table-column fixed="left" type="index" :label="$t('common.sort')" width="60" />
          <el-table-column :label="$t('searchYsn.label.YSNCode')" prop="ysnCode" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('searchYsn.label.productCode')" prop="productCode" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('searchYsn.label.productName')" prop="productName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('searchYsn.label.actualWeight')" prop="actualWeight" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('searchYsn.label.originalWeight')" prop="originalWeight" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('searchYsn.label.YSNStatus')" prop="ysnStatusDesc" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('searchYsn.label.warehouseAreaName')" prop="warehouseAreaName" show-overflow-tooltip></el-table-column>
        </el-table>
        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.limit"
            @pagination="handleQuery"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { emitter } from "@/core/utils/eventBus";
  import {useRouter} from "vue-router";
  import SearchYsnApi,{queryPageDto,queryPageResponse} from "@/modules/wms/api/searchYsn";
  import CommonAPI from "@/modules/wms/api/common";
  defineOptions({
    name: "SearchYsn",
    inheritAttrs: false,
  });
  const router = useRouter();
  const { t } = useI18n();
  const queryFormRef = ref(null);
  const loading = ref(false);
  const tableData = ref<queryPageResponse[]>();
  const total = ref(0);
  const areaList = ref([]);
  const YSNStatusList = ref([]);
  const queryParams = reactive<queryPageDto>({
    page: 1,
    limit: 20,
  });

  /** 查询当前仓库的库区下拉数据源 */
  function getOutWarehouseAreaList() {
    CommonAPI.getOutWarehouseAreaList().then((data: any) => {
      areaList.value = data;
      if(areaList.value && areaList.value.length>0){
        areaList.value.map((item)=>{
          item.warehouseArea = item.areaName + '|' +item.areaCode
          return item
        });
      }
    })
  }
  /** 查询商品库存列表 */
  function handleQuery(){
    loading.value = true;
    let params = {
      ...queryParams,
    }
    SearchYsnApi.queryPage(params).then((data) =>{
      tableData.value = data.records;
      total.value = parseInt(data.total);
    }).finally(() => {
      loading.value = false;
    })
  }
  /** 重置查询 */
  function handleResetQuery() {
    queryFormRef.value.resetFields();
    queryParams.page = 1;
    queryParams.limit = 20;
    handleQuery();
  }
  function getYsnStatusEnumList(){
    SearchYsnApi.getYsnStatusEnumList().then((res) => {
      YSNStatusList.value = res
    }).finally(() => {

    })
  }
  onActivated(() => {
    getOutWarehouseAreaList();
    getYsnStatusEnumList();
    handleQuery();
  });
  emitter.on("reloadListByWarehouseId", (e) => {
    handleQuery();
  });
</script>
<style lang="scss" scoped>
  :deep(.el-button--primary.el-button--default.is-link) {
    color: #762adb;
  }
  :deep(.el-button--danger.el-button--default.is-link) {
    color: #c00c1d;
  }
  .search-ysn{
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }
    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
</style>

