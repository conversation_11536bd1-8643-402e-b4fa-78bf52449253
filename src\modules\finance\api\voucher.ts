import request from "@/core/utils/request";
import type { TVoucherTypeItem, TSummaryItem, TSubjectItem, TVoucherItem, TPaginationList, TVoucherDetail } from "@/modules/finance/types/voucher";
class API {
  static queryVoucherTypeList(data:any) { //获取凭证字列表
    return request<any, TVoucherTypeItem[]>({
      url: '/supply-finance-server/voucher/nextNum',
      method: "post",
      data,
    });
  }
  static querySummaryList(data:any) { //获取摘要列表
    return request<any, TSummaryItem[]>({
      url: '/supply-finance-server/voucherSummary/queryCommonSummaryList',
      method: "post",
      data,
    });
  }
  static querySubjectList(data:any) { //获取科目列表
    return request<any, TSubjectItem[]>({
      url: '/supply-finance-server/subject/getList',
      method: "post",
      data,
    });
  }
  static queryCashFlowList(data:any) { //获取现金流项目
    return request<any, any>({
      url: '/supply-finance-server/baseDict/getCashItem',
      method: "post",
      data,
    });
  }
  static querySubjectBalance(data:any) { //获取科目余额
    return request<any, string>({
      url: '/supply-finance-server/voucher/querySubjectBalance',
      method: "post",
      data,
    });
  }
  static saveVoucher(data:any) { //凭证保存
    return request<any, any>({
      url: '/supply-finance-server/voucher/save',
      method: "post",
      data,
    });
  }
  static queryVoucherPage(data:any) { //获取凭证列表
    return request<any, TPaginationList<TVoucherItem>>({
      url: '/supply-finance-server/voucher/page',
      method: "post",
      data,
    });
  }
  static removeVoucher(data:any) { //删除凭证
    return request<any, any>({
      url: '/supply-finance-server/voucher/remove',
      method: "post",
      data,
    });
  }
  static batchRemoveVoucher(data:any) { //批量删除凭证
    return request<any, any>({
      url: '/supply-finance-server/voucher/batchRemove',
      method: "post",
      data,
    });
  }
  static updateStatusVoucher(data:any) { //更改凭证状态
    return request<any, any>({
      url: '/supply-finance-server/voucher/updateStatus',
      method: "post",
      data,
    });
  }
  static queryVoucherDetail(data:any) { //查询凭证详情
    return request<any, TVoucherDetail>({
      url: '/supply-finance-server/voucher/detail',
      method: "post",
      data,
    });
  }
  static batchUpdateStatusVoucher(data:any) { //批量更改凭证状态
    return request<any, any>({
      url: '/supply-finance-server/voucher/batchUpdateStatus',
      method: "post",
      data,
    });
  }
  static updateStatusVoucherByDate(data:any) { //根据范围更改凭证状态
    return request<any, any>({
      url: '/supply-finance-server/voucher/scopeUpdateStatus',
      method: "post",
      data,
    });
  }
  static checkNumVoucher(data:any) { //检查断号
    return request<any, string[]>({
      url: '/supply-finance-server/voucher/checkNum',
      method: "post",
      data,
    });
  }
  static refreshNumVoucher(data:any) { //整理断号
    return request<any, any>({
      url: '/supply-finance-server/voucher/refreshNum',
      method: "post",
      data,
    });
  }
  static querySettleDetail(data:any) { //期末结转详情
    return request<any, any>({
      url: '/supply-finance-server/period/settle/detail',
      method: "post",
      data,
    });
  }
  static settleCreateVoucher(data:any) { //期末结转生成凭证
    return request<any, TVoucherDetail>({
      url: '/supply-finance-server/period/settle/create',
      method: "post",
      data,
    });
  }
  static settleViewVoucherList(data:any) { //期末结转查看凭证
    return request<any, any>({
      url: '/supply-finance-server/period/settle/list',
      method: "post",
      data,
    });
  }
  static settleVoucherCheck(data:any) { // 结账检查
    return request<any, any>({
      url: '/supply-finance-server/period/settle/check',
      method: "post",
      data,
    });
  }
  static settleVoucher(data:any) { // 结账与反结账
    return request<any, any>({
      url: '/supply-finance-server/period/settle',
      method: "post",
      data,
    });
  }
  static settleVoucherAudit(data:any) { // 一键记账与审核
    return request<any, any>({
      url: '/supply-finance-server/period/settle/audit',
      method: "post",
      data,
    });
  }
  static queryFinalTrialBalance(data:any) { // 期末试算平衡
    return request<any, any>({
      url: '/supply-finance-server/period/finalTrialBalance',
      method: "post",
      data,
    });
  }
}

export default API;

