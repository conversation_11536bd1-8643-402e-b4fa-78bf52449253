import { defineMock } from "./base";

export default defineMock([
  {
    url: "roles/options",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          value: 2,
          label: "系统管理员",
        },
        {
          value: 4,
          label: "系统管理员1",
        },
        {
          value: 5,
          label: "系统管理员2",
        },
        {
          value: 6,
          label: "系统管理员3",
        },
        {
          value: 7,
          label: "系统管理员4",
        },
        {
          value: 8,
          label: "系统管理员5",
        },
        {
          value: 9,
          label: "系统管理员6",
        },
        {
          value: 10,
          label: "系统管理员7",
        },
        {
          value: 11,
          label: "系统管理员8",
        },
        {
          value: 12,
          label: "系统管理员9",
        },
        {
          value: 3,
          label: "访问游客",
        },
      ],
      msg: "一切ok",
    },
  },

  {
    url: "role/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
          {
            roleId: '1',
            roleName: "系统管理员",
            userCount: 5,
            roleDesc: "角色描述1",
            status: 1,
            createTime: "2021-03-25 12:39:54",
          },
          {
            roleId: '2',
            roleName: "访问游客",
            userCount: 2,
            roleDesc: "角色描述2",
            status: 0,
            createTime: "2021-05-26 15:49:05",
          },
          {
            roleId: '3',
            roleName: "系统管理员3",
            userCount: 3,
            roleDesc: "角色描述3",
            status: 0,
            createTime: "2021-03-25 12:39:54",
          },
          {
            roleId: '4',
            roleName: "系统管理员4",
            userCount: 4,
            roleDesc: "角色描述4",
            status: 0,
            createTime: "2021-03-25 12:39:54",
          },
          {
              roleId: '5',
              roleName: "系统管理员",
              userCount: 5,
              roleDesc: "角色描述5",
              status: 1,
              createTime: "2021-03-25 12:39:54",
          },
          {
              roleId: '6',
              roleName: "访问游客",
              userCount:6,
              roleDesc: "角色描述6",
              status: 0,
              createTime: "2021-05-26 15:49:05",
          },
          {
              roleId: '7',
              roleName: "系统管理员7",
              userCount: 7,
              roleDesc: "角色描述7",
              status: 0,
              createTime: "2021-03-25 12:39:54",
          },
          {
              roleId: '8',
              roleName: "系统管理员8",
              userCount: 8,
              roleDesc: "角色描述8",
              status: 0,
              createTime: "2021-03-25 12:39:54",
          },
          {
              roleId: '9',
              roleName: "系统管理员9",
              userCount: 9,
              roleDesc: "角色描述9",
              status: 0,
              createTime: "2021-03-25 12:39:54",
          },
          {
              roleId: '10',
              roleName: "系统管理员10",
              userCount: 10,
              roleDesc: "角色描述8",
              status: 0,
              createTime: "2021-03-25 12:39:54",
          },
          {
              roleId: '11',
              roleName: "系统管理员11",
              userCount: 11,
              roleDesc: "角色描述11",
              status: 0,
              createTime: "2021-03-25 12:39:54",
          },
        ],
        total: 11,
      },
      msg: "一切ok",
    },
  },

  // 新增角色
  {
    url: "role/add",
    method: ["POST"],
    body: {
        code: 0,
        data: null,
        msg: "新增角色成功",
    },
  },

  // 编辑角色
  {
      url: "role/update",
      method: ["POST"],
      body: {
          code: 0,
          data: null,
          msg: "修改角色成功",
      },
  },

    // 修改角色状态
    {
        url: "role/updateEnableStatus",
        method: ["POST"],
        body: {
            code: 0,
            data: null,
            msg: "修改角色状态成功",
        },
    },


  // 删除角色
  {
    url: "role/delete",
    method: ["GET"],
    body:{
        code: 0,
        data: null,
        msg: "删除角色成功",
    },
  },

  // 获取角色权限
  {
    url: "role/authorityInfo",
    method: ["POST"],
    body: {
        code: 0,
        data: [
            {
                roleAuthorityInfo: {
                    authorityIds: ['001','0021'],
                    treeList: [
                        {
                            authorityId: '001',
                            authorityName: "系统设置",
                            children: [
                                {
                                    authorityId: "0011",
                                    authorityName: "账号管理",
                                },
                                {
                                    authorityId:'002',
                                    authorityName:'供应商管理',
                                    children:[
                                        {
                                            authorityId:'0021',
                                            authorityName:'供应商',
                                        },
                                    ]
                                },
                            ],

                        }
                    ]
                },
                systemName: "采购",
                systemType: "1"
            },
            {
                roleAuthorityInfo: {
                    authorityIds: ['0012'],
                    treeList: [
                        {
                            authorityId: '001',
                            authorityName: "系统设置",
                            children: [
                                {
                                    authorityId:'0011',
                                    authorityName:'账号管理',
                                },
                                {
                                    authorityId:'0012',
                                    authorityName:'角色管理',
                                },
                            ],

                        }
                    ]
                },
                systemName: "销售",
                systemType: "2"
            },
            {
                roleAuthorityInfo: {
                    authorityIds: ['003','0041'],
                    treeList: [
                        {
                            authorityId: '003',
                            authorityName: "仓储管理",
                            children: [
                                {
                                    authorityId:'0031',
                                    authorityName:'仓储管理',
                                },
                                {
                                    authorityId:'0032',
                                    authorityName:'仓储设置',
                                },
                            ],
                        },
                        {
                            authorityId: '004',
                            authorityName: "仓库配置",
                            children: [
                                {
                                    authorityId:'0041',
                                    authorityName:'仓库配置',
                                },
                            ],
                        }
                    ]
                },
                systemName: "仓储",
                systemType: "3"
            },
        ],
        msg: "一切ok",
    },
  },

]);

// 角色映射表数据
const roleMap: Record<string, any> = {
  2: {
    id: 2,
    name: "系统管理员",
    code: "ADMIN",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null,
  },
  3: {
    id: 3,
    name: "访问游客",
    code: "GUEST",
    status: 1,
    sort: 3,
    createTime: "2021-05-26 15:49:05",
    updateTime: "2019-05-05 16:00:00",
  },
  4: {
    id: 4,
    name: "系统管理员1",
    code: "ADMIN1",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null,
  },
  5: {
    id: 5,
    name: "系统管理员2",
    code: "ADMIN2",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null,
  },

  6: {
    id: 6,
    name: "系统管理员3",
    code: "ADMIN3",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null,
  },
  7: {
    id: 7,
    name: "系统管理员4",
    code: "ADMIN4",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null,
  },
  8: {
    id: 8,
    name: "系统管理员5",
    code: "ADMIN5",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null,
  },
  9: {
    id: 9,
    name: "系统管理员6",
    code: "ADMIN6",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: "2023-12-04 11:43:15",
  },
  10: {
    id: 10,
    name: "系统管理员7",
    code: "ADMIN7",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null,
  },
  11: {
    id: 11,
    name: "系统管理员8",
    code: "ADMIN8",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null,
  },
};
