<template>
  <span class="encryptBox">
    <template v-if="!props.nameType">
      <slot name="pre"></slot>
      {{ mobilePhoneShow ? phone : encryptPhone(phone) }}
      <el-icon
        v-if="props.phone"
        @click="mobilePhoneShow = !mobilePhoneShow"
        class="encryptBox-icon"
        color="#762ADB "
        size="16"
      >
        <component v-if="!nameShow" :is="nameShow ? 'Hide' : 'View'" />
      </el-icon>
    </template>
    <template v-else>
      <slot name="pre"></slot>
      <!-- {{ nameShow ? name : encryptName(name) }} -->
      {{ shouldEncryptName ? (nameShow ? name : encryptName(name)) : name }}
      <el-icon
        v-if="shouldShowNameIcon"
        @click="nameShow = true"
        class="encryptBox-icon"
        color="#762ADB "
        size="16"
      >
        <component v-if="!nameShow" :is="nameShow ? 'Hide' : 'View'" />
      </el-icon>
    </template>
  </span>
</template>
<script setup>
import { encryptPhone, encryptName } from "../../utils/index";
let props = defineProps({
  phone: {
    type: [String, Number],
  },
  name: {
    type: [String, Number],
  },
  nameType: {
    type: Boolean,
    default: false,
  },
});

let mobilePhoneShow = ref(false);
let nameShow = ref(false);
// 计算属性：是否需要加密姓名（长度>1且非空）
const shouldEncryptName = computed(() => {
  return props.name && String(props.name).length > 1;
});

// 计算属性：是否显示姓名切换图标
const shouldShowNameIcon = computed(() => {
  return props.name && shouldEncryptName.value;
});
</script>
<style lang="scss" scoped>
.encryptBox {
  // display: inline-flex;
  // justify-content: space-between;
  // align-items: center;
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  // align-self: flex-start;
  vertical-align: text-top;
}
</style>
