import request from "@/core/utils/request";

const USER_BASE_URL = "/supply-base";

class supplierAPI {
  /**
   * 获取分页列表
   *
   * @param queryParams 查询参数
   */
  static getPage(queryParams: any) {
    return request({
      // url: `/supply-pms/supplier/page`,
      url: `/supply-pms/supplier/selectPage`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取分页列表
   *
   * @param queryParams 查询参数
   */
  static getPageList(queryParams: any) {
    return request({
      url: `/supply-pms/supplier/page`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取下拉列表
   *
   * @param data 查询参数
   */
  static getsupplierList(data={}) {
    return request({
      url: `/supply-pms/supplier/select`,
      method: "post",
      data,
    });
  }

  /** 详情查询 */
  static async querySupplierDetail(data) {
    const res = await request({
      url: `/supply-pms/supplier/detail`,
      method: "post",
      data: data,
    });

    if (res && typeof res.attachment === "string") {
      res.attachment = JSON.parse(res.attachment);
    }
    if (res && typeof res.businessLicenseUrl === "string") {
      res.businessLicenseUrl = JSON.parse(res.businessLicenseUrl);
    }
    return res;
  }

  /**
   * 添加
   *
   * @param data 用户表单数据
   */
  static add(data: any) {
    return request({
      url: `/supply-pms/supplier/save`,
      method: "post",
      data: data,
    });
  }

  /**
   * 修改
   *
   * @param id 用户ID
   * @param data 用户表单数据
   */
  static update(data: any) {
    return request({
      url: `/supply-pms/supplier/update`,
      method: "post",
      data: data,
    });
  }

  /**
   * 修改状态
   *
   */
  static updateEnableStatus(data: any) {
    return request({
      url: `/supply-pms/supplier/updateEnableStatus`,
      method: "post",
      data: data,
    });
  }

  /**
   * 删除
   *
   */
  static deleteByIds(data: any) {
    return request({
      url: `/supply-pms/supplier/delete`,
      method: "post",
      data: data,
    });
  }

  /**
   * 获取国际国家列表-含港澳台
   *
   */
  static getAllCountry() {
    return request({
      url: `/supply-base/country/all`,
      method: "get",
    });
  }

  /**
   * 获取国家下城市数据列表
   *
   */
  static getAllCity(params: any) {
    return request({
      url: `/supply-base/area/queryAreaListByPid`,
      method: "get",
      params: params,
    });
  }

  /**
   * 懒加载商品分类下拉列表
   *
   */
  static getManagerCategoryList(data: any) {
    return request({
      url: `/supply-pms/product/category/queryManagerCategoryList`,
      method: "post",
      data: data,
    });
  }

  /** 获取供应商下拉数据源 */
  static getSupplierList(data={}) {
    return request({
      url: `/supply-pms/supplier/select`,
      method: "post",
      data,
    });
  }

    /** 获取供应商下拉数据源(采购商品翻页查询使用) */
    static getSupplierListAll() {
        return request({
            url: `/supply-pms/supplier/query/select`,
            method: "post",
            data: {},
        });
    }

  /**
   * 仓库分页查询
   *
   */
  static getWarehouse(params: any) {
    return request({
      url: `/supply-pms/warehouse/selectPage`,
      method: "post",
      data: params,
    });
  }

  /**
   * 仓库电话查询
   *
   */
  static queryWarehouseRealPhone(data: any) {
    return request({
      url: `/supply-pms/warehouse/queryWarehousePhone`,
      method: "post",
      data: data,
    });
  }

  /**
   * 合同分页查询
   *
   */
  static getContract(params: any) {
    return request({
      url: `/supply-pms/contract/selectPage`,
      method: "post",
      data: params,
    });
  }

  /** 根据供应商id查询采购员信息 */
  static queryPurchaseUserList(data?: any) {
    return request({
      url: `/supply-pms/supplier/getPurchaseUserBySupId`,
      method: "post",
      data: data,
    });
  }

  /** 小眼睛查电话号码 */
  static queryRealPhone(data?: any) {
    return request({
      url: `/supply-pms/supplier/querySupplierPhone`,
      method: "post",
      data: data,
    });
  }
}

export default supplierAPI;

/** 登录用户信息 */
export interface UserInfo {
  /** 用户ID */
  userId?: number;

  /** 用户名 */
  username?: string;

  /** 昵称 */
  nickname?: string;

  /** 头像URL */
  avatar?: string;

  /** 角色 */
  roles: string[];

  /** 权限 */
  perms: string[];
}

/**
 * 用户分页查询对象
 */
export interface PageQuery {
  supplierName?: string;
  enableStatus?: number;
  page?: number;
  limit?: number;
  mobile?: number;
}

/** 用户分页对象 */
export interface SupplierPageVO {
  /** 用户头像URL */
  avatar?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 部门名称 */
  deptName?: string;
  /** 用户邮箱 */
  email?: string;
  /** 性别 */
  genderLabel?: string;
  /** 用户ID */
  id?: number;
  /** 手机号 */
  mobile?: string;
  /** 用户昵称 */
  nickname?: string;
  /** 角色名称，多个使用英文逗号(,)分割 */
  roleNames?: string;
  /** 用户状态(1:启用;0:禁用) */
  status?: number;
  /** 用户名 */
  username?: string;
}

/** 用户表单类型 */
export interface SupplierForm {
  id?: string;
  userId?: string;
  supplierName?: string;
  supplierTypeCode?: string;
  countryName?: string;
  countryCode?: string;
  cityCode?: string;
  cityName?: string;
  provinceCode?: string;
  provinceName?: string;
  districtCode?: string;
  districtName?: string;
  address?: string;
  supplierShortName?: string;
  purchaserId?: string;
  legalPerson?: string;
  countryAreaCode?: string;
  mobile?: string;
  payeeName?: string;
  payeeAccount?: string;
  payeeBank?: string;
  companyName?: string;
  companyAddress?: string;
  creditCode?: string;
  financialContact?: string;
  ticketTypeCode?: string;
  settlementCurrency?: string;
  businessLicenseUrl?: any;
  attachment?: any;
  enableSettlement?: number;
  loginAccountName?: string;
  loginAccountPassword?: string;
  fullAddress?: string;
  areaInfo?: any;
  categoryList?: any;
  warehouseList?: any;
  contractIds?: any;
  contractList?: any;
  warehouseIds?: any;
  settlementPeriodType?: number;
  settlementPeriod?: number;
  settlementDay?: number;
  thirdCategoryIds?: any;
  attachmentName?: string;
}
