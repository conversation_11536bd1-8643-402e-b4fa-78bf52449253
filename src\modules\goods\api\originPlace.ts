import request from "@/core/utils/request";

const ORIGIN_PLACE_BASE_URL = "/supply-biz-common/originPlace";

class OriginPlaceAPI {
  /**
   * 分页查询原产地
   * @param queryParams 查询参数
   * @returns 分页结果
   */
  static getPageList(queryParams?: OriginPlacePageQuery) {
    return request<any, OriginPlacePageResult>({
      url: `${ORIGIN_PLACE_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取原产地详情
   * @param id 原产地ID
   * @returns 原产地详情
   */
  static getDetail(id: number) {
    return request<any, OriginPlaceVO>({
      url: `${ORIGIN_PLACE_BASE_URL}/detail`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 新增原产地
   * @param data 原产地表单数据
   * @returns 操作结果
   */
  static add(data: OriginPlaceForm) {
    return request<any, string>({
      url: `${ORIGIN_PLACE_BASE_URL}/save`,
      method: "post",
      data,
    });
  }

  /**
   * 编辑原产地
   * @param data 原产地表单数据（包含ID）
   * @returns 操作结果
   */
  static update(data: OriginPlaceForm) {
    return request<any, string>({
      url: `${ORIGIN_PLACE_BASE_URL}/update`,
      method: "post",
      data,
    });
  }

  /**
   * 删除原产地
   * @param id 原产地ID
   * @returns 操作结果
   */
  static deleteById(id: number) {
    return request<any, string>({
      url: `${ORIGIN_PLACE_BASE_URL}/delete`,
      method: "post",
      data: { id },
    });
  }

  /**
   * 批量启用原产地
   * @param ids ID数组
   * @returns 操作结果
   */
  static enableBatch(ids: number[]) {
    return request<any, string>({
      url: `${ORIGIN_PLACE_BASE_URL}/enableStatusBatch`,
      method: "post",
      data: ids,
    });
  }

  /**
   * 批量禁用原产地
   * @param ids ID数组
   * @returns 操作结果
   */
  static disableBatch(ids: number[]) {
    return request<any, string>({
      url: `${ORIGIN_PLACE_BASE_URL}/disableStatusBatch`,
      method: "post",
      data: ids,
    });
  }
}

/**
 * 原产地分页查询参数
 */
export interface OriginPlacePageQuery {
  /** 当前页 */
  page?: number;
  /** 每页条数（最大1000） */
  limit?: number;
  /** 主键ID */
  id?: number;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 配送方式表单数据
 */
export interface OriginPlaceForm {
  /** 主键ID（编辑时必填） */
  id?: number;
  /*产地名称*/
  originPlaceName: string;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
  /*排序*/
  sort: number;
}

/**
 * 配送方式视图对象
 */
export interface OriginPlaceVO {
  /** 组织编码 */
  orgCode?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 创建用户ID */
  createUser?: number;
  /** 更新用户ID */
  updateUser?: number;
  /** 主键ID */
  id?: number;
  /** 分类ID，配送方式所属的分类id */
  deliveryMethodsCategoryId?: number;
  /** 配送方式名称 */
  methodName?: string;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
  /** 配送方式分类信息 */
  deliveryMethodsCategoryVO?: any;
}

/**
 * 配送方式分页结果对象
 */
export interface OriginPlacePageResult {
  /** 当前页 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 数据列表 */
  records: OriginPlaceVO[];
}

/**
 * 启禁用状态枚举
 */
export enum EnableStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
}

/** 分页响应参数 */
export interface OriginPlaceResp {
  /** ID */
  id?: number;
  /** 支付方式 */
  methodName?: string;
  /** 状态 */
  enableStatus?: number;
  /** 更新时间 */
  updateTime?: string;
}

export default OriginPlaceAPI;
