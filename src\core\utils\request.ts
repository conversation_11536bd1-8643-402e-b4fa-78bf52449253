import axios, { InternalAxiosRequestConfig, AxiosResponse } from "axios";
// declare module 'axios' {
//   export interface AxiosRequestConfig {
//     /**
//      * 自定义字段 - 图片字段过滤
//      */
//     filterImgFields?: string[];
//   }
// }
import { useUserStoreHook } from "@/core/store/modules/user";
import { ResultEnum } from "@/core/enums/ResultEnum";
import { TOKEN_KEY } from "@/core/enums/CacheEnum";
import qs from "qs";
import router from "@/core/router";
import {getTimeZone, parseLanguage} from "@/core/utils";
import { filterImg } from "@/core/utils/filterImg";
// 创建 axios 实例
const {language, region } = parseLanguage();
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 50000,
  headers: {
    "Content-Type": "application/json;charset=utf-8",
    "APP_ID": import.meta.env.VITE_APP_ID,
    "X-Time-Zone": getTimeZone(),
    "Accept-Language": `${language}-${region}`,
  },

  paramsSerializer: (params) => {
    return qs.stringify(params);
  },
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const accessToken = localStorage.getItem(TOKEN_KEY);
    if (accessToken) {
      config.headers.Authorization = accessToken;
    }
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 检查配置的响应类型是否为二进制类型（'blob' 或 'arraybuffer'）, 如果是，直接返回响应对象
    if (
      response.config.responseType === "blob" ||
      response.config.responseType === "arraybuffer"
    ) {
      return response;
    }

    const { code, data, message: msg } = response.data;
    if (code === ResultEnum.SUCCESS) {
      // // 过滤空值图片，赋值默认图片
      // if (response.config?.filterImgFields && response.config?.filterImgFields?.length > 0){
      //   return filterImg(data, response.config.filterImgFields);
      // }else {
        return data;
      // }
    }
    ElMessage.error(msg || "系统出错");
    return Promise.reject(new Error(msg || "Error"));
  },
  (error: any) => {
    // 异常处理
    if (error.response.data) {
      const { code, message } = error.response.data;
      if (code === ResultEnum.TOKEN_INVALID) {
        ElNotification({
          title: "提示",
          message: "您的会话已过期，请重新登录",
          type: "info",
        });
        useUserStoreHook()
          .resetToken()
          .then(() => {
            router.push({
              path: '/login'
            })
          });
      }
      else if (error.status === ResultEnum.FORBIDDEN || error.status === ResultEnum.SERVICE_UNAVAILABLE) {
        ElMessage.error(message || "您没有权限访问");
      }

      else {
        ElMessage.error(message || "系统出错");
      }
    }
    return Promise.reject(error.message);
  }
);

// 导出 axios 实例
export default service;
