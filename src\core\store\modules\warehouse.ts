import { defineStore } from "pinia";

interface WarehouseState {
  selectedWarehouseId: string | null;
  selectedWarehouseName: string | null;
}

export const useWarehouseStore = defineStore("warehouse", {
  state: (): WarehouseState => ({
    selectedWarehouseId: null,
    selectedWarehouseName: null,
  }),

  getters: {
    getSelectedWarehouseId: (state) => state.selectedWarehouseId,
    getSelectedWarehouseName: (state) => state.selectedWarehouseName,
  },

  actions: {
    setSelectedWarehouse(warehouseId: string, warehouseName?: string) {
      this.selectedWarehouseId = warehouseId;
      this.selectedWarehouseName = warehouseName || null;
    },

    clearSelectedWarehouse() {
      this.selectedWarehouseId = null;
      this.selectedWarehouseName = null;
    },
  },
});
