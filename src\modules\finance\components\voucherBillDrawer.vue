<template>
  <el-drawer v-model="showDrawer" :title="drawerType === 'detail' ? '凭证详情' : '修改凭证'" size="80%" :close-on-click-modal="false">
    <voucher-bill :initData="voucherInit" :key="voucherInit.id" :type="drawerType" ref="voucherRef" :loading="drawLoadind">
      <template #user>
        <div style="margin-right: 32px" v-for="(opt, index) in operateLogs" :key="index">{{ opt.operateType === 0 ? '制单人' : opt.operateType === 1 ? '审核人' : '记账人' }}: {{ opt.createUserName }}</div>
      </template>
      <template #btn>
        <el-button @click="closeDrawer">关闭</el-button>
        <el-button type="primary" @click="drawerSaveHandler" v-if="drawerType === 'edit'">保存</el-button>
      </template>
    </voucher-bill>
  </el-drawer>
</template>

<script lang="ts" setup>
import { voucher } from '@/modules/finance/api/index';
import voucherbill from '@/modules/finance/components/voucherBill.vue';
import type { TVoucherForm, TVoucherDetailLogsItem, TVoucherDetailFlowItem, TVoucherFormBalanceItem } from '@/modules/finance/types/voucher';
const drawLoadind = ref(false);
const drawerType = ref('');
const loading = ref(false);
const operateLogs = ref<TVoucherDetailLogsItem[]>([]);
const voucherInit = reactive<TVoucherForm>({
  id: '',
  auditStatus: '',
  voucherWord: '',
  voucherNumber: 0,
  voucherDate: '',
  remark: '',
  attachment: [],
  balanceFlows: [],
});
const props = defineProps({
  visible: Boolean,
});
const emit = defineEmits(['update:visible', 'save']);
const showDrawer = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});
const tanslateStringToArr = (value: string) => {
  // 将数字字符串转换成对应位置的数组
  if (value === '' || value === '0' || value === null) return ['', '', '', '', '', '', '', '', '', '', '', '', '', ''];
  if (!value.includes('.')) {
    value = value + '.00';
  } else {
    const index = value.indexOf('.');
    const len = value.slice(index).length;
    value = len === 2 ? value + '0' : value;
  }
  const formateValue = value.trim().replace(/[-.]/g, '');
  const resultArr = formateValue.split('');
  while (resultArr.length < 14) {
    resultArr.unshift('');
  }
  return resultArr;
};
const initBalanceFlowsHandler = (balanceFlow: TVoucherDetailFlowItem[]): TVoucherFormBalanceItem[] => {
  return balanceFlow.map((item) => {
    return {
      summary: item.summary,
      showSummaryInput: false,
      subjectId: item.subjectId,
      equivalentFlag: item.equivalentFlag,
      subjectCode: item.subjectCode,
      subjectName: item.subjectFullName,
      debitAmount: item.debitAmount || '',
      debitAmountArr: tanslateStringToArr(item.debitAmount),
      showDebitInput: false,
      creditAmount: item.creditAmount || '',
      creditAmountArr: tanslateStringToArr(item.creditAmount),
      showCreditInput: false,
      auxiliaryName: item.auxiliaryName,
      initBalance: item.subjectBalanceDirection === '借' ? Number(item.currentBalanceAmount)  - Number(item.debitAmount) + Number(item.creditAmount) : Number(item.currentBalanceAmount)  - Number(item.creditAmount) + Number(item.debitAmount),
      finalBalance: +item.currentBalanceAmount,
      subjectBalanceDirection: item.subjectBalanceDirection,
    };
  });
};
const openDrawer = async (type: string, voucherId: string) => {
  loading.value = true;
  drawerType.value = type;
  try {
    const params = { id: voucherId };
    const res = await voucher.queryVoucherDetail(params);
    operateLogs.value = res.operateLogs;
    for (let k in voucherInit) {
      if (k === 'balanceFlows') {
        voucherInit.balanceFlows = initBalanceFlowsHandler(res.balanceFlows);
        while ( type === 'edit' && voucherInit.balanceFlows.length < 4) {
          voucherInit.balanceFlows.push({
            summary: '',
            showSummaryInput: false,
            subjectId: '',
            equivalentFlag: 0,
            subjectCode: '',
            subjectName: '',
            debitAmount: '',
            debitAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
            showDebitInput: false,
            creditAmount: '',
            creditAmountArr: ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
            showCreditInput: false,
            auxiliaryName: '',
            initBalance: '',
            finalBalance: 0,
            subjectBalanceDirection: ''
          })
        }
      } else if (k === 'voucherDate') {
        voucherInit.voucherDate = String(res.voucherDate);
      } else if (k === 'attachment') {
        voucherInit.attachment = res.attachment?.length ? JSON.parse(res.attachment) : [];
      } else {
        voucherInit[k] = res[k];
      }
    }
    loading.value = false;
    showDrawer.value = true;
  } catch (e) {
    loading.value = false;
    console.log(e);
  }
};
const closeDrawer =() => {
  showDrawer.value = false;
};
defineExpose({
  openDrawer
});
const drawerSaveHandler = async () => {
  drawLoadind.value = true;
};
onMounted(() => {});
</script>

<style scoped lang="scss"></style>
