
export default {
  WMSPickingOrder: {
    label: {
      sort: "序号",
      pickingListCode: "拣货单号",
      pickOrderCode: '提货单号',
      deliveryNoticeCode: "出库通知单",
      billOfLadingCode: "提货单",
      status: "状态",
      printStatus: "是否打印",
      warehouseName: "仓库名称",
      contact: "联系人",
      contactNumber: "联系电话",
      remark: "备注",
      operate: "操作",
      ownWarehouse: "所属仓库",
      contactPerson: "库区联系人",
      mobile: "联系人电话",
      countryAreaCode: "区号",
      contactLandline: "固定电话",
      areaType: "库区类型",
      conventionalStorageArea: "常规库区",
      defectiveProductStorageArea: "残次品库区",
      is: "是",
      not: "否",
      pickTime: "拣货时间",
      createTime: "创建时间",
      startTime: "开始时间",
      endTime: "结束时间",
      rangeSeparator: "至",
      productCount: "商品个数",
      requestedDeliveryTime:"要求到货时间",
      "actualPicker": "实际拣货人",
      "actualPickingTime": "实际拣货时间",
      "printStatusStr": "是否已打印",
      "printCount": "打印次数",
      "pickingType": "拣货方式",
      "pickingTypeSingle": "按单拣货",
      "pickingType.default": "--",
      "createUserName": "创建人",
      // "createTime": "创建时间",
      "statusStr": "状态",
      "operation": "操作",
      pickByOrder: "按单拣货",
      "pickingList": "拣货单",
    /*  "pickingListCode": "拣货单号",
      "pickingType": "拣货方式",
      "pickingType.single": "按单拣货",
      "pickingType.default": "--",*/
      "createUser": "创建人",
      // "createTime": "创建时间",
      "plannedReceivedTime": "要求到货时间",
      // "deliveryNoticeCode": "出库通知单",
      // "billOfLadingCode": "提货单号",
      "productInfo": "商品信息",
      productDetail: "商品明细",
      "productCode": "商品编码",
      "productSpecs": "规格",
      "totalPlanedPick": "计划拣货总数量",
      actualPickCount: "实际拣货总量",
      "warehouseArea": "拣货库区",
      "availableStock": "库区可用库存",
      "plannedPick": "计划拣货数量",
      "currentPick": "本次拣货量",
      // "status": "状态",
      "cancel": "取消",
      "completePicking": "完成拣货",
      failReason: "失败原因",
      tip: "提示",
      orderInfo: "单据信息",
      pickPageTitle: "拣货：拣货单号",
      pickPerson: "拣货人",
      cancelPerson: "取消人",
      cancelTime: "取消时间",
      cancelReason: "取消原因",
      pickArea: "拣货库区",
      areaPickCount: "库区拣货量",
      pickListCode: "拣货单号"


    },
    placeholder: {
      select: "请选择"
    },
    button: {
      addWarehouse: "新增",
      deactivate: "停用",
      enable: "启用",
      search: "搜索",
      reset: "重置",
      print: "打印",
      "detail": "详情",
      "pick": "去拣货",
      close: "关闭"
    },
    title: {
      addStoreAreaTitle: "添加库区",
      editStoreAreaTitle: "编辑库区",
      pickFail: "拣货失败"
    },
    message: {
      deleteTips: "确定删除此部门吗？",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      deleteCancel: "已取消删除",
      setWarehouseTips: "请选择需要操作的库区",
      statusChangeTips: "停用后不可使用，是否确认要停用？",
      enableSuccessTips: "启用成功",
      deactivateSuccessTips: "停用成功",
      optCancel: "已取消操作",
      productEditOk: "商品编辑成功",
      productCreateOk: "商品创建成功",
      getDetailFail: "获取详情失败"
    },
    rules: {
      warehouseTip: "请选择所属仓库",
      areaNameTip: "请输入库区名称",
      areaTypeTip: "请选择库区类型",
      pickingCodeTip: "请输入大于等于4位的单号",
      pickingListCode: "拣货单号必须大于4位",
      deliveryNoticeCode: "出库通知单号必须大于4位",
      billOfLadingCode: "提货单号必须大于4位",
      "pickQuantityRequired": "请检查每个商品的本次拣货数量",
      "invalidFormat": "拣货数量格式错误，支持小数点前8位，小数点后3位",
      "mustBePositive": "拣货数量必须大于0",
      "insufficientStock": "库存不足，完成拣货失败"
    },
    "confirm": {
      "title": "提示",
      "message": "完成拣货后系统会扣除锁定库存，并自动对商品出库，是否继续完成拣货",
      "confirmText": "确定",
      "cancelText": "取消",
      "success": "拣货成功"
    },
    "failure": {
      "title": "拣货失败！",
      "reason": "失败原因：",
      "close": "关闭"
    },
    "common": {
      "serialNumber": "序号",
      "back": "返回"
    },
    "print": {
      "title": "拣货单",
      "warehouse": "仓库",
      "purchaserSalesperson": "采购员(销售员)",
      "pickingListCode": "拣货单号",
      "customerSupplier": "客户(供应商)",
      "remark": "备注",
      "plannedArrivalTime": "要求到货时间",
      "productCount": "商品个数",
      "tableHeaders": {
        "serialNumber": "序号",
        "productName": "商品名",
        "specification": "规格",
        "unit": "单位",
        "totalPlannedPick": "计划拣货总量",
        "plannedAreaPick": "计划库区|拣货量",
        "actualPick": "实际拣货量"
      },
      "signatures": {
        "printer": "打印人",
        "printTime": "打印时间",
        "picker": "拣货人",
        "pickingDate": "拣货日期"
      }
    },
  },
};
