<template>
  <div class="SelWareHouse">
    <div class="normal14-font whiteColor mr-12px select-warehouse">
      {{ $t("warehouse.title.selWarehouseTitle") }}
    </div>
    <el-select
      v-loading="loading"
      class="autoWidth"
      v-model="selWareHouse"
      collapse-tags
      collapse-tags-tooltip
      :placeholder="$t('common.placeholder.selectTips')"
      style="width: 180px"
      @change="blurAction"
    >
      <el-option
        v-for="item in warehouseData"
        :key="item.warehouseCode"
        :label="item.warehouseName"
        :value="item.warehouseCode"
      >
        {{ item.warehouseName }} | {{ item.warehouseCode }}
      </el-option>
    </el-select>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import CurrentWarehouseAPI, {
  warehouseInfo,
} from "@/modules/wms/api/currentWarehouse";
import { useUserStore, useWarehouseStore } from "@/core/store";
import { emitter } from '@/core/utils/eventBus';

const userStore = useUserStore();
const warehouseStore = useWarehouseStore();
const warehouseData = ref<warehouseInfo[]>([]);
const selWareHouse = ref<string[]>([]);
const loading = ref(false);

function blurAction() {
  loading.value = true;
  const warehouseName = warehouseData.value.find((item) => item.warehouseCode === selWareHouse.value)?.warehouseName;
  warehouseStore.setSelectedWarehouse(selWareHouse.value, warehouseName);
  CurrentWarehouseAPI.checkedwarehouse([selWareHouse.value])
    .then((data) => {
      emitter.emit("reloadListByWarehouseId", { warehouseCode: selWareHouse.value });
      if(userStore.systemId === "wms"){
        emitter.emit("closeHiddenTags", { warehouseCode: selWareHouse.value });
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function refreshWarehouseList() {
  CurrentWarehouseAPI.checkedUserById()
    .then((data) => {
      warehouseData.value = data;
      selWareHouse.value = [];
      warehouseData.value.forEach((item: warehouseInfo) => {
        if (item.checked) {
          selWareHouse.value = item.warehouseCode;
          warehouseStore.setSelectedWarehouse(item.warehouseCode, item.warehouseName);
        }
      });
    })
    .finally(() => {});
}

onMounted(() => {
  let params = {
    userId: localStorage.getItem("userId"),
  };
  refreshWarehouseList();

  // 设置事件监听
  emitter.on("refreshWarehouseList", refreshWarehouseList);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  emitter.off("refreshWarehouseList", refreshWarehouseList);
});
</script>
<style scoped lang="scss">
.SelWareHouse {
  display: flex;
  align-items: center;
  .mr-12px {
    margin-right: 12px;
  }
  .select-warehouse{
    word-break: keep-all;
  }
}
</style>
