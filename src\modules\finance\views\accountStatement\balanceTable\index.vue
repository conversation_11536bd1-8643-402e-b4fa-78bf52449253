<template>
  <div class="delivery-method-container-block">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true" label-width="84px">
        <el-form-item :label="t('balanceTable.label.accountingPeriod')">
          <el-date-picker
            :clearable="false"
            @change="monthRangeChange"
            v-model="searchForm.period"
            type="monthrange"
            range-separator="至"
            :start-placeholder="t('bankJournal.label.startDate')"
            :end-placeholder="t('bankJournal.label.endDate')"
            format="YYYY-MM"
            value-format="YYYYMM"
            :disabled-date="
              (date) => {
                const start = loadPeriodList.periodYearMonthStart;
                const end = loadPeriodList.periodYearMonthEnd;
                if (!start || !end) return false;
                const y = date.getFullYear();
                const m = (date.getMonth() + 1).toString().padStart(2, '0');
                const ym = `${y}${m}`;
                return ym < start || ym > end;
              }
            "
          />
        </el-form-item>
        <el-form-item :label="t('balanceTable.label.startAccount')" prop="subjectCodeStart1">
          <el-select clearable filterable v-model="searchForm.subjectCodeStart1" :placeholder="t('balanceTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in cashAccountList" :key="category.subjectCode" :label="category.subjectCode + '_' + category.subjectName" :value="category.subjectCode" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('balanceTable.label.endAccount')" prop="subjectCodeEnd1">
          <el-select clearable filterable v-model="searchForm.subjectCodeEnd1" :placeholder="t('balanceTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in cashAccountList" :key="category.subjectCode" :label="category.subjectCode + '_' + category.subjectName" :value="category.subjectCode" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('balanceTable.label.accountLevel')" prop="subjectLevelStart">
          <el-select v-model="searchForm.subjectLevelStart" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100px">
            <el-option v-for="category in levelList" :key="category.id" :label="category.label" :value="category.id" />
          </el-select>
          <span style="margin: 0 8px">{{ t('detailsTable.label.to') }}</span>
          <el-select filterable v-model="searchForm.subjectLevelEnd" :placeholder="t('detailsTable.label.pleaseSelect')" style="width: 100px">
            <el-option v-for="category in levelList" :key="category.id" :label="category.label" :value="category.id" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item :label="t('balanceTable.label.showContent')" prop="checkList">
          <el-select filterable v-model="searchForm.checkList" :placeholder="t('balanceTable.label.pleaseSelect')" style="width: 100%">
            <el-option v-for="category in showContentList" :key="category.statusId" :label="category.statusName" :value="category.statusId" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="onSearchHandlerEvent" v-hasPerm="['finance:balanceTable:search']">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="onResetHandlerEvent" v-hasPerm="['finance:balanceTable:reset']">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <section class="delivery-method-container">
      <div class="right-panel">
        <div class="table-container">
          <el-table 
            v-el-table-infinite-scroll="loadMore"
            :infinite-scroll-distance="200"
            style="width: 100%"
            height="calc(100vh - 240px)" 
            show-summary :summary-method="getSummaries" ref="tableRef" v-loading="loading" :data="tableData1" stripe border row-key="id" 
            :tree-props="{ children: 'childList', hasChildren: 'hasChildren' }">
            <template #empty>
              <Empty />
            </template>
            <el-table-column prop="index" :label="t('balanceTable.table.serialNo')" width="80" align="center" />
            <el-table-column :label="t('balanceTable.table.accountCode')" show-overflow-tooltip>
              <template #default="scope"> {{ scope.row.subjectFullCode }}</template>
            </el-table-column>

            <el-table-column :label="t('balanceTable.table.accountName')" prop="subjectName" show-overflow-tooltip />
            <!-- 期初余额 -->
            <el-table-column :label="t('balanceTable.table.openingBalance')" prop="balanceSubjectVo" align="center">
              <el-table-column :label="t('balanceTable.table.debit')" prop="balanceSubjectVo.beginBalanceJie" show-overflow-tooltip>
                <template #default="scope"> {{ scope.row.balanceSubjectVo?.beginBalanceJie }}</template>
              </el-table-column>

              <el-table-column :label="t('balanceTable.table.credit')" prop="balanceSubjectVo.beginBalanceDai" show-overflow-tooltip>
                <template #default="scope"> {{ scope.row.balanceSubjectVo?.beginBalanceDai }}</template>
              </el-table-column>
            </el-table-column>

            <!-- 本期发生额 -->
            <el-table-column :label="t('balanceTable.table.currentPeriodAmount')" prop="balanceSubjectVo" align="center">
              <el-table-column :label="t('balanceTable.table.debit')" prop="balanceSubjectVo.curPeriodJie" show-overflow-tooltip>
                <template #default="scope"> {{ scope.row.balanceSubjectVo?.curPeriodJie }}</template>
              </el-table-column>

              <el-table-column :label="t('balanceTable.table.credit')" prop="balanceSubjectVo.curPeriodDai" show-overflow-tooltip>
                <template #default="scope"> {{ scope.row.balanceSubjectVo?.curPeriodDai }}</template>
              </el-table-column>
            </el-table-column>

            <!-- 本年累计发生额 -->
            <el-table-column :label="t('balanceTable.table.yearToDateAmount')" prop="balanceSubjectVo" align="center">
              <el-table-column :label="t('balanceTable.table.debit')" prop="balanceSubjectVo.yearPeriodJie" show-overflow-tooltip>
                <template #default="scope"> {{ scope.row.balanceSubjectVo?.yearPeriodJie }}</template>
              </el-table-column>
              <el-table-column :label="t('balanceTable.table.credit')" prop="balanceSubjectVo.yearPeriodDai" show-overflow-tooltip>
                <template #default="scope"> {{ scope.row.balanceSubjectVo?.yearPeriodDai }}</template>
              </el-table-column>
            </el-table-column>

            <!-- 期末余额 -->
            <el-table-column :label="t('balanceTable.table.endingBalance')" prop="balanceSubjectVo" align="center">
              <el-table-column :label="t('balanceTable.table.debit')" prop="balanceSubjectVo.endBalanceJie" show-overflow-tooltip>
                <template #default="scope"> {{ scope.row.balanceSubjectVo?.endBalanceJie}}</template>
              </el-table-column>
              <el-table-column :label="t('balanceTable.table.credit')" prop="balanceSubjectVo.endBalanceDai" show-overflow-tooltip>
                <template #default="scope"> {{ scope.row.balanceSubjectVo?.endBalanceDai }}</template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 科目余额表
import tableMixin from '@/modules/finance/mixins/table';
import API from '@/modules/finance/api/accountStatementApi';
import { ref, reactive, onMounted, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
// defineOptions({
//   name: "BalanceTable",
//   inheritAttrs: false,
// })
const loadPeriodList = reactive({
  periodYearMonthEnd: '',
  periodYearMonthStart: '',
  currentPeriodYearMonth:''
});
const loadPeriod = async () => {
  loading.value = true;
  try {
    const response = await API.getPeriodScope({
      queryType: 0,
    });
    loadPeriodList.periodYearMonthStart = String(response.periodYearMonthStart);
    loadPeriodList.periodYearMonthEnd = String(response.periodYearMonthEnd);
    loadPeriodList.currentPeriodYearMonth = String(response.currentPeriodYearMonth);
    searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
    searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
    searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
    onSearchHandler();
  } catch (error) {
    if(error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
    loading.value = false;
  }
};
const searchForm = reactive({
  period: [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth],
  subjectCodeEnd1: undefined,
  subjectCodeStart1: undefined,
  subjectCodeEnd: undefined,
  subjectCodeStart: undefined,
  subjectLevelStart: 1,
  subjectLevelEnd: 4,
  periodYearMonthStart: loadPeriodList.currentPeriodYearMonth,
  periodYearMonthEnd: loadPeriodList.currentPeriodYearMonth,
})
const levelList = ref([
  { id: 1, label: '1' },
  { id: 2, label: '2' },
  { id: 3, label: '3' },
  { id: 4, label: '4' }
])
// const showContentList = ref([
//   {
//     statusId: 1,
//     statusName: t("detailsTable.label.showauxiliaryAccounting"),
//   },
//   {
//     statusId: 4,
//     statusName: t("detailsTable.label.hideZeroBalance"),
//   },
//   {
//     statusId: 5,
//     statusName: t("detailsTable.label.hideNoTransactionZeroBalance"),
//   }
// ]);
const monthRangeChange = (val: [string, string]) => {
  if (val?.length) {
    searchForm.periodYearMonthStart = val[0];
    searchForm.periodYearMonthEnd = val[1];
  } else {
    searchForm.periodYearMonthStart = '';
    searchForm.periodYearMonthEnd = '';
  }
};
const { loading, tableData, total, paginationInfo, headFormRef, router, path, onSearchHandler, onResetHandler, onPaginationChangeHandler, onDeleteHandler, onStatusChangeHandler } = tableMixin({
  searchForm,
  isLimit: false,
  tableGetApi: API.getQuerySubjectBalanceFlowList,
  tableCallback: tableCallbackFun,
  // tableDeleteApi: generalLedger.deleteVoucherSummary,
});
// 处理列表数据
function tableCallbackFun() {
  tableData.value.map((item: any,index) => {
    item.index = index+1;
  });
}
interface CashAccountItem {
  id: number | string;
  subjectCode: string;
  subjectName: string;
  statusId?: number | string;
  statusName?: string;
}
const cashAccountList = ref<CashAccountItem[]>([]);
const loadcashAccountList = async () => {
  try {
    const response = await API.subjectGetList({
      status: 1,
    });
    cashAccountList.value = response || [];
  } catch (error) {
    cashAccountList.value = [];
  }
};
const onResetHandlerEvent = () => {
  index.value = 0;
  allTableData.value = [];
  tableData1.value = [];
  searchForm.period = [loadPeriodList.currentPeriodYearMonth, loadPeriodList.currentPeriodYearMonth];
  searchForm.periodYearMonthStart = loadPeriodList.currentPeriodYearMonth;
  searchForm.periodYearMonthEnd = loadPeriodList.currentPeriodYearMonth;
  searchForm.subjectLevelStart = 1;
  searchForm.subjectLevelEnd = 4;
  searchForm.subjectCodeStart = undefined;
  searchForm.subjectCodeEnd = undefined;
  onResetHandler();
};
const onSearchHandlerEvent = () => {
  index.value = 0;
  allTableData.value = [];
  tableData1.value = [];
  // 补齐 subjectCodeStart 到12位
  searchForm.subjectCodeStart = undefined;
  searchForm.subjectCodeEnd = undefined;
  if (searchForm.subjectCodeStart1 && searchForm.subjectCodeStart1.length < 11) {
    searchForm.subjectCodeStart = searchForm.subjectCodeStart1.padEnd(11, '0')
  }
  // 补齐 subjectCodeEnd 到12位
  if (searchForm.subjectCodeEnd1 && searchForm.subjectCodeEnd1.length < 11) {
    searchForm.subjectCodeEnd = searchForm.subjectCodeEnd1.padEnd(11, '0')
  }
  onSearchHandler();
};
interface TableColumn {
  property: string;
}
interface SummaryMethodProps {
  columns: TableColumn[];
  data: Record<string, any>[];
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: (string | VNode)[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
     sums[index] = h('div', { style: { fontWeight: 'bold' } }, ['合计']);
      return;
    }

    // 获取当前列对应的字段名
    let prop = column.property;
    // 兼容 template 列
    if (!prop && column.label) {
      if (column.label.includes('期初余额') && column.label.includes('借')) prop = 'balanceSubjectVo.beginBalanceJie';
      if (column.label.includes('期初余额') && column.label.includes('贷')) prop = 'balanceSubjectVo.beginBalanceDai';
      if (column.label.includes('本期发生额') && column.label.includes('借')) prop = 'balanceSubjectVo.curPeriodDai';
      if (column.label.includes('本期发生额') && column.label.includes('贷')) prop = 'balanceSubjectVo.curPeriodJie';
      if (column.label.includes('本年累计发生额') && column.label.includes('借')) prop = 'balanceSubjectVo.yearPeriodDai';
      if (column.label.includes('本年累计发生额') && column.label.includes('贷')) prop = 'balanceSubjectVo.yearPeriodJie';
      if (column.label.includes('期末余额') && column.label.includes('借')) prop = 'balanceSubjectVo.endBalanceDai';
      if (column.label.includes('期末余额') && column.label.includes('贷')) prop = 'balanceSubjectVo.endBalanceJie';
    }

    // 递归遍历树结构，累加所有叶子节点的值
function sumTree(list: any[], field: string): number {
  let sum = 0;
  tableData.value.forEach(item => {
    // 支持多层级字段
    const value = field.split('.').reduce((obj, key) => obj?.[key], item);
    sum += Number(value) || 0;
    // if (item.childList && item.childList.length) {
    //   sum += sumTree(item.childList, field);
    // }
  });
  return sum;
}
    if (prop && prop.startsWith('balanceSubjectVo')) {
      const total = sumTree(data, prop);
      sums[index] = total !== undefined ? total.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
let allTableData = ref([]);
let tableData1 = ref([]);
let index = ref(0);
const PAGE_SIZE = 18;

watch(
  () => tableData.value,
  (val) => {
    if (val.length >= PAGE_SIZE) {
      allTableData.value = JSON.parse(JSON.stringify(val));
      tableData1.value = val.slice(0, PAGE_SIZE);
      index.value = 1; // 关键：初始加载后，index设为1
    } else {
      tableData1.value = val;
      index.value = 1;
    }
  },
  { deep: true }
);

const loadMore = () => {
  const start = index.value * PAGE_SIZE;
  const end = start + PAGE_SIZE;
  if (start >= allTableData.value.length) return; // 没有更多数据
  let newTableData = allTableData.value.slice(start, end);
  tableData1.value = tableData1.value.concat(newTableData);
  index.value++;
};
onMounted(() => {
  loadPeriod()
  loadcashAccountList()
})
</script>

<style lang="scss" scoped>
.delivery-method-container {
  .right-panel {
    padding: 20px;
    background-color: #fff;

    .search-card,
    .toolbar-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }

    .search-card {
      :deep(.el-form--inline .el-form-item) {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 20px;
    }
  }
}

:deep(.el-table) {
  .el-switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
  }
}
</style>
