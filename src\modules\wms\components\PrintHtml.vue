<template>
    <div :v-loading="loading">
        <!-- 打印按钮 -->
        <el-button :type="props.buttonType" v-print="printObj">{{ buttonText }}</el-button>
        <div style="display:none">
            <div id="printHtml" class="printpage">
                <slot></slot>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { reactive, defineProps, ref } from 'vue';

let props = defineProps({
    buttonType: {
        type: String,
        default: 'default',
    },
    buttonText: {
        type: String,
        default: '打 印',
    },
    isPreview: {
        type: Boolean,
        default: true,
    }
});
const loading = ref(false);

// 打印点击
const printObj = {
    id: 'printHtml',
    beforeOpenCallback(vue) {
        loading.value = true;
    },
    openCallback(vue) {
        console.log('执行了打印')
    },
    closeCallback(vue) {
        loading.value = false;
    }
}
</script>
<style media="print" lang="scss">
@media print {
    @page {
        size: auto;
        margin-top: 3mm;
        margin-bottom: 0mm;
    }

    body {
        height: auto;
    }

    .printpage {
        height: 100%; // 根据实际情况进行分页设置
        width: 100%;
    }
}
</style>