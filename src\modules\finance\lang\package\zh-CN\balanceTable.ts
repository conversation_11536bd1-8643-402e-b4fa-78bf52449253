export default {
  balanceTable: {
    label: {
      accountingPeriod: "会计期间：",
      startAccount: "起始科目：",
      endAccount: "结束科目：",
      accountLevel: "科目级别：",
      pleaseSelect: "请选择",
      to: "至",
      showContent: "显示内容：",
      start: "开始日期",
      end: "结束日期",
      showauxiliaryAccounting: "显示辅助核算",
      hideZeroBalance: "余额为0不显示",
      hideNoTransactionZeroBalance: "无发生额且余额为0不显示"
    },
    button: {
      export: "导出",
      print: "打印",
      reset: "重置",
      search: "查询"
    },
    table: {
      serialNo: "序号",
      accountCode: "科目编码",
      accountName: "科目名称",
      openingBalance: "期初余额",
      currentPeriodAmount: "本期发生额",
      yearToDateAmount: "本年累计发生额",
      endingBalance: "期末余额",
      debit: "借方",
      credit: "贷方"
    }
  }
}