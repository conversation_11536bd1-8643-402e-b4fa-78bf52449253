export default {
  productDisassemblyAssembleOrder: {
    label: {
      disassemblyOrderCode: "拆装单号",
      orderType: "拆装类型",
      orderStatus: "状态",
      receiveStatus: "是否领单",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      targetProductQty: '目标商品',
      sourceProductQty: '源商品',
      lossOrderCode: '报损单号',
      remark: '备注',
      disassemblerName: '拆装人',
      disassemblyTime: '拆装时间',
      createUserName: '创建人',
      createTime: '创建时间',

      basicInformation: "基本信息",
      productDisassemblyAssembleOrderInformation: "商品拆装明细",
      receiptNoticeCode: '入库通知单',
      sourceProduct: "源商品",
      targetProduct: "目标商品",
      totalCountProduct: "总量",
      totalWeight: "转换量",
      totalRowCount: "商品个数",

      outWarehouseArea: "库区",
      product: "商品",
      productCategory: "商品分类",

      productInformation: "商品信息",
      productCode: "商品编码",
      productSpec: "规格",
      totalStockQty: "总库存",
      availableStockQty: "可用库存",
      sourceWarehouseArea: "出库库区",
      disassemblyOutQty: "出库数量",
      disassemblyOutWeight: "出库重量",
      targetWarehouseArea: "入库库区",
      disassemblyInQty: "入库数量",
      disassemblyInWeight: "入库重量",
      availableStockQtyCount: "可用库存数量",
      availableStockWeight: "可用库存重量",
      printStatus: '打印状态',
      totality: "商品个数",
      quantity: "数量",
      weight: "重量",
      receiveInfo: '领单信息',
      receiveName: "领单人",
      receiveTime: "领单时间",
      disassemblyOrderStatus: "拆装单状态",
      productUnit: " 单位",
      ysnCodeTitle: "商品YSN码",

      template: "拆装模板",
      templateName: "模板名称",
      templateCode: "模板编码",
      groupNum: "组合目标数量",
      splitNum: "分拆源数量",

      price: "单价",
      outWarehouseNum: "出库量",
      outWarehouseTransferNum: "出库转化量",
      inWarehouseNum: "入库量",
      inWarehouseTransferNum: "入库转化量",
      amount: "金额",
      availableWarehouse: "可用库存",
      availableWarehouseNum: "可用量",
      availableWarehouseTransferNum: "转换量",
      allUnitIncreaseAndDecrease: "一级单位增减",
      packaging: "包装",
      inSourceWarehouseArea: "入库库区",

    },
    placeholder: {
      disassemblyOrderCode: '请输入大于等于4位的单号',
      product: "请输入商品名称/编码",
      productCategory: "请输入关键词搜索分类",
    },
    dateTypeList: {
      createDate: "创建时间",
      disassemblyAssembleDate: "拆装时间",
      receiveDate: "领单时间",
    },
    printStatus: {
      0: '未打印',
      1: '部分打印',
      2: '全部打印',
    },
    orderTypeList: {
      productPortfolio: "商品组合",
      productSplit: "商品拆分",
    },
    orderStatusList: {
      draft: "草稿",
      disassemblyAssemble: "拆装中",
      disassemblyAssembleFinish: "完结",
    },
    whetherOption: {
      yes: "是",
      no: "否",
    },
    shelfLifeUnitList: {
      day: "天",
      month: "月",
      year: "年",
    },
    button: {
      addProductDisassemblyAssembleOrder: "新建商品拆装单",
      editProductDisassemblyAssembleOrder: "编辑商品拆装单",
      addProduct: "添加商品",
      saveDraft: "保存草稿",
      confirm: "确认拆装",
      actionReceive: "领单",
      cancelTransfer: "取消领单",
      calculate: "计算"
    },
    title: {
      addProduct: "添加商品",
      addSourceProduct: "添加源商品",
      addTargetProduct: "添加目标商品",
    },
    message: {
      selectNumTips1: "已选",
      selectNumTips2: "个商品",
      deleteTips: "是否确认删除已创建的拆装单？",
      deleteConcel: "已取消删除！",
      deleteSucess: "删除成功！",
      saveDraftSucess: "保存草稿成功！",
      confirmTips: "拆装后商品自动出入库，提交后不可修改，是否继续？",
      confirmSucess: "确认成功！",
      confirmConcel: "已取消确认！",
      addOrEditProductDisassemblyAssembleOrderTips: "商品拆装明细不能为空！",
      changeReceiptNoticeCodeTips: "重新选择单据后，会清空已查询入库通知单上的商品展示现在选择入库通知单上的商品，是否确认清空？",
      changeReceiptNoticeCodeConcel: "已取消切换！",
      targetProductReapetTips: "目标商品重复(商品+库区为唯一标识)！",
      sourceProductRepeatTips: "源商品重复(商品+库区为唯一标识)！",
      disassemblyOutQtyTips1: "出库数量不能大于入库数量！",
      disassemblyOutQtyTips2: "出库数量不能大于可用库存！",
      codeValideTips: "查询单号必须大于等于4位",
      disassemblyOutWeightTips: "出库重量不能大于可用库存重量！",
      receiveTips: '是否确认取消领单？',
      actionSucess: '操作成功',
      groupNumTip: "填写内容点击计算后，系统会按照模板自动计算填写内容数量/价格转换量内容。源商品/目标商品与模板不一致时无法成功计算",
      splitNumTip: "填写内容点击计算后，系统会按照模板自动计算填写内容数量/价格内容。源商品/目标商品出现与模板不一致的商品不进行计算出/入库量和转换量",
      sourceAmountCalc: '原商品',
      targetAmountCalc: '目标商品',
      amountTotalTip: '总金额为',
      sumbitConfirmTips: '提交后商品自动出入库，不可修改，是否继续'
    },
    rules: {
      orderType: '请选择拆装类型',
      sourceWarehouseAreaId: '请选择出库库区',
      targetWarehouseAreaId: '请选择入库库区',
      // disassemblyOutQty: '请输入出库数量',
      // disassemblyOutQtyFormat: '请输入大于0的数字',
      disassemblyInQty: '请输入入库数量',
      disassemblyInQtyFormat: '请输入大于0的数字，支持4位正整数',
      disassemblyOutWeight: '请输入出库重量',
      // disassemblyOutWeightFormat: '请输入大于0的数字，支持小数点前8位后3位',
      disassemblyInWeight: '请输入入库重量',
      disassemblyInWeightFormat: '请输入大于0的数字，支持小数点前8位后3位',
      calcNumFormat: '请输入大于0的数字，支持小数点前8位后3位',
      targetTotalNum: '入库总数量不超过5000',

      unitPrice: "请输入单价",
      // disassemblyOutQty: "请输入出库量",
      disassemblyOutQty: "请输入出库转化量",
      amount: "请输入金额",

      unitPriceFormat: "请输入大于0的数字，支持小数点前8位后3位",
      amountFormat: "金额支持小数点前9位后2位的数字",
      disassemblyOutQtyFormat: "请输入大于0的数字，支持小数点前8位后3位",
      disassemblyOutWeightFormat: "请输入大于0的数字，支持小数点前8位后3位",

    },
  },
};
