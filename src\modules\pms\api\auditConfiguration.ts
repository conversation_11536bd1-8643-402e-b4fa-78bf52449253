import request from "@/core/utils/request";
import { AuditRuleVO } from "@/modules/pms/types/auditConfiguration";

const BASE_URL = "/supply-pms/userAuditRlt";

class PurchaseAuditConfigurationAPI {
  /** 获取审核配置分页数据 */
  static getPage(queryParams?: any) {
    return request<any, PageResult<AuditRuleVO[]>>({
      url: `${BASE_URL}/list/page`,
      method: "post",
      data: queryParams,
    });
  }

  /** 编辑 */
  static update(data: Partial<AuditRuleVO>) {
    return request({
      url: `${BASE_URL}/update`,
      method: "post",
      data: data,
    });
  }

  /** 删除 */
  static delete(data: { userId: string }) {
    return request({
      url: `${BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  static getUserList() {
    return request({
      url: `/supply-base/user/all`,
      method: "get",
    });
  }
}

export default PurchaseAuditConfigurationAPI;
