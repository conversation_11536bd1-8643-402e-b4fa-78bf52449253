import { placeholderSign } from "element-plus/es/components/table-v2/src/private";

export default {
  storeArea: {
    label: {
      sort: "序号",
      areaName: "库区名称",
      areaCode: "库区编码",
      warehouseCoding: "仓库编码",
      status: "状态",
      warehouseName: "仓库名称",
      contact: "联系人",
      contactNumber: "联系电话",
      remark: "备注",
      operate: "操作",
      ownWarehouse: "所属仓库",
      contactPerson: "库区联系人",
      mobile: "联系人电话",
      countryAreaCode: "区号",
      contactLandline: "固定电话",
      areaType: "库区属性",
      conventionalStorageArea: "常规库区",
      defectiveProductStorageArea: "残次品库区",
      areaNewType: "库区类型",
    },
    placeholder: {},
    button: {
      addWarehouse: "新增",
      deactivate: "停用",
      enable: "启用",
    },
    title: {
      addStoreAreaTitle: "添加库区",
      editStoreAreaTitle: "编辑库区",
    },
    message: {
      deleteTips: "确定删除此部门吗？",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      deleteCancel: "已取消删除",
      setWarehouseTips: "请选择需要操作的库区",
      statusChangeTips: "停用后不可使用，是否确认要停用？",
      enableSuccessTips: "启用成功",
      deactivateSuccessTips: "停用成功",
      optCancel: "已取消操作",
      addTips: "仓库已停用，此仓库下无法创建库区",
      systemGenerated: "系统生成",
    },
    rules: {
      warehouseTip: "请选择所属仓库",
      areaNameTip: "请输入库区名称",
      areaTypeTip: "请选择库区属性",
      contactLandline: "请输入正确的固定电话",
      areaNewTypeTip: "请选择库区类型",
    },
  },
};
