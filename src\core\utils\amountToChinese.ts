// 金额转汉字大写函数
const amountToChinese = (amount: number) => {
    // 定义汉字数字和单位
    const chineseNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟'];
    const bigUnits = ['', '万', '亿'];
    const decimalUnits = ['角', '分'];

    // 处理负数
    let isNegative = false;
    if (amount < 0) {
        isNegative = true;
        amount = Math.abs(amount);
    }

    // 处理整数部分
    let integerPart = Math.floor(amount).toString();
    let chineseStr = '';
    let zeroCount = 0;

    // 处理整数部分为0的情况
    if (integerPart === '0') {
        chineseStr = '零';
    } else {
        for (let i = 0; i < integerPart.length; i++) {
            const num = parseInt(integerPart[i]);
            const pos = integerPart.length - i - 1;
            const unit = units[pos % 4];
            const bigUnit = bigUnits[Math.floor(pos / 4)];

            // 处理零的情况
            if (num === 0) {
                zeroCount++;
            } else {
                // 如果有连续的零，添加一个零
                if (zeroCount > 0) {
                    chineseStr += '零';
                    zeroCount = 0;
                }
                // 添加数字和单位
                chineseStr += chineseNums[num] + unit;
            }

            // 处理大单位（万、亿）
            if (pos % 4 === 0 && zeroCount < 4) {
                chineseStr += bigUnit;
                zeroCount = 0;
            }
        }
    }

    // 添加"元"
    chineseStr += '元';

    // 处理小数部分
    const decimalPart = Math.round((amount - Math.floor(amount)) * 100);
    if (decimalPart === 0) {
        chineseStr += '整';
    } else {
        const jiao = Math.floor(decimalPart / 10);
        const fen = decimalPart % 10;

        // 处理角
        if (jiao > 0) {
            chineseStr += chineseNums[jiao] + '角';
        } else if (fen > 0) {
            // 如果角为0但分不为0，则添加"零"
            chineseStr += '零';
        }

        // 处理分
        if (fen > 0) {
            chineseStr += chineseNums[fen] + '分';
        } else {
            // 如果分位为0，添加"整"
            chineseStr += '整';
        }
    }

    // 处理负数
    if (isNegative) {
        chineseStr = '负' + chineseStr;
    }

    return chineseStr;
}
export default amountToChinese;