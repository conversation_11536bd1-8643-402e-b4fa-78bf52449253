<template>
  <div class="pageContainer">
    <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal">
      <el-menu-item v-for="(item,index) in menuList" :key="index" @click="handleSelect(item.value)" :index="item.value">{{ item.label }}</el-menu-item>
    </el-menu>
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true">
        <el-form-item>
          <el-button v-hasPerm="['finance:accounting:add']" type="primary" @click="addAccounting('firstLevel', {level: 0, id: 0})">{{ $t('accounting.tableBtn.add') }}</el-button>
<!--          <el-button type="danger" @click="onSearchHandler">{{ $t('accounting.tableBtn.remove') }}</el-button>-->
<!--          <el-button type="default" @click="onSearchHandler">{{ $t('accounting.tableBtn.import') }}</el-button>-->
<!--          <el-button type="default" @click="onSearchHandler">{{ $t('accounting.tableBtn.export') }}</el-button>-->
        </el-form-item>
        <el-form-item prop="brandName">
          <el-input v-model="searchForm.keyword" :prefix-icon="Search" :placeholder="$t('accounting.searchPlaceholder.label')" clearable @keyup.enter="onSearchHandler"/>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <el-table ref="tableRef" v-loading="loading" :data="tableData" :tree-props="{ children: 'childList'}" highlight-current-row stripe row-key="id">
        <template #empty><Empty /></template>
        <el-table-column :label="$t('accounting.tableLabel.index')" prop="index" width="80" />
        <el-table-column :label="$t('accounting.tableLabel.code')" prop="subjectCode" show-overflow-tooltip min-width="120"/>
        <el-table-column :label="$t('accounting.tableLabel.name')" prop="subjectName" show-overflow-tooltip min-width="120">
          <template #default="scope">
            <span>{{scope.row.subjectName}}</span>
            <span class="equivalentFlag" v-if="scope.row.equivalentFlag === 1">现金</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('accounting.tableLabel.type')" prop="subjectTypeName" show-overflow-tooltip/>
        <el-table-column :label="$t('accounting.tableLabel.balance')" prop="subjectBalanceDirection" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.subjectBalanceDirection === '借' ? 'borrow' : 'loan'">{{scope.row.subjectBalanceDirection}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('accounting.tableLabel.status')" prop="productCount" show-overflow-tooltip>
          <template #default="scope">
            <el-switch v-hasPerm="['finance:accounting:changeStatus']" :active-text="$t('common.activeBtn')" :inactive-text="$t('common.inactiveBtn')" inline-prompt v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="changeRoleStatus(scope.row)"/>
          </template>
        </el-table-column>
        <el-table-column :label="$t('accounting.tableLabel.operate')" fixed="right">
          <template #default="scope">
            <el-button v-hasPerm="['finance:accounting:add']" v-if="scope.row.level !==4" type="primary" size="small" link  @click="addAccounting('nextLevel', scope.row)">{{ $t('accounting.tableBtn.addNextLevel') }}</el-button>
            <el-button v-hasPerm="['finance:accounting:edit']" type="primary" size="small" link @click="onEditHandler(scope.row)">{{ $t('accounting.tableBtn.edit') }}</el-button>
            <el-button v-hasPerm="['finance:accounting:remove']" type="danger" size="small" link @click="removeHandle(scope.row)">{{ $t('accounting.tableBtn.remove') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-drawer v-model="showDialog" :title=" dialogMode === 'add'? $t('accounting.tableBtn.addTitle') : $t('accounting.tableBtn.editTitle')" :close-on-click-modal="false" @close="onCloseHandler">
      <el-form :model="contentForm" :rules="contentFormRules" ref="contentFormRef" label-position="top">
        <el-form-item :label="$t('accounting.formLabel.parentSubjectCode')" prop="parentSubjectCode">
          <span>{{contentForm.parentSubjectCode}}</span>
        </el-form-item>
        <el-form-item :label="$t('accounting.formLabel.parentSubjectName')" prop="parentSubjectName">
          <span>{{ contentForm.parentSubjectName }}</span>
        </el-form-item>
        <el-form-item :label="$t('accounting.formLabel.subjectType')" prop="subjectType">
          <span>{{contentForm.subjectType}}</span>
        </el-form-item>
        <el-form-item :label="$t('accounting.formLabel.subjectCode')" prop="subjectCode">
          <div class="subjectCodeContent">
            <span :class="{isDisabled: contentForm.userFlag|| contentForm.dataSource === 1}">{{contentForm.subjectHeadCode}}</span>
            <el-input :disabled="(contentForm.userFlag) || contentForm.dataSource === 1" class="subjectCode" @input="numberChange(contentForm,'subjectCode')" :maxlength="codeLimit" type="text" v-model="contentForm.subjectCode"/>
          </div>
        </el-form-item>
        <el-form-item :label="$t('accounting.formLabel.subjectName')" prop="subjectName">
          <el-input :disabled="contentForm.userFlag || contentForm.dataSource === 1" v-model="contentForm.subjectName" />
        </el-form-item>
        <el-form-item :label="$t('accounting.formLabel.subjectBalanceDirection')" prop="subjectBalanceDirection">
          <el-radio-group :disabled="contentForm.userFlag || contentForm.dataSource === 1" v-model="contentForm.subjectBalanceDirection">
            <el-radio value="借" size="large">{{ $t('accounting.radioInfo.borrow') }}</el-radio>
            <el-radio value="贷" size="large">{{ $t('accounting.radioInfo.loan') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('accounting.formLabel.subjectAttribute')" prop="subjectAttribute">
          <el-radio-group :disabled="contentForm.userFlag || addType === 'nextLevel'" v-model="contentForm.subjectAttribute" @change="accountingAttChange">
            <el-radio value="1" size="large">{{ $t('accounting.radioInfo.cashAccount') }}</el-radio>
            <el-radio value="2" size="large">{{ $t('accounting.radioInfo.backAccount') }}</el-radio>
            <el-radio value="0" size="large">{{ $t('accounting.radioInfo.noHave') }}</el-radio>
          </el-radio-group>
          <span class="line"></span>
          <el-checkbox :disabled="contentForm.userFlag || addType === 'nextLevel'" v-model="contentForm.journalFlag" v-if="['1','2'].includes(contentForm.subjectAttribute)" :label="$t('accounting.radioInfo.journal')"/>
          <el-checkbox :disabled="contentForm.userFlag || equivalentFlag || addType === 'nextLevel'" v-model="contentForm.equivalentFlag" :label="$t('accounting.radioInfo.cashEquivalents')"/>
        </el-form-item>
        <el-form-item :label="$t('accounting.formLabel.quantityAccountingCode')" prop="quantityAccountingCode">
          <div>
            <el-checkbox :disabled="contentForm.userFlag" v-model="contentForm.quantityAccountingFlag"/>
            <el-select :disabled="contentForm.userFlag" v-if="contentForm.quantityAccountingFlag" v-model="contentForm.quantityAccountingCode" style="width: 240px;margin-left: 10px">
              <el-option v-for="item in accountingOptions" :key="item.itemCode" :label="item.itemName" :value="item.itemCode"/>
            </el-select>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">{{ $t("common.cancel") }}</el-button>
          <el-button v-if="!contentForm.userFlag" type="primary" :loading="dialogLoading" @click="onSaveHandler">{{ $t("common.confirm") }}</el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import formMixin from "@/modules/finance/mixins/form";
import { Search } from '@element-plus/icons-vue'
import {useCommon} from "@/modules/finance/composables/common";
import {generalLedger} from "@/modules/finance/api";
import {useRouter} from "vue-router";

const router = useRouter();
const {numberChange} = useCommon();

let codeLimit = 0;
const { t } = useI18n();
const loading = ref(false);
const activeIndex = ref('1');
const currentIndex = ref('1');
let accountingOptions = [];
const currentId = ref('');
const equivalentFlag = ref(false);
const menuList = [
  {value: '1', label: t('accounting.tabLabel.property')},
  {value: '2', label: t('accounting.tabLabel.liabilities')},
  {value: '3', label: t('accounting.tabLabel.equity')},
  {value: '4', label: t('accounting.tabLabel.cost')},
  {value: '5', label: t('accounting.tabLabel.profitLoss')},
]
const searchForm = reactive({
  showTree: true,
  keyword: '',
  pageFlag: false,
  status: ''
});
const addType = ref('');
const tableData = ref([]);
const dialogMode = ref(false);
const contentForm = reactive({
  id: '',
  subjectHeadCode: '',
  parentSubjectCode: '',
  parentSubjectName: '',
  subjectCode: '',
  subjectName: '',
  subjectType: '',
  subjectBalanceDirection: '借',
  subjectAttribute: '0',
  journalFlag: false,
  equivalentFlag: false,
  quantityAccountingFlag: false,
  quantityAccountingCode: '',
  level: '',
  parentId: '',
  dataSource: false,
  userFlag: false
});
const contentFormRules = reactive({
  subjectCode: [{required: true, message: t("accounting.rules.subjectCode"), trigger: "blur"}],
  subjectName: [{required: true, message: t("accounting.rules.subjectName"), trigger: "blur"}],
});
// 编辑回调
function editCallbackHandle(data) {
  const params = {
    id: data.id
  }
  generalLedger.queryAccountingDetail(params).then(data => {
    contentForm.subjectType = data.subjectTypeName;
    contentForm.equivalentFlag = data.equivalentFlag === 1;
    contentForm.journalFlag = data.journalFlag === 1;
    if(data.level === 1) {
      contentForm.parentSubjectCode = '--';
      contentForm.parentSubjectName = '--';
      contentForm.parentId = 0;
    } else {
      let parentCodeList = data.subjectFullCode.split('_');
      let parentNameList = data.subjectFullName.split('_');
      contentForm.parentSubjectCode = parentCodeList[parentCodeList.length-2];
      contentForm.parentSubjectName = parentNameList[parentNameList.length-2];
      contentForm.parentId = 0;
    }

    switch (data.level) {
      case 1:
        codeLimit = 4;
        contentForm.subjectHeadCode = '';
        break;
      case 2:
        codeLimit = 3;
        contentForm.subjectHeadCode = data.subjectCode.slice(0,4);
        contentForm.subjectCode = data.subjectCode.slice(4);
        break;
      case 3:
        codeLimit = 2;
        contentForm.subjectHeadCode = data.subjectCode.slice(0,7);
        contentForm.subjectCode = data.subjectCode.slice(7);
        break;
      case 4:
        codeLimit = 2;
        contentForm.subjectHeadCode = data.subjectCode.slice(0,9);
        contentForm.subjectCode = data.subjectCode.slice(9);
        break;
    }
    contentForm.quantityAccountingCode += '';
    contentForm.subjectAttribute += '';
    contentForm.quantityAccountingFlag = Boolean(data.quantityAccountingCode)
    if(!contentForm.quantityAccountingFlag) {
      contentForm.quantityAccountingCode = '';
    }
    contentForm.userFlag = data.userFlag;
    contentForm.dataSource = data.dataSource;
    // accountingAttChange();
    if(['1','2'].includes(contentForm.subjectAttribute)) {
      equivalentFlag.value = true;
    } else {
      equivalentFlag.value = false;
    }
  })
}
function saveCallbackHandler () {
  onSearchHandler();
}
// 切换类别
function handleSelect(data) {
  currentIndex.value = data;
  onSearchHandler();
}
const {
  showDialog,
  dialogLoading,
  contentFormRef,
  onAddHandler,
  onEditHandler,
  onSaveHandler,
  onCloseHandler,
} = formMixin({
  contentForm,
  idName: "id",
  uselessParams: ["parentSubjectCode",'parentSubjectName'],
  formAddApi: generalLedger.addAccounting,
  formEditApi: generalLedger.updateAccounting,
  editCallback: editCallbackHandle,
  saveCallback: saveCallbackHandler,
  checkCallback: () => {
    if(contentForm.quantityAccountingFlag && contentForm.quantityAccountingCode === '') {
      ElMessage.warning(t('accounting.messageInfo.checkQuantityAccounting'));
      return true;
    }
    if(contentForm.subjectCode.length !== codeLimit) {
      ElMessage.warning(t('accounting.messageInfo.checkSubjectCode'));
      return true;
    }
  },
  formatParamsCallback: (params: any) => {
    if(!params.quantityAccountingFlag) {
      params.quantityAccountingCode = '';
    }
    params.subjectType = menuList.filter(item => item.label === params.subjectType)[0].value;
    params.equivalentFlag = params.equivalentFlag ? '1' : '0';
    params.journalFlag = params.journalFlag ? '1' : '0';
    params.subjectCode = params.subjectHeadCode + params.subjectCode;
    delete params.subjectHeadCode;
    delete params.quantityAccountingFlag;
  },
});

// 新增科目弹框
function addAccounting(type, data) {
  onAddHandler('firstLevel');
  dialogMode.value = 'add';
  addType.value = type;
  contentForm.subjectType = menuList.filter(item => item.value === currentIndex.value)[0].label;
  switch (data.level) {
    case 0: codeLimit = 4;break;
    case 1: codeLimit = 3;break;
    case 2:
    case 3: codeLimit = 2;break;
  }
  contentForm.level = data.level + 1;
  currentId.value = data.id;

  if(type === 'firstLevel') {
    contentForm.subjectHeadCode = '';
    contentForm.parentSubjectCode = '--';
    contentForm.parentSubjectName = '--';
    contentForm.parentId = 0;
  }
  if(type === 'nextLevel') {
    contentForm.subjectHeadCode = data.subjectCode;
    contentForm.parentSubjectCode = data.subjectCode;
    contentForm.parentSubjectName = data.subjectName;
    contentForm.parentId = data.id;
  }
  contentForm.dataSource = false;
  contentForm.journalFlag = false;
  contentForm.equivalentFlag = false;
  contentForm.quantityAccountingFlag = false;
  equivalentFlag.value = false
  contentForm.userFlag = false;
  if(type === 'nextLevel') {
    contentForm.subjectAttribute = data.subjectAttribute + '';
    contentForm.journalFlag = data.journalFlag == 1;
    contentForm.equivalentFlag = data.equivalentFlag == 1;
  }
  generateCodeData();
}

// 删除
function removeHandle(row: any) {
  if(row.childList && row.childList.length > 0) {
    ElMessage.warning(t('accounting.messageInfo.checkNextLevel'));
    return;
  }
  const params = {
    id: row.id
  }
  generalLedger.queryAccountingDetail(params).then(data => {
    if(data.userFlag) {
      ElMessage.warning(t('accounting.messageInfo.checkUserFlag'));
      return;
    } else {
      ElMessageBox.confirm(`${t('accounting.messageInfo.remove')}`,  t('purchasePayable.message.reconciled'), {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: "warning",
        lockScroll: false,
      }).then(() => {
        const params = {
          id: row.id
        }
        generalLedger.removeAccounting(params).then(() => {
          ElMessage.success(t('accounting.messageInfo.removeSuccess'));
          onSearchHandler();
        })
      })
    }
  });


}

// 处理日记账数据
function accountingAttChange() {
  if(['1','2'].includes(contentForm.subjectAttribute)) {
    contentForm.equivalentFlag = true;
    equivalentFlag.value = true;
  } else {
    contentForm.equivalentFlag = false;
    equivalentFlag.value = false;
  }
  contentForm.journalFlag = false;
}

// 获取列表数据
function onSearchHandler() {
  const params = {
    showTree: true,
    subjectType: currentIndex.value,
    status: '',
    ...searchForm
  }
  loading.value = true;
  generalLedger.getAccountingList(params).then(res => {
    loading.value = false;
    tableData.value = res;
    tableData.value.map((item,index) => {
      item.index = index + 1;
    })
  }).catch((err) => {
    loading.value = false;
    if(err.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
  });
}

/** 修改角色状态 */
function changeRoleStatus(row) {
  let flag = row.status
  row.status = row.status === 0 ? 1 : 0//保持switch点击前的状态
  let data = {
    id: row.id,
    status: flag
  }
  if(flag === 0){
    ElMessageBox.confirm(t('accounting.messageInfo.disableTips'), t('common.tipTitle'), {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: "warning",
    }).then(() => {
      generalLedger.updateStatusAccounting(data).then(res => {
        ElMessage.success(t('accounting.messageInfo.disableSuccess'))
        onSearchHandler()
      })
    })
  }else{
    generalLedger.updateStatusAccounting(data).then(res => {
      ElMessage.success(t('accounting.messageInfo.enableSuccess'))
      onSearchHandler()
    })
  }
}


// 获取科目编码
function generateCodeData() {
  const params = {
    parentId: currentId.value
  }
  generalLedger.generateCode(params).then(res => {
    contentForm.subjectCode = res;
  });
}

function getSelectItemByTypeCodeData() {
  let arr = ['quantity']
  generalLedger.getSelectItemByTypeCode(JSON.stringify(arr)).then(res => {
    accountingOptions = res.quantity;
  });
}

onMounted(() => {
  onSearchHandler();
  getSelectItemByTypeCodeData();
})
// 编辑
</script>
<style scoped lang="scss">
.pageContainer{
  background: #FFFFFF;
  padding: 20px 20px 40px 20px;
  height: 100%;
  width: 100%;
  .el-menu--horizontal{
    height: 40px;
    .el-menu-item{
      height: 39px!important;
      background: #fff!important;
      padding: 10px 46px!important;
      &.is-active:before{
        display: none;
      }
    }
  }
  .search-container{
    padding: 20px 0 0;
    margin-bottom: 0;
    .el-form{
      width: 100%;
      display: flex;
      justify-content: space-between;
      .el-form-item{
        margin-right: 0;
      }
      .el-button--default{
        --el-border-color: #762ADB!important;
        --el-text-color-regular: #762ADB!important;
      }
    }
  }
  :deep(.el-card__body){
    padding: 0!important;
  }
  .line{
    margin: 0 10px;
    &:before{
      content: '';
      display: inline-block;
      height: 16px;
      width: 1px;
      position: relative;
      background: #ddd;
      top: 2px;
    }
  }
  .subjectCodeContent{
    display: flex;
    width: 100%;
    >span{
      padding-left: 8px;
      color: #52585F !important;
      display: inline-block;
      border: 1.5px solid #dcdfe6;
      border-radius: 4px 0 0 4px;
      border-right: none;
      &.isDisabled{
        background: #f5f7fa;
        color: #a8abb2 !important;
      }
    }
    :deep(.el-input__wrapper){
      box-shadow: none;
      padding-left: 0;
      border: 1.5px solid #dcdfe6;
      border-radius: 0 4px 4px 0;
      border-left: none;
    }
  }
  .borrow{
    background: rgba(41,182,16,0.08);
    border-radius: 2px;
    border: 1px solid rgba(41,182,16,0.2);
    padding: 3px 21px;
    display: inline-block;
    color: #29B610;
  }
  .loan{
    background: rgba(255,156,0,0.08);
    border-radius: 2px;
    border: 1px solid rgba(255,156,0,0.2);
    padding: 3px 21px;
    display: inline-block;
    color: #FF9C00;
  }
  .equivalentFlag{
    font-size: 12px;
    color: #762adb;
    border: 1px solid #762adb;
    padding: 1px 4px;
    margin-left: 8px;
  }
}
</style>
