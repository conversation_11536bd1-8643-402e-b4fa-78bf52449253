<template>
  <el-dialog v-model="batchTagVisible" width="600px" title="提示">
    <div class="mb20px">确认已核算清楚勾选订单？ 确认后单据将更改为已核算</div>
    <el-form
      :model="batchTagForm"
      ref="batchTagFormRef"
      label-width="108px"
      label-position="right"
    >
      <el-row class="flex-center-start">
        <el-col :span="24">
          <el-form-item label-width="0px" prop="remark">
            <el-input
              :rows="4"
              type="textarea"
              show-word-limit
              v-model="batchTagForm.remark"
              placeholder="备注信息,最多输入100字，非必填"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
        <span class="dialog-footer">
          <el-button @click="errorCloseHandle">{{$t('common.cancel')}}</el-button>
          <el-button type="primary" @click="errorCloseConfirm">{{$t('common.confirm')}}</el-button>
        </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
    default: false
  }
});
const { t } = useI18n();
const emit = defineEmits(['update:visible','confirm']);
const batchTagVisible = ref(false)
const batchTagFormRef = ref('')

watch(() => props.visible, (val) => {
  batchTagVisible.value = true;
},{immediate:true})

const batchTagForm = reactive({
  remark: '',
})
function errorCloseHandle() {
  emit('update:visible', false)
}
function errorCloseConfirm() {
    emit('confirm', batchTagForm.remark)
}
</script>
<style scoped lang="scss">

</style>
