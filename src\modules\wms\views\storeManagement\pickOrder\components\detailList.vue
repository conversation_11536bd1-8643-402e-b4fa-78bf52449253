<template>
  <el-dialog
    v-model="isVisible"
    :title="title"
    :close-on-click-modal="false"
    width="800px"
    class="dialog-wrapper"
    @close="closeDialog"
    header-class="dialog-header"
  >
    <div class="dialog-content">
      <el-table
        :data="tableData"
        border
        highlight-current-row
        stripe
        :max-height="600"
        style="overflow: auto"
        v-loading="loading"
      >
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          type="index"
          :label="$t('common.sort')"
          width="60"
          show-overflow-tooltip
        />
        <el-table-column
          :label="$t('pickOrder.label.ysnCode')"
          prop="ysnCode"
          show-overflow-tooltip
        />

        <el-table-column
          :label="`${$t('pickOrder.label.weight')}(kg)`"
          prop="weight"
          width="200"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="getTableList"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">{{ $t("common.cancel") }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { parseDateTime } from "@/core/utils";
import API from "@/modules/wms/api/pickOrder";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

const state = reactive({
  loading: false,
  total: 0,
  queryParams: {
    page: 1,
    limit: 20,
  },
  tableData: [],
});

const { loading, total, queryParams, tableData } = toRefs(state);

const emit = defineEmits(["update:dialogVisible", "onSubmit"]);

const isVisible = computed({
  get: () => {
    return props.dialogVisible;
  },
  set: (val: any) => {
    closeDialog();
    reset();
  },
});

/**
 * 关闭
 */

function closeDialog() {
  emit("update:dialogVisible", false);
}

/**
 * 重置
 */
function reset() {
  queryParams.value.page = 1;
  tableData.value = [];
}

/**
 * 查询
 * @param data
 */
function handleQuery(data: any) {
  queryParams.value = {
    ...queryParams.value,
    ...data,
  };
  getTableList();
}

/**
 * 查询列表

 */
function getTableList() {
  loading.value = true;
  let params = {
    ...queryParams.value,
  };
  API.queryYsnList(params)
    .then((res: any) => {
      const { records, pages } = res;
      tableData.value = records || [];
      total.value = parseInt(res.total);
    })
    .catch((err: any) => {
      tableData.value = [];
      console.warn(err);
    })
    .finally(() => {
      loading.value = false;
    });
}

defineExpose({
  handleQuery,
});
</script>

<style lang="scss" scoped>
.dialog-content {
}
</style>
<style lang="scss">
.dialog-wrapper {
  .dialog-header {
    border-bottom: 1px solid #e5e7f3;
    margin-bottom: 20px;
  }
}
</style>
