<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" size="850px" @close="close" class="add-product">
        <div>
            <el-form ref="productFromRef" :model="productFrom" :inline="true">
               <el-form-item :label="$t('productDisassemblyAssembleOrder.label.outWarehouseArea')" prop="warehouseAreaCodes" v-if="outWarehouseAreaFromMultipShow">
                   <el-select
                           @change="orderTypeChange"
                           v-model="productFrom.warehouseAreaCodes"
                           :placeholder="$t('common.placeholder.selectTips')"
                           clearable
                           multiple
                           collapse-tags
                           collapse-tags-tooltip
                           class="!w-[256px]"
                   >
                       <el-option v-for="item in outWarehouseAreaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
                   </el-select>
               </el-form-item>
               <el-form-item :label="$t('productDisassemblyAssembleOrder.label.outWarehouseArea')" prop="warehouseAreaCode" v-if="outWarehouseAreaFromShow">
                   <el-select
                           v-model="productFrom.warehouseAreaCode"
                           :placeholder="$t('common.placeholder.selectTips')"
                           clearable
                           disabled
                           class="!w-[256px]"
                   >
                       <el-option v-for="item in outWarehouseAreaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
                   </el-select>
               </el-form-item>
                <el-form-item prop="productCategory" :label="$t('productDisassemblyAssembleOrder.label.productCategory')">
                    <el-cascader v-model="productFrom.productCategory"
                                 :options="categoryList"
                                 :props="propsCategory"
                                 @change="handleChange"
                                 ref="cascaderRef"
                                 filterable
                                 class="!w-[256px]"
                                 collapse-tags
                                 collapse-tags-tooltip
                                 :placeholder="$t('productDisassemblyAssembleOrder.placeholder.productCategory')"
                                 clearable />
                </el-form-item>
<!--                <el-form-item prop="productName" :label="$t('productDisassemblyAssembleOrder.label.product')">
                    <el-input
                            v-model="productFrom.productName"
                            :placeholder="$t('productDisassemblyAssembleOrder.placeholder.product')"
                            clearable
                            class="!w-[256px]"
                    />
                </el-form-item>-->
              <el-form-item prop="keyword" :label="$t('productDisassemblyAssembleOrder.label.product')">
                <el-input
                  v-model="productFrom.keyword"
                  :placeholder="$t('productDisassemblyAssembleOrder.placeholder.product')"
                  clearable
                  class="!w-[256px]"
                />
              </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryProductAll">
                        {{$t('common.search')}}
                    </el-button>
                    <el-button  @click="reset">
                        {{$t('common.reset')}}
                    </el-button>
                </el-form-item>
            </el-form>
            <div class="select-div">{{ $t("productDisassemblyAssembleOrder.message.selectNumTips1")}}<span class="select-num">{{multipleSelectionProduct.length}}</span>{{ $t("productDisassemblyAssembleOrder.message.selectNumTips2")}}</div>
            <el-table
                    v-loading="loading"
                    :data="productTable"
                    highlight-current-row
                    stripe
                    @selection-change="handleSelectionProductChange"
            >
                <el-table-column type="selection" width="60" align="center"/>
                <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productInformation')" width="200">
                    <template #default="scope">
                        <div class="product-div">
                            <div class="product">
                                <div class="product-name" style="word-break: break-all">{{scope.row.productName}}</div>
                                <div class="product-code">
                                    <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productCode')}}：</span>
                                    <span class="product-value">{{scope.row.productCode}}</span>
                                </div>
                                <div class="product-code" v-if="typeList!==2">
                                    <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productSpec')}}：</span>
                                    <span class="product-value">{{scope.row.productSpec}}</span>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productCategory')" prop="fullCategoryName" show-overflow-tooltip/>
                <el-table-column v-if="typeList==2" :label="$t('productDisassemblyAssembleOrder.label.productSpec')" prop="productSpec" show-overflow-tooltip/>
                <el-table-column v-if="outWarehouseAreaShow" :label="$t('productDisassemblyAssembleOrder.label.outWarehouseArea')" show-overflow-tooltip>
                    <template #default="scope">
                        {{scope.row.warehouseAreaName }} <!--| {{scope.row.warehouseAreaCode}}-->
                    </template>
                </el-table-column>
                <el-table-column v-if="totalStockQtyShow" :label="$t('productDisassemblyAssembleOrder.label.totalStockQty')" prop="totalStockQty" show-overflow-tooltip align="right"/>
                <el-table-column v-if="availableStockQtyShow" :label="$t('productDisassemblyAssembleOrder.label.availableStockQty')" prop="availableStockQty" show-overflow-tooltip align="right"/>
                <el-table-column v-if="showConverUnit" :label="'库存'" prop="converUnit" show-overflow-tooltip align="left">
                    <template #default="scope">
                        <section>
                            <div>可用库存：{{scope.row.availableStockQty}}{{ scope.row.productUnitName }}</div>
                            <div>转换量：{{scope.row.availableStockWeight}}{{ scope.row.conversionRelSecondUnitName }}</div>
                        </section>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                    v-if="productTotal > 0"
                    v-model:total="productTotal"
                    v-model:page="productFrom.page"
                    v-model:limit="productFrom.limit"
                    @pagination="queryProductAll"
            />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" :loading="submitLoading" @click="submitForm" :disabled="multipleSelectionProduct.length==0">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-drawer>
</template>
<script setup lang="ts">
    import type { CascaderProps } from 'element-plus';
    import productCategoryAPI from "@/modules/pms/api/productCategory";
    import CommonAPI, { ProductAllPageQuery,ProductAllPageVO}  from "@/modules/wms/api/common";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
        /*表单库区单选是否展示*/
        outWarehouseAreaFromShow:{
            type: Boolean,
            default: false,
        },
        /*表单库区多选是否展示*/
        outWarehouseAreaFromMultipShow:{
            type: Boolean,
            default: false,
        },
        /*列表库区是否展示*/
        outWarehouseAreaShow:{
            type: Boolean,
            default: false,
        },
        /*列表总库存是否展示*/
        totalStockQtyShow:{
            type: Boolean,
            default: false,
        },
        /*列表可用库存是否展示*/
        availableStockQtyShow:{
            type: Boolean,
            default: false,
        },
        hasTotalStockQty:{
            type: Boolean,
            default: false,
        },
        showConverUnit:{
            type: Boolean,
            default: false,
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();

    const outWarehouseAreaList = ref([])
    const  supplierType= ref()
    const  ids = ref([])
    const  submitLoading= ref(false)
    const loading = ref(false);
    const typeList = ref();
    const productTotal = ref(0);
    const productFromRef = ref();
    const multipleSelectionProduct = ref([]);
    const productTable = ref<ProductAllPageVO[]>()
    const cascaderRef = ref();
    const categoryList = ref([])
    const propsCategory: CascaderProps = {
        multiple: true,
        checkStrictly :false ,
        value: 'id',
        label: 'categoryName',
        children: 'children',
    }
    /*商品是否展示加权平均单价*/
    const showWeightedAveragePrice = ref(false);
    let productFrom = reactive<ProductAllPageQuery>({
        page: 1,
        limit: 20,
        keyword: ''
    });

    function close() {
        emit("update:visible", false);
        productFrom.productName = '';
        productFrom.warehouseAreaCodes = [];
        productFrom.productCategory = [];
        productFrom.firstCategoryIds = [];
        productFrom.secondCategoryIds =[];
        productFrom.thirdCategoryIds = [];
        productFrom.page = 1;

        productFrom.keyword = '';
        productFrom.limit = 20;
    }

    function reset() {
        productFrom.productName = '';
        productFrom.warehouseAreaCodes = [];
        productFrom.productCategory = [];
        productFrom.firstCategoryIds = [];
        productFrom.secondCategoryIds =[];
        productFrom.thirdCategoryIds = [];
        productFrom.page = 1;
        productFrom.limit = 20;
        productFrom.keyword = '';
        queryProductAll()
    }

    function handleChange(){
        let valueArr = productFrom.productCategory
        let firstCategoryIds =[];
        let secondCategoryIds =[];
        let thirdCategoryIds =[];
        if(valueArr && valueArr.length>0){
            valueArr.forEach(item=>{
                if(item[0]){
                    firstCategoryIds.push(item[0]);
                }
                if(item[1]){
                    secondCategoryIds.push(item[1]);
                }
                if(item[2]){
                    thirdCategoryIds.push(item[2]);
                }
            })
        }
        productFrom.firstCategoryIds = Array.from(new Set(firstCategoryIds));
        productFrom.secondCategoryIds = Array.from(new Set(secondCategoryIds));
        productFrom.thirdCategoryIds = Array.from(new Set(thirdCategoryIds));
        // console.log('firstCategoryIds'+ productFrom.firstCategoryIds)
        // console.log('secondCategoryIds'+ productFrom.secondCategoryIds)
        // console.log('thirdCategoryIds'+ productFrom.thirdCategoryIds)
        /* if(cascaderRef.value.getCheckedNodes()){
             let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues
             productFrom.firstCategoryId = valueArr[0];
             productFrom.secondCategoryId = valueArr[1];
             productFrom.thirdCategoryId = valueArr[2];
             productFrom.productCategory = valueArr;
         }*/
    }

    function handleSelectionProductChange(val) {
        multipleSelectionProduct.value = val;
    }

    function submitForm() {
        submitLoading.value = true;
        const  collection = multipleSelectionProduct.value
        close();
        submitLoading.value = false;
        emit("onSubmit", {collection:collection,typeList:typeList.value});
    }

    function queryProductAll(){
        loading.value = true;
        submitLoading.value=true
        let data = {
            ...productFrom,
            isSku: 1,
        }
        if(props.hasTotalStockQty){
            data.hasTotalStockQty=props.hasTotalStockQty
        }
        delete data.productCategory

        if(typeList.value==2){
            delete data.warehouseAreaCodes
            delete data.warehouseAreaCode
            if(showWeightedAveragePrice.value){
              // 查询所有可选的目标商品列表(分页展示拆装单选择的目标商品带-加权平均价)
              CommonAPI.queryTargetProductAllHasAveragePrice(data)
                .then((data) => {
                  productTable.value = data.records;
                  productTotal.value = parseInt(data.total);
                })
                .finally(() => {
                  loading.value = false;
                  submitLoading.value=false
                });
            }else{
              // 查询所有可选的目标商品列表(分页展示拆装单选择的目标商品)
              CommonAPI.queryTargetProductAll(data)
                .then((data) => {
                  productTable.value = data.records;
                  productTotal.value = parseInt(data.total);
                })
                .finally(() => {
                  loading.value = false;
                  submitLoading.value=false
                });
            }
        }else if(typeList.value==3){
            data.warehouseAreaCodeList=productFrom.warehouseAreaCodes
            // 查询所有可选的商品列表(质检单库内巡检-包含库存为0的商品)
            CommonAPI.queryProductAllPage(data)
                .then((data) => {
                    productTable.value = data.records;
                    productTotal.value = parseInt(data.total);
                })
                .finally(() => {
                    loading.value = false;
                    submitLoading.value=false
                });
        } else{
            data.warehouseAreaCodeList=productFrom.warehouseAreaCodes
            /*有库存并展示加权平均价的字段的商品*/
            if(showWeightedAveragePrice.value){
              // 询所有可选的商品列表(分页展示库存转移选择的商品带-加权平均价)
              CommonAPI.queryProductAllHasAveragePrice(data)
                .then((data) => {
                  productTable.value = data.records;
                  productTotal.value = parseInt(data.total);
                })
                .finally(() => {
                  loading.value = false;
                  submitLoading.value=false
                });
            }else{
              // 询所有可选的商品列表(分页展示库存转移选择的商品)
              console.log("源商品====")
              CommonAPI.queryProductAll(data)
                .then((data) => {
                  productTable.value = data.records;
                  productTotal.value = parseInt(data.total);
                })
                .finally(() => {
                  loading.value = false;
                  submitLoading.value=false
                });
            }
        }
    }

    function setFormData(data) {
        typeList.value = data.queryParams.typeList?data.queryParams.typeList:'';
        productFrom.warehouseAreaCode = data.queryParams.warehouseAreaCode?data.queryParams.warehouseAreaCode:'';
        productFrom.warehouseAreaCodes = data.queryParams.warehouseAreaCodes?data.queryParams.warehouseAreaCodes:[];
        productFrom.productName = '';
        productFrom.productCategory = [];
        productFrom.firstCategoryId = '';
        productFrom.secondCategoryId = '';
        productFrom.thirdCategoryId = '';
        if (data.queryParams.status!==''){ //商品状态  1->上架，2->下架
            productFrom.status = data.queryParams.status;
        }else {
           delete productFrom.status
        }
        if (data.queryParams.hasAvailableStockQty!=='' && typeList.value==1){ // 是否有可用库存标志
            productFrom.hasAvailableStockQty = data.queryParams.hasAvailableStockQty;
        }else {
            delete productFrom.hasAvailableStockQty ;
        }
        if (data.queryParams.isExcludeVirtualArea!=='' && typeList.value==1){ //  是否过滤虚拟库区库存
            productFrom.isExcludeVirtualArea = data.queryParams.isExcludeVirtualArea;
        }else {
            delete productFrom.isExcludeVirtualArea;
        }
        if (data.queryParams.enableExcludeVirtualArea!=='' && typeList.value==1){ //  是否过滤虚拟库区库存
            productFrom.enableExcludeVirtualArea = data.queryParams.enableExcludeVirtualArea;
        }else {
            delete productFrom.enableExcludeVirtualArea;
        }
        if(data?.queryParams?.isShowAveragePrice){
          console.log("展示加权平均价的商品=====")
          showWeightedAveragePrice.value = true;
        }
        queryProductAll()
    }


    /** 查询商品分类列表 */
    function queryManagerCategoryList(id?:any) {
        CommonAPI.queryCategoryTreeList({}).then((data: any) => {
            // productCategoryAPI.queryCategoryTreeList({}).then((data: any) => {
            categoryList.value = data;
        })
    }

    /** 查询出库库区列表 */
    function getOutWarehouseAreaList() {
        CommonAPI.getOutWarehouseAreaList()
            .then((data) => {
                outWarehouseAreaList.value = data;
                if(outWarehouseAreaList.value && outWarehouseAreaList.value.length>0){
                    outWarehouseAreaList.value.map((item)=>{
                        item.warehouseArea = item.areaName + '|' +item.areaCode
                        return item
                    });
                }
            })
    }

    defineExpose({
        setFormData,
        queryManagerCategoryList,
        getOutWarehouseAreaList
    });
</script>

<style scoped lang="scss">
    .add-product{
        .supplier-div{
            width: calc(100% - 170px);
        }
        .product-div{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .picture{
                margin-right: 16px;
                img{
                    width: 80px;
                    height: 80px;
                }
            }
            .product{
                font-family: PingFangSC, PingFang SC;
                font-style: normal;
                .product-code{
                    font-weight: 400;
                    font-size: 14px;
                    color: #90979E;
                }
                .product-name{
                    font-weight: 500;
                    font-size: 14px;
                    color: #52585F;
                }
            }
        }
        .select-div{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #52585F;
            font-style: normal;
            margin-bottom: 16px;
            .select-num{
                color:var(--el-color-primary)
            }
        }
    }
</style>
<style lang="scss">
</style>
