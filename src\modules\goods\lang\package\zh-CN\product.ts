/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-25 16:01:59
 * @LastEditors: ch<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-14 13:04:30
 * @FilePath: \supply-manager-web\src\modules\goods\lang\package\zh-CN\product.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  product: {
    label: {
      sort: "排序",
      brandName: "品牌名称",
      updateTime: "更新时间",
      addBrand: "添加品牌",
      editBrand: "编辑品牌",
      showSort: "显示顺序",
      categoryName: "分类名称",
      count: "商品个数",
      categoryImage: "分类图片",
      categorySequence: "分类顺序",
      parentName: "上级分类",
      level: "分类层级",
      firstCategory: "一级分类",
      secondCategory: "二级分类",
      thirdCategory: "三级分类",
      categoryCode: "分类编码",
    },
    placeholder: {
      automaticallyGeneratedBySystem: "系统自动生成",
    },
    button: {
      addBrand: "添加品牌",
      editBrand: "编辑品牌",
      addCategory: "新增分类",
      editCategory: "编辑分类",
      addSubordinateCategory: "新增下级分类",
    },
    title: {},
    message: {
      addCategorySucess: "新增分类成功",
      deleteBrandTip: "确定删除商品品牌",
      deleteCategoryTip: "确定删除此分类？",
      pictureTip: "800 * 800像素，支持jpg、jpeg、png格式",
    },
    rules: {
      brandNameRule: "请输入品牌名称",
      brandSortRule: "请输入品牌序号",
      categoryNameRule: "请输入分类名称",
      parentIdRule: "请选择上级分类",
      sort: "请输入分类顺序",
    },
  },
};
