# 项目重构计划

## 1. 核心功能迁移（core目录）

### 1.1 公共组件迁移
- src/components -> src/core/components
  - AppLink
  - Breadcrumb
  - CURD
  - CopyButton
  - Dictionary
  - GithubCorner
  - Hamburger
  - IconSelect
  - LangSelect
  - Pagination
  - SizeSelect
  - SvgIcon
  - TableSelect
  - UpdatePassword
  - Upload
  - WangEditor

### 1.2 工具函数迁移
- src/utils -> src/core/utils
- src/directive -> src/core/utils/directives

### 1.3 类型定义迁移
- src/types -> src/core/types

### 1.4 公共API迁移
- src/api/common -> src/core/api

### 1.5 公共状态管理迁移
- src/store -> src/core/store

## 2. 业务模块迁移（modules目录）

### 2.1 WMS模块
- src/wms/views -> src/modules/wms/views
- src/wms/api -> src/modules/wms/api
- src/wms/components -> src/modules/wms/components
- src/wms/store -> src/modules/wms/store
- src/wms/types -> src/modules/wms/types

### 2.2 PMS模块
- src/pms/views -> src/modules/pms/views
- src/pms/api -> src/modules/pms/api
- src/pms/components -> src/modules/pms/components
- src/pms/store -> src/modules/pms/store
- src/pms/types -> src/modules/pms/types

### 2.3 OMS模块
- src/oms/views -> src/modules/oms/views
- src/oms/api -> src/modules/oms/api
- src/oms/components -> src/modules/oms/components
- src/oms/store -> src/modules/oms/store
- src/oms/types -> src/modules/oms/types

### 2.4 TMS模块
- src/tms/views -> src/modules/tms/views
- src/tms/api -> src/modules/tms/api
- src/tms/components -> src/modules/tms/components
- src/tms/store -> src/modules/tms/store
- src/tms/types -> src/modules/tms/types

## 3. 其他目录保持不变
- src/assets
- src/styles
- src/router

## 注意事项
1. 迁移前请确保代码已提交到版本控制系统
2. 迁移过程中需要同步修改相关的import路径
3. 迁移后需要更新路由配置
4. 需要更新构建配置以适应新的目录结构
