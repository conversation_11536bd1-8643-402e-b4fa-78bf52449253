<script setup lang="ts">
import InqueryAPI from "@pms/api/inquery";
import { useDetail } from "./composables/useDetail";
import UserAPI from "@pms/api/user";
import { useI18n } from "vue-i18n";
const { inqueryDetail, getInqueryDetail } = useDetail();
import { parseTime } from "@/core/utils/index";
import { useTagsViewStore } from "@/core/store";
import {useCommon} from '@pms/composables/common'
const { currencyFilter } = useCommon();
const { t } = useI18n();

interface InquiryDetailVO {
  productId: number;
  productCode: number;
  productName: string;
  brandName: string;
  unitName: string;
  productImg: string;
  firstCategoryId: number;
  secondCategoryId: number;
  thirdCategoryId: number;
  productCategoryFullName: string;
  defaultSupplierId: number;
  defaultSupplierName: string;
  remark: string;
  lastPurchaseAmountCurrency: string;
  lastPurchaseAmount: number;
  inquiryProductSupplierListVOS: Array<{
    supplierId: number;
    supplierName: string;
    price?: number;
    selected?: boolean;
  }>;
}

interface InquiryReport {
  inquiryNo: string;
  title: string;
  status: string;
  deliveryStartDate: string;
  deliveryEndDate: string;
  inquiryType: string;
  inquiryDetailVOS: InquiryDetailVO[];
}

const searchKeyword = ref("");
const inquiryData = ref<InquiryReport>({
  inquiryNo: "",
  title: "",
  status: "",
  deliveryStartDate: "",
  deliveryEndDate: "",
  inquiryType: "",
  inquiryDetailVOS: [],
});

const loading = ref(false);
// 商品显示状态Map
const productVisibilityMap = ref(new Map<string | number, boolean>());

// 搜索处理函数
const handleSearch = () => {
  const keyword = searchKeyword.value.trim().toLowerCase();
  
  // 重置所有商品的显示状态
  inqueryDetail.value.inquiryDetailVOS.forEach((product) => {
    const isVisible =
      !keyword || product.productName.toLowerCase().includes(keyword);
    productVisibilityMap.value.set(product.productId, isVisible);
  });
};

// 获取商品的显示状态
const getProductVisible = (product: any): boolean => {
  return productVisibilityMap.value.get(product.productId) ?? true;
};

const selectSupplier = (product: InquiryDetailVO, supplierName: string) => {
  // Implement supplier selection logic
  console.log(
    "Selected supplier:",
    supplierName,
    "for product:",
    product.productName
  );
};

const submitQuotation = () => {
  loading.value = true;
  const supplierList = inqueryDetail.value.inquiryDetailVOS.flatMap((detail) =>
    detail.inquiryProductSupplierListVOS.map((supplier) => ({
      inquiryDetailId: supplier.inquiryDetailId, // Assuming this is the correct ID to use
      inquiryAmount: supplier.inquiryAmount,
    }))
  );

  const data = {
    inquiryId: route.query.inquiryId,
    type: 1, // 0-暂存 1-确认提交
    inquiryPurchaseListRequestDTOList: supplierList,
  };

  InqueryAPI.fillPrice(data)
    .then((res) => {
      console.log(res);
      router.push({
        path: "/pms/purchase/inquery",
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

const statusMapClass = { // 0->询价中，1->已确定，2->已完成，3->已关闭
  0: "unwokring",
  1: "executing",
  2: "cancelled",
  3: "cancelled",
};

const route = useRoute();
const saveQuotation = () => {
  loading.value = true;
  const supplierList = inqueryDetail.value.inquiryDetailVOS.flatMap((detail) =>
    detail.inquiryProductSupplierListVOS.map((supplier) => ({
      inquiryDetailId: supplier.inquiryDetailId, // Assuming this is the correct ID to use
      pricingAmount: supplier.pricingAmount,
    }))
  );

  const data = {
    inquiryId: route.query.inquiryId,
    type: 0, // 0-暂存 1-确认提交
    inquiryPurchaseListRequestDTOList: supplierList,
  };

  InqueryAPI.fillPrice(data)
    .then((res) => {
      
      /*console.log(res);
      router.push({
        path: "/pms/inquery/inquery",
      });*/
      cancelQuotation();
    })
    .finally(() => {
      loading.value = false;
    });
};

const tagsViewStore = useTagsViewStore();

const navigateAndCloseCurrentTab = async (path: string) => {
  // 先删除当前页面的标签
  await tagsViewStore.delView(route);
  
  // 等待标签删除完成后再进行路由跳转
  nextTick(() => {
    router.push(path);
  });
};

const router = useRouter();
const cancelQuotation = () => {
  // Implement cancel logic
  /*router.push({
    path: "/pms/inquery/inquery",
  });*/
  navigateAndCloseCurrentTab("/pms/purchase/inquery");
};

const operationRecords = ref([]);
const getOperationRecords = () => {
  UserAPI.getOperationLogList({
    businessCode: route.query.inquiryCode,
    businessId: route.query.inquiryId,
  }).then((data) => {
    operationRecords.value = data;
  });
};
const parseTimeHandle = (time: string, type: string) => {
  return parseTime(time, type);
};
onMounted(() => {
  getOperationRecords();
});

</script>

<template>
  <div class="inquiry-detail" v-loading="loading">
    <el-card>
      <div class="card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="cancelQuotation"
        />
        <span class="code" @click="cancelQuotation">{{ t('purchaseInquiry.inqueryDetail.title') }}：{{ inqueryDetail.inquiryCode }}</span>
        <span
          class="contract status"
          :class="statusMapClass[inqueryDetail.inquiryStatus]"
        >
          {{ inqueryDetail.inquiryStatusStr }}
        </span>
      </div>

      <!-- 基本信息区域 -->
      <section class="basic-info">
        <div class="section-title card-title mb-20px">{{ t('purchaseInquiry.inqueryDetail.basicInfo') }}</div>
        <div class="info-grid">
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryDetail.deliveryPeriod') }}</label>
            <span class="card-form-text">
              {{ parseTimeHandle(new Date(inqueryDetail.startDeliveryDate), "{y}-{m}-{d}") }}至{{ parseTimeHandle(new Date(inqueryDetail.endDeliveryDate), "{y}-{m}-{d}") }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryDetail.inquiryType') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.inquiryTypeStr }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryDetail.name') }}</label>
            <span class="card-form-text">{{ inqueryDetail.title }}</span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryDetail.warehouse') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.warehouseName }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryDetail.createTime') }}</label>
            <span class="card-form-text">
              {{
                parseTimeHandle(
                  new Date(inqueryDetail.submitTime),
                  "{y}-{m}-{d} {h}:{i}:{s}"
                )
              }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryDetail.inquirySupplier') }}</label>
            <span class="card-form-text">{{ inqueryDetail.suppliers }}</span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryDetail.creator') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.createUserName }}
            </span>
          </div>
          <!-- 其他基本信息项 -->
        </div>
        <div class="card-bottomm-border mt-30px mb-30px"></div>
      </section>

      <!-- 询价商品列表 -->
      <section class="product-list">
        <div class="section-title card-title mb-20px">{{ t('purchaseInquiry.inqueryDetail.inquiryProducts') }}</div>
        <div class="search-bar">
          <el-input
            style="width: 500px"
            class="mr-12px"
            v-model="searchKeyword"
            :placeholder="t('purchaseInquiry.inqueryDetail.searchPlaceholder')"
            clearable
          />
          <el-button type="primary" @click="handleSearch">{{ t('purchaseInquiry.inqueryDetail.search') }}</el-button>
        </div>

        <!-- 商品项模板 -->
        <div
          class="product-item"
          v-for="product in inqueryDetail.inquiryDetailVOS"
          :key="product.productId"
          v-show="getProductVisible(product)"
        >
          <div class="product-info">
            <section class="flex w-100%">
              <img :src="product.productImg" class="product-image mr-12px" />
              <div class="product-details">
                <div class="product-name">{{ product.productName || '--' }}</div>
                <div class="flex w-100% gap-16px">
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryDetail.purchaseUnit') }}&nbsp;</span>
                    <span class="form-text">{{ product.unitName || '--' }}</span>
                  </div>
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryDetail.productBrand') }}&nbsp;</span>
                    <span class="form-text">{{ product.brandName || '--' }}</span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryDetail.remarkMessage') }}&nbsp;</span>
                    <span class="form-text">{{ product.remark || '--' }}</span>
                  </div>
                </div>
              </div>
              <div class="mr-24px">
                <span class="form-label">{{ t('purchaseInquiry.inqueryDetail.defaultSupplier') }}&nbsp;</span>
                <span class="form-text">{{ product.defaultSupplierName || '--' }}</span>
              </div>
              <div>
                <span class="form-label">{{ t('purchaseInquiry.inqueryDetail.lastPurchasePrice') }}&nbsp;</span>
                <span class="form-text">{{currencyFilter(product.lastPurchaseAmountCurrency)}}{{ product.lastPurchaseAmount || '--'}}</span>
              </div>
            </section>
            <div class="custom-border"></div>
          </div>

          <!-- 供应商报价列表 -->
          <!--
          
          0->询价中，1->已确定，2->已完成，3->已关闭  inquiryStatus
          询价单详情价格展示
          询价中：询价价格  inquiryAmount
          已确定：询价价格  inquiryAmount
          已完成：定价价格  pricingAmount
          已关闭：询价价格  inquiryAmount
          -->
          <div class="supplier-quotes">
            <div
              class="quote-item"
              v-for="supplier in product.inquiryProductSupplierListVOS"
              :key="supplier.supplierId"
            >
              <div class="supplier-name">
                <el-tooltip :content="supplier.supplierName">
                  <div class="ellipsis">{{supplier.supplierName}}</div>
                </el-tooltip>
              </div>
              <section class="price-block">
                <div class="currency">{{ currencyFilter(supplier.currency) }}</div>
                <div class="price">{{(inqueryDetail.inquiryStatus === 2 ? supplier.pricingAmount : supplier.inquiryAmount) || '--'}}</div>
              </section>
            </div>
            
          </div>
        </div>
      </section>
      <section>
        <div class="section-title card-title mb-20px">{{ t('purchaseInquiry.inqueryDetail.operationRecords') }}</div>
        <el-table :data="operationRecords" style="width: 100%" >
          <el-table-column prop="operationTime" :label="t('purchaseInquiry.inqueryDetail.operationTime')" width="280">
            <template #default="scope">
              {{ parseTime(scope.row.operationTime, "{y}-{m}-{d} {h}:{i}") }}
            </template>
          </el-table-column>
          <el-table-column prop="operationRemark" :label="t('purchaseInquiry.inqueryDetail.operationType')" />
          <el-table-column prop="operationName" :label="t('purchaseInquiry.inqueryDetail.operator')" />
        </el-table>
      </section>
      <!-- 底部按钮 -->
      <footer class="footer">
        <button class="cancel-btn" @click="cancelQuotation">{{ t('purchaseInquiry.inqueryDetail.cancel') }}</button>
      </footer>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.inquiry-detail {
  background: #f5f5f5;
  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;
    .code {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    .status {
      margin-left: 10px;
    }
  }

  .basic-info {
    background: #fff;
    padding: 20px 20px 0;
    border-radius: 8px;
    .info-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      .info-item {
        margin-bottom: 4px;
      }
    }
  }

  .product-list {
    background: #fff;
    padding: 0 20px;
    border-radius: 8px;

    .search-bar {
      margin-bottom: 20px;
    }

    .product-item {
      background: #f8f9fc;
      border-radius: 2px;
      border: 1px dashed #d7dbdf;
      padding: 24px;
      margin-bottom: 15px;

      .product-info {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .product-image {
          width: 80px;
          height: 80px;
          border-radius: 4px;
        }
        .product-details {
          flex: 1;
          .product-name {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #151719;
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }
        }
      }

      .supplier-quotes {
        /*display: grid;
        grid-template-columns: repeat(5, 1fr);*/
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 24px;
        .quote-item {
          width: calc(20% - 10px);
          flex-grow: 0;
          display: flex;
          align-items: center;
          gap: 10px;
          background: #E9EBEE;
          border-radius: 2px;
          border: 1px solid #D7DBDF;
          justify-content: space-between;
          padding: 6px 8px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #151719;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          .supplier-name {
            flex: 1;
            align-items: center;
            min-width: 0;
          }
          .price-block{
            width: 80px;
            display: flex;
            .currency{
            
            }
            .price{
            
            }
          }
        }
      }
    }
  }

  .footer {
    padding: 15px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

    .submit-btn {
      background: #762ADB;
      color: #fff;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }

    .save-btn {
      background: #fff;
      border: 1px solid #762ADB;
      color: #762ADB;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }

    .cancel-btn {
      background: #fff;
      border: 1px solid #ddd;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}
</style>
