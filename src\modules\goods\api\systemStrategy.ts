import request from "@/core/utils/request";

const SYSTEM_STRATEGY_BASE_URL = "/supply-biz-common/strategyTenant";

class SystemStrategyAPI {
  /**
   * 分页查询快速出库策略
   * @param queryParams 查询参数
   * @returns 分页结果
   */
  static getPageList(queryParams?: SystemStrategyPageQuery) {
    return request<any, SystemStrategyPageResult>({
      url: `${SYSTEM_STRATEGY_BASE_URL}/queryPageListAndMergeGlobalAndTenant`,
      method: "post",
      data: queryParams,
    });
  }
  /**
   * 保存快速出库策略
   * @param data 快速出库策略数据
   * @returns 响应结果
   */
  static save(data: SystemStrategyVO) {
    return request<any, string>({
      url: `${SYSTEM_STRATEGY_BASE_URL}/saveOrUpdate`,
      method: "post",
      data,
    });
  }


  /**
   * 查询页面页面下拉数据
   * @param queryParams 查询参数
   * @returns 分页结果
   */
  static getPageOption(queryParams: any) {
    return request<any, SystemStrategyPageResult>({
      url: `/supply-biz-common/strategyGlobal/queryStrategyModulePageEnumList?strategyModuleEnumCode=${queryParams?.strategyModuleEnumCode}`,
      method: "get",
      // data: queryParams,
    });
  }
}


/**
 * 原产地分页查询参数
 */
export interface SystemStrategyPageQuery {
  /** 当前页 */
  page?: number;
  /** 每页条数（最大1000） */
  limit?: number;
  /** 是否显示 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /*页面名称*/
  pageName?: string;
  /*字段名称*/
  modulePageFieldName?: string;
  /** 策略模块编码 */
  moduleCode?: string;
  /** 策略模块页面编码 */
  modulePageCode?: string;
}

/**
 * 策略分页结果对象
 */
export interface SystemStrategyPageResult {
  /** 当前页 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 数据列表 */
  records: SystemStrategyVO[];
}


/**
 * 快速出库策略视图对象
 */
export interface SystemStrategyVO {
  /** 是否显示 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /*页面名称*/
  pageName?: string;
  /*字段名称*/
  fieldsName?: string;
  /** 主键ID */
  id?: number;
}


/**
 * 启禁用状态枚举
 */
export enum EnableStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
}

/*
* 配置策略枚举
* */

export enum StrategyModuleEnum {
  /** 快速出库 */
  QUICK_OUTBOUND = 'quick_stock_out',
  /** 快速入库 */
  QUICK_INBOUND = 'quick_stock_in',
}


export default SystemStrategyAPI;
