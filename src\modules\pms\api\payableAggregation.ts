import request from "@/core/utils/request";

const BASE_URL = "/supply-pms";

class API {
  /**
   * 应付账单汇总列表
   * @param data
   */
  static getPageList(data: any) {
    return request({
      url: `${BASE_URL}/payableAccount/page`,
      method: "post",
      data: data,
    });
  }




  /**
   * 应付账单汇总详情
   * @param data
   */
  static queryDetail(data: any) {
    return request<any>({
      url: `${BASE_URL}/payableAccount/detail`,
      method: "post",
      data: data,
    });
  }



  /**
   * 付款
   * @param data
   */
  static savePayment(data: any) {
    return request<any>({
      url: `${BASE_URL}/payableAccountPaymentRecord/save`,
      method: "post",
      data: data,
    });
  }

  /**
   * 付款记录列表
   * @param data
   */
  static queryPaymentRecordList(data: any) {
    return request({
      url: `${BASE_URL}/payableAccountPaymentRecord/page`,
      method: "post",
      data: data,
    });
  }
  /**
   * 采购单列表
   * @param data
   */
  static queryPurchaserOrderList(data: any) {
    return request({
      url: `${BASE_URL}/purchaserOrderStatisticsDetail/queryPageList`,
      method: "post",
      data: data,
    });
  }
  /**
   * 采购单列表合计
   * @param data
   */
  static queryPurchaserOrderTotal(data: any) {
    return request({
      url: `${BASE_URL}/purchaserOrderStatisticsDetail/total`,
      method: "post",
      data: data,
    });
  }

  /**
   * 支付方式
   */
  static queryPaymentMethodList(data: any) {
    return request({
      url: `/supply-biz-common/paymentMethods/queryList`,
      method: "get",
      params: data
    })
  }

}


export default API;
