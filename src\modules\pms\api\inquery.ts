import request from "@/core/utils/request";
import {
  SupplierListParams,
  InquiryDetailDTO,
  SaveInquiryProductListDTO,
  SaveInquirySupplierListDTO,
  PageInquery,
  FillQuotationParams,
  GoodParams
} from "../types/inquiry";
const PURCHASE_BASE_URL = "/supply-pms";
class InqueryAPI {
  /**
   * 分页展示新增询价单、采购需求、采购任务及采购单选择的商品
   *
   * @param queryParams 查询参数
   */
  static getGoods(queryParams: GoodParams) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/queryInquiryPurchaseChooseProductListPage`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 新增询价单添加商品供应商下拉框
   */
  /**
   * warehouseId integer 仓库id 必需
   * inquiryType integer 询价类型：0->国内供应，1->国外直采 必需
   */
  static getSupplierList(params: SupplierListParams) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/supplierSelect`,
      method: "get",
      params,
    });
  }

  // 新增询价单第二步供应商信息【获取供应商报价信息】
  static getSupplierInqueryInfo(data: InquiryDetailDTO) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/saveInquirySecondStepOfSupplierInfo`,
      method: "post",
      data,
    });
  }

  // 新增询价单提交
  static saveInquery(data: SaveInquiryProductListDTO) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/saveInquiry`,
      method: "post",
      data,
    });
  }

  // 分页查询询价单列表
  static getInqueryList(data: PageInquery) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/listPage`,
      method: "post",
      data,
    });
  }

  // 询价单详情
  static getInqueryDetail(inquiryId: number) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/detail`,
      method: "get",
      params: {
        inquiryId,
      },
    });
  }

  // 填写报价
  static fillQuotation(data: FillQuotationParams) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/fillQuotation`,
      method: "post",
      data,
    });
  }

  // 填写定价
  static fillPrice(data: FillQuotationParams) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/fillPrice`,
      method: "post",
      data,
    });
  }

  // 关闭询价单
  static closeInquiry(inquiryId: number) {
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/closeInquiry`,
      method: "get",
      params: {
        inquiryId,
      },
    });
  }

  static querySupplierList(params: any) {
    /*{
      inquiryType 询价类型：0->国内供应，1->国外直采
      keywords
      warehouseId
    }*/
    return request({
      url: `${PURCHASE_BASE_URL}/inquiry/supplierSelect`,
      method: "get",
      params,
    });
  }
}

export default InqueryAPI;
