<template>
  <div class="voucher-audit">
    <div class="audit-title">凭证审核</div>
    <div class="audit-content">
      <div class="audit-daterange">
        <span class="piker-label">凭证日期</span>
        <el-date-picker
          v-if="showDatePicker"
          style="width: 541px;"
          v-model="dateRange"
          type="daterange"
          format="YYYY-MM-DD"
          value-format="YYYYMMDD"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
          :disabled-date="disabledDate"
          />
      </div>
      <div class="audit-notice">温馨提示：点击操作按钮即为对所选日期内的凭证做批量审核或反审核。</div>
      <div class="audit-btn">
        <el-button v-hasPerm="['finance:voucheraudit:audit']" type="primary" @click="auditHandler(1)">审核</el-button>
        <el-button v-hasPerm="['finance:voucheraudit:auditback']" type="primary" plain @click="auditHandler(3)">反审核</el-button>
      </div>
    </div>
    <el-dialog v-model="showDialog" :title="`${statusLabel}结果`" width="480" class="finance-moudle">
      <div class="dialog-content">
        <div class="content-count">执行：{{ count }}张</div>
        <el-table :data="dialogTableData" style="width: 100%" border v-if="count > 0">
          <el-table-column label="借方金额" prop="debitAmount" align="center"/>
          <el-table-column label="贷方金额" prop="creditAmount" align="center"/>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false" type="primary">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang='ts' setup>
import { ref, onMounted } from 'vue';
import { voucher } from '@/modules/finance/api/index';
import {useRouter} from "vue-router";
import API from '@/modules/finance/api/accountStatementApi';
import moment from 'moment';
const router = useRouter();
const dateRange = ref<string[]>([]);
const showDialog = ref(false);
const statusLabel = ref('');
const count = ref(0);
const dialogTableData = reactive([
  {
    debitAmount: 0,
    creditAmount: 0
  }
])
/* 会计期间范围 */
const translateMonth = (mm: number) => {
  const dateStr = String(mm)
  const year = dateStr.slice(0, 4);
  const month = dateStr.slice(4, 6);
  return `${year}-${month}`;
}
const dateRangeStart = ref('');
const dateRangeEnd = ref('');
const currentPeriodYearMonth = ref('');
const showDatePicker = ref(false);
const disabledDate = (time: Date) => {
  let dateRangeStartPrev = moment(dateRangeStart.value).subtract(1, 'days').format('YYYY-MM-DD');
  return time.getTime() < new Date(dateRangeStartPrev).getTime() || time.getTime() > new Date(dateRangeEnd.value).getTime()
}
const loadPeriodHandler = async () => {
  showDatePicker.value = false;
  try {
    const res = await API.getPeriodScope({
      queryType: 3
    });
    const periodYearMonthStart = translateMonth(res.periodYearMonthStart);
    dateRangeStart.value = `${periodYearMonthStart}-01`
    let periodYearMonthEnd = translateMonth(res.periodYearMonthEnd);
    periodYearMonthEnd = `${periodYearMonthEnd}-01`;
    dateRangeEnd.value = moment(periodYearMonthEnd).endOf("month").format("YYYY-MM-DD");
    currentPeriodYearMonth.value = translateMonth(res.currentPeriodYearMonth);
    const currentPeriodYearMonthStart = `${currentPeriodYearMonth.value}-01`;
    const currentPeriodYearMonthEnd = moment(currentPeriodYearMonthStart).endOf("month").format("YYYYMMDD");
    dateRange.value = [currentPeriodYearMonthStart.replace(/-/g, ''), currentPeriodYearMonthEnd];
  } catch (error) {
    if(error.message === '用户未设置账套!') {
      setTimeout(() => {
        router.push('/finance/generalLedger/setAccounts');
      }, 1000);
    }
  }
  showDatePicker.value = true;
};
const auditHandler = (status: number) => {
  if (dateRange.value.length === 0) {
    ElMessage.warning('请先选择凭证日期');
    return;
  }
  const voucherDateStart = dateRange.value[0];
  const voucherDateEnd = dateRange.value[1];
  statusLabel.value = status === 1 ? '审核' : '反审核';
  ElMessageBox.confirm(`确定要对${voucherDateStart}至${voucherDateEnd}期间的凭证进行${statusLabel.value}吗`, '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const params = {
      voucherDateStart,
      voucherDateEnd,
      auditStatus: status
    }
    voucher.updateStatusVoucherByDate(params).then((res) => {
      count.value = res.count;
      dialogTableData[0].debitAmount = res.debitAmount;
      dialogTableData[0].creditAmount = res.creditAmount;
      ElMessage.success(`${statusLabel.value}成功!`);
      showDialog.value = true;
    }).catch((err) => {

    })
    }).catch(() => {
      ElMessage.info('已取消');
    })
}
onMounted(() => {
  loadPeriodHandler();
})
</script>
<style scoped lang='scss'>
.voucher-audit {
  height: 100%;
  background-color: #FFFFFF;
  padding: 0 30px;
  .audit-title {
    padding: 60px 0 40px 0;
    border-bottom: 1px solid #E5E7F3;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 30px;
    color: #151719;
    line-height: 42px;
    text-align: center;
  }
  .audit-content {
    width: 634px;
    margin: 0 auto;
    padding-top: 139px;
    .audit-daterange {
      .piker-label {
        display: inline-block;
        width: 84px;
        text-align: right;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #151719;
        line-height: 20px;
        margin-right: 9px;
      }
    }
    .audit-notice {
      margin-top: 26px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #FF9C00;
      line-height: 17px;
      padding-left: 100px;
    }
    .audit-btn {
      margin-top: 74px;
      text-align: center;
    }
  }
  .content-count {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #151719;
    line-height: 20px;
    margin-bottom: 12px;
  }
}
</style>
