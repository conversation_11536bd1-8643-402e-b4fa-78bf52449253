export default {
  supplierManagement: {
    label: {
      supplierName: "供应商名称",
      status: "状态",
      phone: "手机号",
      supplierCode: "供应商编码",
      curator: "负责人",
      loginAccount: "登录账号",
      supplierType: "供应商类型",
      country: "国家",
      area: "地区",
      supplierAbbreviation: "供应商简称",
      address: "详细地址",
      addressW: "地址",
      contactsPerson: "联系人",
      purchaser: "采购员",
      supplyGoods: "可供应商品",
      mobileNumber: "手机号码",
      basicInformation: "基本信息",
      contacts: "联系人信息",
      associatedWarehouse: "关联仓库",
      warehouseName: "仓库名称",
      contactInformation: "联系方式",
      contractInformation: "合同信息",
      contractName: "合同名称",
      signatory: "签订对方",
      contractCode: "合同编码",
      contractPeriod: "合同期限",
      createTime: "签订日期",
      financeInformation: "财务信息",
      payeeName: "收款方姓名",
      payeeAccount: "收款方账户",
      receivingBank: "收款开户行",
      corporateName: "公司名称",
      companyAddress: "公司地址",
      creditCode: "统一社会信用代码",
      financialContactPerson: "财务联系人",
      billingType: "开票类型",
      settlementCurrency: "结算币种",
      businessLicense: "营业执照",
      attachmentUpload: "附件上传",
      settlementCycle: "结算周期",
      paymentCycle: "出账周期",
      accountInformation: "账号信息",
      password: "登录密码",
      relatedContracts: "关联合同",
      generalTaxpayer: "一般纳税人",
      smallScaleTaxpayer: "小规模纳税人",
      individualBusiness: "个体工商户",
      specialTicket: "专票",
      generalTicket: "普票",
      RMB: "人民币",
      dollar: "美元",
      month: "月",
      week: "周",
      to: "至",
      areaCode: "区号",
      close: "关闭",
      open: "开启",
    },
    button: {
      addSupplier: "添加供应商",
      editSupplier: "编辑供应商",
      supplierDetails: "供应商详情",
      resetPassword: "重置密码",
      goodTypeBtn: "选择商品分类",
      cancel: "取消",
      submit: "提交",
      warehouseBtn: "添加关联仓库",
      contractsBtn: "选择关联合同",
      close: "关闭",
      open: "开启",
      updatePassword: "重置密码",
    },
    title: {
      addUnitTitle: "添加基本单位",
      editUnitTitle: "编辑基本单位",
      addGoods: "选择商品分类",
      addWarehouse: "选择关联仓库",
      addContracts: "选择关联合同",
      addSupplier: "添加供应商",
      editSupplier: "编辑供应商",
      supplierDetails: "供应商详情",
    },
    message: {
      deleteTips: "确定删除此供应商",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      addSucess: "添加成功",
      disabledSucess: "禁用成功",
      editSucess: "编辑成功",
      deleteNotTips: "该供应商处于启用状态，不能删除！",
      deleteCancel: "已取消删除",
      updateStatusTips: "确定禁用此供应商",
      accountTip: "保存后生成账号",
      pictureTip: "800*1200px或等比例图片",
      goodsMountTip: "商品分类数量不能大于30，已选择",
    },
    rules: {
      supplierNameTip: "请输入供应商名称",
      supplierTypeTip: "请选择供应商类型",
      countryTip: "请选择国家",
      areaTip: "请选择地区",
      supplierAbbreviationTip: "请输入供应商简称",
      addressTip: "请输入详细地址",
      purchaserTip: "请选择采购员",
      supplyGoodsTip: "请选择商品分类",
      phoneTip: "请输入手机号",
      curatorTip: "请输入负责人",
      associatedWarehouseTip: "请选择关联仓库",
      relatedContractsTip: "请选择关联合同",
      payeeNameTip: "请输入收款方姓名",
      payeeAccountTip: "请输入收款方账户",
      receivingBankTip: "请输入收款开户行",
      corporateNameTip: "请输入公司名称",
      companyAddressTip: "请输入公司地址",
      creditCodeTip: "请输入统一社会信用代码",
      financialContactPersonTip: "请输入财务联系人",
      billingTypeTip: "请选择开票类型",
      settlementCurrencyTip: "请选择结算币种",
      businessLicenseTip: "请上传营业执照",
      attachmentUploadTip: "请上传附件",
      paymentCycleTip: "请选择出账周期",
      passwordTip: "请输入登录密码",
      loginAccountTip: "请输入登录账号",
      areaTips: "请输入区号",
      nameFomart: "可输入中文、英文和数字",
      creditCodeFomart: "可输入英文和数字",
      mobileLengthTips: "手机号码不能少于4位",
    },
  },
};
