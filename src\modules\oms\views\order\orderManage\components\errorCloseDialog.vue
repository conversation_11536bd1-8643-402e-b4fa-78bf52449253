<template>
  <el-dialog v-model="errorCloseDialog" width="600px" :title="dialogTitle">
    <div class="mb20px">{{ titleLabel }}</div>
    <el-form
      :model="errorForm"
      :rules="errorRules"
      ref="errorFormRef"
      label-width="108px"
      label-position="right"
    >
      <el-row class="flex-center-start">
        <el-col :span="24">
          <el-form-item label-width="0px" prop="remark">
            <el-input
              :rows="4"
              type="textarea"
              show-word-limit
              v-model="errorForm.remark"
              :placeholder="placeholderLabel"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
        <span class="dialog-footer">
          <el-button @click="errorCloseHandle">{{$t('common.cancel')}}</el-button>
          <el-button type="primary" @click="errorCloseConfirm">{{$t('common.confirm')}}</el-button>
        </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import OrderAPI, {ErrorOrderFrom} from "@/modules/oms/api/order";


const props = defineProps({
  dialogType: {
    type: String,
    required: true,
    default: 'error'
  },
  visible: {
    type: Boolean,
    required: true,
    default: false
  }
});
const { t } = useI18n();
const emit = defineEmits(['update:visible','confirm']);
const errorCloseDialog = ref(false)
const errorFormRef = ref('')

watch(() => props.visible, (val) => {
  errorCloseDialog.value = true;
},{immediate:true})

const dialogTitle = computed(() => {
  if(props.dialogType === 'errorClose'){
    return  t('omsOrder.button.errorClose')
  }
  if(props.dialogType === 'reject'){
    return  t('omsOrder.button.reject')
  }
})

const placeholderLabel = computed(() => {
  if(props.dialogType === 'errorClose'){
    return  t('omsOrder.placeholder.errorCloseMsg')
  }
  if(props.dialogType === 'reject'){
    return  t('omsOrder.placeholder.rejectMsg')
  }
})
const titleLabel = computed(() => {
  if(props.dialogType === 'errorClose'){
    return  t('omsOrder.title.errorClose')
  }
  if(props.dialogType === 'reject'){
    return  t('omsOrder.title.rejectTitle')
  }
})

const errorForm = reactive<ErrorOrderFrom>({
  remark: '',
})
const errorRules = reactive({
  remark: [{ required: true, message:
      props.dialogType === 'errorClose' ?  t("omsOrder.rules.errorReason") : t("omsOrder.rules.rejectReason"),
    trigger: ["blur","change"] }],
})


function errorCloseHandle() {
  emit('update:visible', false)
  emit('close', )
}

function errorCloseConfirm() {
  errorFormRef.value?.validate((valid) => {
    if (!valid) return;
    emit('confirm', Object.assign({type: props.dialogType}, errorForm))
  })
}



</script>



<style scoped lang="scss">

</style>
