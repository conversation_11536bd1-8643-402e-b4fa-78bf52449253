export default {
  purchaseTasks: {
    label: {
      createTime: "创建日期",
      startTime: "开始日期",
      endTime: "结束日期",
      to: "至",
      supplier: "供应商",
      purchaser: "采购员",
      status: "状态",
      classification: "分类",
      purchaseGoods: "采购商品",
      procurementReqCode: "采购需求编码",
      warehouseName: "仓库名称",
      warehouse: "仓库",
      purchaseQuantity: "采购数量",
      purchaseUnitPrice: "采购单价",
      purchaseAmount: "采购金额",
      expectedDeliveryDate: "期望收货日期",
      creator: "制单人",
      generated: "已生成",
      notGenerated: "未生成",
    },
    button: {
      addPurchaseTask: "新建采购任务",
      generatePurchaseOrder: "生成采购单",
      addGood: "添加商品",
    },
    title: {
      addPurchasePersonnelUnitTitle: "添加采购员",
      productSpecificationsTitle: "选择商品规格",
    },
    placeholder: {
      primaryClassification: "一级分类",
      secondaryClassification: "二级分类",
      thirdClassification: "三级分类",
    },
    message: {
      deleteTips: "确定删除此采购员吗？",
      deleteConcel: "已取消删除！",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      addSucess: "添加成功",
      editSucess: "编辑成功",
    },
    rules: {
      userCode: "请选择人员",
    },
  },
};
