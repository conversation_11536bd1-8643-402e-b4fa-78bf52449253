<template>
  <el-drawer :model-value="visible" :title="$t('purchaseRequirements.title.addProduct')" :close-on-click-modal="false"
    :size="800" @close="close">
    <el-form :model="goodsQueryParams" ref="goodsQueryFormRef">
      <el-row :gutter="20">
<!--        <el-col :span="12">
          <el-form-item :label="$t('purchaseRequirements.label.product')" prop="productName">
            <el-input v-model="goodsQueryParams.keywords"
              :placeholder="$t('purchaseRequirements.placeholder.productInput')" clearable />
          </el-form-item>
        </el-col>-->
        <el-col :span="12">
          <el-form-item :label="$t('purchaseRequirements.label.productName')">
            <el-input
              v-model="goodsQueryParams.productName"
              :placeholder="$t('purchaseRequirements.placeholder.enterProductName')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('purchaseRequirements.label.productCode')">
            <el-input
              v-model="goodsQueryParams.productCode"
              :placeholder="$t('purchaseRequirements.placeholder.enterProductCode')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('purchaseRequirements.label.productCategory')" prop="category">
            <el-cascader v-model="goodsQueryParams.category" :options="classificationList" clearable :props="{
                value: 'id',
                label: 'categoryName',
                children: 'children',
                checkStrictly: true, // 允许选择任意层级
              }" />
          </el-form-item>
        </el-col>

      </el-row>
      <el-row>
        <el-col :span="24" style="text-align: right">
          <el-button type="primary" @click="handleQuery">
            {{ $t('purchaseRequirements.button.search') }}
          </el-button>
          <el-button @click="handleResetQuery">
            {{ $t('purchaseRequirements.button.reset') }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <div class="selected-info">
      {{ $t('purchaseRequirements.table.selected') }}
      <span class="selected-count">{{ multipleSelection.length }}</span>
      {{ $t('purchaseRequirements.table.items') }}
    </div>

    <el-table ref="dataTableRef" v-loading="productLoading" :data="productList"
      @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('purchaseRequirements.label.product')" min-width="200">
        <template #default="scope">
          <div class="product-info">
<!--            <img :src="scope.row.productImg" alt="商品图片" />-->
            <div class="product-info-content">
              <div class="product-info-content-item code">
                {{ $t("purchaseRequirements.label.productCode") }}: {{ scope.row.productCode }}
              </div>
              <div class="product-info-content-item name">
                {{ $t("purchaseRequirements.label.productName") }}: {{ scope.row.productName }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('purchaseRequirements.label.purchaseUnit')" prop="unitName" min-width="120" />
      <el-table-column :label="$t('purchaseRequirements.label.productCategory')" prop="productCategoryFullName"
        min-width="100" align="center" />
    </el-table>

    <div class="pagination-container">
      <el-pagination v-model:current-page="goodsQueryParams.page" v-model:page-size="goodsQueryParams.limit"
        :total="productListTotal" :page-sizes="[20, 50, 100, 200]" layout="total, sizes , prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">
          {{ $t("purchaseRequirements.button.cancel") }}
        </el-button>
        <el-button type="primary" :loading="submitLoading" :disabled="!multipleSelection.length" @click="submitForm">
          {{ $t("purchaseRequirements.button.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import InqueryAPI from "@pms/api/inquery";
import ProductCategoryAPI from "@pms/api/productCategory";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  deliveryDate: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();

let roleFormRef = ref();
const goodsQueryFormRef = ref(ElForm);
const productListTotal = ref(0);
const goodsQueryParams = reactive({
  page: 1,
  limit: 20,
  keywords: "",
  category: [], // 商品分类
  firstCategoryId: "", // 一级分类Id
  secondCategoryId: "", // 二级分类Id
  thirdCategoryId: "", // 三级分类Id
  productName: "",
  productCode: "",
});
const classificationList = ref([]);
const submitLoading = ref(false);

const productLoading = ref(false);
const productList = ref([]);
const multipleSelection = ref([]);

const productOptions = ref([]); // 商品选项
const categoryOptions = ref([]); // 分类选

const getCategoryList = () => {
  ProductCategoryAPI.queryCategoryTreeList({})
    .then((data) => {
      classificationList.value = data;
    })
    .finally(() => { });
};
const getTime = (date) => {
  return new Date(date).getTime();
};

const queryGoodList = () => {
  InqueryAPI.getGoods({
    page: goodsQueryParams.page,
    limit: goodsQueryParams.limit,
    chooseType: 3, // 1-新增询价单选择产品 2-新增采购单选择商品 3-新增采购需求和采购任务选择商品
    warehouseId: props.formData.warehouseId,
    startExpectedDeliveryDate: new Date(props.deliveryDate).getTime(), //props.deliveryDate,
    inquiryType: 1, //	询价类型：0->国内供应，1->国外直采
    warehouseName: props.formData.warehouseName,
    ...goodsQueryParams,
  }).then((res) => {
    productList.value = res.records;
    productListTotal.value = parseInt(res.total) || 0;
  });
};
function close() {
  emit("update:visible", false);
  reset();
}

function reset() {
  goodsQueryFormRef.value.clearValidate();
  goodsQueryFormRef.value.resetFields();
}

function handleSelectionChange(val: any) {
  multipleSelection.value = val;
  console.log("handleSelectionChange", multipleSelection.value);
}

function handleQuery() {
  goodsQueryParams.firstCategoryId = "";
  goodsQueryParams.secondCategoryId = "";
  goodsQueryParams.thirdCategoryId = "";
  if (goodsQueryParams.category?.length) {
    goodsQueryParams.firstCategoryId = goodsQueryParams.category[0];
    goodsQueryParams.secondCategoryId = goodsQueryParams.category[1];
    goodsQueryParams.thirdCategoryId = goodsQueryParams.category[2];
  }
  queryGoodList();
}

function handleResetQuery() {
  goodsQueryParams.page = 1;
  goodsQueryParams.keywords = "";
  goodsQueryParams.category = [];
  goodsQueryParams.firstCategoryId = "";
  goodsQueryParams.secondCategoryId = "";
  goodsQueryParams.thirdCategoryId = "";
  goodsQueryParams.productName = "";
  goodsQueryParams.productCode = "";
  queryGoodList();
}

function submitForm() {
  emit("onSubmit", multipleSelection.value);
  close();
}

// 获取商品选项
const getProductOptions = async () => {
  // TODO: 调用API获取商品选项
};

// 获取分类选项
const getCategoryOptions = async () => {
  // TODO: 调用API获取分类选项
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  goodsQueryParams.limit = val;
  handleQuery();
};

// 当前页改变
const handleCurrentChange = (val: number) => {
  goodsQueryParams.page = val;
  handleQuery();
};

// 初始化
onMounted(() => {
  queryGoodList();
  // getProductOptions();
  getCategoryList();
});

defineExpose({
  queryGoodList,
});
</script>

<style scoped lang="scss">
.selected-info {
  margin: 16px 0;
  color: #606266;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #52585f;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  margin-bottom: 10px;

  .selected-count {
    color: #762ADB ;
  }
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.product-info {
  display: flex;
  align-items: center;

  img {
    width: 64px;
    height: 64px;
    border-radius: 4px;
  }

  .product-info-content {
    margin-left: 10px;

    .product-info-content-item {
      &.code {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #90979e;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      &.name {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #151719;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>
