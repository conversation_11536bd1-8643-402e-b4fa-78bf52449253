<template>
  <div class="app-container">
    <div class="addProfitAndLoss">
      <div class="page-title">
        <div @click="handleClose()" class="cursor-pointer mr8px">
          <el-icon>
            <Back />
          </el-icon>
        </div>
        <div class="purchase-title">
          <span v-if="type == 'add'">
            {{ $t("profitAndLossManagement.button.addMaterial") }}
          </span>
          <span v-else>
            {{ $t("profitAndLossManagement.label.profitLossCode") }}：{{
              form.profitLossCode
            }}
          </span>
        </div>
        <!--  <div class="purchase" v-if="type == 'detail'">
          <div class="purchase">
            <div
              class="purchase-status purchase-status-color0"
              v-if="form.status == 0"
            >
              {{ t("profitAndLossManagement.statusList.draft") }}
            </div>
            <div
              class="purchase-status purchase-status-color2"
              v-if="form.status == 1"
            >
              {{ t("profitAndLossManagement.statusList.dealWidth") }}
            </div>
            <div
              class="purchase-status purchase-status-color3"
              v-if="form.status == 2"
            >
              {{ t("profitAndLossManagement.statusList.finish") }}
            </div>
          </div>
        </div> -->
      </div>
      <!-- <div
        class="grad-row"
        style="position: absolute; top: 15px; right: 30px"
        v-if="type == 'edit'"
      >
        <span class="el-form-item__label">
          {{ $t("profitAndLossManagement.label.createUserName") }}：
          <span class="el-form-item__content">{{ form.createUserName }}</span>
        </span>
        <span class="el-form-item__label">
          {{ t("profitAndLossManagement.label.createTime") }}：
          <span class="el-form-item__content">
            {{ parseDateTime(form.createTime, "dateTime") }}
          </span>
        </span>
      </div> -->
      <div class="page-content">
        <el-form :model="form" ref="formRef" label-width="110px" label-position="right">
          <div class="title-lable">
            <div class="title-content">
              {{ $t("profitAndLossManagement.label.basicInformation") }}
            </div>
          </div>
          <div>
            <!--  disabled="form.status === 0" 从pda创建的损益单，不允许修改损益类型 和盘点单号 -->
            <!--  <template v-if="type == 'add' || type == 'edit'">
              <el-row>
                <el-col :span="8">
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.profitLossType')"
                    prop="profitLossType"
                    :rules="[
                      {
                        required: true,
                        message: t(
                          'profitAndLossManagement.rules.profitLossType'
                        ),
                        trigger: ['change', 'blur'],
                      },
                    ]"
                  >
                    <el-select
                      v-model="form.profitLossType"
                      :placeholder="$t('common.placeholder.selectTips')"
                      clearable
                    >
                      <el-option
                        v-for="item in profitAndLossTypeList"
                        :key="item.typeId"
                        :label="item.typeName"
                        :value="item.typeId"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-if="form.profitLossType == 0">
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.checkCode')"
                    :rules="[
                      {
                        required: true,
                        message: t('profitAndLossManagement.rules.checkCode'),
                        trigger: ['change', 'blur'],
                      },
                    ]"
                    prop="checkCode"
                  >
                    <el-select
                      v-model="form.checkCode"
                      :placeholder="$t('common.placeholder.selectTips')"
                      clearable
                      filterable
                      @change="changeCheckCode(null)"
                      class="!w-[256px]"
                    >
                      <el-option
                        v-for="item in checkCodeList"
                        :key="item.checkCode"
                        :label="item.checkCode"
                        :value="item.checkCode"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-if="form.profitLossType == 0">
                  <el-form-item
                    :label="
                      $t('profitAndLossManagement.label.warehouseAreaCode')
                    "
                    prop="warehouseAreaCode"
                  >
                    <el-select
                      v-model="form.warehouseAreaCode"
                      :placeholder="$t('common.placeholder.selectTips')"
                      clearable
                      filterable
                      disabled
                      class="!w-[256px]"
                    >
                      <el-option
                        v-for="item in outWarehouseAreaListAll"
                        :key="item.areaCode"
                        :label="`${item.areaName} | ${item.areaCode}`"
                        :value="item.areaCode"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="type == 'edit'">
                <el-col :span="8">
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.receiptStatus')"
                  >
                    <span v-if="form.receiptStatus == 0">
                      {{ $t("profitAndLossManagement.claimTypeList.no") }}
                    </span>
                    <span v-if="form.receiptStatus == 1">
                      {{ $t("profitAndLossManagement.claimTypeList.yes") }}
                    </span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    :label="
                      $t('profitAndLossManagement.label.handleUserNameCopy')
                    "
                  >
                    <span>{{ form.handleUserName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.handleTimeCopy')"
                  >
                    <span>
                      {{ parseDateTime(form.handleTime, "dateTime") }}
                    </span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="16">
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.remark')"
                    prop="remark"
                  >
                    <el-input
                      :rows="1"
                      type="textarea"
                      show-word-limit
                      v-model="form.remark"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="200"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template> -->
            <template v-if="type == 'detail'">
              <el-row>
                <el-col :span="6">
                  <!-- 损益单号 -->
                  <el-form-item :label="$t('profitAndLossManagement.label.profitLossCode')" prop="profitLossCode">
                    <span>{{ form.profitLossCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">

                  <!-- 损益类型 -->
                  <el-form-item :label="$t('profitAndLossManagement.label.profitLossType')" prop="profitLossType">
                    <!-- <span v-if="form.profitLossType == 0">
                      {{
                        $t(
                          "profitAndLossManagement.profitAndLossTypeList.inventoryCheck"
                        )
                      }}
                    </span>
                    <span v-if="form.profitLossType == 1">
                      {{
                        $t(
                          "profitAndLossManagement.profitAndLossTypeList.singleProduct"
                        )
                      }}
                    </span>
                    <span v-if="form.profitLossType == 2">
                      {{
                        $t(
                          "profitAndLossManagement.profitAndLossTypeList.ysnCode"
                        )
                      }}
                    </span> -->
                    <span v-if="form.profitLossType == 3">
                      {{
                        $t(
                          "profitAndLossManagement.profitAndLossTypeList.fastProfitAndLoss"
                        )
                      }}
                    </span>
                  </el-form-item>
                </el-col>
                <!-- 盘点单号 -->
                <!--  <el-col :span="6" v-if="form.profitLossType == 0">
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.checkCode')"
                    prop="checkCode"
                  >
                    <span>{{ form.checkCode || "-" }}</span>
                  </el-form-item>
                </el-col> -->

                <!-- YSN损益 -->
                <!-- <el-col :span="6" v-if="form.profitLossType == 2">
                  <el-form-item
                    :label="
                      $t('profitAndLossManagement.label.warehouseAreaName')
                    "
                  >
                    <span>
                      {{ form.warehouseAreaCode }} |
                      {{ form.warehouseAreaName }}
                    </span>
                  </el-form-item>
                </el-col> -->
                <!-- 创建人 -->
                <el-col :span="6">
                  <el-form-item :label="$t('profitAndLossManagement.label.createUserName')" prop="createUserName">
                    <span>{{ form.createUserName }}</span>
                  </el-form-item>
                </el-col>
                <!-- 创建时间 -->
                <el-col :span="6">
                  <el-form-item :label="$t('profitAndLossManagement.label.createTime')">
                    <span>
                      {{ parseDateTime(form.createTime, "dateTime") }}
                    </span>
                  </el-form-item>
                </el-col>

                <!--  0=盘点单损益 1=单包装商品损益 2=YSN损益：盘点单损益 -->
                <!--  <el-col :span="6" v-if="form.profitLossType == 0">
                  <el-form-item
                    :label="
                      $t('profitAndLossManagement.label.warehouseAreaName')
                    "
                  >
                    <span>
                      {{ form.warehouseAreaCode }} |
                      {{ form.warehouseAreaName }}
                    </span>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="form.profitLossType == 1">
                  <el-form-item
                    :label="
                      $t('profitAndLossManagement.label.warehouseAreaName')
                    "
                  >
                    <span>
                      {{ form.warehouseAreaCode }} |
                      {{ form.warehouseAreaName }}
                    </span>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="form.profitLossType == 3">
                  <el-form-item
                    :label="
                      $t(
                        'profitAndLossManagement.label.beforeProfitLossTotalQtyCopy'
                      )
                    "
                  >
                    <span>{{ form.beforeProfitLossTotalQty }}</span>
                  </el-form-item>
                </el-col> -->
                <!-- </el-row> -->
                <!--  0=盘点单损益 1=单包装商品损益 2=YSN损益：盘点单损益 -->
                <!-- <template v-if="form.profitLossType == 0">
                  <el-col :span="6">
                    <el-form-item
                      :label="
                        $t('profitAndLossManagement.label.beforeProfitLossQty')
                      "
                    >
                      <span>{{ form.beforeProfitLossQty }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :label="
                        $t(
                          'profitAndLossManagement.label.afterProfitLossQtyCopy'
                        )
                      "
                    >
                      <span>{{ form.afterProfitLossQty }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :label="
                        $t(
                          'profitAndLossManagement.label.beforeProfitLossTotalQty'
                        )
                      "
                    >
                      <span>{{ form.beforeProfitLossTotalQty }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :label="
                        $t(
                          'profitAndLossManagement.label.afterProfitLossTotalQty'
                        )
                      "
                    >
                      <span>{{ form.afterProfitLossTotalQty }}</span>
                    </el-form-item>
                  </el-col>
                </template> -->
                <!--  0=盘点单损益 1=单包装商品损益 2=YSN损益： 盘点单损益、单包装商品损益 -->
                <!-- <template> -->
                <!--  <el-col
                  :span="6"
                  v-if="form.profitLossType == 0 || form.profitLossType == 2"
                >
                  <el-form-item
                    :label="
                      $t('profitAndLossManagement.label.increaseProfitLossQty')
                    "
                  >
                    <span>{{ form.increaseProfitLossQty }}</span>
                  </el-form-item>
                </el-col>
                <el-col
                  :span="6"
                  v-if="form.profitLossType == 0 || form.profitLossType == 2"
                >
                  <el-form-item
                    :label="
                      $t('profitAndLossManagement.label.decreaseProfitLossQty')
                    "
                  >
                    <span>{{ form.decreaseProfitLossQty }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="form.profitLossType == 3">
                  <el-form-item
                    :label="
                      $t(
                        'profitAndLossManagement.label.afterProfitLossTotalQtyCopy'
                      )
                    "
                  >
                    <span>{{ form.afterProfitLossTotalQty }}</span>
                  </el-form-item>
                </el-col> -->
                <!--  <el-col :span="6">
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.receiptStatus')"
                    prop="receiptStatus"
                  >
                    <span v-if="form.receiptStatus == 0">
                      {{ $t("profitAndLossManagement.claimTypeList.no") }}
                    </span>
                    <span v-if="form.receiptStatus == 1">
                      {{ $t("profitAndLossManagement.claimTypeList.yes") }}
                    </span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    :label="
                      $t('profitAndLossManagement.label.handleUserNameCopy')
                    "
                    prop="handleUserName"
                  >
                    <span>{{ form.handleUserName }}</span>
                  </el-form-item>
                </el-col> -->

                <!--  0=盘点单损益 1=单包装商品损益 2=YSN损益 -->
                <!-- 领单时间 -->
                <!--  <el-col
                  :span="6"
                  v-if="
                    form.profitLossType == 3 ||
                    form.profitLossType == 2 ||
                    form.profitLossType == 1
                  "
                >
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.handleTimeCopy')"
                  >
                    <span>
                      {{ parseDateTime(form.handleTime, "dateTime") }}
                    </span>
                  </el-form-item>
                </el-col>
                <el-col
                  :span="6"
                  v-if="form.profitLossType == 2 || form.profitLossType == 1"
                >
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.remark')"
                    prop="remark"
                  >
                    <span style="word-break: break-all">{{ form.remark }}</span>
                  </el-form-item>
                </el-col> -->
                <!-- </template> -->
                <!-- <template> -->
                <!-- <el-col :span="6" v-if="form.profitLossType == 3">
                  <el-form-item
                    :label="
                      $t(
                        'profitAndLossManagement.label.beforeProfitLossWeightCopy'
                      )
                    "
                  >
                    <span>{{ form.beforeProfitLossWeight }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="form.profitLossType == 3">
                  <el-form-item
                    :label="
                      $t(
                        'profitAndLossManagement.label.afterProfitLossWeightCopy'
                      )
                    "
                  >
                    <span>{{ form.afterProfitLossWeight }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="form.profitLossType == 3">
                  <el-form-item
                    :label="$t('profitAndLossManagement.label.remark')"
                    prop="remark"
                  >
                    <span style="word-break: break-all">{{ form.remark }}</span>
                  </el-form-item>
                </el-col> -->
                <el-col :span="6" v-if="form.profitLossType == 0">
                  <el-form-item :label="$t('profitAndLossManagement.label.handleTimeCopy')">
                    <span>
                      {{ parseDateTime(form.handleTime, "dateTime") }}
                    </span>
                  </el-form-item>
                </el-col>
                <!-- 状态 -->
                <el-col :span="6">
                  <el-form-item :label="$t('profitAndLossManagement.label.status')">
                    <!-- 损益状态 0 草稿 1处理中 2完成 -->

                    <div class="">
                      {{ statusMap(form.status) }}
                      <!-- <span :class="['purchase-status',statusClassMap(form.status)]">{{ statusMap(form.status)}}</span> -->
                    </div>
                  </el-form-item>
                </el-col>

                <el-col :span="6">
                  <el-form-item :label="$t('profitAndLossManagement.label.remark')" prop="remark">
                    <span style="word-break: break-all">{{ form.remark }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="''" prop="imagesUrls">
                    <el-badge :value="form.imagesUrls ? form.imagesUrls.length : 0" :offset="[5, 10]">
                      <el-button type="primary" link @click="handleUploadDialog">
                        附件
                      </el-button>
                    </el-badge>
                  </el-form-item>

                </el-col>
                <!-- </template> -->
              </el-row>
            </template>
          </div>
          <div class="title-lable" style="justify-content: space-between">
            <div class="title-content">
              {{ $t("profitAndLossManagement.label.lossDetail") }}
            </div>
            <div class="button-add cursor-pointer" @click="addProduct()"
              v-if="type != 'detail' && form.profitLossType == 3">
              {{ $t("profitAndLossManagement.button.addBtn") }}
            </div>
          </div>
          <div>
            <!-- 按盘点结果损益 -->
            <el-table v-if="form.profitLossType == 0" v-loading="loading" :data="form.profitLossDetailList"
              highlight-current-row stripe>
              <el-table-column type="index" :label="$t('common.sort')" width="60" />
              <el-table-column :label="$t('profitAndLossManagement.label.productInformation')" min-width="230">
                <template #default="scope">
                  <div class="product-div">
                    <div class="product">
                      <div>
                        <span class="product-key">
                          {{
                            $t("profitAndLossManagement.label.productCode")
                          }}：
                        </span>
                        <span class="product-value">
                          {{ scope.row.productCode }}
                        </span>
                      </div>
                      <div class="product-name">
                        {{ scope.row.productName }}
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('profitAndLossManagement.label.productSpec')" prop="productSpec"
                min-width="100" show-overflow-tooltip />
              <el-table-column :label="$t('profitAndLossManagement.label.beforeCheckTotalQty')"
                prop="beforeCheckTotalQty" min-width="100" align="right" show-overflow-tooltip>
                <template #default="scope">
                  <span style="color: #52585f" v-if="
                    scope.row.beforeCheckTotalQty !== undefined &&
                    scope.row.beforeCheckTotalQty !== null &&
                    scope.row.beforeCheckTotalQty !== ''
                  ">
                    {{ scope.row.beforeCheckTotalQty }}
                  </span>
                  <span style="color: #52585f" v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('profitAndLossManagement.label.afterCheckTotalQty')" prop="afterCheckTotalQty"
                min-width="100" align="right" show-overflow-tooltip>
                <template #default="scope">
                  <span style="color: #52585f" v-if="
                    scope.row.afterCheckTotalQty !== undefined &&
                    scope.row.afterCheckTotalQty !== null &&
                    scope.row.afterCheckTotalQty !== ''
                  ">
                    {{ scope.row.afterCheckTotalQty }}
                  </span>
                  <span style="color: #52585f" v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('profitAndLossManagement.label.lossQty')" prop="lossQty" align="right"
                min-width="120" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.lossQty >= 0" style="color: #29b610">
                    +{{ scope.row.lossQty }}
                  </span>
                  <span v-if="scope.row.lossQty < 0" style="color: #52585f">
                    {{ scope.row.lossQty }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('profitAndLossManagement.label.lossInfo')" min-width="120">
                <template #default="scope">
                  <el-button link type="primary" @click="openDetailLossInfo(scope.row)">
                    {{ $t("profitAndLossManagement.button.openDetail") }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-table v-loading="loading" v-if="form.profitLossType == 1" :data="form.profitLossDetailList"
              highlight-current-row stripe>
              <el-table-column type="index" :label="$t('common.sort')" width="60" />
              <el-table-column :label="$t('profitAndLossManagement.label.ysnCode')" min-width="200"
                show-overflow-tooltip>
                <template #default="scope">
                  <span>{{ scope.row.profitLossDetailYsnVO.ysnCode }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('profitAndLossManagement.label.beforeProfitLossWeight')
                " prop="beforeProfitLossWeight" show-overflow-tooltip />
              <el-table-column :label="$t('profitAndLossManagement.label.afterProfitLossWeight')
                " prop="afterProfitLossWeight" show-overflow-tooltip />
            </el-table>
            <template v-if="form.profitLossType == 2">
              <div class="title-lable">
                <div class="title-content">
                  {{
                    $t("profitAndLossManagement.label.profitLossDetailYsnAdd")
                  }}
                </div>
              </div>
              <el-table v-loading="loading" :data="form.profitLossDetailYsnAddVOList" highlight-current-row stripe>
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <el-table-column :label="$t('profitAndLossManagement.label.ysnCode')" prop="ysnCode" min-width="200"
                  show-overflow-tooltip />
                <el-table-column :label="$t('profitAndLossManagement.label.productInformation')
                  " min-width="230" show-overflow-tooltip>
                  <template #default="scope">
                    <div class="product-div">
                      <div class="product">
                        <div>
                          <span class="product-key">
                            {{
                              $t("profitAndLossManagement.label.productCode")
                            }}：
                          </span>
                          <span class="product-value">
                            {{ scope.row.productCode }}
                          </span>
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.weight')" prop="weight"
                  show-overflow-tooltip />
              </el-table>
              <div class="title-lable">
                <div class="title-content">
                  {{
                    $t("profitAndLossManagement.label.profitLossDetailYsnSub")
                  }}
                </div>
              </div>
              <el-table v-loading="loading" :data="form.profitLossDetailYsnSubVOList" highlight-current-row stripe>
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <el-table-column :label="$t('profitAndLossManagement.label.ysnCode')" prop="ysnCode" min-width="200"
                  show-overflow-tooltip />
                <el-table-column :label="$t('profitAndLossManagement.label.productInformation')
                  " min-width="230" show-overflow-tooltip>
                  <template #default="scope">
                    <div class="product-div">
                      <div class="product">
                        <div>
                          <span class="product-key">
                            {{
                              $t("profitAndLossManagement.label.productCode")
                            }}：
                          </span>
                          <span class="product-value">
                            {{ scope.row.productCode }}
                          </span>
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.weight')" prop="weight"
                  show-overflow-tooltip />
              </el-table>
            </template>
            <!-- 快速损益:损益明细 -->
            <el-table v-if="form.profitLossType == 3" v-loading="loading" :data="form.profitLossDetailList"
              highlight-current-row stripe>
              <el-table-column type="index" :label="$t('common.sort')" width="60" fixed="left" />
              <el-table-column :label="$t('profitAndLossManagement.label.productInformation')" min-width="230"
                fixed="left">
                <template #default="scope">
                  <div class="product-div">
                    <div class="product">
                      <div>
                        <span class="product-key">
                          {{
                            $t("profitAndLossManagement.label.productCode")
                          }}：
                        </span>
                        <span class="product-value">
                          {{ scope.row.productCode }}
                        </span>
                      </div>
                      <div class="product-name">
                        {{ scope.row.productName }}
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <!--  <el-table-column
                v-if="form.profitLossType == 0"
                :label="$t('profitAndLossManagement.label.productSpec')"
                prop="productSpec"
                min-width="100"
                show-overflow-tooltip
              /> -->
              <el-table-column :label="'*' + $t('profitAndLossManagement.label.warehouseAreaCode')
                " prop="warehouseAreaCode" show-overflow-tooltip min-width="230">
                <template #default="scope">
                  <el-form-item class="mt15px" label-width="0px" :prop="'profitLossDetailList.' +
                    scope.$index +
                    '.warehouseAreaCode'
                    " :rules="[
                      {
                        required: true,
                        message: t(
                          'profitAndLossManagement.rules.warehouseAreaCode'
                        ),
                        trigger: ['blur', 'change'],
                      },
                    ]">
                    <el-select v-model="scope.row.warehouseAreaCode" :placeholder="$t('common.placeholder.selectTips')"
                      @change="getWarehouseAreaDetail($event, scope.$index)" clearable v-if="type != 'detail'">
                      <el-option v-for="item in outWarehouseAreaList" :key="item.areaCode"
                        :label="`${item.areaName} | ${item.areaCode}`" :value="item.areaCode" />
                    </el-select>
                    <el-input v-if="type == 'detail'" v-model="scope.row.warehouseAreaCode" disabled></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <!-- 库区总库存 -->
              <el-table-column :label="$t('profitAndLossManagement.label.beforeInventoryQty')" min-width="150"
                show-overflow-tooltip align="right">
                <template #default="scope">
                  <div>
                    {{ t("profitAndLossManagement.label.totalQty") }}({{ scope.row.productUnit }}):{{
                      scope.row.beforeInventoryQty || "-"
                    }}
                  </div>
                  <div>
                    {{ t("profitAndLossManagement.label.totalWeight") }}({{ scope.row.conversionRelSecondUnitName }}):{{
                      scope.row.beforeInventoryWeight || "-"
                    }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('profitAndLossManagement.label.unitPrice')" prop="unitPrice" min-width="150"
                show-overflow-tooltip></el-table-column>
              <!--  <el-table-column
                :label="
                  '*' + $t('profitAndLossManagement.label.afterInventoryQty')
                "
                prop="afterInventoryQty"
                min-width="260"
                v-if="type != 'detail'"
              >
                <template #default="scope">
                  <el-form-item
                    class="mt15px"
                    label-width="0px"
                    :prop="
                      'profitLossDetailList.' +
                      scope.$index +
                      '.afterInventoryQty'
                    "
                    :rules="[
                      {
                        required: true,
                        message: t(
                          'profitAndLossManagement.rules.afterInventoryQtyNull'
                        ),
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern: /^(0|[1-9][0-9]{0,3})$/,
                        message: t(
                          'profitAndLossManagement.rules.afterInventoryQty'
                        ),
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="scope.row.afterInventoryQty"
                      @change="setLossQty(scope.$index, 1)"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      maxLength="8"
                    >
                      <template #append>{{ scope.row.productUnit }}</template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column> -->
              <!--  <el-table-column
                :label="
                  '*' + $t('profitAndLossManagement.label.afterInventoryWeight')
                "
                prop="afterInventoryWeight"
                min-width="260"
                v-if="type != 'detail'"
              >
                <template #default="scope">
                  <el-form-item
                    class="mt15px"
                    label-width="0px"
                    :prop="
                      'profitLossDetailList.' +
                      scope.$index +
                      '.afterInventoryWeight'
                    "
                    :rules="[
                      {
                        required: true,
                        message: t(
                          'profitAndLossManagement.rules.afterInventoryWeightNull'
                        ),
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern:
                          /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                        message: t(
                          'profitAndLossManagement.rules.afterInventoryWeight'
                        ),
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="scope.row.afterInventoryWeight"
                      @change="setLossQty(scope.$index, 2)"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    >
                      <template #append>
                        {{
                          $t(
                            "profitAndLossManagement.label.afterInventoryWeightUnit"
                          )
                        }}
                      </template>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column> -->
              <el-table-column :label="$t('profitAndLossManagement.label.afterInventoryQty')" prop="afterInventoryQty"
                min-width="200" align="right" v-if="type == 'detail'" show-overflow-tooltip />
              <!--损益后库存转换量 -->
              <el-table-column :label="$t('profitAndLossManagement.label.inventoryConversionAfterProfitAndLoss')"
                prop="afterInventoryWeight" min-width="200" align="right" v-if="type == 'detail'"
                show-overflow-tooltip />
              <!-- 金额 -->
              <el-table-column :label="$t('profitAndLossManagement.label.amount')" prop="amount" align="left"
                show-overflow-tooltip min-width="150">
              </el-table-column>
              <!-- 损益后库存总量 -->
              <!-- <el-table-column
                :label="
                  $t('profitAndLossManagement.label.afterInventoryWeight') +
                  '(' +
                  $t('profitAndLossManagement.label.afterInventoryWeightUnit') +
                  ')'
                "
                prop="afterInventoryWeight"
                min-width="200"
                align="right"
                v-if="type == 'detail'"
                show-overflow-tooltip
              /> -->
              <!-- 损益数量 -->
              <el-table-column :label="$t('profitAndLossManagement.label.lossQty')" prop="lossQty" align="right"
                min-width="120" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.lossQty >= 0" style="color: #29b610">
                    +{{ scope.row.lossQty }}
                  </span>
                  <span v-if="scope.row.lossQty < 0" style="color: #52585f">
                    {{ scope.row.lossQty }}
                  </span>
                </template>
              </el-table-column>
              <!-- 损益重量/损益转换量 -->
              <el-table-column :label="$t('profitAndLossManagement.label.lossConversion')" prop="lossWeight" align="right" min-width="120"
                show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.lossWeight >= 0" style="color: #29b610">
                    +{{ scope.row.lossWeight }}
                  </span>
                  <span v-if="scope.row.lossWeight < 0" style="color: #52585f">
                    {{ scope.row.lossWeight }}
                  </span>
                </template>
              </el-table-column>
              <!-- <el-table-column
                :label="
                  $t('profitAndLossManagement.label.lossWeight') +
                  '(' +
                  $t('profitAndLossManagement.label.afterInventoryWeightUnit') +
                  ')'
                "
                prop="lossWeight"
                align="right"
                min-width="120"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span v-if="scope.row.lossWeight >= 0" style="color: #29b610">
                    +{{ scope.row.lossWeight }}
                  </span>
                  <span v-if="scope.row.lossWeight < 0" style="color: #52585f">
                    {{ scope.row.lossWeight }}
                  </span>
                </template>
              </el-table-column> -->
              <!-- <el-table-column
                v-if="type != 'detail'"
                :label="$t('common.handle')"
                width="160"
              >
                <template #default="scope">
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="handleDelete(scope.$index)"
                  >
                    {{ $t("common.delete") }}
                  </el-button>
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("common.reback") }}</el-button>
        <!--                <el-button v-if="type=='add' || type=='edit'" plain @click="handleSubmit(0)" :loading="submitLoading">{{ $t("profitAndLossManagement.button.saveDraft") }}</el-button>-->
        <el-button v-if="type == 'add' || type == 'edit'" type="primary" :loading="submitLoading"
          @click="handleSubmit(1)">
          {{ $t("common.confirm") }}
        </el-button>
      </div>
      <AddProduct ref="addProductRef" v-model:visible="dialog.visible" :title="dialog.title" @onSubmit="onSubmit" />
      <LossInfoListDialog ref="lossInfoListRef" v-model:visible="lossDialog.visible" :title="lossDialog.title" />
      <UploadDialog ref="uploadDialogRef" v-model:visible="uploadDialog.visible" :showUploadBtn="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import profitAndLossManagementApi from "@/modules/wms/api/profitAndLossManagement";
import ProductMgAPI from "@/modules/wms/api/productManagement";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import AddProduct from "./components/addProduct.vue";
import LossInfoListDialog from "./components/lossInfoListDialog.vue";
import { parseDateTime } from "@/core/utils/index.js";
import ProfitAndLossManagementApi, {
  InventoryLossFrom,
} from "@/modules/wms/api/profitAndLossManagement";
import CommonAPI, { ProductAllPageVO } from "@/modules/wms/api/common";
// import UploadMultiple from "@/core/components/Upload/UploadMultiple.vue";
import UploadDialog from "@/modules/wms/components/uploadDialog.vue";
defineOptions({
  name: "AddProfitAndLoss",
  inheritAttrs: false,
});
const uploadDialog = reactive({
  visible: false,
});
const uploadDialogRef = ref();
function handleUploadDialog() {
  uploadDialog.visible = true;
  nextTick(() => {
    uploadDialogRef.value.setFormData(getImagesUrls());
  });
}
function getImagesUrls() {
  return form.imagesUrls || [];
}
const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const submitLoading = ref(false);
const formRef = ref(ElForm);
const productTotal = ref(0);
const lossInfoTotal = ref(0);
const lossInfoList = ref<ProductAllPageVO[]>();
const productAllList = ref<ProductAllPageVO[]>();
const confirmDisabled = ref(false);
const loading = ref(false);
const id = route.query.id;
const type = route.query.type;
const outWarehouseAreaListAll = ref([]);
const outWarehouseAreaList = ref([]);
const dialog = reactive({
  title: "",
  visible: false,
});
const lossDialog = reactive({
  title: "",
  visible: false,
});
const lossInfoListRef = ref();
const addProductRef = ref();
const checkCodeList = ref([]);
const form = reactive<InventoryLossFrom>({
  profitLossDetailList: [],
  profitLossDetailYsnAddVOList: [],
  profitLossDetailYsnSubVOList: [],
});
const profitAndLossTypeList = ref([
  {
    typeId: 0,
    typeName: t("profitAndLossManagement.profitAndLossTypeList.inventoryCheck"),
  },

  {
    typeId: 3,
    typeName: t(
      "profitAndLossManagement.profitAndLossTypeList.fastProfitAndLoss"
    ),
  },
]);
const formUpdateRef = ref(null);
const show = ref(false);
async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}
/** 查询当前仓库的所有库区下拉数据源 */
function getOutWarehouseAreaList() {
  CommonAPI.getOutWarehouseAreaList({}).then((data: any) => {
    outWarehouseAreaListAll.value = data;
  });
}
/** 获取盘点单号下拉列表 */
function queryCheckCodeList() {
  profitAndLossManagementApi.queryCheckCodeList().then((data: any) => {
    checkCodeList.value = data;
  });
}
/** 查询前仓库的库区列表(库存大于0的库区) ---废弃 */
// 根据当前仓库获取有库存的库区列表(可选0:停用 1:正常) ---废弃

// 查询已启用库区
function getOutWarehouseAreaNumberList() {
  return new Promise((resolve, reject) => {
    // CommonAPI.getOutWarehouseAreaNumberList({ status: 1 })
    CommonAPI.getOutWarehouseAreaList({ status: 1 })
      .then((data) => {
        outWarehouseAreaList.value = data;
        if (
          outWarehouseAreaList.value &&
          outWarehouseAreaList.value.length > 0
        ) {
          outWarehouseAreaList.value.map((item) => {
            item.warehouseArea = item.warehouseName + "|" + item.areaName;
            return item;
          });
        }
        resolve();
      })
      .catch((error) => {
        loading.value = false;
        reject(error);
      });
  });
}

function changeCheckCode(val) {
  if (val == 1) {
    form.checkCode = undefined;
    form.warehouseAreaCode = undefined;
  }
  if (form.profitLossType == 0 && form.checkCode !== undefined) {
    show.value = true;
    queryCheckCodeDetail();
  } else {
    show.value = false;
    form.profitLossDetailList = [];
  }
}

/** 获取盘点单详情 */
function queryCheckCodeDetail() {
  let params = {
    checkCode: form.checkCode,
  };
  profitAndLossManagementApi.queryCheckCodeDetail(params).then((data) => {
    if (data) {
      form.warehouseAreaCode = data.warehouseAreaCode;
      if (data.detailList && data.detailList.length > 0) {
        form.profitLossDetailList = data.detailList;
        form.profitLossDetailList.forEach((item) => {
          if (
            item.afterCheckTotalQty !== "" &&
            item.afterCheckTotalQty != null &&
            item.afterCheckTotalQty != undefined
          ) {
            if (item.beforeCheckTotalQty !== "") {
              item.lossQty = Number(
                item.afterCheckTotalQty - item.beforeCheckTotalQty
              ).toFixed(3);
            } else {
              item.lossQty = Number(item.afterCheckTotalQty).toFixed(3);
            }
          } else {
            item.lossQty = Number(item.beforeCheckTotalQty).toFixed(3);
          }
        });
      }
    }
  });
}

/** 查询所有可选的商品列表 */
function queryProductAll(type) {
  return new Promise<void>((resolve, reject) => {
    loading.value = true;
    let params = {
      limit: 20,
      page: 1,
    };
    ProductMgAPI.getPageList(params)
      .then((data) => {
        productAllList.value = [];
        if (data.records && data.records.length > 0) {
          data.records.forEach((data) => {
            let obj = {
              productName: data.productName,
              productCode: data.productCode,
              productSpec: data.productSpec,
              fullCategoryName: data.fullCategoryName,
              productUnit: data.productUnitName,
            };
            productAllList.value.push(obj);
          });
        }
        productTotal.value = parseInt(data.total);
        resolve();
      })
      .catch(() => {
        reject();
      })
      .finally(() => {
        loading.value = false;
      });
    // })
  });
}
// 原损益模块逻辑代码，需要优化
function getWarehouseAreaDetail(event: any, index: number) {
  if (event && event != "") {
    outWarehouseAreaList.value.forEach((item: any) => {
      if (item.areaCode == event) {
        let params = {
          productCode: form.profitLossDetailList[index].productCode,
          warehouseAreaCode: event,
        };
        //查所选库区当前商品的总库存
        profitAndLossManagementApi
          .queryAreaProductStockQty(params)
          .then((data) => {
            form.profitLossDetailList[index].beforeInventoryQty =
              data && data.totalStockQty ? data.totalStockQty : "";
            form.profitLossDetailList[index].beforeInventoryWeight =
              data && data.totalStockWeight ? data.totalStockWeight : "";
            form.profitLossDetailList[index].warehouseAreaName = item.areaName;
            if (
              form.profitLossDetailList[index].afterInventoryQty !== "" &&
              form.profitLossDetailList[index].afterInventoryQty != null &&
              form.profitLossDetailList[index].afterInventoryQty != undefined
            ) {
              if (form.profitLossDetailList[index].beforeInventoryQty !== "") {
                form.profitLossDetailList[index].lossQty = Number(
                  form.profitLossDetailList[index].afterInventoryQty -
                  form.profitLossDetailList[index].beforeInventoryQty
                ).toFixed(3);
              } else {
                form.profitLossDetailList[index].lossQty = Number(
                  form.profitLossDetailList[index].afterInventoryQty
                ).toFixed(3);
              }
            } else {
              form.profitLossDetailList[index].lossQty = "-";
            }
            if (
              form.profitLossDetailList[index].afterInventoryWeight !== "" &&
              form.profitLossDetailList[index].afterInventoryWeight != null &&
              form.profitLossDetailList[index].afterInventoryWeight != undefined
            ) {
              if (
                form.profitLossDetailList[index].beforeInventoryWeight !== ""
              ) {
                form.profitLossDetailList[index].lossWeight = Number(
                  form.profitLossDetailList[index].afterInventoryWeight -
                  form.profitLossDetailList[index].beforeInventoryWeight
                ).toFixed(3);
              } else {
                form.profitLossDetailList[index].lossWeight = Number(
                  form.profitLossDetailList[index].afterInventoryWeight
                ).toFixed(3);
              }
            } else {
              form.profitLossDetailList[index].lossWeight = "-";
            }
          })
          .finally(() => {
            return false;
          });
      }
    });
  } else {
    form.profitLossDetailList[index].beforeInventoryQty = "";
    form.profitLossDetailList[index].warehouseAreaName = "";
    form.profitLossDetailList[index].lossQty = "";
    form.profitLossDetailList[index].lossWeight = "";
  }
}
/* function setLossQty(index, val) {
  if (val == 1) {
    if (
      form.profitLossDetailList[index].beforeInventoryQty ||
      form.profitLossDetailList[index].beforeInventoryQty === 0
    ) {
      if (
        form.profitLossDetailList[index].afterInventoryQty !== "" &&
        form.profitLossDetailList[index].afterInventoryQty != null &&
        form.profitLossDetailList[index].afterInventoryQty != undefined
      ) {
        form.profitLossDetailList[index].lossQty = Number(
          form.profitLossDetailList[index].afterInventoryQty -
          form.profitLossDetailList[index].beforeInventoryQty
        );
      } else {
        form.profitLossDetailList[index].lossQty = "-";
      }
    } else {
      if (
        form.profitLossDetailList[index].afterInventoryQty !== "" &&
        form.profitLossDetailList[index].afterInventoryQty != null &&
        form.profitLossDetailList[index].afterInventoryQty != undefined
      ) {
        form.profitLossDetailList[index].lossQty = Number(
          form.profitLossDetailList[index].afterInventoryQty
        );
      } else {
        form.profitLossDetailList[index].lossQty = "-";
      }
    }
  } else {
    if (
      form.profitLossDetailList[index].beforeInventoryWeight ||
      form.profitLossDetailList[index].beforeInventoryWeight === 0
    ) {
      if (
        form.profitLossDetailList[index].afterInventoryWeight !== "" &&
        form.profitLossDetailList[index].afterInventoryWeight != null &&
        form.profitLossDetailList[index].afterInventoryWeight != undefined
      ) {
        form.profitLossDetailList[index].lossWeight = Number(
          form.profitLossDetailList[index].afterInventoryWeight -
          form.profitLossDetailList[index].beforeInventoryWeight
        ).toFixed(3);
      } else {
        form.profitLossDetailList[index].lossWeight = "-";
      }
    } else {
      if (
        form.profitLossDetailList[index].afterInventoryWeight !== "" &&
        form.profitLossDetailList[index].afterInventoryWeight != null &&
        form.profitLossDetailList[index].afterInventoryWeight != undefined
      ) {
        form.profitLossDetailList[index].lossWeight = Number(
          form.profitLossDetailList[index].afterInventoryWeight
        ).toFixed(3);
      } else {
        form.profitLossDetailList[index].lossWeight = "-";
      }
    }
  }
} */

/** 损益明细查看 */
async function openDetailLossInfo(row) {
  lossDialog.title =
    t("profitAndLossManagement.title.lossInfo") +
    row.productCode +
    " | " +
    row.productName;
  let data = {
    checkDetailId: row.id,
    productCode: row.productCode,
    productName: row.productName,
    checkCode: form.checkCode,
  };
  lossInfoListRef.value.setFormData({
    lossInfoList: lossInfoList.value,
    lossInfoTotal: lossInfoTotal.value,
    queryParams: data,
  });
  lossDialog.visible = true;
}

/** 添加商品 */
async function addProduct() {
  await queryProductAll(null);
  dialog.title = t("profitAndLossManagement.title.addProduct");
  let data = {
    outWarehouseArea: form.outWarehouseArea,
  };
  addProductRef.value.setFormData({
    productAllList: productAllList.value,
    productTotal: productTotal.value,
    queryParams: data,
  });
  addProductRef.value.queryManagerCategoryList();
  dialog.visible = true;
}

function handleSubmit(val) {
  if (
    form.profitLossType == 3 &&
    form.profitLossDetailList &&
    form.profitLossDetailList.length == 0
  ) {
    return ElMessage.error(
      t("profitAndLossManagement.message.addOrEditInventoryLossTips")
    );
  }
  formRef.value.validate((valid) => {
    if (!valid) return;
    let params = {
      profitLossType: form.profitLossType,
      remark: form.remark,
    };
    if (form.profitLossType == 3) {
      let profitLossDetailList = [];
      if (
        form.profitLossType == 3 &&
        form.profitLossDetailList &&
        form.profitLossDetailList.length > 0
      ) {
        let arr = [];
        form.profitLossDetailList.forEach((list) => {
          let obj = {
            productCode: list.productCode,
            warehouseAreaCode: list.warehouseAreaCode,
          };
          arr.push(obj);
        });
        const uniqueItems = new Map();
        for (let i = 0; i < arr.length; i++) {
          const key = JSON.stringify(arr[i]); // 将对象转换为字符串
          if (!uniqueItems.has(key)) {
            uniqueItems.set(key, arr[i]); // 如果尚未存在，则添加到Map中
          } else {
            submitLoading.value = false;
            return ElMessage.error(
              t("profitAndLossManagement.message.sameData")
            );
          }
        }
        form.profitLossDetailList.forEach((item) => {
          let obj = {
            productCode: item.productCode,
            warehouseAreaCode: item.warehouseAreaCode,
            warehouseAreaName: item.warehouseAreaName,
            afterInventoryQty: item.afterInventoryQty,
            afterInventoryWeight: item.afterInventoryWeight,
            id: item.id,
          };
          profitLossDetailList.push(obj);
        });
      }
      params.profitLossDetailList = profitLossDetailList;
    } else if (form.profitLossType == 0) {
      params.checkCode = form.checkCode;
    }
    if (type !== "add") {
      params.id = id;
    }
    submitLoading.value = true;
    ElMessageBox.confirm(
      t("profitAndLossManagement.message.saveTips"),
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }
    ).then(
      () => {
        ProfitAndLossManagementApi.saveInventoryLoss(params)
          .then((data) => {
            ElMessage.success(t("profitAndLossManagement.message.addSuccess"));
            handleClose();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      },
      () => {
        submitLoading.value = false;
        ElMessage.info(t("profitAndLossManagement.message.saveCancel"));
      }
    );
  });
}

/** 假删除*/
function handleDelete(index?: number) {
  form.profitLossDetailList.splice(index, 1);
  ElMessage.success(t("profitAndLossManagement.message.deleteSuccess"));
}
function queryInventoryLossDetail() {
  loading.value = true;
  let params = {
    profitLossId: id,
  };
  ProfitAndLossManagementApi.queryInventoryLossDetail(params)
    .then((data) => {
      Object.assign(form, data);
      /*  if (type == "edit") {
         const set = new Set();
         for (let i = 0; i < outWarehouseAreaList.value.length; i++) {
           const key = outWarehouseAreaList.value[i].areaCode;
           set.add(key);
         }
         for (let i = 0; i < form.profitLossDetailList.length; i++) {
       
           if (!set.has(form.profitLossDetailList[i].warehouseAreaCode)) {
             form.profitLossDetailList[i].warehouseAreaCode = "";
             form.profitLossDetailList[i].warehouseAreaName = "";
           }
         }
       } */
      if (type == "detail") {
        for (let i = 0; i < form.profitLossDetailList.length; i++) {
          form.profitLossDetailList[i].warehouseAreaCode =
            form.profitLossDetailList[i].warehouseAreaName +
            "|" +
            form.profitLossDetailList[i].warehouseAreaCode;
        }

      }

      form.imagesUrls = form.imagesUrls ? JSON.parse(form.imagesUrls) : [];
    })
    .finally(() => {
      loading.value = false;
    });
}
function onSubmit(data) {
  let arr = data.concat(form.profitLossDetailList);
  form.profitLossDetailList = arr;
}

const statusMap = (status: string | number) => {
  const obj = {
    0: "草稿",
    1: "处理中",
    2: "完成",
  };
  return obj[status] || "-";
};
const statusClassMap = (status: string | number) => {
  const obj = {
    0: "purchase-status-color0",
    1: "purchase-status-color1",
    2: "purchase-status-color2",
  };
  return obj[status] || "";
};
onMounted(async () => {
  getOutWarehouseAreaList();
  await getOutWarehouseAreaNumberList();
  queryCheckCodeList();
  if (type !== "add") {
    confirmDisabled.value = true;
    queryInventoryLossDetail();
  }
});
</script>
<style scoped lang="scss">
.addProfitAndLoss {
  background: #ffffff;
  border-radius: 4px;

  .page-content {
    .equal {
      height: 32px;
      line-height: 32px;
      padding: 0px 8px;
      margin-bottom: 18px;
    }

    .button-add {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary);
    }

    .default-supplier {
      margin-left: 8px;
      padding: 2px 7px;
      background: #fe8200;
      border-radius: 4px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 16px;
      text-align: left;font-style: normal;
    }
  }
}
</style>
