import request from "@/core/utils/request";

const WAREHOUSR_BASE_URL = "/supply-wms/warehouseReceiptNotice";

class warehouseEntryNoticeAPI {
  /** 获取分页数据 */
  static queryPageList(queryParams?: PageQuery) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /** 获取不分页数据 */
  static queryList(queryParams?: PageQuery) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryList`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取国际国家列表-含港澳台
   *
   */
  static getAllCountry() {
    return request({
      url: `/supply-base/country/all`,
      method: "get",
    });
  }

  /** 添加 */
  static addWarehouseReceiptNotice(data: addFormData) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }

  /** 编辑 */
  static editWarehouseReceiptNotice(data: addFormData) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/edit`,
      method: "post",
      data: data,
    });
  }

  /** 详情 */
  static queryDetail(data: any) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryDetail`,
      method: "post",
      data: data,
    });
  }

  /** 详情查询(手机号加密) */
  static queryDetailMobileEncrypt(data: any) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryDetailMobileEncrypt`,
      method: "post",
      data: data,
    });
  }

  /** 删除 */
  static delete(data: any) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  /** 商品选择页面分页查询 */
  static queryProductPageList(data: any) {
    return request({
      url: `/supply-wms/product/product/page`,
      method: "post",
      data: data,
    });
  }

  /** 管理端懒加载分类下拉列表 */
  static queryCategoryTreeList(data: any) {
    return request({
      url: `/supply-wms/product/category/queryManagerCategoryList`,
      method: "post",
      data: data,
    });
  }
  /** 获取入库通知单手机号(小眼睛查看手机号) */
  static queryRealPhone(queryParams: any) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/getPhoneNum`,
      method: "get",
      params: queryParams,
    });
  }
}

export interface addFormData {
  id?: string;
  receiptNoticeCode?: string;
  receiptType?: number;
  plannedDeliveryTime?: string;
  purchaseSalesPerson?: string;
  sourceOrderCode?: string;
  customerName?: string;
  supplierName?: string;
  contactPerson?: string;
  countryAreaCode?: string;
  mobile?: string;
  countryId?: string;
  provinceId?: string;
  cityId?: string;
  districtId?: string;
  address?: string;
  remark?: string;
  provinceName?: string;
  areaInfo?: any;
  cityName?: string;
  districtName?: string;
  countryName?: string;
  productList?: any;
  addressShow?: boolean;
  addressFormat?: string;
  mobilePhoneShow?: boolean;
  fullAddress?: string;
  status?: number;
}

export interface WarehousePageQuery extends PageQuery {
  /** 状态 */
  status?: number;
  receiptNoticeCode?: string;
  receiptType?: number;
  sourceOrderCode?: number;
  queryType?: number;
  receiptTypeList?: any;
  statusList?: any;
}

export default warehouseEntryNoticeAPI;
