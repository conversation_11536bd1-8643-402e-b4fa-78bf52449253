<template>
  <div class="app-container purchase-requirements">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" label-width="100px">
        <el-row gutter="20">
          <el-col :span="8">
            <el-form-item
              prop="orderCode"
              :label="$t('purchaseRequirements.label.orderNo')"
            >
              <el-input
                v-model="queryParams.orderCode"
                :placeholder="
                  $t('purchaseRequirements.placeholder.pleaseEnter') +
                  $t('purchaseRequirements.label.orderNo')
                "
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
              prop="customerInfo"
              :label="$t('purchaseRequirements.label.customerInfo')"
            >
              <el-input
                v-model="queryParams.customerInfo"
                :placeholder="
                  $t('purchaseRequirements.placeholder.pleaseEnter') +
                  $t('purchaseRequirements.label.customerInfo')
                "
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              prop="reqCode"
              :label="$t('purchaseRequirements.label.purchaseReqNo')"
            >
              <el-input
                v-model="queryParams.reqCode"
                :placeholder="
                  $t('purchaseRequirements.placeholder.pleaseEnter') +
                  $t('purchaseRequirements.label.purchaseReqNo')
                "
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <el-form-item prop="dateRange" label-width="0">
              <el-select
                v-model="queryParams.dateType"
                :placeholder="$t('common.placeholder.selectTips')"
                class="!w-[200px] ml28px"
              >
                <el-option
                  v-for="item in dateTypeList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
              <el-date-picker
                :editable="false"
                class="!w-[370px]"
                v-model="queryParams.dateRange"
                type="daterange"
                range-separator="~"
                :start-placeholder="$t('purchaseRequirements.label.startDate')"
                :end-placeholder="$t('purchaseRequirements.label.endDate')"
                value-format="YYYY-MM-DD"
                :placeholder="$t('common.placeholder.selectTips')"
              />
              <span
                class="ml16px mr14px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="changeDateRange(1)"
              >
                {{ $t("outboundNotice.label.today") }}
              </span>
              <span
                class="mr14px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="changeDateRange(2)"
              >
                {{ $t("outboundNotice.label.yesterday") }}
              </span>
              <span
                class="mr16px cursor-pointer"
                style="color: var(--el-color-primary)"
                @click="changeDateRange(3)"
              >
                {{ $t("purchaseRequirements.label.weekday") }}
              </span>
            </el-form-item>
          </el-col>

          <el-col :span="8"  class="align-right">
              <el-button
                type="primary"
                @click="handleQuery"
                v-hasPerm="['pms:requirements:search']"
              >
                {{ $t("purchaseRequirements.button.confirm") }}
              </el-button>
              <el-button
                @click="handleReset"
                v-hasPerm="['pms:requirements:reset']"
              >
                {{ $t("purchaseRequirements.button.reset") }}
              </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPerm="['pms:requirements:create']"
        >
          {{ $t("purchaseRequirements.button.newPurchaseRequirement") }}
        </el-button>
      </template>

      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          prop="reqCode"
          :label="$t('purchaseRequirements.label.purchaseReqNo')"
          min-width="180"
        />
        <el-table-column
          prop="orderCode"
          :label="$t('purchaseRequirements.label.orderNo')"
          min-width="180"
        />
        <el-table-column
          :label="$t('purchaseRequirements.label.customerInfo')"
          min-width="200"
        >
          <template #default="scope">
            <div>
              {{ $t("purchaseRequirements.label.labelName") }}：
              <el-tooltip
                v-if="scope.row.customerName"
                effect="dark"
                :content="scope.row.customerName"
                placement="top"
              >
                {{ scope.row.customerName }}
              </el-tooltip>
              <span v-else>{{ scope.row.customerName }}</span>
            </div>
            <div class="encryptBox">
              {{ $t("purchaseRequirements.label.address") }}：
              <template v-if="scope.row.deliveryAddress">
                <el-tooltip
                  effect="dark"
                  :content="scope.row.deliveryAddress"
                  placement="top"
                >
                  {{ scope.row.deliveryAddress }}
                </el-tooltip>
                <el-icon
                  v-if="
                    scope.row.deliveryAddress.includes('*') &&
                    !scope.row.isAddressDecrypted
                  "
                  v-hasPermEye="['pms:requirements:eye']"
                  color="#762ADB "
                  size="16"
                  @click="handleViewInfo(scope.row, 2)"
                >
                  <View />
                </el-icon>
              </template>
              <span v-else>{{ scope.row.deliveryAddress }}</span>
            </div>
            <div class="cursor-pointer">
              {{ $t("purchaseRequirements.label.name") }}：{{
                scope.row.receiveName
              }}
              <el-icon
                v-if="!scope.row.isNameDecrypted"
                @click="handleViewInfo(scope.row, 3)"
                v-hasPermEye="['pms:requirements:eye']"
                color="#762ADB "
                size="16"
              >
                <View />
              </el-icon>
            </div>
            <div class="cursor-pointer">
              {{ $t("purchaseRequirements.label.phone") }}：{{
                scope.row.receiveMobileArea
              }}&nbsp{{ scope.row.receiveMobile }}
              <el-icon
                @click="handleViewInfo(scope.row, 1)"
                v-if="!scope.row.isMobileDecrypted"
                v-hasPermEye="['pms:requirements:eye']"
                color="#762ADB "
                size="16"
              >
                <View />
              </el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="productCount"
          :label="$t('purchaseRequirements.label.goodsQuantity')"
          width="100"
        />
        <el-table-column
          prop="createTime"
          :label="$t('purchaseRequirements.label.createTime')"
          min-width="160"
        >
          <template #default="scope">
            {{ parseTimeUtil(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="receivingTime"
          :label="$t('purchaseRequirements.label.receivingTime')"
          min-width="160"
        >
          <template #default="scope">
            {{ parseTimeUtil(scope.row.expectedDeliveryTimeStar) }}
            -{{
              parseTimeUtil(scope.row.expectedDeliveryTimeEnd, "{h}:{i}:{s}")
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          :label="$t('purchaseRequirements.label.remark')"
          min-width="120"
          show-overflow-tooltip
          :class-name="'text-nowrap'"
        />

        <el-table-column
          prop="reqStatus"
          :label="$t('purchaseRequirements.label.status')"
          width="150"
        >
          <!-- <template #default="scope">
            <el-tag :type="getStatusType(scope.row.reqStatus)">
              {{ getStatusText(scope.row.reqStatus) }}
            </el-tag>
          </template> -->
          <template #default="scope">
            <div
              class="purchase-requirements status"
              :class="getStatusClass[scope.row.reqStatus]"
            >
              {{ getStatusText(scope.row.reqStatus) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseRequirements.label.operation')"
          fixed="right"
          width="180"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="handleDetail(scope.row)"
              v-hasPerm="['pms:requirements:details']"
            >
              {{ $t("purchaseRequirements.button.detail") }}
            </el-button>
            <!--            <el-button link type="danger" @click="handleDelete(scope.row)" v-hasPerm="['pms:requirements:del']">-->
            <el-button
              link
              type="danger"
              v-if="scope.row.showDel"
              @click="handleDelete(scope.row)"
              v-hasPerm="['pms:requirements:del']"
            >
              {{ $t("purchaseRequirements.button.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        layout="prev, pager, next, jumper,total, sizes"
        @pagination="handleQuery"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { convertToTimestamp, parseTime } from "@/core/utils/index";
import type { FormInstance } from "element-plus";
import {
  getPurchaseRequirementsList,
  deletePurchaseRequirement,
  querySrcInfo,
} from "@pms/api/purchaseRequirements";
import moment from "moment";
defineOptions({
  name: "PurchaseRequirements",
  inheritAttrs: false,
});

const { t } = useI18n();
const router = useRouter();
const dateTypeList = ref([
  {
    key: 1,
    value: t("purchaseRequirements.dateTypeList.createDate"),
  },
  {
    key: 2,
    value: t("purchaseRequirements.dateTypeList.expectedDeliveryDate"),
  },
]);

const handleViewInfo = async (row: any, type: number) => {
  try {
    const data = await querySrcInfo({ id: row.id, querySrcType: type });
    if (type === 3) {
      row.receiveName = data.name;
      row.isNameDecrypted = true;
    } else if (type === 1) {
      row.receiveMobile = data.mobile;
      row.isMobileDecrypted = true;
    } else if (type == 2) {
      row.deliveryAddress = data.address;
      row.isAddressDecrypted = true;
    }
  } catch (e) {}
};

// 查询参数
const queryParams = reactive({
  dateType: 1,
  dateRange: [],
  orderCode: "",
  customerInfo: "",
  startCreateTime: "",
  endCreateTime: "",
  purchaseReqNo: "",
  page: 1,
  limit: 20,
  expectedDeliveryTimeStar: "",
  expectedDeliveryTimeEnd: "",
});

const getStatusClass = {
  1: "executing",
  0: "cancelled",
};
// 禁用当前日期之前的日期
const disabledDate = (time: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() < today.getTime();
};
const parseTimeUtil = (time: string | number | Date, pattern: string) => {
  if (time) {
    return parseTime(time, pattern);
  }
  return "";
};

const loading = ref(false);
const total = ref(0);
const tableData = ref([]);
const purchaseReqNoList = ref([]);
const queryFormRef = ref<FormInstance>();

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return "success";
    case 0:
      return "warning";
    case 2:
      return "info";
    default:
      return "";
  }
};

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return t("purchaseRequirements.status.generated");
    case 0:
      return t("purchaseRequirements.status.notGenerated");
    case 2:
      return t("purchaseRequirements.status.closed");
    default:
      return "";
  }
};

// 查询列表
const handleQuery = async () => {
  loading.value = true;
  try {
    let params = {
      ...queryParams,
    };
    if (
      queryParams.dateType == 1 &&
      queryParams.dateRange &&
      queryParams.dateRange.length > 0
    ) {
      params.startCreateTime = convertToTimestamp(
        queryParams.dateRange[0] + " 00:00:00"
      );
      params.endCreateTime = convertToTimestamp(
        queryParams.dateRange[1] + " 23:59:59"
      );
    }
    if (
      queryParams.dateType == 2 &&
      queryParams.dateRange &&
      queryParams.dateRange.length > 0
    ) {
      params.expectedDeliveryTimeStar = convertToTimestamp(
        queryParams.dateRange[0] + " 00:00:00"
      );
      params.expectedDeliveryTimeEnd = convertToTimestamp(
        queryParams.dateRange[1] + " 23:59:59"
      );
    }
    delete params.dateType;
    delete params.dateRange;
    const data = await getPurchaseRequirementsList(params);
    tableData.value = data.records;
    total.value = parseInt(data.total);
  } catch (error) {
    console.error("获取采购需求列表失败:", error);
    ElMessage.error(t("purchaseRequirements.message.queryError"));
  } finally {
    loading.value = false;
  }
};

// 重置查询
const handleReset = () => {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  handleQuery();
};

// 新增
const handleAdd = () => {
  // TODO: 跳转到新增页面
  router.push("/pms/purchase/addPurchaseRequirements");
};

// 查看详情
const handleDetail = (row: any) => {
  // TODO: 跳转到详情页面
  router.push({
    path: "/pms/purchase/purchaseRequirementsDetail",
    query: {
      id: row.id,
    },
  });
};

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t("purchaseRequirements.message.deleteConfirm"),
      t("purchaseRequirements.message.warning"),
      {
        confirmButtonText: t("purchaseRequirements.button.confirm"),
        cancelButtonText: t("purchaseRequirements.button.cancel"),
        type: "warning",
      }
    );

    await deletePurchaseRequirement(row.id);
    ElMessage.success(t("purchaseRequirements.message.deleteSuccess"));
    handleReset();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除采购需求失败:", error);
      ElMessage.error(t("purchaseRequirements.message.deleteError"));
    }
  }
};

/** 时间转换 */
function changeDateRange(val) {
  if (val === 1) {
    // var date = moment(new Date()).format('YYYY-MM-DD')
    var date1 = moment().subtract("days", 0).format("YYYY-MM-DD");
    queryParams.dateRange = [date1, date1];
  } else if (val === 2) {
    // var date = moment(new Date().getTime() - 3600 * 24 * 1000).format('YYYY-MM-DD')
    var date1 = moment().subtract("days", 1).format("YYYY-MM-DD");
    queryParams.dateRange = [date1, date1];
  } else if (val === 3) {
    // var endDate = moment(new Date().getTime() - 3600 * 24 * 1000 * 6).format('YYYY-MM-DD')
    var endDate1 = moment(new Date()).format("YYYY-MM-DD");
    var startDate = moment().subtract("days", 6).format("YYYY-MM-DD");
    queryParams.dateRange = [startDate, endDate1];
  }
}

onActivated(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.purchase-requirements {
  &.status {
    border-radius: 2px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    font-style: normal;
    padding: 6px 16px;
    display: inline-block;

    &.cancelled {
      background: rgba(144, 151, 158, 0.1);
      border: 1px solid rgba(200, 201, 204, 0.2);
      color: #90979e;
    }

    &.executing {
      background: rgba(41, 182, 16, 0.08);
      border: 1px solid rgba(41, 182, 16, 0.2);
      color: #29b610;
    }

    &.dateexpired {
      background: rgba(64, 158, 255, 0.08);
      border: 1px solid rgba(64, 158, 255, 0.2);
      color: #409eff;
    }

    .unwokring {
      background: rgba(255, 156, 0, 0.08);
      border: 1px solid rgba(255, 156, 0, 0.2);
      color: #ff9c00;
    }
  }
}

.purchase-requirements {
  .search-container {
    margin-bottom: 10px;
  }

  .table-container {
    margin-bottom: 20px;
  }

  .date-picker-container {
    display: flex;
    align-items: center;
    gap: 10px;

    .date-shortcuts {
      display: flex;
      gap: 8px;
    }
  }

  .encryptBox {
    // display: inline-flex;
    // justify-content: space-between;
    // align-items: center;
    word-wrap: break-word;
    word-break: break-all;
  }

  .encryptBox-icon {
    margin-left: 4px;
    cursor: pointer;
    // align-self: flex-start;
    vertical-align: text-top;
  }
}
</style>
